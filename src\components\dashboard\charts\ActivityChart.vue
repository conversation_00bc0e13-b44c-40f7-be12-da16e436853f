<template>
  <div class="chart-container">
    <h3 class="chart-title">Recent Activity</h3>
    <div class="chart-wrapper">
      <Doughnut
        v-if="chartData && chartData.datasets[0].data.length > 0"
        :data="chartData"
        :options="chartOptions"
        :height="200"
      />
      <div v-else-if="chartData && chartData.datasets[0].data.length === 0" class="chart-empty">
        No activity found for the selected period
      </div>
      <div v-else class="chart-loading">Loading chart data...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Doughnut } from 'vue-chartjs'
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'
import { useElectronAPI } from '../../../useElectronAPI'
import { useSettingsStore } from '../../../stores/settingsStore'
import { getChartColors, resolveTheme } from '../../../utils/themeUtils'

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend)

interface ActivityData {
  notes: number
  books: number
  sessions: number
}

const db = useElectronAPI()
const settingsStore = useSettingsStore()
const activityData = ref<ActivityData>({ notes: 0, books: 0, sessions: 0 })

const chartData = computed(() => {
  const data = activityData.value
  const total = data.notes + data.books + data.sessions
  
  if (total === 0) {
    return {
      labels: [],
      datasets: [{
        data: [],
        backgroundColor: [],
        borderColor: [],
        borderWidth: 0
      }]
    }
  }

  const theme = resolveTheme(settingsStore.currentTheme)
  const colors = getChartColors(theme)

  return {
    labels: ['Notes Created', 'Books Added', 'Study Sessions'],
    datasets: [
      {
        data: [data.notes, data.books, data.sessions],
        backgroundColor: [
          colors[0],
          colors[1], 
          colors[2]
        ],
        borderColor: [
          colors[0].replace('0.8', '1'),
          colors[1].replace('0.8', '1'),
          colors[2].replace('0.8', '1')
        ],
        borderWidth: 2,
        hoverOffset: 4
      }
    ]
  }
})

const chartOptions = computed<ChartOptions<'doughnut'>>(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        padding: 15,
        usePointStyle: true,
        font: {
          size: 12,
          family: 'Montserrat, sans-serif'
        },
        color: getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim()
      }
    },
    tooltip: {
      backgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg').trim(),
      titleColor: getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim(),
      bodyColor: getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary').trim(),
      borderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-card-border').trim(),
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      callbacks: {
        label: function(context) {
          const label = context.label || ''
          const value = context.parsed
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
          const percentage = total > 0 ? Math.round((value / total) * 100) : 0
          return `${label}: ${value} (${percentage}%)`
        }
      }
    }
  },
  cutout: '60%',
  elements: {
    arc: {
      borderWidth: 2
    }
  }
}))

const loadChartData = async () => {
  try {
    // Get data from the last 7 days
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    // Load notes created in the last 7 days
    const notes = await db.notes.getAll()
    const recentNotes = notes.filter(note => {
      const noteDate = new Date(note.created_at || '')
      return noteDate >= sevenDaysAgo
    })

    // Load books added in the last 7 days
    const books = await db.books.getAll()
    const recentBooks = books.filter(book => {
      const bookDate = new Date(book.created_at || '')
      return bookDate >= sevenDaysAgo
    })

    // Load sessions from the last 7 days
    const sevenDaysAgoStr = sevenDaysAgo.toISOString().split('T')[0]
    const today = new Date().toISOString().split('T')[0]
    const sessions = await db.timer.getSessionsByDateRange(sevenDaysAgoStr, today)

    activityData.value = {
      notes: recentNotes.length,
      books: recentBooks.length,
      sessions: sessions.filter(s => s.is_completed === 1).length
    }
  } catch (error) {
    console.error('Failed to load activity chart data:', error)
  }
}

onMounted(() => {
  loadChartData()
})

// Expose method to refresh data
defineExpose({
  refreshData: loadChartData
})
</script>

<style scoped>
.chart-container {
  background: var(--color-card-bg);
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  padding: 20px;
  margin-bottom: 15px;
}

.chart-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 15px;
  font-family: 'Montserrat', sans-serif;
}

.chart-wrapper {
  height: 200px;
  position: relative;
}

.chart-loading,
.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-tertiary);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
  }
  
  .chart-wrapper {
    height: 180px;
  }
}
</style>
