# Discord Rich Presence Implementation

## Overview
This document outlines the implementation of Discord Rich Presence for the Noti application, allowing users to display their study activities on Discord.

## Files Modified

### New Files Created
- `electron/main/api/discord-rpc-api.ts` - Core Discord RPC integration
- `src/components/settings/DiscordSettings.vue` - Discord settings UI component
- `Code Documentation/Feature Implementations/Discord Integration/discord-rich-presence-implementation.md` - This documentation

### Modified Files
- `package.json` - Added discord-rpc dependency
- `src/stores/settingsStore.ts` - Added Discord settings to app settings
- `electron/main/ipc-handlers.ts` - Added Discord IPC handlers
- `electron/preload/api-bridge.ts` - Exposed Discord API to frontend
- `src/views/SettingsView.vue` - Added Discord settings component
- `src/stores/timerStore.ts` - Integrated Discord presence updates
- `electron/main/index.ts` - Added Discord RPC cleanup on app quit

## What Was Implemented

### 1. Core Discord RPC Integration (`discord-rpc-api.ts`)

**Features:**
- Discord client initialization and connection management
- Automatic reconnection with exponential backoff
- Multiple activity types (study sessions, reading, note-taking, idle)
- Privacy controls for showing/hiding different information
- Proper cleanup and error handling

**Activity Types:**
- **Study Sessions**: Shows session name, category, timer status, and pomodoro count
- **Reading**: Shows book title and reading status
- **Note-taking**: Shows note title and writing status
- **Idle**: Shows basic Noti presence

### 2. Settings Integration

**New Settings Added:**
- `discordRichPresenceEnabled`: Enable/disable Discord Rich Presence
- `discordShowSessionDetails`: Show session name and category
- `discordShowTimer`: Show current timer and remaining time
- `discordShowPomodoroCount`: Show completed pomodoros

**Settings UI:**
- Toggle switches for all Discord settings
- Real-time connection status indicator
- Privacy controls section
- Automatic initialization when enabled

### 3. Timer Store Integration

**Discord Updates Triggered On:**
- Timer start/pause/resume
- Timer type changes (pomodoro ↔ break)
- Session creation/end
- Pomodoro completion

**Information Displayed:**
- Current session name and category
- Timer type (Pomodoro, Short Break, Long Break)
- Remaining time with countdown
- Total completed pomodoros
- Session start time

### 4. IPC Communication

**Discord API Methods:**
- `discord:initialize` - Initialize Discord RPC client
- `discord:setEnabled` - Enable/disable Discord Rich Presence
- `discord:setStudySession` - Update study session activity
- `discord:setReading` - Set reading activity
- `discord:setNoteTaking` - Set note-taking activity
- `discord:setIdle` - Set idle activity
- `discord:clearActivity` - Clear current activity
- `discord:getStatus` - Get connection status
- `discord:destroy` - Cleanup Discord RPC

## Technical Implementation Details

### Discord Application Setup
**Note**: You need to create a Discord application and replace the CLIENT_ID in `discord-rpc-api.ts`:

1. Go to https://discord.com/developers/applications
2. Create a new application named "Noti"
3. Copy the Application ID
4. Replace `CLIENT_ID` in `discord-rpc-api.ts`
5. Upload application assets (noti-logo, timer-running, timer-paused, etc.)

### Connection Management
- Automatic initialization when enabled in settings
- Reconnection attempts with exponential backoff (max 3 attempts)
- Graceful handling of Discord not being available
- Proper cleanup on application exit

### Privacy Controls
Users can control what information is shared:
- **Session Details**: Show/hide session name and category
- **Timer Information**: Show/hide current timer and countdown
- **Pomodoro Count**: Show/hide completed pomodoro count

### Error Handling
- All Discord operations are wrapped in try-catch blocks
- Errors are logged but don't break application functionality
- Graceful degradation when Discord is unavailable
- Connection status monitoring and user feedback

## Integration Points

### Timer Integration
The Discord presence automatically updates when:
- Starting/stopping the timer
- Switching between pomodoro and break periods
- Creating or ending study sessions
- Completing pomodoro cycles

### Settings Integration
- Discord settings are persisted in localStorage
- Settings changes take effect immediately
- Connection status is monitored and displayed
- Automatic initialization based on user preferences

## User Experience

### Settings Interface
- Clean toggle switches for all Discord options
- Real-time connection status indicator
- Privacy controls grouped logically
- Immediate feedback on setting changes

### Discord Display Examples
**During Pomodoro:**
```
Playing Noti
Studying: Deep Work Session
Category: Work • 🍅 3 pomodoros
25:00 remaining
```

**During Break:**
```
Playing Noti
Taking a break
Short Break • 🍅 3 pomodoros
5:00 remaining
```

**Privacy Mode:**
```
Playing Noti
Studying
In a study session
```

## Testing Recommendations

### Manual Testing
1. **Enable Discord Rich Presence** in settings
2. **Create a study session** and verify it appears on Discord
3. **Start/pause timer** and verify status updates
4. **Complete pomodoros** and verify count updates
5. **Test privacy settings** by toggling each option
6. **Test connection recovery** by closing/reopening Discord

### Edge Cases
- Discord not installed/running
- Network connectivity issues
- Application ID not configured
- Rapid setting changes
- App quit during active session

## Future Enhancements

### Potential Features
- Custom activity messages
- Book reading integration
- Study group sessions
- Achievement displays
- Time-based statistics

### Technical Improvements
- Dynamic timeout scaling
- Better error recovery
- Activity caching
- Performance optimizations

## Dependencies Added
- `discord-rpc`: ^4.0.1 - Discord Rich Presence client library

## Configuration Required
1. Create Discord application at https://discord.com/developers/applications
2. Replace CLIENT_ID in `discord-rpc-api.ts` with your application ID
3. Upload application assets (logos, icons) to Discord Developer Portal
4. Test connection with Discord client running

## Conclusion
The Discord Rich Presence integration provides a seamless way for users to share their study activities with friends while maintaining full control over privacy. The implementation is robust, handles errors gracefully, and integrates naturally with the existing timer and settings systems.
