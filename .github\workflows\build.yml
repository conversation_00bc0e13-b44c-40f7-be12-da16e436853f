name: Build
on:
  push:
    branches: [main]
    paths-ignore:
      - "**.md"
      - "**.spec.js"
      - ".idea"
      - ".vscode"
      - ".dockerignore"
      - "Dockerfile"
      - ".gitignore"
      - ".github/**"
      - "!.github/workflows/build.yml"
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          
      # Set up Python 3.11 instead of using latest Python
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      # Install setuptools which includes distutils
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install setuptools
          
      - name: Install Dependencies
        run: npm install
        
      # Add postinstall script to package.json
      - name: Add postinstall script
        run: |
          node -e "const fs=require('fs');const pkg=JSON.parse(fs.readFileSync('package.json'));pkg.scripts=pkg.scripts||{};pkg.scripts.postinstall='electron-builder install-app-deps';fs.writeFileSync('package.json',JSON.stringify(pkg,null,2))"
        
      # Run electron-builder install-app-deps explicitly
      - name: Rebuild native dependencies
        run: npx electron-builder install-app-deps
        
      - name: Build Release Files
        run: npm run build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: release_on_${{ matrix.os }}
          path: release/
          retention-days: 5
