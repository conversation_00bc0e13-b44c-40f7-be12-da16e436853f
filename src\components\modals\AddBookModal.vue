<template>
  <Teleport to="body">
    <div class="modal-overlay">
    <div class="modal-content">      <div class="modal-header">
        <h3>Add Book</h3>
        <div class="close-button" @click="handleClose">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>
      </div>

      <div class="search-section">
        <div class="search-container">
          <div class="search-icon-wrapper">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="search-icon">
              <path d="M12 6.79999C12 3.92811 9.67185 1.59999 6.8 1.59999C3.92811 1.59999 1.59999 3.92811 1.59999 6.79999C1.59999 9.67185 3.92811 12 6.8 12C9.67185 12 12 9.67185 12 6.79999ZM13.6 6.79999C13.6 8.39225 13.0527 9.85667 12.136 11.0155L15.7648 14.6335L15.7793 14.6482C16.0777 14.9612 16.0736 15.4568 15.7665 15.7648C15.4595 16.0728 14.9638 16.0784 14.65 15.7809L14.6352 15.7665L11.0033 12.1456C9.84646 13.0565 8.38669 13.6 6.8 13.6C3.04445 13.6 1.97924e-07 10.5555 0 6.79999C0 3.04445 3.04445 0 6.8 0C10.5555 1.97971e-07 13.6 3.04445 13.6 6.79999Z" fill="var(--color-primary)"/>
            </svg>
          </div>
          <input
            ref="searchInputRef"
            type="text"
            placeholder="Search by title, author, or ISBN..."
            v-model="searchQuery"
            @input="handleSearchInput"
            class="search-input"
          />
        </div>
      </div>      <div class="books-list-container">
        <div class="books-list">
          <div v-if="loading" class="loading-state">
            <p>Searching for books...</p>
          </div>
          <div v-else-if="error" class="error-state">
            <p>{{ error }}</p>
            <button @click="retrySearch" class="retry-button">Retry</button>
          </div>
          <div v-else-if="showTooShortMessage" class="too-short-state">
            <p>Type at least 3 characters to search for books</p>
          </div>
          <div v-else-if="hasSearched && searchResults.length === 0 && !loading" class="no-results">
            <p>No books found matching "{{ searchQuery }}".</p>
            <p class="no-results-hint">Try searching with different terms or add your book manually.</p>
          </div>
          <div v-else-if="!searchQuery" class="search-prompt">
            <p>Start typing to search for books.</p>
          </div>
          <div v-for="(book, index) in searchResults" :key="index" class="book-item">
            <div class="book-thumbnail">
              <img v-if="book.cover_url" :src="book.cover_url" :alt="book.title" class="cover-image" />
              <div v-else class="default-cover">
                <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="32" height="40" rx="2" fill="var(--color-border-primary)"/>
                  <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="var(--color-text-muted)"/>
                </svg>
              </div>
            </div>
            <div class="book-info">
              <div class="book-title">{{ book.title }}</div>
              <div class="book-author" v-if="book.author_name">
                by {{ book.author_name.join(', ') }}
              </div>
              <div class="book-details">
                <div v-if="book.first_publish_year" class="book-year">{{ book.first_publish_year }}</div>
                <div v-if="book.isbn_primary" class="book-isbn">ISBN: {{ book.isbn_primary }}</div>
                <div v-if="book.genres" class="book-genres">{{ book.genres }}</div>
              </div>
            </div>            <div class="add-button-container">
              <div>                <button
                  class="edit-button"
                  @click="handleEditBook(book)"
                  :disabled="isAddingAnyBook"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="edit-icon">
                    <path d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <span>Edit</span>
                </button>                <button
                  class="add-button"
                  @click="handleAddBook(book)"
                  :disabled="isAddingAnyBook"
                >
                  <svg width="12" height="12" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="add-icon" v-if="addingBookIndex !== index">
                    <path d="M8 0V16M0 8H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                  <span v-if="addingBookIndex === index">Adding...</span>
                  <span v-else>Add</span>
                </button>
              </div>
              <div v-if="bookErrors.has(index)" class="book-error-message">
                {{ bookErrors.get(index) }}
              </div>
            </div>
          </div>
        </div>
      </div>      <div class="modal-footer">
        <button class="btn btn-secondary" @click="handleClose">Cancel</button>
        <button class="btn btn-primary" @click="handleAddManually">
          <img src="/icons/books-icon.svg" alt="Books" class="books-icon" />
          <span>Add Manually</span>
        </button>
      </div>
    </div>
  </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from 'vue'
import { useElectronAPI } from '../../useElectronAPI'
import type { BookSearchResult } from '../../types/electron-api'

export default defineComponent({
  name: 'AddBookModal',
  props: {
    searchQuery: {
      type: String,
      default: ''
    },
    searchResults: {
      type: Array as () => BookSearchResult[],
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    hasSearched: {
      type: Boolean,
      default: false
    },
    showTooShortMessage: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'add-book', 'add-manually', 'edit-book', 'search-update'],
  setup(props, { emit }) {
    const db = useElectronAPI()
    const searchInputRef = ref<HTMLInputElement | null>(null)

    // Use local refs that sync with props for reactive updates
    const searchQuery = ref(props.searchQuery)
    const searchResults = ref<BookSearchResult[]>([...props.searchResults])
    const loading = ref(props.loading)
    const error = ref(props.error)
    const hasSearched = ref(props.hasSearched)
    const showTooShortMessage = ref(props.showTooShortMessage)

    // Local state for UI interactions
    const isAddingAnyBook = ref(false)
    const addingBookIndex = ref<number | null>(null)
    const bookErrors = ref(new Map<number, string>())

    let searchTimeout: number | null = null
    let tooShortTimeout: number | null = null

    // Function to emit search state updates to parent
    const emitSearchUpdate = () => {
      emit('search-update', {
        query: searchQuery.value,
        results: searchResults.value,
        loading: loading.value,
        error: error.value,
        hasSearched: hasSearched.value,
        showTooShortMessage: showTooShortMessage.value
      })
    }

    const searchBooks = async (query: string) => {
      if (!query.trim() || query.trim().length < 3) {
        searchResults.value = []
        hasSearched.value = false
        emitSearchUpdate()
        return
      }

      try {
        loading.value = true
        error.value = ''
        hasSearched.value = false
        emitSearchUpdate()

        const results = await db.books.searchOnline(query.trim(), 20)
        searchResults.value = results
        hasSearched.value = true
      } catch (err) {
        console.error('Search failed:', err)

        // Check if it's a 503 server unavailable error
        if (err && (
          (typeof err === 'object' && 'status' in err && err.status === 503) ||
          (typeof err === 'object' && 'code' in err && err.code === 503) ||
          (err.toString && err.toString().includes('503')) ||
          (typeof err === 'object' && 'message' in err && typeof err.message === 'string' && err.message.includes('503'))
        )) {
          error.value = 'Server is currently unavailable. Please try again later.'
        } else {
          error.value = 'Failed to search for books. Please check your internet connection and try again.'
        }

        searchResults.value = []
        hasSearched.value = true
      } finally {
        loading.value = false
        emitSearchUpdate()
      }
    }
      const handleSearchInput = () => {
      // Clear previous timeouts
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      if (tooShortTimeout) {
        clearTimeout(tooShortTimeout)
      }

      // Reset states
      showTooShortMessage.value = false
      hasSearched.value = false
      bookErrors.value.clear() // Clear any book errors when searching

      const query = searchQuery.value.trim()

      if (!query) {
        // Empty search - clear everything immediately
        searchResults.value = []
        emitSearchUpdate()
        return
      }

      if (query.length < 3) {
        // Too short - show message after 300ms delay
        searchResults.value = []
        tooShortTimeout = window.setTimeout(() => {
          if (searchQuery.value.trim().length > 0 && searchQuery.value.trim().length < 3) {
            showTooShortMessage.value = true
            emitSearchUpdate()
          }
        }, 300)
        return
      }

      // Valid length - search after 500ms debounce
      searchTimeout = window.setTimeout(() => {
        searchBooks(searchQuery.value)
      }, 500)
    }

    const retrySearch = () => {
      searchBooks(searchQuery.value)
    }
      const handleClose = () => {
      // Clear any pending search
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      if (tooShortTimeout) {
        clearTimeout(tooShortTimeout)
      }
      // Clear all errors when closing
      bookErrors.value.clear()
      emit('close')
    }
      const handleAddBook = async (book: BookSearchResult) => {
      const bookIndex = searchResults.value.indexOf(book)

      try {
        isAddingAnyBook.value = true
        addingBookIndex.value = bookIndex
        bookErrors.value.delete(bookIndex) // Clear any previous error

        // Create a clean, serializable copy before emitting
        const cleanBook: BookSearchResult = {
          title: book.title,
          author_name: book.author_name ? [...book.author_name] : undefined,
          isbn: book.isbn ? [...book.isbn] : undefined,
          cover_i: book.cover_i,
          cover_edition_key: book.cover_edition_key,
          first_publish_year: book.first_publish_year,
          language: book.language ? [...book.language] : undefined,
          edition_count: book.edition_count,
          key: book.key,
          subject: book.subject ? [...book.subject] : undefined,
          publisher: book.publisher ? [...book.publisher] : undefined,
          publish_year: book.publish_year ? [...book.publish_year] : undefined,
          oclc: book.oclc ? [...book.oclc] : undefined,
          lccn: book.lccn ? [...book.lccn] : undefined,
          olid: book.olid,
          cover_url: book.cover_url,
          description: book.description,
          genres: book.genres,
          isbn_primary: book.isbn_primary
        }

        // Add a delay to show the "Adding..." state before emitting and closing
        setTimeout(() => {
          // Emit the book to parent component (BooksView) to handle
          emit('add-book', cleanBook)
          // Close modal immediately after emitting
          handleClose()
        }, 1500) // 1.5 second delay
      } catch (err: any) {
        console.error('Failed to add book:', err)

        // Check if it's a duplicate book error
        if (err && err.message && err.message.includes('already exists in your library')) {
          bookErrors.value.set(bookIndex, 'This book is already in your library')
        } else {
          bookErrors.value.set(bookIndex, 'Failed to add book. Please try again.')
        }

        // Remove from adding state on error
        isAddingAnyBook.value = false
        addingBookIndex.value = null
      }
    }
      const handleAddManually = () => {
      emit('add-manually')
    }

    const handleEditBook = (book: BookSearchResult) => {
      // Create a clean, serializable copy before emitting
      const cleanBook: BookSearchResult = {
        title: book.title,
        author_name: book.author_name ? [...book.author_name] : undefined,
        isbn: book.isbn ? [...book.isbn] : undefined,
        cover_i: book.cover_i,
        cover_edition_key: book.cover_edition_key,
        first_publish_year: book.first_publish_year,
        language: book.language ? [...book.language] : undefined,
        edition_count: book.edition_count,
        key: book.key,
        subject: book.subject ? [...book.subject] : undefined,
        publisher: book.publisher ? [...book.publisher] : undefined,
        publish_year: book.publish_year ? [...book.publish_year] : undefined,
        oclc: book.oclc ? [...book.oclc] : undefined,
        lccn: book.lccn ? [...book.lccn] : undefined,
        olid: book.olid,
        cover_url: book.cover_url,
        description: book.description,
        genres: book.genres,
        isbn_primary: book.isbn_primary
      }

      emit('edit-book', cleanBook)
    }
      // Watch for search query changes
    watch(searchQuery, (newQuery) => {
      if (!newQuery.trim()) {
        searchResults.value = []
        showTooShortMessage.value = false
        hasSearched.value = false
        bookErrors.value.clear() // Clear errors when query changes
        if (searchTimeout) {
          clearTimeout(searchTimeout)
        }
        if (tooShortTimeout) {
          clearTimeout(tooShortTimeout)
        }
        emitSearchUpdate()
      }
    })

    // Watch for prop changes and sync local state
    watch(() => props.searchQuery, (newQuery) => {
      searchQuery.value = newQuery
    })

    watch(() => props.searchResults, (newResults) => {
      searchResults.value = [...newResults]
    })

    watch(() => props.loading, (newLoading) => {
      loading.value = newLoading
    })

    watch(() => props.error, (newError) => {
      error.value = newError
    })

    watch(() => props.hasSearched, (newHasSearched) => {
      hasSearched.value = newHasSearched
    })

    watch(() => props.showTooShortMessage, (newShowTooShortMessage) => {
      showTooShortMessage.value = newShowTooShortMessage
    })

    // Focus the search input when modal opens
    const focusSearchInput = () => {
      if (searchInputRef.value) {
        searchInputRef.value.focus()
      }
    }

    // Auto-focus search input when component mounts
    onMounted(() => {
      // Use setTimeout to ensure the modal is fully rendered
      setTimeout(() => {
        focusSearchInput()
      }, 100)
    })

    return {
      searchInputRef,
      searchQuery,
      searchResults,
      loading,
      error,
      isAddingAnyBook,
      addingBookIndex,
      bookErrors,
      showTooShortMessage,
      hasSearched,
      handleSearchInput,
      retrySearch,
      handleClose,
      handleAddBook,
      handleAddManually,
      handleEditBook
    }
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; /* Ensure it's on top */
}

.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 772px;
  max-width: 90%;
  height: 720px; /* Increased height to display more books */
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden; /* For rounded corners */
}

.modal-header {
  padding: 24px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

.search-section {
  padding: 20px 32px;
}

.search-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid var(--color-border-primary);
  border-radius: 5px;
  gap: 8px;
}

.search-input {
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: var(--color-input-text);
  background: transparent;
}

.books-list-container {
  flex: 1;
  overflow: hidden;
  margin-bottom: 20px;
  border: 1px solid var(--color-border-primary);
  border-radius: 5px;
  margin-left: 32px;
  margin-right: 32px;
  display: flex;
  position: relative;
}

.books-list {
  overflow-y: auto;
  flex: 1;
  height: 440px; /* Increased height to display more books */
  padding: 10px 20px 10px 20px;
  scroll-snap-type: y proximity;
  scrollbar-gutter: stable; /* Reserves space for the scrollbar */
  box-sizing: border-box;
}

/* Custom scrollbar styling for books list */
.books-list::-webkit-scrollbar {
  width: 8px;
  position: absolute;
  right: 0;
}

.books-list::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 0;
}

.books-list::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.books-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.book-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 10px;
  background-color: var(--color-bg-secondary);
  border-radius: 5px;
  scroll-snap-align: start;
}

.book-thumbnail {
  width: 48px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--color-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-tertiary);
}

.book-info {
  margin-left: 16px;
  flex-grow: 1;
}

.book-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
}

.book-author {
  color: var(--color-text-secondary);
  font-size: 14px;
  margin-bottom: 6px;
}

.book-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
}

.book-year {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.book-isbn {
  font-size: 11px;
  color: var(--color-text-tertiary);
  font-family: monospace;
}

.book-genres {
  font-size: 11px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  font-size: 16px;
  text-align: center;
  padding: 20px;
}

.add-button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.add-button-container > div:first-child {
  display: flex;
  gap: 8px;
  align-items: center;
}

.add-button,
.edit-button {
  min-width: 70px;
  height: 36px;
  border-radius: 8px;
  border: none;
  color: var(--color-text-primary);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0 12px;
}

.add-button {
  background-color: var(--color-btn-secondary-bg);
}

.edit-button {
  background-color: var(--color-btn-secondary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.edit-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.add-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.book-error-message {
  font-size: 11px;
  color: var(--color-error);
  text-align: right;
  max-width: 120px;
  line-height: 1.2;
  margin-top: 2px;
}

.add-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.edit-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.edit-button:disabled,
.add-button:disabled {
  background-color: var(--color-bg-tertiary);
  cursor: not-allowed;
}

.modal-footer {
  padding: 24px 40px;
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.manual-add-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.manual-add-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.books-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}

.loading-state,
.error-state,
.search-prompt,
.too-short-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-secondary);
}

.retry-button {
  margin-top: 8px;
  padding: 4px 12px;
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.retry-button:hover {
  background-color: var(--color-btn-primary-hover);
}

.no-results-hint {
  font-size: 12px;
  color: var(--color-text-tertiary);
  margin-top: 4px;
}

.add-button:disabled {
  background-color: var(--color-bg-tertiary);
  cursor: not-allowed;
}

.add-button:disabled:hover {
  background-color: var(--color-bg-tertiary);
}

@media (max-width: 991px) {
  .modal-content {
    width: 90%;
  }

  .books-list-container {
    margin-left: 20px;
    margin-right: 20px;
  }
}

@media (max-width: 640px) {
  .modal-header h3 {
    font-size: 20px;
  }

  .search-section {
    padding: 16px;
  }

  .books-list-container {
    margin-left: 16px;
    margin-right: 16px;
  }

  .book-item {
    flex-direction: column;
    text-align: center;
  }

  .book-info {
    margin: 12px 0;
    text-align: center;
  }

  .add-button {
    width: 100%;
    margin-top: 10px;
  }

  .modal-footer {
    padding: 16px;
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}
</style>
