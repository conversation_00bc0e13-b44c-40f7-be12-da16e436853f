<template>
  <div class="session-card" :class="{ 'active-session': isActive }" @click="handleCardClick">
    <div class="session-card-container" :class="{ 'active-container': isActive }"></div>
    <div class="session-title" :class="{ 'active-title': isActive }">
      {{ title || 'Unnamed Session' }}
    </div>
    <div class="session-category">{{ category || 'No Category' }}</div>
    <div class="session-date">{{ formattedDate }}</div>

    <div class="timer-container">
      <div class="timer-wrapper">
        <div class="timer-icon">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.67 1.33H9.33" stroke="#777" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 3.33V6" stroke="#777" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 14.67C10.95 14.67 13.33 12.28 13.33 9.33C13.33 6.39 10.95 4 8 4C5.05 4 2.67 6.39 2.67 9.33C2.67 12.28 5.05 14.67 8 14.67Z" stroke="#777" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="timer-value">{{ formattedFocusTime }}</div>
      </div>
    </div>

    <div class="pomodoro-container">
      <div class="pomodoro-wrapper">
        <div class="pomodoro-icon">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.0275 5.24047C13.8695 5.02682 13.5683 4.98178 13.3546 5.13963C13.141 5.29763 13.0958 5.59885 13.2538 5.81253C13.9718 6.78357 14.3513 7.91363 14.3513 9.08063C14.3513 12.3654 11.3481 15.0378 7.65678 15.0378C3.96544 15.0378 0.962344 12.3654 0.962344 9.0806C0.962344 7.25644 1.91009 5.53044 3.51313 4.40338L4.32928 5.02278L3.76716 6.85878C3.71359 7.03366 3.76387 7.22375 3.89681 7.34938C4.02978 7.475 4.22247 7.51425 4.394 7.45091L7.65713 6.24466L10.9203 7.45091C10.9746 7.47097 11.031 7.48078 11.087 7.48078C11.2078 7.48078 11.3266 7.43522 11.4175 7.34938C11.5504 7.22382 11.6007 7.03363 11.5471 6.85878L10.9848 5.02278L13.1612 3.371C13.3136 3.25535 13.383 3.06038 13.3379 2.87444C13.2928 2.68853 13.1419 2.54694 12.9535 2.51385C12.3403 2.40622 11.682 2.37813 10.9981 2.43028C9.73313 2.52685 8.67819 2.87816 8.13778 3.09053V1.57541L9.22463 0.887753C9.44916 0.745691 9.51603 0.448503 9.37397 0.223941C9.23194 -0.000590593 8.93475 -0.0674656 8.71016 0.0745969L7.39941 0.903909C7.26003 0.992097 7.17553 1.14553 7.17553 1.3105V3.09035C6.63119 2.87672 5.57697 2.52653 4.316 2.43025C4.09297 2.41316 3.87309 2.4045 3.66253 2.4045H3.65166C3.20563 2.4045 2.77087 2.44116 2.35962 2.51353C2.17122 2.54663 2.02038 2.68822 1.97534 2.87419C1.93031 3.06013 1.99975 3.25503 2.15209 3.37066L2.71306 3.79641C1.00275 5.10482 0 7.0366 0 9.08063C9.375e-05 12.896 3.43491 16 7.65681 16C11.8787 16 15.3135 12.896 15.3135 9.08063C15.3135 7.70619 14.8688 6.37832 14.0275 5.24047ZM4.24284 3.38969C5.81606 3.50978 7.05328 4.079 7.14697 4.12297L7.44659 4.2686C7.57916 4.33297 7.73388 4.33313 7.86656 4.26894L8.16788 4.12307C8.18056 4.117 9.45381 3.51319 11.0715 3.38969C11.241 3.37675 11.4098 3.36925 11.5745 3.36732L10.1357 4.45925C9.97497 4.58122 9.90741 4.7905 9.9665 4.98338L10.3426 6.21138L7.82403 5.28038C7.71641 5.2406 7.59806 5.2406 7.49038 5.28038L4.97169 6.21144L5.34769 4.98335C5.40678 4.79047 5.33916 4.58122 5.17847 4.45925L3.73944 3.36716C3.90222 3.369 4.07119 3.37657 4.24284 3.38969Z" fill="#777"/>
            <path d="M7.65676 13.5944C4.7613 13.5944 2.40564 11.5695 2.40564 9.08061C2.40564 8.81489 2.62105 8.59949 2.88676 8.59949C3.15248 8.59949 3.36789 8.81489 3.36789 9.08061C3.36789 11.039 5.29189 12.6322 7.65676 12.6322C7.92248 12.6322 8.13789 12.8476 8.13789 13.1133C8.13789 13.379 7.92248 13.5944 7.65676 13.5944Z" fill="#777"/>
            <path d="M3.65183 2.88577C3.85699 2.88549 4.06618 2.89377 4.27946 2.91005C6.00393 3.04167 7.34287 3.68358 7.35487 3.6892L7.6569 3.83599L7.95821 3.69011C7.9714 3.68358 9.3104 3.0417 11.0349 2.91002C11.6836 2.86052 12.2956 2.88686 12.8704 2.98777L10.4265 4.84258L11.0871 6.99967L7.65715 5.7318L4.22721 6.99967L4.88758 4.84258L2.44312 2.98745C2.82937 2.91955 3.23221 2.88577 3.65183 2.88577Z" fill="#777"/>
          </svg>
        </div>
        <div class="pomodoro-count">{{ formattedPomodoroCount }}</div>
      </div>
    </div>

    <div class="session-status" :class="{ 'status-active': isActive }">
      {{ isActive ? 'Active' : 'Finished' }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'SessionCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    category: {
      type: String,
      default: ''
    },
    date: {
      type: Date,
      required: true
    },
    totalFocusTime: {
      type: Number,
      default: 0
    },
    pomodoroCount: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: false
    },
    sessionId: {
      type: Number,
      default: undefined
    },
    focus: {
      type: String,
      default: ''
    }
  },
  emits: ['view-details'],
  setup(props, { emit }) {
    const formattedDate = computed(() => {
      try {
        return props.date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        });
      } catch (error) {
        return 'Invalid Date';
      }
    });
    
    const formattedFocusTime = computed(() => {
      const timeInSeconds = Number(props.totalFocusTime) || 0;
      const totalMinutes = Math.floor(timeInSeconds / 60);
      
      if (totalMinutes < 60) {
        return `${totalMinutes}m`;
      }
      
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      return minutes === 0 ? `${hours}h` : `${hours}h ${minutes}m`;
    });
    
    const formattedPomodoroCount = computed(() => {
      const count = Number(props.pomodoroCount) || 0;
      return count === 1 ? `${count} Pomodoro` : `${count} Pomodoros`;
    });

    const handleCardClick = () => {
      emit('view-details', {
        id: props.sessionId,
        sessionName: props.title,
        focus: props.focus,
        category: props.category,
        date: props.date,
        totalFocusTime: props.totalFocusTime,
        pomodoroCount: props.pomodoroCount,
        isActive: props.isActive
      });
    };
    
    return {
      formattedDate,
      formattedFocusTime,
      formattedPomodoroCount,
      handleCardClick
    };
  }
});
</script>

<style scoped>
.session-card {
  width: 300px;
  height: 127px;
  position: relative;
  cursor: pointer;
  transition: opacity 0.3s ease;
  contain: layout style paint;
}

.session-card:hover .session-card-container {
  background-color: var(--color-nav-item-hover);
}

.session-card-container {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  position: absolute;
  background-color: var(--color-card-bg);
  transform: translateZ(0);
}

.session-title {
  font-family: Montserrat;
  font-weight: 700;
  font-size: 16px;
  color: var(--color-text-primary);
  width: calc(100% - 20px);
  position: absolute;
  left: 10px;
  top: 14px;
  height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  z-index: 1;
}

.session-category {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 12px;
  color: var(--color-text-secondary);
  position: absolute;
  left: 10px;
  top: 34px;
  height: 15px;
  z-index: 1;
}

.session-date {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 12px;
  color: var(--color-text-secondary);
  position: absolute;
  left: 10px;
  top: 98px;
  height: 15px;
  z-index: 1;
}

.timer-container {
  position: absolute;
  left: 10px;
  top: 61px;
  z-index: 1;
}

.timer-wrapper {
  display: flex;
  align-items: center;
}

.timer-icon {
  display: flex;
  width: 16px;
  height: 16px;
  align-items: center;
  justify-content: center;
}

.timer-value {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-left: 7px;
}

.pomodoro-container {
  position: absolute;
  left: 93px;
  top: 62px;
  z-index: 1;
}

.pomodoro-wrapper {
  display: flex;
  align-items: center;
}

.pomodoro-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pomodoro-count {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-left: 7px;
  white-space: nowrap;
}

.session-status {
  position: absolute;
  bottom: 8px;
  right: 10px;
  font-family: Montserrat;
  font-weight: 500;
  font-size: 10px;
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: var(--color-bg-secondary);
  z-index: 2;
}

/* Active session styles */
.active-container {
  border: 2px solid var(--color-primary);
  background: var(--color-nav-item-active);
  box-shadow: var(--color-card-hover-shadow) 0 4px 15px;
}

.active-title {
  color: var(--color-primary);
  font-weight: 700;
}

.status-active {
  color: var(--color-primary);
  background-color: var(--color-nav-item-active);
}
</style>