# Change Detector SQLite3 Migration Fix

## Files Modified
- `electron/main/api/sync-logic/change-detector.ts`

## What Was Done
Fixed the ChangeDetector module to remove better-sqlite3 dependency and use the callback-based sqlite3 API instead, following the existing pattern from database-api.ts.

## How It Was Fixed

### 1. Updated Import Statements
- Removed `import * as sqlite3 from 'sqlite3'` 
- Changed to `import { Database } from 'sqlite3'`
- Removed the local type alias for Database

### 2. Converted Database Query Methods
All database query methods were converted from synchronous to asynchronous callback-based API:

#### getDbBooks()
```typescript
private getDbBooks(): Promise<SyncItem[]> {
  return new Promise((resolve, reject) => {
    this.db.all(
      `SELECT id, title, author, isbn, cover_image, created_at, updated_at
       FROM books
       ORDER BY created_at DESC`,
      [],
      (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        
        const books = rows as any[];
        const syncItems = books.map(book => ({
          id: book.id.toString(), // Convert to string for consistency
          type: 'book' as const,
          hash: this.generateHash(book),
          lastModified: new Date(book.updated_at || book.created_at).toISOString(),
          metadata: {
            title: book.title,
            author: book.author,
            isbn: book.isbn,
            coverImage: book.cover_image
          }
        }));
        
        resolve(syncItems);
      }
    );
  });
}
```

#### Similar conversions for:
- `getDbFolders()` - Retrieves folders from database
- `getDbNotes()` - Retrieves notes from database  
- `getSyncHashes()` - Retrieves sync hashes from sync_items table

### 3. Fixed Type Issues
- Updated conflict detection to match the ConflictItem interface from types.ts
- Fixed the `categorizeByType()` method to return `ManifestItemsByType` instead of `ItemsByType`
- Ensured all IDs are converted to strings for consistency

### 4. Key Changes
- All database operations now use promises with callback-based sqlite3 API
- No synchronous database calls remain
- Maintained all existing functionality
- Proper error handling in all promise rejections
- IDs are consistently converted to strings since sync system uses string IDs

## Technical Details
The migration follows the exact same pattern used in database-api.ts:
- Use `db.all()` for SELECT queries
- Wrap in Promise with resolve/reject
- Handle errors by rejecting the promise
- Cast rows to appropriate types
- Convert numeric IDs to strings where needed

This ensures consistency across the codebase and removes the dependency on better-sqlite3.