// Recent Items API - Functions for tracking recently viewed notes and books
import { getDatabase } from '../database/database'; // Corrected import path
import sqlite3 from 'sqlite3';
import { Note } from '../database/database-api'; // Assuming Note type exists

// Types
type Database = sqlite3.Database;

// Define interfaces for clarity (adjust based on actual schema)

// Basic Book interface (adjust if needed)
interface Book {
    id: number;
    title?: string; // Assuming book has a title
    // Add other book properties if known
}

interface RecentItem {
    id: number;
    note_id?: number | null;
    book_id?: number | null;
    viewed_at: string; // ISO 8601 format
}

interface RecentNoteWithDetails extends Note {
    viewed_at: string;
}

interface RecentBookWithDetails extends Book {
    viewed_at: string;
}

interface RecentItemSummary {
    id: number;
    note_id?: number | null;
    book_id?: number | null;
    viewed_at: string;
    item_type: 'note' | 'book';
    title?: string; // Title from note or book
}

interface DbRunResult {
    id: number;
    changes: number;
}

// Helper functions for database operations with types
const dbGet = <T>(query: string, params: any[] = []): Promise<T | undefined> => {
    return new Promise((resolve, reject) => {
        const db: Database = getDatabase();
        db.get(query, params, (err: Error | null, row: T) => { // Add type T to row and Error type
            if (err) {
                reject(err);
            } else {
                resolve(row);
            }
            // db.close(); // Closing here might be problematic if db is reused quickly
        });
    });
};

const dbAll = <T>(query: string, params: any[] = []): Promise<T[]> => {
    return new Promise((resolve, reject) => {
        const db: Database = getDatabase();
        db.all(query, params, (err: Error | null, rows: T[]) => { // Add type T[] to rows and Error type
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
            // db.close();
        });
    });
};

const dbRun = (query: string, params: any[] = []): Promise<DbRunResult> => {
    return new Promise((resolve, reject) => {
        const db: Database = getDatabase();
        // Use function() to access 'this' context from sqlite
        db.run(query, params, function (this: { lastID: number; changes: number }, err: Error | null) {
            if (err) {
                reject(err);
            } else {
                // 'this' context provides lastID and changes
                resolve({ id: this.lastID, changes: this.changes });
            }
            // db.close();
        });
    });
};

// Add a recently viewed note
export const addRecentNote = async (noteId: number): Promise<{ id: number; noteId: number }> => {
    if (typeof noteId !== 'number') {
        throw new Error('Note ID must be a number.');
    }
    try {
        // Check if this note is already in recent items
        const existing = await dbGet<RecentItem>(
            'SELECT id FROM recent_items WHERE note_id = ?',
            [noteId]
        );

        if (existing) {
            // Update the viewed_at timestamp for an existing entry
            await dbRun(
                'UPDATE recent_items SET viewed_at = CURRENT_TIMESTAMP WHERE note_id = ?',
                [noteId]
            );
            return { id: existing.id, noteId };
        } else {
            // Add a new entry
            const result = await dbRun(
                'INSERT INTO recent_items (note_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)',
                [noteId]
            );
            return { id: result.id, noteId };
        }
    } catch (error: any) {
        console.error('Error adding recent note:', error);
        throw new Error(`Failed to add recent note: ${error.message}`);
    }
};

// Add a recently viewed book
export const addRecentBook = async (bookId: number): Promise<{ id: number; bookId: number }> => {
    if (typeof bookId !== 'number') {
        throw new Error('Book ID must be a number.');
    }
    try {
        // Check if this book is already in recent items
        const existing = await dbGet<RecentItem>(
            'SELECT id FROM recent_items WHERE book_id = ?',
            [bookId]
        );

        if (existing) {
            // Update the viewed_at timestamp for an existing entry
            await dbRun(
                'UPDATE recent_items SET viewed_at = CURRENT_TIMESTAMP WHERE book_id = ?',
                [bookId]
            );
            return { id: existing.id, bookId };
        } else {
            // Add a new entry
            const result = await dbRun(
                'INSERT INTO recent_items (book_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)',
                [bookId]
            );
            return { id: result.id, bookId };
        }
    } catch (error: any) {
        console.error('Error adding recent book:', error);
        throw new Error(`Failed to add recent book: ${error.message}`);
    }
};

// Get recent notes with details (limit is optional, defaults to 10)
export const getRecentNotes = async (limit: number = 10): Promise<RecentNoteWithDetails[]> => {
    if (typeof limit !== 'number' || limit <= 0) {
        throw new Error('Limit must be a positive number.');
    }
    try {
        const query = `
      SELECT n.*, r.viewed_at
      FROM recent_items r
      JOIN notes n ON r.note_id = n.id
      WHERE r.note_id IS NOT NULL
      ORDER BY r.viewed_at DESC
      LIMIT ?
    `;

        return await dbAll<RecentNoteWithDetails>(query, [limit]);
    } catch (error: any) {
        console.error('Error getting recent notes:', error);
        throw new Error(`Failed to get recent notes: ${error.message}`);
    }
};

// Get recent books with details (limit is optional, defaults to 10)
export const getRecentBooks = async (limit: number = 10): Promise<RecentBookWithDetails[]> => {
    if (typeof limit !== 'number' || limit <= 0) {
        throw new Error('Limit must be a positive number.');
    }
    try {
        const query = `
      SELECT b.*, r.viewed_at
      FROM recent_items r
      JOIN books b ON r.book_id = b.id
      WHERE r.book_id IS NOT NULL
      ORDER BY r.viewed_at DESC
      LIMIT ?
    `;
        return await dbAll<RecentBookWithDetails>(query, [limit]);
    } catch (error: any) {
        console.error('Error getting recent books:', error);
        throw new Error(`Failed to get recent books: ${error.message}`);
    }
};

// Get all recent items (both notes and books, limit is optional, defaults to 10)
export const getAllRecentItems = async (limit: number = 10): Promise<RecentItemSummary[]> => {
    if (typeof limit !== 'number' || limit <= 0) {
        throw new Error('Limit must be a positive number.');
    }
    try {
        const query = `
      SELECT
        r.id,
        r.note_id,
        r.book_id,
        r.viewed_at,
        CASE
          WHEN r.note_id IS NOT NULL THEN 'note'
          WHEN r.book_id IS NOT NULL THEN 'book'
        END as item_type,
        CASE
          WHEN r.note_id IS NOT NULL THEN n.title
          WHEN r.book_id IS NOT NULL THEN b.title
        END as title
      FROM recent_items r
      LEFT JOIN notes n ON r.note_id = n.id
      LEFT JOIN books b ON r.book_id = b.id
      ORDER BY r.viewed_at DESC
      LIMIT ?
    `;

        return await dbAll<RecentItemSummary>(query, [limit]);
    } catch (error: any) {
        console.error('Error getting all recent items:', error);
        throw new Error(`Failed to get all recent items: ${error.message}`);
    }
};

// Clear all recent items
export const clearRecentItems = async (): Promise<{ success: boolean }> => {
    try {
        await dbRun('DELETE FROM recent_items');
        return { success: true };
    } catch (error: any) {
        console.error('Error clearing recent items:', error);
        throw new Error(`Failed to clear recent items: ${error.message}`);
    }
};

// Delete a specific recent item by id
export const deleteRecentItem = async (id: number): Promise<{ success: boolean; id: number }> => {
    if (typeof id !== 'number') {
        throw new Error('Recent item ID must be a number.');
    }
    try {
        const result = await dbRun('DELETE FROM recent_items WHERE id = ?', [id]);
        if (result.changes === 0) {
             console.warn(`Attempted to delete non-existent recent item with ID ${id}`);
        }
        return { success: result.changes > 0, id };
    } catch (error: any) {
        console.error(`Error deleting recent item with ID ${id}:`, error);
        throw new Error(`Failed to delete recent item ${id}: ${error.message}`);
    }
};

export default {
    addRecentNote,
    addRecentBook,
    getRecentNotes,
    getRecentBooks,
    getAllRecentItems,
    clearRecentItems,
    deleteRecentItem
};