<template>
  <div class="delete-modal">
    <div class="close-icon" @click="$emit('close')">
      <img src="/icons/close-icon.svg" alt="Close" />
    </div>
    
    <div class="modal-header">
      <h1 class="modal-title">Delete Items</h1>
      <p class="modal-subtitle">
        Are you sure you want to delete the selected items?
      </p>
      
      <div class="summary-section">
        <div class="summary-item" v-if="folderCount > 0">
          <span class="summary-count">{{ folderCount }}</span> 
          <span class="summary-label">{{ folderCount === 1 ? 'folder' : 'folders' }}</span>
        </div>
        <div class="summary-item" v-if="noteCount > 0">
          <span class="summary-count">{{ noteCount }}</span> 
          <span class="summary-label">{{ noteCount === 1 ? 'note' : 'notes' }}</span>
        </div>
      </div>
      
      <div class="items-list" v-if="displayItems.length > 0">
        <ul>
          <li v-for="(item, index) in displayItems" :key="index">
            <img :src="item.type === 'folder' ? '/icons/folder-icon.svg' : '/icons/note-icon.svg'" 
                 class="item-icon" 
                 alt="" />
            {{ item.name }}
          </li>
        </ul>
        <p v-if="totalItems > maxDisplayed" class="more-items">
          ...and {{ totalItems - maxDisplayed }} more items
        </p>
      </div>
      
      <div v-if="folderCount > 0 && totalNotesCount > 0" class="warning-section">
        <p><strong>Warning:</strong> The selected folders contain {{ totalNotesCount }} additional notes.</p>
        <div class="notes-target-selector" v-if="showTargetSelector">
          <label for="target-folder">Move notes to:</label>
          <select id="target-folder" v-model="targetFolderId">
            <option value="null">None (Delete all notes)</option>
            <option v-for="folder in availableTargetFolders" 
                    :key="folder.id" 
                    :value="folder.id">
              {{ folder.name }}
            </option>
          </select>
        </div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="modal-footer">
      <button class="btn btn-cancel" @click="$emit('cancel')">
        Cancel
      </button>
      <button class="btn btn-delete" @click="handleDelete">
        <img src="/icons/trash-icon.svg" class="delete-icon" alt="Delete" />
        Delete All
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import type { Folder } from '../../types/electron-api';

export default defineComponent({
  name: 'DeleteMixedItemsModal',
  props: {
    folderCount: {
      type: Number,
      default: 0
    },
    noteCount: {
      type: Number,
      default: 0
    },    items: {
      type: Array as () => Array<{ type: string; name: string; id: number | undefined }>,
      default: () => []
    },
    totalNotesCount: {
      type: Number,
      default: 0
    },
    availableTargetFolders: {
      type: Array as () => Folder[],
      default: () => []
    }
  },
  setup(props, { emit }) {
    const maxDisplayed = 5;
    const targetFolderId = ref<number | null>(null);
    
    const totalItems = computed(() => {
      return props.items.length;
    });
    
    const displayItems = computed(() => {
      return props.items.slice(0, maxDisplayed);
    });
    
    const showTargetSelector = computed(() => {
      return props.totalNotesCount > 0 && props.availableTargetFolders.length > 0;
    });
    
    const handleDelete = () => {
      emit('delete', { targetFolderId: targetFolderId.value });
    };

    return {
      maxDisplayed,
      totalItems,
      displayItems,
      targetFolderId,
      showTargetSelector,
      handleDelete
    };
  },
  emits: ['close', 'delete', 'cancel']
})
</script>

<style scoped>
.delete-modal {
  max-width: 700px;
  width: 90%;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: #4A4A4A;
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: #777777;
  font-size: 24px;
  font-weight: 400;
  margin: 0;
  line-height: 1.5;
}

.summary-section {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.summary-count {
  font-size: 32px;
  font-weight: 600;
  color: #4A4A4A;
}

.summary-label {
  font-size: 18px;
  color: #777777;
}

.items-list {
  margin-top: 24px;
  max-height: 200px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 10px;
  border: 1px solid #E3E3E3;
}

/* Custom scrollbar styling for items list */
.items-list::-webkit-scrollbar {
  width: 8px;
}

.items-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.items-list::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 4px;
}

.items-list::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.items-list ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: none;
}

.items-list li {
  margin-bottom: 8px;
  color: #4A4A4A;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-icon {
  width: 16px;
  height: 16px;
}

.more-items {
  font-style: italic;
  color: #777777;
  font-size: 16px;
  margin: 12px 0 0;
}

.warning-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #FFF4F4;
  border: 1px solid #FFCACA;
  border-radius: 10px;
}

.warning-section p {
  margin: 0 0 16px 0;
  color: #D01E1E;
  font-size: 16px;
}

.warning-section strong {
  font-weight: 700;
}

.notes-target-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
}

.notes-target-selector label {
  font-weight: 500;
  color: #4A4A4A;
  font-size: 14px;
}

.notes-target-selector select {
  flex-grow: 1;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #E3E3E3;
  font-size: 14px;
  background-color: white;
}

.divider {
  height: 1px;
  background-color: #E3E3E3;
  width: 100%;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: #F8F8F8;
  color: #4A4A4A;
  border: 1px solid #E3E3E3;
}

.btn-delete {
  background-color: #D01E1E;
  color: white;
}

.delete-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}
</style>
