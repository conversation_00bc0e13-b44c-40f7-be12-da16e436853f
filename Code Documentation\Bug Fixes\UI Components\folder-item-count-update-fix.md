# Folder Item Count Update Bug Fix

## Issue Description
When a note was created in a folder and then deleted, the folder item count displayed in both FolderNavigator and FolderContent components would not update correctly. The count would remain at 1 even after the note was deleted and the user left the folder.

## Root Cause
The `confirmNoteDelete` and `confirmMixedItemsDelete` functions in `FoldersView.vue` were removing notes from local arrays (`folderNotes.value` and `notes.value`) but were not updating the `notesCount` property on the affected folders in both the flat folders array and the folder hierarchy.

## Files Modified
- `src/views/FoldersView.vue`

## What Was Done

### 1. Created `updateFolderCounts` Function
Added a new function to update folder counts for specific folders after note operations:

```javascript
const updateFolderCounts = async (folderIds: number[]) => {
  console.log('updateFolderCounts: Updating counts for folders:', folderIds);
  
  try {
    for (const folderId of folderIds) {
      // Get the current note count for this folder
      const notes = await db.notes.getByFolderId(folderId);
      const noteCount = notes.length;
      
      // Update in the flat folders array
      const flatFolder = folders.value.find(f => f.id === folderId);
      if (flatFolder) {
        flatFolder.notesCount = noteCount;
        console.log(`updateFolderCounts: Updated folder "${flatFolder.name}" count to ${noteCount}`);
      }
      
      // Update in the hierarchy
      const updateInHierarchy = (folders: FolderWithMeta[]): boolean => {
        for (const folder of folders) {
          if (folder.id === folderId) {
            // Special handling for Books folder - don't overwrite with note count
            if (folder.name === 'Books' && folder.parent_id === null) {
              console.log(`updateFolderCounts: Skipping note count update for Books folder`);
            } else {
              folder.notesCount = noteCount;
              console.log(`updateFolderCounts: Updated hierarchy folder "${folder.name}" count to ${noteCount}`);
            }
            return true;
          }
          
          if (folder.children && folder.children.length > 0) {
            if (updateInHierarchy(folder.children)) {
              return true;
            }
          }
        }
        return false;
      };
      
      updateInHierarchy(folderHierarchy.value);
    }
    
    console.log('updateFolderCounts: Folder count updates completed');
  } catch (error) {
    console.error('updateFolderCounts: Error updating folder counts:', error);
  }
};
```

### 2. Updated `confirmNoteDelete` Function
Modified the function to track which folders need their counts updated and call the new `updateFolderCounts` function:

```javascript
// Track which folders need their counts updated
const foldersToUpdate = new Set<number>();

for (const note of notesToDelete.value) {
  if (note.id !== undefined) {
    // Track the folder that contained this note
    if (note.folder_id) {
      foldersToUpdate.add(note.folder_id);
    }
    
    // ... existing deletion logic ...
  }
}

// Update folder counts for affected folders
await updateFolderCounts(Array.from(foldersToUpdate));
```

### 3. Updated `confirmMixedItemsDelete` Function
Applied the same folder count tracking and updating logic to the mixed items deletion function.

## How It Was Fixed
1. **Tracking**: Before deleting notes, we track which folders contain the notes being deleted by collecting their `folder_id` values.

2. **Database Query**: After deletion, we query the database to get the current note count for each affected folder using `db.notes.getByFolderId(folderId)`.

3. **Update Both Data Structures**: We update the `notesCount` property in both:
   - The flat folders array (`folders.value`)
   - The folder hierarchy (`folderHierarchy.value`)

4. **Special Handling**: We preserve the special handling for the Books folder, which uses child folder count instead of note count.

## Result
- Folder item counts now update correctly in both FolderNavigator and FolderContent components
- The count immediately reflects the actual number of notes in the folder
- The fix works for both single note deletion and mixed item deletion scenarios
- No visual artifacts or stale data remain after note deletion operations

## Testing Recommendations
1. Create a note in a folder
2. Delete the note
3. Navigate away from the folder and back
4. Verify the folder count shows 0 instead of 1
5. Test with multiple notes and partial deletions
6. Test with mixed item deletions (folders + notes)
