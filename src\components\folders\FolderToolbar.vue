<template>
  <div class="folder-toolbar">
    <div class="toolbar-container">
      <div class="toolbar-divider bottom-divider"></div>
      <!-- Removed top divider to avoid overlapping with header's divider -->
      <div class="frame-2">
        <!-- Item-specific actions - only shown when items are checked -->
        <div class="item-actions" v-if="hasCheckedItems">
          <button class="toolbar-button" @click="handleAction('delete')" :disabled="isProtectedFolderSelected">
            <img src="/icons/trash-icon.svg" alt="Delete" class="button-icon" />
            <span>Delete</span>
          </button>

          <button class="toolbar-button" @click="handleAction('rename')" :disabled="checkedItems.length !== 1 || isProtectedFolderSelected">
            <img src="/icons/rename-icon.svg" alt="Rename" class="button-icon" />
            <span>Rename</span>
          </button>

          <button class="toolbar-button" @click="handleAction('color')" :disabled="isColorButtonDisabled">
            <img src="/icons/color-icon.svg" alt="Color" class="button-icon" />
            <span>Color</span>
          </button>

          <button class="toolbar-button" @click="handleAction('move')" :disabled="isProtectedFolderSelected">
            <img src="/icons/move-icon.svg" alt="Move" class="button-icon" />
            <span>Move</span>
          </button>
          
          <button class="toolbar-button" @click="handleAction('export')">
            <img src="/icons/export-icon.svg" alt="Export" class="button-icon" />
            <span>Export</span>
          </button>
          
          <div class="vertical-divider"></div>
        </div>        <!-- Always visible actions -->        <!-- New button with dropdown -->
        <div class="new-dropdown-container" ref="newDropdownContainer">
          <button class="toolbar-button new-button" ref="newButton" @click="toggleNewDropdown">
            <img src="/icons/plus-icon.svg" alt="New" class="button-icon" />
            <span>New</span>
            <img src="/icons/dropdown-arrow-icon.svg" class="dropdown-arrow" :class="{ 'open': newDropdownOpen }" alt="▼" />
          </button>
            <div v-if="newDropdownOpen" class="new-dropdown-options" :style="dropdownStyle">
            <div class="new-option" @click="handleAction('newFolder')">
              <img src="/icons/folder-icon.svg" alt="New Folder" class="option-icon" />
              <span>New Folder</span>
            </div>
            <div class="new-option" @click="handleAction('newNote')">
              <img src="/icons/notes-icon.svg" alt="New Note" class="option-icon" />
              <span>New Note</span>
            </div>          </div>
        </div>
        
        <button class="toolbar-button" @click="handleAction('import')">
          <img src="/icons/import-icon.svg" alt="Import" class="button-icon" />
          <span>Import</span>
        </button>
          <!-- Sort button with dropdown -->
        <div class="sort-dropdown-container" ref="sortDropdownContainer">          <button class="toolbar-button sort-button" ref="sortButton" @click="toggleSortDropdown">
            <img src="/icons/sort-icon.svg" alt="Sort" class="button-icon" />
            <span>Sort</span>
            <img src="/icons/dropdown-arrow-icon.svg" class="dropdown-arrow" :class="{ 'open': sortDropdownOpen }" alt="▼" />
          </button>
          <div v-if="sortDropdownOpen" class="sort-dropdown-options" :style="sortDropdownStyle">
            <div class="sort-option" @click="handleAction('sortByName')">
              <span>Name</span>
            </div>
            <div class="sort-option" @click="handleAction('sortByDate')">
              <span>Date Created</span>
            </div>
            <div class="sort-option" @click="handleAction('sortByModified')">
              <span>Date Modified</span>
            </div>
            <div class="sort-option" @click="handleAction('sortBySize')">
              <span>Size</span>
            </div>
            <div class="sort-option" @click="handleAction('sortByType')">
              <span>Type</span>
            </div>
          </div>
        </div>
          <div class="search-container">
          <img src="/icons/search-icon.svg" class="search-icon" alt="Search" />
          <input type="text" placeholder="Search in this folder" class="search-input" 
                v-model="searchQuery" 
                @input="handleSearch" 
                @keydown.esc="clearSearch" />
          <img v-if="searchQuery" 
               src="/icons/close-icon.svg" 
               class="clear-search-icon" 
               alt="Clear" 
               @click="clearSearch" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';

export default defineComponent({
  name: 'FolderToolbar',
  emits: ['delete', 'rename', 'color', 'move', 'export', 'import', 'sort', 'search', 'newFolder', 'newNote'],  props: {
    selectedItemId: {
      type: [Number, String, null],
      default: null
    },
    selectedItemType: {
      type: String,
      default: null
    },
    checkedItems: {
      type: Array,
      default: () => []
    },
    currentSearchQuery: {
      type: String,
      default: ''
    },
    isProtectedFolderSelected: {
      type: Boolean,
      default: false
    }
  },  setup(props, { emit }) {
    const searchQuery = ref(props.currentSearchQuery);
    const newDropdownOpen = ref(false);
    const sortDropdownOpen = ref(false);
    const newDropdownContainer = ref<HTMLElement | null>(null);
    const sortDropdownContainer = ref<HTMLElement | null>(null);
    const newButton = ref<HTMLElement | null>(null);
    const sortButton = ref<HTMLElement | null>(null);
    const dropdownStyle = ref({});
    const sortDropdownStyle = ref({});

    // Watch for changes in the currentSearchQuery prop
    watch(() => props.currentSearchQuery, (newValue) => {
      if (newValue !== searchQuery.value) {
        searchQuery.value = newValue;
      }
    });

    // Compute if any items are checked
    const hasCheckedItems = computed(() => {
      return props.checkedItems.length > 0;
    });

    // Compute if color button should be disabled
    const isColorButtonDisabled = computed(() => {
      // Disable if no items are checked
      if (props.checkedItems.length === 0) {
        return true;
      }

      // Disable if any checked items are notes (only allow folders)
      const hasNotes = props.checkedItems.some((item: any) => item.type === 'note');
      if (hasNotes) {
        return true;
      }

      // Allow color changes for all folders, including Books folder and its subfolders
      // The only restriction is the root Books folder for other operations, but color is allowed
      return false;
    });
    
    // Reset toolbar state when checked items change
    watch(() => props.checkedItems, (newItems) => {
      if (newItems.length === 0) {
        // Reset dropdown state when all items are unchecked
        newDropdownOpen.value = false;
      }
    });    // Toggle new dropdown menu
    const toggleNewDropdown = () => {
      newDropdownOpen.value = !newDropdownOpen.value;
      sortDropdownOpen.value = false; // Close sort dropdown when opening new dropdown
      if (newDropdownOpen.value && newButton.value) {
        const rect = newButton.value.getBoundingClientRect();
        dropdownStyle.value = {
          top: `${rect.bottom + 2}px`, // Add small offset below the button
          left: `${rect.left}px`,
          position: 'fixed'
        };
      }
    };
    
    // Toggle sort dropdown menu
    const toggleSortDropdown = () => {
      sortDropdownOpen.value = !sortDropdownOpen.value;
      newDropdownOpen.value = false; // Close new dropdown when opening sort dropdown
      if (sortDropdownOpen.value && sortButton.value) {
        const rect = sortButton.value.getBoundingClientRect();
        sortDropdownStyle.value = {
          top: `${rect.bottom + 2}px`, // Add small offset below the button
          left: `${rect.left}px`,
          position: 'fixed'
        };
      }
    };
      // Click outside to close dropdowns
    const handleClickOutside = (event: MouseEvent) => {
      if (newDropdownContainer.value && !newDropdownContainer.value.contains(event.target as Node)) {
        newDropdownOpen.value = false;
      }
      if (sortDropdownContainer.value && !sortDropdownContainer.value.contains(event.target as Node)) {
        sortDropdownOpen.value = false;
      }
    };
    
    // Add click outside listener
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });
    
    // Remove click outside listener
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });
    
    // Safely handle button actions
    const handleAction = (action: string) => {
      try {
        switch (action) {
          case 'delete':
            emit('delete');
            break;
          case 'rename':
            emit('rename');
            break;
          case 'color':
            emit('color');
            break;
          case 'move':
            emit('move');
            break;
          case 'export':
            emit('export');
            break;
          case 'import':
            emit('import');
            break;          case 'sortByName':
            emit('sort', 'name');
            sortDropdownOpen.value = false;
            break;
          case 'sortByDate':
            emit('sort', 'date');
            sortDropdownOpen.value = false;
            break;
          case 'sortByModified':
            emit('sort', 'modified');
            sortDropdownOpen.value = false;
            break;
          case 'sortBySize':
            emit('sort', 'size');
            sortDropdownOpen.value = false;
            break;
          case 'sortByType':
            emit('sort', 'type');
            sortDropdownOpen.value = false;
            break;
          case 'newFolder':
            emit('newFolder');
            newDropdownOpen.value = false;
            break;
          case 'newNote':
            emit('newNote');
            newDropdownOpen.value = false;
            break;
          default:
            console.warn('Unknown action:', action);
        }
      } catch (error) {
        console.error(`Error handling ${action} action:`, error);
      }
    };
    
    // Safely handle search input
    const handleSearch = () => {
      try {
        emit('search', searchQuery.value);
        console.log('Searching for:', searchQuery.value);
      } catch (error) {
        console.error('Error handling search:', error);
      }
    };
    
    // Clear search input and emit empty search
    const clearSearch = () => {
      searchQuery.value = '';
      emit('search', '');
      console.log('Search cleared');
    };

    return {
      searchQuery,
      hasCheckedItems,
      isColorButtonDisabled,
      newDropdownOpen,
      sortDropdownOpen,
      newDropdownContainer,
      sortDropdownContainer,
      newButton,
      sortButton,      dropdownStyle,
      sortDropdownStyle,
      toggleNewDropdown,
      toggleSortDropdown,
      handleAction,
      handleSearch,
      clearSearch
    };
  }
});
</script>

<style scoped>
.folder-toolbar {
  background-color: var(--color-bg-primary);
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  position: relative;
  z-index: 900; /* Add z-index to ensure toolbar is above other content */
}

.toolbar-container {
  background-color: var(--color-bg-primary);
  height: 50px;
  position: relative;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.toolbar-divider {
  background-color: var(--color-border-primary);
  height: 1px;
  left: 0;
  position: absolute;
  width: 100%;
}

.bottom-divider {
  bottom: 0;
}

.top-divider {
  top: 0;
}

.frame-2 {
  display: flex;
  align-items: center; /* Center align all items vertically */
  gap: 10px; /* Reduced gap to save space */
  padding: 0 20px;
  height: 100%;
  width: 100%; /* Ensure it takes full width */
  min-height: 50px; /* Match container height */
  flex-wrap: wrap; /* Allow wrapping if needed for very small screens */
  justify-content: flex-start; /* Align items to the start */
}

/* Item actions */
.item-actions {
  display: flex;
  align-items: center;
  gap: 10px; /* Reduced gap */
  flex-shrink: 1; /* Allow some shrinking */
  height: 100%; /* Ensure it takes full height */
}

/* Vertical divider should not shrink */
.vertical-divider {
  height: 30px;
  width: 1px;
  background-color: var(--color-border-primary);
  flex-shrink: 0;
  align-self: center; /* Align with buttons */
}

.toolbar-button {
  display: flex;
  align-items: center; /* Align items vertically centered */
  justify-content: center;
  gap: 6px; /* Slightly reduced gap */
  height: 30px;
  padding: 0 12px; /* Reduced padding */
  border: 1px solid var(--color-input-border);
  border-radius: 4px;
  background-color: var(--color-input-bg);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-input-text);
  cursor: pointer;
  min-width: 90px; /* Reduced min-width */
  flex-shrink: 1; /* Allow some shrinking */
  transition: background-color 0.2s;
  align-self: center; /* Ensure every button aligns center with parent */
  box-sizing: border-box; /* Include padding and border in element's size */
  position: relative; /* For proper alignment with other elements */
}

.toolbar-button:hover:not(:disabled) {
  background-color: var(--color-nav-item-hover);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.short {
  min-width: 80px; /* Match the min-width of new-button */
  padding: 0 25px 0 12px; /* Match the padding of standard buttons and new button */
  justify-content: flex-start; /* Align content to left */
}

.button-icon {
  width: 16px;
  height: 16px;
}

/* Search container should stay at the right */
.search-container {
  display: flex;
  align-items: center;
  background-color: var(--color-input-bg);
  border-radius: 4px;
  padding: 0 12px;
  height: 30px;
  margin-left: auto;
  flex-shrink: 1; /* Allow it to shrink */
  min-width: 120px; /* Minimum width */
  max-width: 250px; /* Increased maximum width for better visibility */
  align-self: center; /* Ensure vertical alignment */
  transition: all 0.2s ease;
  border: 1px solid var(--color-input-border);
}

.search-container:focus-within {
  background-color: var(--color-input-bg);
  box-shadow: 0 0 0 2px var(--color-input-focus);
  max-width: 300px; /* Expand slightly when focused */
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  opacity: 1;
}

.clear-search-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  opacity: 1;
  cursor: pointer;
  transition: opacity 0.2s;
}

.clear-search-icon:hover {
  opacity: 0.8;
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: var(--color-input-text);
  width: 100%; /* Fill the container */
  min-width: 80px; /* Minimum width */
}

.search-input::placeholder {
  color: var(--color-input-placeholder);
}

/* Dropdown Styles - Common */
.new-dropdown-container,
.sort-dropdown-container {
  position: relative;
  flex-shrink: 0;
  align-self: center; /* Ensure dropdown is vertically centered */
  height: 30px; /* Match button height */
  z-index: 1000; /* Higher z-index to ensure the dropdown appears above other elements */
}

.new-button {
  position: relative;
  padding-right: 25px; /* Reduced padding */
  min-width: 80px; /* Original minimum width */
}

.sort-button {
  position: relative;
  padding-right: 25px; /* Same padding as new-button */
  min-width: 80px; /* Same min-width as new-button */
}

.dropdown-arrow {
  width: 10px;
  height: 10px;
  position: absolute;
  right: 12px;
  transition: transform 0.2s ease;
}

/* Specific positioning for sort dropdown arrow */
.sort-dropdown-arrow {
  right: 12px; /* Match the same position as regular dropdown arrow */
  width: 10px; /* Match the same size as regular dropdown arrow */
  height: 10px;
  position: absolute; /* Ensure absolute positioning */
}

.sort-text {
  margin-right: 0; /* No additional margin needed */
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow text */
  text-overflow: ellipsis; /* Show ellipsis for overflow text */
  max-width: calc(100% - 22px); /* Maximum width for the text */
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.new-dropdown-options,
.sort-dropdown-options {
  position: fixed; /* Changed to fixed positioning to break out of stacking context */
  width: 180px;
  background: var(--color-modal-bg);
  border: 1px solid var(--color-modal-border);
  border-radius: 4px;
  box-shadow: 0 4px 12px var(--color-card-shadow);
  z-index: 9999; /* Very high z-index to ensure it appears above everything */
  margin-top: 5px;
  animation: fadeIn 0.15s ease-in-out;
}

/* Sort dropdown specific styles */
.sort-dropdown-options {
  width: 150px; /* Slightly narrower than new dropdown */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.new-option,
.sort-option {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-option:hover,
.sort-option:hover {
  background-color: var(--color-nav-item-hover);
}

/* Specific styles for sort options */
.sort-option {
  font-size: 14px;
  color: var(--color-text-primary);
  font-family: 'Montserrat', sans-serif;
  padding: 10px 12px; /* Slightly smaller padding */
}

.option-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}
</style>