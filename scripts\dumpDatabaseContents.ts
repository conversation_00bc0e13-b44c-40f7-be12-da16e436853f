#!/usr/bin/env node

/**
 * Database Contents Dump Script
 * 
 * This script outputs the entire contents of the sync tables, folders, books, and notes tables
 * from the Noti SQLite database.
 */

import sqlite3 from 'sqlite3';
import path from 'path';
import os from 'os';
import fs from 'fs';

// Get the database path (same as used in the main application)
const getDbPath = (): string => {
    if (process.platform === 'win32') {
        return path.join(os.homedir(), 'AppData', 'Roaming', 'noti', 'noti-database.sqlite');
    } else if (process.platform === 'darwin') {
        return path.join(os.homedir(), 'Library', 'Application Support', 'noti', 'noti-database.sqlite');
    } else {
        return path.join(os.homedir(), '.config', 'noti', 'noti-database.sqlite');
    }
};

// Helper function to run database queries as promises
const allAsync = (db: sqlite3.Database, sql: string, params: any[] = []): Promise<any[]> => {
    return new Promise((resolve, reject) => {
        db.all(sql, params, (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
        });
    });
};

// Helper function to check if table exists
const tableExists = (db: sqlite3.Database, tableName: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        db.get(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            [tableName],
            (err, row) => {
                if (err) reject(err);
                else resolve(!!row);
            }
        );
    });
};

// Function to print table contents in a formatted way
const printTableContents = (tableName: string, rows: any[]): void => {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`TABLE: ${tableName.toUpperCase()}`);
    console.log(`${'='.repeat(60)}`);
    console.log(`Total records: ${rows.length}`);
    
    if (rows.length === 0) {
        console.log('No data found in this table.');
        return;
    }
    
    // Get column names from the first row
    const columns = Object.keys(rows[0]);
    
    // Print header
    console.log(`\n${'-'.repeat(60)}`);
    console.log('COLUMNS:', columns.join(' | '));
    console.log(`${'-'.repeat(60)}`);
    
    // Print each row
    rows.forEach((row, index) => {
        console.log(`\nRecord ${index + 1}:`);
        columns.forEach(column => {
            let value = row[column];
            
            // Handle different data types for better display
            if (value === null || value === undefined) {
                value = 'NULL';
            } else if (typeof value === 'string' && value.length > 100) {
                // Truncate very long strings
                value = value.substring(0, 100) + '...';
            }
            
            console.log(`  ${column}: ${value}`);
        });
    });
};

// Main function to dump database contents
const dumpDatabaseContents = async (): Promise<void> => {
    const dbPath = getDbPath();
    
    console.log('Noti Database Contents Dump');
    console.log(`Database path: ${dbPath}`);
    console.log(`Timestamp: ${new Date().toISOString()}\n`);
    
    // Check if database file exists
    if (!fs.existsSync(dbPath)) {
        console.error(`❌ Database file not found at: ${dbPath}`);
        console.error('Make sure Noti has been run at least once to create the database.');
        process.exit(1);
    }
    
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, async (err) => {
            if (err) {
                console.error('❌ Error opening database:', err.message);
                reject(err);
                return;
            }
            
            console.log('✅ Successfully connected to database');
            
            try {
                // Define the tables we want to dump
                const tablesToDump = [
                    // Sync tables
                    'sync_state',
                    'sync_sessions', 
                    'sync_directory_state',
                    // Main tables
                    'folders',
                    'books',
                    'notes'
                ];
                
                console.log('\n📊 Starting database content dump...\n');
                
                for (const tableName of tablesToDump) {
                    try {
                        // Check if table exists
                        const exists = await tableExists(db, tableName);
                        
                        if (!exists) {
                            console.log(`\n⚠️  Table '${tableName}' does not exist in the database.`);
                            continue;
                        }
                        
                        // Get all data from the table - handle tables without id column
                        let query = `SELECT * FROM ${tableName}`;
                        if (tableName === 'sync_state') {
                            // sync_state uses composite primary key (item_type, item_id)
                            query += ` ORDER BY item_type ASC, item_id ASC`;
                        } else {
                            query += ` ORDER BY id ASC`;
                        }
                        const rows = await allAsync(db, query);
                        printTableContents(tableName, rows);
                        
                    } catch (tableError) {
                        console.error(`❌ Error querying table '${tableName}':`, tableError.message);
                    }
                }
                
                console.log(`\n${'='.repeat(60)}`);
                console.log('✅ Database dump completed successfully!');
                console.log(`${'='.repeat(60)}\n`);
                
            } catch (error) {
                console.error('❌ Error during database dump:', error);
                reject(error);
            } finally {
                db.close((closeErr) => {
                    if (closeErr) {
                        console.error('❌ Error closing database:', closeErr.message);
                        reject(closeErr);
                    } else {
                        console.log('📝 Database connection closed.');
                        resolve();
                    }
                });
            }
        });
    });
};

// Additional function to dump to file
const dumpToFile = async (outputPath: string): Promise<void> => {
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    let output = '';
    
    // Capture console output
    console.log = (...args) => {
        const message = args.join(' ');
        output += message + '\n';
        originalConsoleLog(...args);
    };
    
    console.error = (...args) => {
        const message = args.join(' ');
        output += 'ERROR: ' + message + '\n';
        originalConsoleError(...args);
    };
    
    try {
        await dumpDatabaseContents();
        
        // Write output to file
        fs.writeFileSync(outputPath, output);
        originalConsoleLog(`\n💾 Output also saved to: ${outputPath}`);
        
    } finally {
        // Restore original console functions
        console.log = originalConsoleLog;
        console.error = originalConsoleError;
    }
};

// Command line interface
const main = async (): Promise<void> => {
    const args = process.argv.slice(2);
    const shouldSaveToFile = args.includes('--save') || args.includes('-s');
    const outputFile = args.find(arg => arg.startsWith('--output='))?.split('=')[1] || 
                      args.find(arg => arg.startsWith('-o='))?.split('=')[1] ||
                      `database-dump-${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
    
    try {
        if (shouldSaveToFile) {
            await dumpToFile(outputFile);
        } else {
            await dumpDatabaseContents();
        }
    } catch (error) {
        console.error('❌ Script failed:', error);
        process.exit(1);
    }
};

// Show help
const showHelp = (): void => {
    console.log(`
Noti Database Contents Dump Script

Usage: 
  npm run dump-db                           # Output to console only
  npm run dump-db -- --save                 # Save output to timestamped file
  npm run dump-db -- --save --output=my.txt # Save output to specific file
  npm run dump-db -- -s -o=my.txt          # Short form of above

Options:
  --save, -s              Save output to file in addition to console
  --output=FILE, -o=FILE  Specify output filename (requires --save)
  --help, -h              Show this help message

The script will dump contents of these tables:
  - sync_state (sync status tracking)
  - sync_sessions (sync operation history) 
  - sync_directory_state (unified sync engine state)
  - folders (folder structure)
  - books (book information)
  - notes (notes content)
`);
};

// Check for help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// Run the script
main();