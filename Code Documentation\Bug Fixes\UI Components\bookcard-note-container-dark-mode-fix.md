# BookCard Note Container Dark Mode Color Fix

## Issue Description
The `note-container` class in BookCard component was not displaying text color properly in dark mode. The text was either invisible or very hard to read due to insufficient contrast or CSS inheritance issues.

## Root Cause
The issue was caused by CSS specificity and inheritance problems where:

1. **Parent Color Inheritance**: The `.book` class has `color: var(--color-text-secondary)` which was being inherited by child elements
2. **CSS Specificity**: The `.note-title` color declaration wasn't specific enough to override the inherited color
3. **Scoped CSS Issues**: The scoped styling might have been interfering with proper color application

## Files Modified
- `src/components/books/BookCard.vue` - Enhanced CSS specificity for note-title color

## Solution Implementation

### 1. Enhanced CSS Specificity
Changed the CSS selector from:
```css
.note-title {
  color: var(--color-text-primary);
  /* ... other styles ... */
}
```

To:
```css
.note-container .note-title {
  color: var(--color-text-primary) !important;
  /* ... other styles ... */
}
```

### 2. Added !important Declaration
Added `!important` to ensure the color declaration takes precedence over any inherited colors from parent elements.

### 3. More Specific Selector
Used `.note-container .note-title` instead of just `.note-title` to increase CSS specificity and ensure the color is applied correctly within the note container context.

## Technical Details

### CSS Variable Values
The note container uses these CSS variables:

**Light Theme:**
- Background: `--color-bg-secondary: #f5f5f5`
- Text: `--color-text-primary: #4A4A4A`

**Dark Theme:**
- Background: `--color-bg-secondary: #1e1e1e`
- Text: `--color-text-primary: #E0E0E0`

### Inheritance Chain
The inheritance chain that was causing the issue:
```
.book (color: var(--color-text-secondary))
  └── .book-container
      └── .note-container (background: var(--color-bg-secondary))
          └── .note-title (color: var(--color-text-primary))
```

The parent `.book` color was overriding the `.note-title` color due to CSS inheritance and specificity rules.

## Testing
After the fix:
1. ✅ Note container text is clearly visible in light mode
2. ✅ Note container text is clearly visible in dark mode
3. ✅ Proper contrast maintained in both themes
4. ✅ Text color switches instantly when changing themes
5. ✅ Both "Recent Note" titles and "No notes yet" text are properly themed

## Benefits
1. **Improved Readability**: Text is now clearly visible in both light and dark modes
2. **Consistent Theming**: Note container follows the same theming patterns as other components
3. **Better UX**: Users can easily read note information in their preferred theme
4. **Robust CSS**: Enhanced specificity prevents future inheritance issues

## Impact
This fix ensures that the BookCard component is fully compatible with dark mode, providing a consistent and readable experience across all theme variations. The note container now properly displays text with appropriate contrast ratios in both light and dark themes.
