/**
 * Language Code Converter Utility
 *
 * Converts ISO 639-3 language codes (used by OpenLibrary) to human-readable language names
 * using a hybrid approach: iso-639-3 package for conversion + Intl.DisplayNames for display
 */

import { iso6393 } from 'iso-639-3';

// Cache for performance optimization
const languageCache = new Map<string, string>();

// Supported display languages (can be extended)
export type DisplayLanguage = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'zh' | 'ja' | 'ko';

/**
 * Convert ISO 639-3 language code to human-readable name
 * @param code - ISO 639-3 code (e.g., "eng", "spa", "fra") or ISO 639-1 code (e.g., "en", "es", "fr")
 * @param displayLanguage - Language to display the name in (default: 'en')
 * @returns Human-readable language name or the original code if conversion fails
 */
export function convertLanguageCode(code: string, displayLanguage: DisplayLanguage = 'en'): string {
  if (!code) return 'N/A';

  // Normalize the code
  const normalizedCode = code.toLowerCase().trim();

  // Create cache key
  const cacheKey = `${normalizedCode}-${displayLanguage}`;

  // Check cache first
  if (languageCache.has(cacheKey)) {
    return languageCache.get(cacheKey)!;
  }

  try {
    let iso639_1Code: string | undefined;

    // Check if it's already an ISO 639-1 code (2 characters)
    if (normalizedCode.length === 2) {
      iso639_1Code = normalizedCode;
    } else {
      // Convert ISO 639-3 to ISO 639-1 using iso-639-3 package
      const language = iso6393.find(lang => lang.iso6393 === normalizedCode);
      iso639_1Code = language?.iso6391;
    }

    // If we have an ISO 639-1 code, use Intl.DisplayNames
    if (iso639_1Code) {
      const displayNames = new Intl.DisplayNames([displayLanguage], { type: 'language' });
      const displayName = displayNames.of(iso639_1Code);

      if (displayName && displayName !== iso639_1Code) {
        // Cache the result
        languageCache.set(cacheKey, displayName);
        return displayName;
      }
    }

    // Fallback: try to find the language name directly from iso-639-3
    const language = iso6393.find(lang =>
      lang.iso6393 === normalizedCode ||
      lang.iso6391 === normalizedCode
    );

    if (language?.name) {
      // Cache the result
      languageCache.set(cacheKey, language.name);
      return language.name;
    }

  } catch (error) {
    console.warn(`Failed to convert language code "${code}":`, error);
  }

  // Final fallback: return the original code with proper casing
  const fallback = code.charAt(0).toUpperCase() + code.slice(1).toLowerCase();
  languageCache.set(cacheKey, fallback);
  return fallback;
}

/**
 * Convert multiple language codes to human-readable names
 * @param codes - Array of ISO 639-3 or ISO 639-1 codes
 * @param displayLanguage - Language to display the names in (default: 'en')
 * @returns Array of human-readable language names
 */
export function convertLanguageCodes(codes: string[], displayLanguage: DisplayLanguage = 'en'): string[] {
  return codes.map(code => convertLanguageCode(code, displayLanguage));
}

/**
 * Get the primary language from an array of language codes
 * @param codes - Array of language codes
 * @param displayLanguage - Language to display the name in (default: 'en')
 * @returns Primary language name or 'N/A' if no codes provided
 */
export function getPrimaryLanguage(codes: string[] | string | null | undefined, displayLanguage: DisplayLanguage = 'en'): string {
  if (!codes) return 'N/A';

  if (typeof codes === 'string') {
    return convertLanguageCode(codes, displayLanguage);
  }

  if (Array.isArray(codes) && codes.length > 0) {
    return convertLanguageCode(codes[0], displayLanguage);
  }

  return 'N/A';
}

/**
 * Check if a language code is valid
 * @param code - Language code to validate
 * @returns true if the code is valid, false otherwise
 */
export function isValidLanguageCode(code: string): boolean {
  if (!code) return false;

  const normalizedCode = code.toLowerCase().trim();

  // Check if it's a valid ISO 639-1 code (2 characters)
  if (normalizedCode.length === 2) {
    try {
      const displayNames = new Intl.DisplayNames(['en'], { type: 'language' });
      const displayName = displayNames.of(normalizedCode);
      return displayName !== normalizedCode;
    } catch {
      return false;
    }
  }

  // Check if it's a valid ISO 639-3 code
  const language = iso6393.find(lang => lang.iso6393 === normalizedCode);
  return !!language;
}

/**
 * Get all supported language codes with their names
 * @param displayLanguage - Language to display the names in (default: 'en')
 * @returns Array of objects with code and name properties
 */
export function getAllSupportedLanguages(displayLanguage: DisplayLanguage = 'en'): Array<{ code: string; name: string }> {
  const languages: Array<{ code: string; name: string }> = [];

  // Get all languages from iso-639-3 that have ISO 639-1 codes (most common ones)
  const commonLanguages = iso6393.filter(lang => lang.iso6391);

  for (const lang of commonLanguages) {
    if (lang.iso6391) {
      const name = convertLanguageCode(lang.iso6391, displayLanguage);
      languages.push({
        code: lang.iso6391,
        name: name
      });
    }
  }

  // Sort by name
  return languages.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Clear the language cache (useful for testing or memory management)
 */
export function clearLanguageCache(): void {
  languageCache.clear();
}

/**
 * Get cache statistics (useful for debugging)
 */
export function getLanguageCacheStats(): { size: number; keys: string[] } {
  return {
    size: languageCache.size,
    keys: Array.from(languageCache.keys())
  };
}
