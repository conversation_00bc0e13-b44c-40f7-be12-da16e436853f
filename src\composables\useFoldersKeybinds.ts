// Folders view keybinds composable
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useFoldersKeybinds() {
  const isActive = ref(false)

  // Folders functions (to be passed from FoldersView)
  let createNewFolder: () => void = () => console.log('📁 Create new folder')
  let renameSelectedFolder: () => void = () => console.log('✏️ Rename selected folder')
  let deleteSelectedFolders: () => void = () => console.log('🗑️ Delete selected folders')
  let enterSelectedFolder: () => void = () => console.log('📂 Enter selected folder')
  let navigateUp: () => void = () => console.log('⬆️ Navigate up')
  let goToRoot: () => void = () => console.log('🏠 Go to root')
  let focusFolderSearch: () => void = () => console.log('🔍 Focus folder search')

  // Register folders-specific keybinds
  const registerFoldersKeybinds = () => {
    console.log('📁 Registering folders keybinds...')

    // Core folder actions
    globalKeybindManager.register({
      key: 'ctrl+n',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen) {
          createNewFolder()
        }
      },
      description: 'Create new folder',
      category: KeybindCategory.FOLDERS,
      priority: 'high',
      enabled: true,
      context: { view: 'folders', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'f2',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen && !context.editorFocused) {
          renameSelectedFolder()
        }
      },
      description: 'Rename selected folder',
      category: KeybindCategory.FOLDERS,
      priority: 'medium',
      enabled: true,
      context: { view: 'folders', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'delete',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen && !context.editorFocused) {
          deleteSelectedFolders()
        }
      },
      description: 'Delete selected folders',
      category: KeybindCategory.FOLDERS,
      priority: 'medium',
      enabled: true,
      context: { view: 'folders', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'enter',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen && !context.editorFocused) {
          enterSelectedFolder()
        }
      },
      description: 'Enter selected folder',
      category: KeybindCategory.FOLDERS,
      priority: 'medium',
      enabled: true,
      context: { view: 'folders', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'backspace',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen && !context.editorFocused) {
          navigateUp()
        }
      },
      description: 'Navigate up one level',
      category: KeybindCategory.FOLDERS,
      priority: 'medium',
      enabled: true,
      context: { view: 'folders', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+home',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen) {
          goToRoot()
        }
      },
      description: 'Go to root folder',
      category: KeybindCategory.FOLDERS,
      priority: 'medium',
      enabled: true,
      context: { view: 'folders', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+f',
      handler: (context) => {
        if (context.view === 'folders' && !context.modalOpen && !context.editorFocused) {
          focusFolderSearch()
        }
      },
      description: 'Focus folder search',
      category: KeybindCategory.FOLDERS,
      priority: 'high',
      enabled: true,
      context: { view: 'folders', modalOpen: false, editorFocused: false }
    })

    console.log('✅ Folders keybinds registered')
  }

  // Unregister folders keybinds
  const unregisterFoldersKeybinds = () => {
    console.log('🗑️ Unregistering folders keybinds...')
    
    globalKeybindManager.unregister('ctrl+n')
    globalKeybindManager.unregister('f2')
    globalKeybindManager.unregister('delete')
    globalKeybindManager.unregister('enter')
    globalKeybindManager.unregister('backspace')
    globalKeybindManager.unregister('ctrl+home')
    globalKeybindManager.unregister('ctrl+f')
  }

  // Activate folders keybinds
  const activate = () => {
    if (!isActive.value) {
      registerFoldersKeybinds()
      isActive.value = true
      console.log('🟢 Folders keybinds activated')
    }
  }

  // Deactivate folders keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterFoldersKeybinds()
      isActive.value = false
      console.log('🔴 Folders keybinds deactivated')
    }
  }

  // Setup functions (to be called from FoldersView)
  const setupFolderFunctions = (functions: {
    createNewFolder?: () => void
    renameSelectedFolder?: () => void
    deleteSelectedFolders?: () => void
    enterSelectedFolder?: () => void
    navigateUp?: () => void
    goToRoot?: () => void
    focusFolderSearch?: () => void
  }) => {
    if (functions.createNewFolder) createNewFolder = functions.createNewFolder
    if (functions.renameSelectedFolder) renameSelectedFolder = functions.renameSelectedFolder
    if (functions.deleteSelectedFolders) deleteSelectedFolders = functions.deleteSelectedFolders
    if (functions.enterSelectedFolder) enterSelectedFolder = functions.enterSelectedFolder
    if (functions.navigateUp) navigateUp = functions.navigateUp
    if (functions.goToRoot) goToRoot = functions.goToRoot
    if (functions.focusFolderSearch) focusFolderSearch = functions.focusFolderSearch
    
    console.log('🔧 Folders functions configured')
  }

  // Auto-activate when mounted (if currently in folders view)
  onMounted(() => {
    // Check current route and activate if in folders view
    const currentPath = window.location.hash.slice(1) || window.location.pathname
    if (currentPath.includes('/folders')) {
      activate()
    }
  })

  // Cleanup on unmount
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupFolderFunctions,
    registerFoldersKeybinds,
    unregisterFoldersKeybinds
  }
}