// Protocol handlers for custom URL schemes
import { protocol } from 'electron';
import fs from 'node:fs';

/**
 * Registers the noti-media:// protocol for handling media files in the application
 */
export function registerMediaProtocol(): void {
  protocol.registerFileProtocol('noti-media', (request, callback) => {
    try {
      // Remove the protocol prefix
      let filePath = request.url.replace('noti-media://', '');
      
      // Handle Windows paths correctly (remove leading slash on Windows if present)
      if (process.platform === 'win32' && filePath.startsWith('/')) {
        filePath = filePath.substring(1);
      }

      // Decode each path component separately to avoid issues with special characters
      const pathParts = filePath.split('/');
      const decodedParts = pathParts.map(part => {
        try {
          return decodeURIComponent(part);
        } catch (decodeError) {
          console.warn(`Failed to decode path component: ${part}`, decodeError);
          return part; // Use original if decoding fails
        }
      });
      
      // Reconstruct the path
      let decodedPath = decodedParts.join('/');
      
      // Convert to proper Windows path if on Windows
      if (process.platform === 'win32') {
        decodedPath = decodedPath.replace(/\//g, '\\');
      }
      
      // Verify the path exists
      if (fs.existsSync(decodedPath)) {
        console.log(`Serving file: ${decodedPath}`);
        callback({ path: decodedPath });
      } else {
        console.error(`File not found: ${decodedPath}`);
        console.error(`Original URL: ${request.url}`);
        callback({ error: -2 }); // File not found error
      }
    } catch (error) {
      console.error('Protocol handler error:', error);
      console.error(`Failed to process URL: ${request.url}`);
      callback({ error: -324 }); // Generic error
    }
  });
  
  console.log('Media protocol handler registered successfully');
}

/**
 * Initialize all custom protocol handlers
 */
export function initializeProtocolHandlers(): void {
  registerMediaProtocol();
}
