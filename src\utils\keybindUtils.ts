// Utility functions for keybind system
import type { KeybindConfig, KeybindContext, KeyCombination } from '../types/keybinds'

export class KeybindManager {
  private keybinds: Map<string, KeybindConfig> = new Map()
  private isDebugMode = process.env.NODE_ENV === 'development'

  constructor() {
    if (this.isDebugMode) {
      console.log('🎹 KeybindManager initialized')
    }
  }

  /**
   * Register a new keybind
   */
  register(config: KeybindConfig): void {
    const normalizedKey = this.normalizeKey(config.key)
    
    if (this.keybinds.has(normalizedKey)) {
      console.warn(`⚠️ Keybind '${normalizedKey}' is already registered. Overwriting.`)
    }
    
    this.keybinds.set(normalizedKey, { ...config, key: normalizedKey })
    
    if (this.isDebugMode) {
      console.log(`✅ Registered keybind: ${normalizedKey} (${config.category})`)
    }
  }

  /**
   * Unregister a keybind
   */
  unregister(key: string): boolean {
    const normalizedKey = this.normalizeKey(key)
    const result = this.keybinds.delete(normalizedKey)
    
    if (this.isDebugMode && result) {
      console.log(`🗑️ Unregistered keybind: ${normalizedKey}`)
    }
    
    return result
  }

  /**
   * Handle keyboard events
   */
  handleKeyEvent(event: KeyboardEvent, context: KeybindContext): boolean {
    const key = this.getKeyFromEvent(event)
    const keybind = this.keybinds.get(key)
    
    if (!keybind || !keybind.enabled) {
      return false
    }

    // Check if keybind is valid for current context
    if (!this.isValidForContext(keybind, context)) {
      return false
    }

    // Prevent default browser behavior for registered shortcuts
    event.preventDefault()
    event.stopPropagation()

    try {
      keybind.handler(context, event)
      
      if (this.isDebugMode) {
        console.log(`🎹 Executed keybind: ${key} in ${context.view}`)
      }
      
      return true
    } catch (error) {
      console.error(`❌ Error executing keybind '${key}':`, error)
      return false
    }
  }

  /**
   * Get all active keybinds
   */
  getActiveKeybinds(): KeybindConfig[] {
    return Array.from(this.keybinds.values()).filter(kb => kb.enabled)
  }

  /**
   * Get keybinds by category
   */
  getKeybindsByCategory(category: string): KeybindConfig[] {
    return Array.from(this.keybinds.values()).filter(kb => kb.category === category && kb.enabled)
  }

  /**
   * Toggle a keybind on/off
   */
  toggleKeybind(key: string): boolean {
    const normalizedKey = this.normalizeKey(key)
    const keybind = this.keybinds.get(normalizedKey)
    
    if (keybind) {
      keybind.enabled = !keybind.enabled
      if (this.isDebugMode) {
        console.log(`🔄 Toggled keybind '${normalizedKey}': ${keybind.enabled ? 'enabled' : 'disabled'}`)
      }
      return keybind.enabled
    }
    
    return false
  }

  /**
   * Normalize key combinations to a consistent format
   */
  private normalizeKey(key: string): string {
    // First normalize the key string
    const normalized = key.toLowerCase()
      .replace(/\s+/g, '')
      .replace(/command/g, 'meta')
      .replace(/cmd/g, 'meta')
      .replace(/control/g, 'ctrl')
      .replace(/\+/g, '+');
    
    // Split into parts and sort modifiers
    const parts = normalized.split('+');
    const modifiers: string[] = [];
    let mainKey = '';
    
    // Separate modifiers from main key
    parts.forEach(part => {
      if (['ctrl', 'shift', 'alt', 'meta'].includes(part)) {
        modifiers.push(part);
      } else {
        mainKey = part;
      }
    });
    
    // Sort modifiers in consistent order: ctrl, shift, alt, meta
    const modifierOrder = ['ctrl', 'shift', 'alt', 'meta'];
    modifiers.sort((a, b) => modifierOrder.indexOf(a) - modifierOrder.indexOf(b));
    
    // Reconstruct the normalized key
    return modifiers.length > 0 ? `${modifiers.join('+')}+${mainKey}` : mainKey;
  }

  /**
   * Extract key combination from keyboard event
   */
  private getKeyFromEvent(event: KeyboardEvent): string {
    const parts: string[] = []
    
    if (event.ctrlKey) parts.push('ctrl')
    if (event.shiftKey) parts.push('shift')
    if (event.altKey) parts.push('alt')
    if (event.metaKey) parts.push('meta')
    
    // Handle special keys
    let key = event.key.toLowerCase()
    
    // Normalize special keys
    switch (key) {
      case ' ':
        key = 'space'
        break
      case 'arrowup':
        key = 'arrowup'
        break
      case 'arrowdown':
        key = 'arrowdown'
        break
      case 'arrowleft':
        key = 'arrowleft'
        break
      case 'arrowright':
        key = 'arrowright'
        break
      case '`':
        key = '`'
        break
      case 'backquote':
        key = '`'
        break
      default:
        // Handle number keys
        if (event.code.startsWith('Digit')) {
          key = `digit${event.key}`
        }
        break
    }
    
    parts.push(key)
    return parts.join('+')
  }

  /**
   * Check if keybind is valid for the current context
   */
  private isValidForContext(keybind: KeybindConfig, context: KeybindContext): boolean {
    if (!keybind.context) {
      return true // No context restrictions
    }

    // Check view restriction
    if (keybind.context.view && keybind.context.view !== context.view) {
      return false
    }

    // Check modal restriction
    if (keybind.context.modalOpen !== undefined && keybind.context.modalOpen !== context.modalOpen) {
      return false
    }

    // Check editor focus restriction
    if (keybind.context.editorFocused !== undefined && keybind.context.editorFocused !== context.editorFocused) {
      return false
    }

    return true
  }

  /**
   * Clear all keybinds
   */
  clear(): void {
    this.keybinds.clear()
    if (this.isDebugMode) {
      console.log('🧹 Cleared all keybinds')
    }
  }

  /**
   * Get debug information
   */
  getDebugInfo(): object {
    return {
      totalKeybinds: this.keybinds.size,
      enabledKeybinds: Array.from(this.keybinds.values()).filter(kb => kb.enabled).length,
      categories: [...new Set(Array.from(this.keybinds.values()).map(kb => kb.category))],
      keybinds: Array.from(this.keybinds.entries())
    }
  }
}

/**
 * Utility function to parse key combinations
 */
export function parseKeyCombination(keyString: string): KeyCombination {
  const parts = keyString.toLowerCase().split('+')
  const combination: KeyCombination = { key: '' }
  
  for (const part of parts) {
    switch (part) {
      case 'ctrl':
      case 'control':
        combination.ctrl = true
        break
      case 'shift':
        combination.shift = true
        break
      case 'alt':
        combination.alt = true
        break
      case 'meta':
      case 'cmd':
      case 'command':
        combination.meta = true
        break
      default:
        combination.key = part
        break
    }
  }
  
  return combination
}

/**
 * Utility function to format key combinations for display
 */
export function formatKeyForDisplay(keyString: string): string {
  return keyString
    .split('+')
    .map(part => {
      switch (part.toLowerCase()) {
        case 'ctrl': return 'Ctrl'
        case 'shift': return 'Shift'
        case 'alt': return 'Alt'
        case 'meta': return 'Cmd'
        case 'space': return 'Space'
        case 'enter': return 'Enter'
        case 'escape': return 'Esc'
        case 'backspace': return 'Backspace'
        case 'delete': return 'Delete'
        case 'arrowup': return '↑'
        case 'arrowdown': return '↓'
        case 'arrowleft': return '←'
        case 'arrowright': return '→'
        default: return part.charAt(0).toUpperCase() + part.slice(1)
      }
    })
    .join(' + ')
}

/**
 * Global keybind manager instance
 */
export const globalKeybindManager = new KeybindManager()