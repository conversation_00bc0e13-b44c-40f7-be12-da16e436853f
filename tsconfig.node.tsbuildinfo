{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.dom.asynciterable.d.ts", "./node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "./node_modules/typescript/lib/lib.scripthost.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/typescript/lib/lib.es2020.full.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/buffer/index.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/sqlite.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/@types/estree/index.d.ts", "./node_modules/rollup/dist/rollup.d.ts", "./node_modules/vite/types/hmrPayload.d.ts", "./node_modules/vite/types/customEvent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/dist/node/moduleRunnerTransport.d-DJ_mE5sf.d.ts", "./node_modules/vite/dist/node/module-runner.d.ts", "./node_modules/esbuild/lib/main.d.ts", "./node_modules/source-map-js/source-map.d.ts", "./node_modules/postcss/lib/previous-map.d.ts", "./node_modules/postcss/lib/input.d.ts", "./node_modules/postcss/lib/css-syntax-error.d.ts", "./node_modules/postcss/lib/declaration.d.ts", "./node_modules/postcss/lib/root.d.ts", "./node_modules/postcss/lib/warning.d.ts", "./node_modules/postcss/lib/lazy-result.d.ts", "./node_modules/postcss/lib/no-work-result.d.ts", "./node_modules/postcss/lib/processor.d.ts", "./node_modules/postcss/lib/result.d.ts", "./node_modules/postcss/lib/document.d.ts", "./node_modules/postcss/lib/rule.d.ts", "./node_modules/postcss/lib/node.d.ts", "./node_modules/postcss/lib/comment.d.ts", "./node_modules/postcss/lib/container.d.ts", "./node_modules/postcss/lib/at-rule.d.ts", "./node_modules/postcss/lib/list.d.ts", "./node_modules/postcss/lib/postcss.d.ts", "./node_modules/vite/types/internal/lightningcssOptions.d.ts", "./node_modules/vite/types/internal/cssPreprocessorOptions.d.ts", "./node_modules/vite/types/importGlob.d.ts", "./node_modules/vite/types/metadata.d.ts", "./node_modules/vite/dist/node/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/magic-string/dist/magic-string.cjs.d.ts", "./node_modules/typescript/lib/typescript.d.ts", "./node_modules/@vue/compiler-sfc/dist/compiler-sfc.d.ts", "./node_modules/vue/compiler-sfc/index.d.ts", "./node_modules/@vitejs/plugin-vue/dist/index.d.ts", "./node_modules/vite-plugin-electron/dist/utils.d.ts", "./node_modules/vite-plugin-electron/dist/index.d.ts", "./node_modules/vite-plugin-electron-renderer/dist/index.d.ts", "./node_modules/vite-plugin-electron/dist/simple.d.ts", "./node_modules/vite-plugin-electron/simple.d.ts", "./package.json", "./vite.config.ts", "./node_modules/vite/types/importMeta.d.ts", "./node_modules/vite/client.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/dist/vue.d.ts", "./node_modules/electron/electron.d.ts", "./electron/electron-env.d.ts", "./node_modules/sqlite3/lib/sqlite3.d.ts", "./electron/main/database/database-api.ts", "./electron/main/api/media-api.ts", "./electron/main/api/sync-logic/types.ts", "./electron/main/api/sync-logic/file-operations.ts", "./electron/main/api/settings-api.ts", "./electron/main/api/sync-logic/manifest-manager.ts", "./electron/main/api/sync-logic/change-detector.ts", "./electron/main/api/sync-logic/conflict-resolver.ts", "./electron/main/api/sync-logic/unified-sync-engine.ts", "./electron/main/api/sync-logic/auto-sync.ts", "./electron/main/database/database-hooks.ts", "./electron/main/database/database.ts", "./electron/utils/filename-sanitizer.ts", "./electron/main/api/folders-api.ts", "./node_modules/@types/fs-extra/index.d.ts", "./node_modules/typed-query-selector/parser.d.ts", "./node_modules/devtools-protocol/types/protocol.d.ts", "./node_modules/devtools-protocol/types/protocol-mapping.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/generated/webdriver-bidi.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/cdp.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/generated/webdriver-bidi-bluetooth.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/generated/webdriver-bidi-permissions.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/chromium-bidi.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/ErrorResponse.d.ts", "./node_modules/chromium-bidi/lib/cjs/protocol/protocol.d.ts", "./node_modules/puppeteer/lib/types.d.ts", "./node_modules/@types/linkify-it/build/index.cjs.d.ts", "./node_modules/@types/linkify-it/index.d.ts", "./node_modules/@types/mdurl/build/index.cjs.d.ts", "./node_modules/@types/mdurl/index.d.ts", "./node_modules/@types/markdown-it/dist/index.cjs.d.ts", "./node_modules/@types/markdown-it/index.d.ts", "./electron/main/api/notes-api.ts", "./electron/main/api/recent-items-api.ts", "./electron/main/api/timer-api.ts", "./node_modules/axios/index.d.ts", "./node_modules/iso-639-3/iso6393.d.ts", "./node_modules/iso-639-3/iso6393-to-1.d.ts", "./node_modules/iso-639-3/iso6393-to-2b.d.ts", "./node_modules/iso-639-3/iso6393-to-2t.d.ts", "./node_modules/iso-639-3/index.d.ts", "./electron/utils/language-converter.ts", "./electron/main/api/books-api.ts", "./public/discord-rpc-api.ts", "./electron/main/api/sync-logic/sync-api.ts", "./electron/main/ipc-handlers.ts", "./electron/main/protocol-handlers.ts", "./electron/main/index.ts", "./electron/main/api/sync-logic/import-handler.ts", "./electron/main/database/db-test-utils.ts", "./src/types/electron-api.d.ts", "./electron/preload/api-bridge.ts", "./electron/preload/index.ts", "./node_modules/keyv/src/index.d.ts", "./node_modules/@types/http-cache-semantics/index.d.ts", "./node_modules/@types/responselike/index.d.ts", "./node_modules/@types/cacheable-request/index.d.ts", "./node_modules/@types/ms/index.d.ts", "./node_modules/@types/debug/index.d.ts", "./node_modules/@types/trusted-types/lib/index.d.ts", "./node_modules/dompurify/dist/purify.cjs.d.ts", "./node_modules/@types/keyv/index.d.ts", "./node_modules/@types/trusted-types/index.d.ts", "./node_modules/@types/yauzl/index.d.ts"], "fileIdsList": [[57, 100, 200, 206, 207], [57, 100, 115, 117, 210, 211, 220, 222, 223, 245, 251], [57, 100, 210, 220, 222], [57, 100, 113, 122, 207, 209, 210], [57, 100, 113, 122, 207, 210, 211, 220, 222, 223, 224, 235, 241], [57, 100, 209, 210, 221], [57, 100, 209, 221], [57, 100, 112, 212, 218], [57, 100, 105, 210, 212, 215], [57, 100, 212], [57, 100, 105, 113, 122, 212], [57, 100, 105, 114, 122, 212, 213, 215, 218], [57, 100, 105, 122, 210, 212, 213, 214], [57, 100, 112, 114, 122, 210, 212, 214, 215, 218, 219], [57, 100], [57, 100, 112, 122, 210, 211, 212, 213, 215, 216, 217, 221], [57, 100, 113, 209, 221], [57, 100, 112, 219], [57, 100, 113, 122, 207, 209, 220], [57, 100, 113, 122, 143, 209], [57, 100, 119, 121, 122, 143, 207, 214, 221, 244, 253, 254, 255, 256], [57, 100, 122, 207, 210, 211, 214, 220, 221, 223, 242, 243, 244, 252, 253, 254], [57, 100, 113, 207], [57, 100, 207, 260], [57, 100, 207, 261], [57, 100, 250], [57, 100, 183], [57, 100, 112, 115, 143, 150, 263, 264, 265], [57, 100, 267], [57, 100, 113, 150], [57, 100, 112, 150], [57, 100, 236], [57, 100, 237, 239], [57, 100, 240], [57, 100, 238], [57, 97, 100], [57, 99, 100], [100], [57, 100, 105, 135], [57, 100, 101, 106, 112, 113, 120, 132, 143], [57, 100, 101, 102, 112, 120], [52, 53, 54, 57, 100], [57, 100, 103, 144], [57, 100, 104, 105, 113, 121], [57, 100, 105, 132, 140], [57, 100, 106, 108, 112, 120], [57, 99, 100, 107], [57, 100, 108, 109], [57, 100, 110, 112], [57, 99, 100, 112], [57, 100, 112, 113, 114, 132, 143], [57, 100, 112, 113, 114, 127, 132, 135], [57, 95, 100], [57, 95, 100, 108, 112, 115, 120, 132, 143], [57, 100, 112, 113, 115, 116, 120, 132, 140, 143], [57, 100, 115, 117, 132, 140, 143], [55, 56, 57, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149], [57, 100, 112, 118], [57, 100, 119, 143], [57, 100, 108, 112, 120, 132], [57, 100, 121], [57, 100, 122], [57, 99, 100, 123], [57, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149], [57, 100, 125], [57, 100, 126], [57, 100, 112, 127, 128], [57, 100, 127, 129, 144, 146], [57, 100, 112, 132, 133, 135], [57, 100, 134, 135], [57, 100, 132, 133], [57, 100, 135], [57, 100, 136], [57, 97, 100, 132], [57, 100, 112, 138, 139], [57, 100, 138, 139], [57, 100, 105, 120, 132, 140], [57, 100, 141], [57, 100, 120, 142], [57, 100, 115, 126, 143], [57, 100, 105, 144], [57, 100, 132, 145], [57, 100, 119, 146], [57, 100, 147], [57, 100, 112, 114, 123, 132, 135, 143, 146, 148], [57, 100, 132, 149], [57, 100, 115, 132, 150], [57, 100, 269], [57, 100, 112, 132, 150], [57, 100, 182, 190], [57, 100, 183, 184, 185], [57, 100, 186], [57, 100, 177, 183, 185, 186, 187, 188], [57, 100, 184], [57, 100, 184, 202, 203, 205], [57, 100, 202, 203, 204, 205], [57, 100, 228], [57, 100, 226, 227, 228], [57, 100, 228, 229, 230, 231], [57, 100, 228, 229, 230, 231, 232, 233], [57, 100, 226], [57, 100, 112, 113, 150], [57, 100, 246, 247, 248, 249], [57, 100, 112], [57, 100, 174], [57, 100, 172, 174], [57, 100, 163, 171, 172, 173, 175], [57, 100, 161], [57, 100, 164, 169, 174, 177], [57, 100, 160, 177], [57, 100, 164, 165, 168, 169, 170, 177], [57, 100, 164, 165, 166, 168, 169, 177], [57, 100, 161, 162, 163, 164, 165, 169, 170, 171, 173, 174, 175, 177], [57, 100, 159, 161, 162, 163, 164, 165, 166, 168, 169, 170, 171, 172, 173, 174, 175, 176], [57, 100, 159, 177], [57, 100, 164, 166, 167, 169, 170, 177], [57, 100, 168, 177], [57, 100, 169, 170, 174, 177], [57, 100, 162, 172], [57, 100, 101, 132, 150, 225, 226, 227, 234], [57, 100, 151, 152], [57, 67, 71, 100, 143], [57, 67, 100, 132, 143], [57, 62, 100], [57, 64, 67, 100, 140, 143], [57, 100, 120, 140], [57, 100, 150], [57, 62, 100, 150], [57, 64, 67, 100, 120, 143], [57, 59, 60, 63, 66, 100, 112, 132, 143], [57, 67, 74, 100], [57, 59, 65, 100], [57, 67, 88, 89, 100], [57, 63, 67, 100, 135, 143, 150], [57, 88, 100, 150], [57, 61, 62, 100, 150], [57, 67, 100], [57, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 90, 91, 92, 93, 94, 100], [57, 67, 82, 100], [57, 67, 74, 75, 100], [57, 65, 67, 75, 76, 100], [57, 66, 100], [57, 59, 62, 67, 100], [57, 67, 71, 75, 76, 100], [57, 71, 100], [57, 65, 67, 70, 100, 143], [57, 59, 64, 67, 74, 100], [57, 100, 132], [57, 62, 67, 88, 100, 148, 150], [57, 100, 158, 182], [57, 100, 101, 150, 152, 181, 182, 192], [57, 100, 152, 181, 182, 193, 194], [57, 100, 182, 193], [57, 100, 195], [57, 100, 199], [57, 100, 112, 113, 115, 116, 117, 120, 132, 140, 143, 149, 150, 152, 153, 154, 156, 157, 158, 177, 178, 179, 180, 181, 182], [57, 100, 153, 154, 155, 156], [57, 100, 153], [57, 100, 154], [57, 100, 155, 180], [57, 100, 152, 182], [57, 100, 189], [57, 100, 201, 205], [57, 100, 113, 182, 191, 196, 197]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7a3c8b952931daebdfc7a2897c53c0a1c73624593fa070e46bd537e64dcd20a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "80e18897e5884b6723488d4f5652167e7bb5024f946743134ecc4aa4ee731f89", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cd034f499c6cdca722b60c04b5b1b78e058487a7085a8e0d6fb50809947ee573", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1305d1e76ca44e30fb8b2b8075fa522b83f60c0bcf5d4326a9d2cf79b53724f8", "impliedFormat": 1}, {"version": "6c7176368037af28cb72f2392010fa1cef295d6d6744bca8cfb54985f3a18c3e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "437e20f2ba32abaeb7985e0afe0002de1917bc74e949ba585e49feba65da6ca1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d802f0e6b5188646d307f070d83512e8eb94651858de8a82d1e47f60fb6da4e2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "a12d953aa755b14ac1d28ecdc1e184f3285b01d6d1e58abc11bf1826bc9d80e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a38efe83ff77c34e0f418a806a01ca3910c02ee7d64212a59d59bca6c2c38fa1", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "3fe4022ba1e738034e38ad9afacbf0f1f16b458ed516326f5bf9e4a31e9be1dc", "impliedFormat": 1}, {"version": "a957197054b074bcdf5555d26286e8461680c7c878040d0f4e2d5509a7524944", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4314c7a11517e221f7296b46547dbc4df047115b182f544d072bdccffa57fc72", "impliedFormat": 1}, {"version": "e9b97d69510658d2f4199b7d384326b7c4053b9e6645f5c19e1c2a54ede427fc", "impliedFormat": 1}, {"version": "c2510f124c0293ab80b1777c44d80f812b75612f297b9857406468c0f4dafe29", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "f478f6f5902dc144c0d6d7bdc919c5177cac4d17a8ca8653c2daf6d7dc94317f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "19d5f8d3930e9f99aa2c36258bf95abbe5adf7e889e6181872d1cdba7c9a7dd5", "impliedFormat": 1}, {"version": "9855e02d837744303391e5623a531734443a5f8e6e8755e018c41d63ad797db2", "impliedFormat": 1}, {"version": "a6bf63d17324010ca1fbf0389cab83f93389bb0b9a01dc8a346d092f65b3605f", "impliedFormat": 1}, {"version": "e009777bef4b023a999b2e5b9a136ff2cde37dc3f77c744a02840f05b18be8ff", "impliedFormat": 1}, {"version": "1e0d1f8b0adfa0b0330e028c7941b5a98c08b600efe7f14d2d2a00854fb2f393", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "88bc59b32d0d5b4e5d9632ac38edea23454057e643684c3c0b94511296f2998c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a0a1dda070290b92da5a50113b73ecc4dd6bcbffad66e3c86503d483eafbadcf", "impliedFormat": 1}, {"version": "59dcad36c4549175a25998f6a8b33c1df8e18df9c12ebad1dfb25af13fd4b1ce", "impliedFormat": 1}, {"version": "206a70e72af3e24688397b81304358526ce70d020e4c2606c4acfd1fa1e81fb2", "impliedFormat": 1}, {"version": "017caf5d2a8ef581cf94f678af6ce7415e06956317946315560f1487b9a56167", "impliedFormat": 1}, {"version": "528b62e4272e3ddfb50e8eed9e359dedea0a4d171c3eb8f337f4892aac37b24b", "impliedFormat": 1}, {"version": "d71535813e39c23baa113bc4a29a0e187b87d1105ccc8c5a6ebaca38d9a9bff2", "impliedFormat": 1}, {"version": "4c3148420835de895b9218b2cea321a4607008ba5cefa57b2a57e1c1ef85d22f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f72bc8fe16da67e4e3268599295797b202b95e54bd215a03f97e925dd1502a36", "impliedFormat": 1}, {"version": "b1b6ee0d012aeebe11d776a155d8979730440082797695fc8e2a5c326285678f", "impliedFormat": 1}, {"version": "45875bcae57270aeb3ebc73a5e3fb4c7b9d91d6b045f107c1d8513c28ece71c0", "impliedFormat": 1}, {"version": "915e18c559321c0afaa8d34674d3eb77e1ded12c3e85bf2a9891ec48b07a1ca5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "636302a00dfd1f9fe6e8e91e4e9350c6518dcc8d51a474e4fc3a9ba07135100b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3f16a7e4deafa527ed9995a772bb380eb7d3c2c0fd4ae178c5263ed18394db2c", "impliedFormat": 1}, {"version": "933921f0bb0ec12ef45d1062a1fc0f27635318f4d294e4d99de9a5493e618ca2", "impliedFormat": 1}, {"version": "71a0f3ad612c123b57239a7749770017ecfe6b66411488000aba83e4546fde25", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "e1120271ebbc9952fdc7b2dd3e145560e52e06956345e6fdf91d70ca4886464f", "impliedFormat": 1}, {"version": "814118df420c4e38fe5ae1b9a3bafb6e9c2aa40838e528cde908381867be6466", "impliedFormat": 1}, {"version": "e1ce1d622f1e561f6cdf246372ead3bbc07ce0342024d0e9c7caf3136f712698", "impliedFormat": 1}, {"version": "199c8269497136f3a0f4da1d1d90ab033f899f070e0dd801946f2a241c8abba2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "27e4532aaaa1665d0dd19023321e4dc12a35a741d6b8e1ca3517fcc2544e0efe", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2754d8221d77c7b382096651925eb476f1066b3348da4b73fe71ced7801edada", "impliedFormat": 1}, {"version": "6266d94fb9165d42716e45f3a537ca9f59c07b1dfa8394a659acf139134807db", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f0be1b8078cd549d91f37c30c222c2a187ac1cf981d994fb476a1adc61387b14", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0aaed1d72199b01234152f7a60046bc947f1f37d78d182e9ae09c4289e06a592", "impliedFormat": 1}, {"version": "98ffdf93dfdd206516971d28e3e473f417a5cfd41172e46b4ce45008f640588e", "impliedFormat": 1}, {"version": "66ba1b2c3e3a3644a1011cd530fb444a96b1b2dfe2f5e837a002d41a1a799e60", "impliedFormat": 1}, {"version": "7e514f5b852fdbc166b539fdd1f4e9114f29911592a5eb10a94bb3a13ccac3c4", "impliedFormat": 1}, {"version": "7d6ff413e198d25639f9f01f16673e7df4e4bd2875a42455afd4ecc02ef156da", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a7692a54334fd08960cd0c610ff509c2caa93998e0dcefa54021489bcc67c22d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74736930d108365d7bbe740c7154706ccfb1b2a3855a897963ab3e5c07ecbf19", "impliedFormat": 1}, {"version": "3a051941721a7f905544732b0eb819c8d88333a96576b13af08b82c4f17581e4", "impliedFormat": 1}, {"version": "ac5ed35e649cdd8143131964336ab9076937fa91802ec760b3ea63b59175c10a", "impliedFormat": 1}, {"version": "1e25f8d0a8573cafd5b5a16af22d26ab872068a693b9dbccd3f72846ab373655", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3797dd6f4ea3dc15f356f8cdd3128bfa18122213b38a80d6c1f05d8e13cbdad8", "impliedFormat": 1}, {"version": "ad90122e1cb599b3bc06a11710eb5489101be678f2920f2322b0ac3e195af78d", "impliedFormat": 1}, {"version": "e2b48abff5a8adc6bb1cd13a702b9ef05e6045a98e7cfa95a8779b53b6d0e69d", "impliedFormat": 1}, {"version": "ffb518fc55181aefd066c690dbc0f8fa6a1533c8ddac595469c8c5f7fda2d756", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a7ca8df4f2931bef2aa4118078584d84a0b16539598eaadf7dce9104dfaa381c", "impliedFormat": 1}, {"version": "11443a1dcfaaa404c68d53368b5b818712b95dd19f188cab1669c39bee8b84b3", "impliedFormat": 1}, {"version": "36977c14a7f7bfc8c0426ae4343875689949fb699f3f84ecbe5b300ebf9a2c55", "impliedFormat": 1}, {"version": "035d0934d304483f07148427a5bd5b98ac265dae914a6b49749fe23fbd893ec7", "impliedFormat": 99}, {"version": "e2ed5b81cbed3a511b21a18ab2539e79ac1f4bc1d1d28f8d35d8104caa3b429f", "impliedFormat": 99}, {"version": "dd7ca4f0ef3661dac7043fb2cdf1b99e008d2b6bc5cd998dd1fa5a2968034984", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "333caa2bfff7f06017f114de738050dd99a765c7eb16571c6d25a38c0d5365dc", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "impliedFormat": 1}, {"version": "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "impliedFormat": 1}, {"version": "1251d53755b03cde02466064260bb88fd83c30006a46395b7d9167340bc59b73", "impliedFormat": 1}, {"version": "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "impliedFormat": 1}, {"version": "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "impliedFormat": 1}, {"version": "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "impliedFormat": 1}, {"version": "4ec16d7a4e366c06a4573d299e15fe6207fc080f41beac5da06f4af33ea9761e", "impliedFormat": 1}, {"version": "7870becb94cbc11d2d01b77c4422589adcba4d8e59f726246d40cd0d129784d8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "f70b8328a15ca1d10b1436b691e134a49bc30dcf3183a69bfaa7ba77e1b78ecd", "impliedFormat": 1}, {"version": "ff3660e2664e6096196280deb4e176633b1bb1e58a7dcc9b021ec0e913a6f96f", "impliedFormat": 99}, {"version": "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "impliedFormat": 1}, {"version": "2faebd3f3d53964c95d291bc1545c20a5db8b9886d44bc1d7b0afb6ecc261841", "impliedFormat": 1}, {"version": "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "impliedFormat": 1}, {"version": "3ef2a48cf1b15a53a748b921b4e39c17f8de3a41839c359f5c2661eaace3894e", "impliedFormat": 1}, {"version": "2be2227c3810dfd84e46674fd33b8d09a4a28ad9cb633ed536effd411665ea1e", "impliedFormat": 1}, {"version": "c302df1d6f371c6064cb5f4d0b41165425b682b287a3b8625527b2752eb433ee", "impliedFormat": 1}, {"version": "ecd06646deae73979326583132d1c688566399a3bf44bcffbdc77b1cc2220d05", "impliedFormat": 1}, {"version": "3feec212c0aeb91e5a6e62caaf9f128954590210f8c302910ea377c088f6b61a", "impliedFormat": 1}, {"version": "f3e2483d5a6562f60624d9e3ebeeca2678a3bea1e1fac6c425b5b222d9eec0e0", "impliedFormat": 1}, {"version": "0890467498b67e20cec24aaa50bebc232dc4f588a982e60dc6bb07b6e797da52", "impliedFormat": 1}, {"version": "928e3aa1a5dab12a194d90c71959d1251917515554f45793f98d06ab731f3fbf", "impliedFormat": 1}, {"version": "c400678110f688feba4d6d3f93269fca02834bb3d6a1ecd99b28b3f69f7d23f4", "impliedFormat": 1}, {"version": "0531ff3bd78d643b584e02909c5fdb86938169d818afbbef1bc3a2a768ab32ec", "impliedFormat": 1}, {"version": "dedb427b9d3d0240fc4c7cd1109ad0763e5eba421b6943950d6793716d343490", "impliedFormat": 1}, "066340abf1e27b20725f614e76861532ab8b1d575f3d7c71c4dbab1af9450378", "4170bc4c7388d60213f90c07a0de345f2835776918cd8941b69a8771ffaf3da2", {"version": "19efad8495a7a6b064483fccd1d2b427403dd84e67819f86d1c6ee3d7abf749c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1eef826bc4a19de22155487984e345a34c9cd511dd1170edc7a447cb8231dd4a", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "cf76e2f59b26aea7c923015783083b4820d6c0d85cda2fec3a0f232aabfc56c3", "impliedFormat": 1}, {"version": "616a55b9694bdb765470c1f66dc9606779b250520a36b87f4de2e4c594cea9bc", "impliedFormat": 1}, {"version": "9aab7aec34d809b2868dd0f0743e47ff35c0795ec5072d825c4ba934206cc7d3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "7f869e3f84556b05562cfac2ab3b046ea468c79f1b3dc7d9af0205075cb44fa5", "impliedFormat": 1}, {"version": "c0191592be8eb7906f99ac4b8798d80a585b94001ea1a5f50d6ce5b0d13a5c62", "impliedFormat": 1}, {"version": "f1b4268529636fec8722efb9ed630629eea91aac4a3b93b527d8227bcc3c7cc3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "583be9eff87f9261a4b15df9b5824b4fa6e603696713e7403b35a19bb5fc19f1", "affectsGlobalScope": true}, {"version": "a39db87a3a3aa954ac3f6553b9fbfc642eb22bef7586cc1f0559e676aa073fa8", "impliedFormat": 1}, "b4ab1c4ba1b12b767b6f0094fc0edfd156cdec62da6d037d4ac9c51272a2d214", "5509fa8d601ecf10a243e36a06bef26ad7476360412f7deb6a356fd58b45d386", "0903db498fee053ce4b94c038f4357c02f6c7cfea53fb0b3da90bbf96bdedc47", "1b485bf3d1854e6b4087ca8431e89884a04e6a9eaf318211a5ff7278f4f0ca7a", "4785f3022dd30889c8b4c0559daeef1cef524cacf8b6308c53427d3e8c2578a6", "d6db94807c514c0ba559adb9ad85e76d330c6377faaaf073ce2b75ba51a95117", "449169b5de805d80dabc13f593f1f503463a58a1ac2b9636c4eec2edef07d213", "459dbaa04150dcfda742593c2257210a6ec7c91b14e509a1d22a8c3aeb18ecca", "adf88187017bcf6abbc502815eb4563c5708758798dc4d9773e3f71f0b9736f9", "2673457737118089a42887a8ae0bb77bd80becb06f9f849b0987e49180930bb1", "2d4542fad9fed0eb7695ac8c5036bead5770bee861c232d51d97ea70f03e24b9", "d472a45836944bc297b9cb9dc185cfa7c971f26c02f25580c4b96588d5a05b15", "a3d15d31e99daacb3560867ec10d0ce441a07e954f33a546811cc34566d387e6", "b21a277c38823ec28db88bf6c23dd31bc7f4190cf0e7decbd071d61df8b46bd9", {"version": "ed19da84b7dbf00952ad0b98ce5c194f1903bcf7c94d8103e8e0d63b271543ae", "impliedFormat": 1}, {"version": "f21ce049835dad382b22691fb6b34076d0717307d46d92320893765be010cd56", "impliedFormat": 1}, {"version": "d8265f19cd3ed7859117d26b9f640c6d5cdfc772a21d155032a402fe9ae5e64b", "impliedFormat": 1}, {"version": "9a84f6f9fe16f98434849b68deeb39e566a5a3ebd0cf66b160190ecf81655c64", "impliedFormat": 1}, {"version": "c0bd68c97b48edc620d3d1769570f23c6aba817e717cf91a9163f5147af83719", "impliedFormat": 1}, {"version": "0954f48dcfbc647b2845dbcf2bf97534c09d6e44fc7538ec000e00adacb0188e", "impliedFormat": 1}, {"version": "d04c8d9728b08c80d967203c5699d83455237eda8601336451efd4efd38a1f76", "impliedFormat": 1}, {"version": "8fbaf75bd54bf741ecdacb702ea711e3fea3dc1c66fe15422c7cc5253f13cb92", "impliedFormat": 1}, {"version": "ee87efc6992980469b3f518fd30af00ec88ca82b9cfe6c63ec637a9cd97d4780", "impliedFormat": 1}, {"version": "dd36b144e0e70b4e38f588913af663ed959b10b5cf80952a4beb22a10255bf08", "impliedFormat": 1}, {"version": "d064b43717b5b5dfca0d6cd738022ab377c90e45f05edbcd5a0c8753e6627d88", "impliedFormat": 1}, {"version": "6a0408fd2cd82b76f44a3649401921bc605f9cd4bc916e4d4bf4b9b854f906c8", "impliedFormat": 1}, {"version": "742f21debb3937c3839a63245648238555bdab1ea095d43fd10c88a64029bf76", "impliedFormat": 1}, {"version": "7cfdf3b9a5ba934a058bfc9390c074104dc7223b7e3c16fd5335206d789bc3d3", "impliedFormat": 1}, {"version": "0944f27ebff4b20646b71e7e3faaaae50a6debd40bc63e225de1320dd15c5795", "impliedFormat": 1}, {"version": "5d30565583300c9256072a013ac0318cc603ff769b4c5cafc222394ea93963e1", "impliedFormat": 1}, {"version": "8a7219b41d3c1c93f3f3b779146f313efade2404eeece88dcd366df7e2364977", "impliedFormat": 1}, {"version": "a109c4289d59d9019cfe1eeab506fe57817ee549499b02a83a7e9d3bdf662d63", "impliedFormat": 1}, "6c2f17dcbf2b3730c7658a164414517d49f99676e94256d2f624ee1e91b060b0", "fdfa69295463deb0bd30aed896d6832399193809ac73b29e12f46f1d88756390", "bb81e57068780fa2370f79825d1a238b1e85e2504c336109b5868affc4c7a438", {"version": "c1a44418b7e3f9381e55dea86cc32b26ec3d5ccf6510102716aaa55023919f38", "impliedFormat": 99}, {"version": "db0d2c99eda1a68b16025b8cecbd51e2c83827d21401c2628023a26455e6ea05", "impliedFormat": 99}, {"version": "5e91cc52298efabfc94cdf2474302ad7873d8896a544ab96e4ffde6496d71ebc", "impliedFormat": 99}, {"version": "b968ca8105a9e9f30e7a6f3e5b23bf16c1079cfda88ae8ae7a3f44d94774374d", "impliedFormat": 99}, {"version": "ba47a4946f5702d6e48d06e3a27e3afb7cf03ceea2be5a678b13669dedbc609c", "impliedFormat": 99}, {"version": "b996a245ccf4c98affe7efaa0df464596bf6297122bccfc05de275930741c045", "impliedFormat": 99}, "2b3bebd0fd7288880c668fa72556570f9e4b20ecf486987c0810986ee6e66d29", "e97415c3712d5d7f8de4fdb87006739b47030a7a591710e90ab405236aa84757", "932be91d97f4063e59cbf1f0e184977f6211e2956c7dcd93f4927a00330ae9dc", "7e6cb903c90e0d6610f4fbcee9963c0d19d1251fe3bc11ead7a3372a41d675a0", "b0f15adab1de4495ee83200568e69a3275dbeaa3aa9baa633d0c3625df801bb9", "4190487b203e783ff99de61864de0686e5a585dcafb6ed6f38fcf0dd73496787", "dbd468223ca445087877198ba6b425199af33824121a5af5ed053315f2cb1e26", "a0f9e80b1c920021e490cd2b714bf378ed1060bd50023f8b31fa03fc08e61570", "ed55ab385c70a430bb8a43e7b7187714cebcf728f359978bbd97d98ec2cec77a", {"version": "5f7b1b626e7f0c9caa32d6f4349e60c835fb4c7431a4a4e07714cbdc2739808d", "affectsGlobalScope": true}, "2d2de47ab7ffe25a377aa24fa32439d38c6dbebdf23cbb3c5c594d331b266872", "b4c14ee1b9595ea096f587d3eec46b4d0458fd85f91c58d771f9dea3461d3f53", {"version": "42baf4ca38c38deaf411ea73f37bc39ff56c6e5c761a968b64ac1b25c92b5cd8", "impliedFormat": 1}, {"version": "d7dbe0ad36bdca8a6ecf143422a48e72cc8927bab7b23a1a2485c2f78a7022c6", "impliedFormat": 1}, {"version": "8718fa41d7cf4aa91de4e8f164c90f88e0bf343aa92a1b9b725a9c675c64e16b", "impliedFormat": 1}, {"version": "f992cd6cc0bcbaa4e6c810468c90f2d8595f8c6c3cf050c806397d3de8585562", "impliedFormat": 1}, {"version": "fb893a0dfc3c9fb0f9ca93d0648694dd95f33cbad2c0f2c629f842981dfd4e2e", "impliedFormat": 1}, {"version": "3eb11dbf3489064a47a2e1cf9d261b1f100ef0b3b50ffca6c44dd99d6dd81ac1", "impliedFormat": 1}, {"version": "15fe687c59d62741b4494d5e623d497d55eb38966ecf5bea7f36e48fc3fbe15e", "impliedFormat": 1}, {"version": "331205cfe59853bedacd9deb31bc6336f742a2075c4ff6f43d349290559b4e50", "impliedFormat": 1}, {"version": "fec943fdb3275eb6e006b35e04a8e2e99e9adf3f4b969ddf15315ac7575a93e4", "impliedFormat": 1}, {"version": "2c3b8be03577c98530ef9cb1a76e2c812636a871f367e9edf4c5f3ce702b77f8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74d5a87c3616cd5d8691059d531504403aa857e09cbaecb1c64dfb9ace0db185", "impliedFormat": 1}], "root": [197, 198, 208, [210, 223], [242, 244], [251, 259], 261, 262], "options": {"allowSyntheticDefaultImports": true, "composite": true, "downlevelIteration": true, "esModuleInterop": true, "module": 99, "target": 7}, "referencedMap": [[208, 1], [252, 2], [223, 3], [211, 4], [242, 5], [243, 6], [214, 7], [219, 8], [216, 9], [217, 10], [213, 11], [258, 12], [215, 13], [254, 14], [212, 15], [218, 16], [244, 7], [210, 17], [220, 18], [221, 19], [259, 20], [257, 21], [255, 22], [256, 23], [261, 24], [262, 25], [222, 15], [251, 26], [185, 27], [183, 15], [266, 28], [268, 29], [151, 15], [224, 30], [264, 15], [271, 31], [236, 15], [237, 32], [240, 33], [241, 34], [238, 15], [239, 35], [267, 15], [97, 36], [98, 36], [99, 37], [57, 38], [100, 39], [101, 40], [102, 41], [52, 15], [55, 42], [53, 15], [54, 15], [103, 43], [104, 44], [105, 45], [106, 46], [107, 47], [108, 48], [109, 48], [111, 15], [110, 49], [112, 50], [113, 51], [114, 52], [96, 53], [56, 15], [115, 54], [116, 55], [117, 56], [150, 57], [118, 58], [119, 59], [120, 60], [121, 61], [122, 62], [123, 63], [124, 64], [125, 65], [126, 66], [127, 67], [128, 67], [129, 68], [130, 15], [131, 15], [132, 69], [134, 70], [133, 71], [135, 72], [136, 73], [137, 74], [138, 75], [139, 76], [140, 77], [141, 78], [142, 79], [143, 80], [144, 81], [145, 82], [146, 83], [147, 84], [148, 85], [149, 86], [265, 87], [272, 88], [269, 15], [273, 89], [191, 90], [186, 91], [201, 92], [189, 93], [202, 94], [203, 95], [205, 96], [184, 15], [245, 15], [58, 15], [233, 97], [229, 98], [232, 99], [230, 15], [231, 15], [228, 15], [234, 100], [204, 15], [227, 101], [226, 15], [270, 88], [207, 102], [158, 15], [250, 103], [247, 15], [248, 15], [249, 15], [246, 15], [263, 104], [187, 15], [175, 105], [173, 106], [174, 107], [162, 108], [163, 106], [170, 109], [161, 110], [166, 111], [176, 15], [167, 112], [172, 113], [177, 114], [160, 115], [168, 116], [169, 117], [164, 118], [171, 105], [165, 119], [235, 120], [152, 121], [159, 15], [209, 31], [225, 15], [49, 15], [50, 15], [10, 15], [8, 15], [9, 15], [14, 15], [13, 15], [2, 15], [15, 15], [16, 15], [17, 15], [18, 15], [19, 15], [20, 15], [21, 15], [22, 15], [3, 15], [23, 15], [24, 15], [4, 15], [25, 15], [29, 15], [26, 15], [27, 15], [28, 15], [30, 15], [31, 15], [32, 15], [5, 15], [33, 15], [34, 15], [35, 15], [36, 15], [6, 15], [40, 15], [37, 15], [38, 15], [39, 15], [41, 15], [7, 15], [42, 15], [51, 15], [47, 15], [48, 15], [43, 15], [44, 15], [45, 15], [46, 15], [1, 15], [12, 15], [11, 15], [188, 15], [74, 122], [84, 123], [73, 122], [94, 124], [65, 125], [64, 126], [93, 127], [87, 128], [92, 129], [67, 130], [81, 131], [66, 132], [90, 133], [62, 134], [61, 127], [91, 135], [63, 136], [68, 137], [69, 15], [72, 137], [59, 15], [95, 138], [85, 139], [76, 140], [77, 141], [79, 142], [75, 143], [78, 144], [88, 127], [70, 145], [71, 146], [80, 147], [60, 148], [83, 139], [82, 137], [86, 15], [89, 149], [194, 150], [193, 151], [195, 152], [192, 153], [196, 154], [200, 155], [182, 156], [157, 157], [156, 158], [154, 158], [153, 15], [155, 159], [180, 15], [199, 160], [179, 15], [178, 15], [181, 161], [190, 162], [206, 163], [197, 15], [253, 15], [260, 15], [198, 164]], "semanticDiagnosticsPerFile": [[182, [{"start": 559, "length": 17, "code": 2307, "category": 1, "messageText": {"messageText": "Cannot find module 'rollup/parseAst' or its corresponding type declarations.", "category": 1, "code": 2307, "next": [{"info": {"moduleReference": "rollup/parseAst"}}]}}]], [244, [{"start": 8265, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}, {"start": 8469, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}, {"start": 8577, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}, {"start": 21424, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}, {"start": 21640, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}, {"start": 21756, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'actual_pomodoro_count' does not exist on type 'TimerSession'."}]], [260, [{"start": 10817, "length": 2, "messageText": "Subsequent property declarations must have the same type.  Property 'db' must be of type '{ notes: { create: (note: { title: string; content?: string; html_content?: string; folder_id?: number; book_id?: number; type?: string; color?: string; order?: number; }) => Promise<any>; getAll: (options?: any) => Promise<...>; ... 5 more ...; search: (searchTerm: string) => Promise<...>; }; ... 4 more ...; timer:...', but here has type '{ notes: NotesAPI; folders: FoldersAPI; recentItems: RecentItemsAPI; media: MediaAPI; books: BooksAPI; timer: TimerAPI; discord: DiscordAPI; }'.", "category": 1, "code": 2717, "relatedInformation": [{"file": "./electron/electron-env.d.ts", "start": 353, "length": 2, "messageText": "'db' was also declared here.", "category": 3, "code": 6203}]}]], [262, [{"start": 380, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'backup' does not exist on type '{ notes: { create: (note: Note) => Promise<any>; createForBook: (bookId: number, customTitle?: string, folderId?: number) => Promise<any>; getAll: (options?: any) => Promise<...>; ... 8 more ...; autoLinkInFolder: (folderId: number) => Promise<...>; }; ... 8 more ...; selectFolder: () => Promise<...>; }'."}, {"start": 409, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'autoBackup' does not exist on type '{ notes: { create: (note: Note) => Promise<any>; createForBook: (bookId: number, customTitle?: string, folderId?: number) => Promise<any>; getAll: (options?: any) => Promise<...>; ... 8 more ...; autoLinkInFolder: (folderId: number) => Promise<...>; }; ... 8 more ...; selectFolder: () => Promise<...>; }'."}, {"start": 445, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'reverseBackup' does not exist on type '{ notes: { create: (note: Note) => Promise<any>; createForBook: (bookId: number, customTitle?: string, folderId?: number) => Promise<any>; getAll: (options?: any) => Promise<...>; ... 8 more ...; autoLinkInFolder: (folderId: number) => Promise<...>; }; ... 8 more ...; selectFolder: () => Promise<...>; }'."}, {"start": 8956, "length": 7, "messageText": "Cannot find name 'safeD<PERSON>'.", "category": 1, "code": 2304}, {"start": 9001, "length": 7, "messageText": "Cannot find name 'safeD<PERSON>'.", "category": 1, "code": 2304}, {"start": 9075, "length": 7, "messageText": "Cannot find name 'safeD<PERSON>'.", "category": 1, "code": 2304}, {"start": 9120, "length": 7, "messageText": "Cannot find name 'safeD<PERSON>'.", "category": 1, "code": 2304}]]], "affectedFilesPendingEmit": [252, 223, 211, 242, 243, 214, 219, 216, 217, 213, 258, 215, 254, 212, 218, 244, 210, 220, 221, 259, 257, 255, 256, 261, 262, 222, 251, 253, 198], "emitSignatures": [198, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 242, 243, 244, 251, 252, 253, 254, 255, 256, 257, 258, 259, 261, 262], "version": "5.8.3"}