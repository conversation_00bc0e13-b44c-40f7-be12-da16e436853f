// Database hooks manager for change detection and sync integration
import { EventEmitter } from 'events';
import { autoSync } from '../api/sync-logic/auto-sync';

export type DatabaseChangeType = 'create' | 'update' | 'delete';
export type DatabaseItemType = 'note' | 'folder' | 'book';

export interface DatabaseChangeEvent {
  type: DatabaseChangeType;
  itemType: DatabaseItemType;
  itemId: number;
  timestamp: Date;
  details?: any;
  options?: DatabaseHookOptions;
}

export interface DatabaseHookOptions {
  skipSyncNotification?: boolean;
  skipBackupEvent?: boolean;
}

/**
 * Centralized database hooks manager
 * Handles change detection and notifications for sync system
 */
class DatabaseHooksManager extends EventEmitter {
  private isInitialized: boolean = false;
  private changeHistory: DatabaseChangeEvent[] = [];
  private readonly maxHistorySize = 1000;

  // Track pending deletions until next sync
  private pendingDeletions: Array<{
    id: string;
    type: 'book' | 'folder' | 'note';
    deletedAt: string;
    details?: any;
  }> = [];

  constructor() {
    super();
    this.setMaxListeners(20); // Increase listener limit for multiple subscribers
  }

  /**
   * Initialize the hooks manager
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    console.log('[DatabaseHooks] Initializing database hooks manager');
    this.isInitialized = true;
    
    // Set up event listeners for auto-sync integration
    this.setupAutoSyncIntegration();
  }

  /**
   * Notify about a database change
   */
  public notifyChange(
    type: DatabaseChangeType,
    itemType: DatabaseItemType,
    itemId: number,
    details?: any,
    options: DatabaseHookOptions = {}
  ): void {
    if (!this.isInitialized) {
      console.warn('[DatabaseHooks] Hooks manager not initialized, skipping change notification');
      return;
    }

    const changeEvent: DatabaseChangeEvent = {
      type,
      itemType,
      itemId,
      timestamp: new Date(),
      details,
      options
    };

    // Add to change history
    this.addToHistory(changeEvent);

    // Store deletion info for sync system
    if (type === 'delete') {
      this.storeDeletionInfo(changeEvent);
    }

    // Emit local event for any listeners
    // Auto-sync notification will be handled by the event listener if enabled
    this.emit('database-change', changeEvent);

    // Log the change for debugging
    console.log(`[DatabaseHooks] Change detected: ${type} ${itemType} (ID: ${itemId})`);
  }

  /**
   * Set up integration with auto-sync system
   */
  private setupAutoSyncIntegration(): void {
    // Listen for our own change events and forward to auto-sync
    this.on('database-change', (changeEvent: DatabaseChangeEvent) => {
      // Check if sync notification is not explicitly skipped
      if (!changeEvent.options?.skipSyncNotification) {
        this.notifyAutoSync(changeEvent);
      }
    });
  }

  /**
   * Store deletion information for sync system
   */
  private storeDeletionInfo(changeEvent: DatabaseChangeEvent): void {
    if (changeEvent.type === 'delete') {
      const itemId = `${changeEvent.itemType}_${changeEvent.itemId}`;

      // Check if this deletion is already tracked
      const existingIndex = this.pendingDeletions.findIndex(d => d.id === itemId);

      if (existingIndex === -1) {
        this.pendingDeletions.push({
          id: itemId,
          type: changeEvent.itemType as 'book' | 'folder' | 'note',
          deletedAt: changeEvent.timestamp.toISOString(),
          details: changeEvent.details
        });
        console.log(`[DatabaseHooks] Stored deletion info for ${itemId}`);
      }
    }
  }

  /**
   * Notify the auto-sync system about a database change
   */
  private notifyAutoSync(changeEvent: DatabaseChangeEvent): void {
    try {
      // Check if auto-sync is available and enabled
      if (autoSync && autoSync.isEnabled()) {
        const changeType = `${changeEvent.itemType}_${changeEvent.type}`;
        console.log(`[DatabaseHooks] Notifying auto-sync of change: ${changeType}`);
        autoSync.onDatabaseChange(changeType, {
          itemId: changeEvent.itemId,
          itemType: changeEvent.itemType,
          timestamp: changeEvent.timestamp,
          details: changeEvent.details
        });
      } else {
        console.log('[DatabaseHooks] Auto-sync not enabled, skipping notification');
      }
    } catch (error) {
      console.error('[DatabaseHooks] Error notifying auto-sync:', error);
    }
  }

  /**
   * Add change to history with size management
   */
  private addToHistory(changeEvent: DatabaseChangeEvent): void {
    this.changeHistory.push(changeEvent);
    
    // Maintain maximum history size
    if (this.changeHistory.length > this.maxHistorySize) {
      this.changeHistory = this.changeHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Get recent change history
   */
  public getChangeHistory(limit: number = 50): DatabaseChangeEvent[] {
    return this.changeHistory.slice(-limit);
  }

  /**
   * Get changes for a specific item type
   */
  public getChangesForItemType(itemType: DatabaseItemType, limit: number = 50): DatabaseChangeEvent[] {
    return this.changeHistory
      .filter(change => change.itemType === itemType)
      .slice(-limit);
  }

  /**
   * Get changes for a specific item
   */
  public getChangesForItem(itemType: DatabaseItemType, itemId: number): DatabaseChangeEvent[] {
    return this.changeHistory.filter(change => 
      change.itemType === itemType && change.itemId === itemId
    );
  }

  /**
   * Clear change history
   */
  public clearHistory(): void {
    this.changeHistory = [];
    console.log('[DatabaseHooks] Change history cleared');
  }

  /**
   * Get pending deletions for sync system
   */
  public getPendingDeletions(): Array<{
    id: string;
    type: 'book' | 'folder' | 'note';
    deletedAt: string;
    details?: any;
  }> {
    return [...this.pendingDeletions];
  }

  /**
   * Clear pending deletions after they've been processed by sync
   */
  public clearPendingDeletions(): void {
    const count = this.pendingDeletions.length;
    this.pendingDeletions = [];
    if (count > 0) {
      console.log(`[DatabaseHooks] Cleared ${count} pending deletions`);
    }
  }

  /**
   * Get statistics about recent changes
   */
  public getChangeStats(since?: Date): {
    total: number;
    byType: Record<DatabaseChangeType, number>;
    byItemType: Record<DatabaseItemType, number>;
  } {
    const changes = since 
      ? this.changeHistory.filter(change => change.timestamp >= since)
      : this.changeHistory;

    const stats = {
      total: changes.length,
      byType: { create: 0, update: 0, delete: 0 } as Record<DatabaseChangeType, number>,
      byItemType: { note: 0, folder: 0, book: 0 } as Record<DatabaseItemType, number>
    };

    changes.forEach(change => {
      stats.byType[change.type]++;
      stats.byItemType[change.itemType]++;
    });

    return stats;
  }

  /**
   * Shutdown the hooks manager
   */
  public shutdown(): void {
    console.log('[DatabaseHooks] Shutting down database hooks manager');
    this.removeAllListeners();
    this.clearHistory();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const databaseHooks = new DatabaseHooksManager();

// Helper functions for common operations
export const notifyNoteChange = (
  type: DatabaseChangeType, 
  noteId: number, 
  details?: any, 
  options?: DatabaseHookOptions
) => {
  databaseHooks.notifyChange(type, 'note', noteId, details, options);
};

export const notifyFolderChange = (
  type: DatabaseChangeType, 
  folderId: number, 
  details?: any, 
  options?: DatabaseHookOptions
) => {
  databaseHooks.notifyChange(type, 'folder', folderId, details, options);
};

export const notifyBookChange = (
  type: DatabaseChangeType, 
  bookId: number, 
  details?: any, 
  options?: DatabaseHookOptions
) => {
  databaseHooks.notifyChange(type, 'book', bookId, details, options);
};

// Export the class for testing purposes
export { DatabaseHooksManager };