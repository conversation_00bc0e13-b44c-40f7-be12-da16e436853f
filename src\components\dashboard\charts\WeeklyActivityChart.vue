<template>
  <div class="weekly-activity-chart">
    <h3 class="chart-title">Weekly Activity</h3>
    <div class="chart-container">
      <svg :width="chartWidth" :height="chartHeight" class="chart-svg">
        <!-- Grid lines -->
        <g class="grid">
          <line 
            v-for="i in 4" 
            :key="`grid-${i}`"
            :x1="padding" 
            :y1="padding + (i - 1) * (chartHeight - 2 * padding) / 3"
            :x2="chartWidth - padding" 
            :y2="padding + (i - 1) * (chartHeight - 2 * padding) / 3"
            stroke="var(--color-dashboard-chart-grid)"
            stroke-width="1"
            opacity="0.3"
          />
        </g>
        
        <!-- Bars for notes -->
        <g class="notes-bars">
          <rect
            v-for="(day, index) in weekData"
            :key="`notes-${index}`"
            :x="padding + index * barWidth + barWidth * 0.1"
            :y="chartHeight - padding - (day.notes / maxValue) * (chartHeight - 2 * padding)"
            :width="barWidth * 0.35"
            :height="(day.notes / maxValue) * (chartHeight - 2 * padding)"
            :fill="notesColor"
            rx="2"
          />
        </g>
        
        <!-- Bars for sessions -->
        <g class="sessions-bars">
          <rect
            v-for="(day, index) in weekData"
            :key="`sessions-${index}`"
            :x="padding + index * barWidth + barWidth * 0.55"
            :y="chartHeight - padding - (day.sessions / maxValue) * (chartHeight - 2 * padding)"
            :width="barWidth * 0.35"
            :height="(day.sessions / maxValue) * (chartHeight - 2 * padding)"
            :fill="sessionsColor"
            rx="2"
          />
        </g>
        
        <!-- Day labels -->
        <g class="day-labels">
          <text
            v-for="(day, index) in weekData"
            :key="`label-${index}`"
            :x="padding + index * barWidth + barWidth / 2"
            :y="chartHeight - 5"
            text-anchor="middle"
            class="day-label"
          >
            {{ day.dayLabel }}
          </text>
        </g>
      </svg>
      
      <!-- Legend -->
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color" :style="{ backgroundColor: notesColor }"></div>
          <span>Notes</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" :style="{ backgroundColor: sessionsColor }"></div>
          <span>Sessions</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useElectronAPI } from '../../../useElectronAPI'

interface DayData {
  date: string
  dayLabel: string
  notes: number
  sessions: number
}

const db = useElectronAPI()

const weekData = ref<DayData[]>([])
const chartWidth = 280
const chartHeight = 120
const padding = 20

const barWidth = computed(() => (chartWidth - 2 * padding) / 7)
const maxValue = computed(() => {
  const allValues = weekData.value.flatMap(day => [day.notes, day.sessions])
  return Math.max(...allValues, 5) // Minimum scale of 5
})

// Theme-aware colors
const notesColor = computed(() => {
  if (typeof window === 'undefined') return '#4A4A4A'
  return getComputedStyle(document.documentElement)
    .getPropertyValue('--color-dashboard-chart-primary')
    .trim() || '#4A4A4A'
})

const sessionsColor = computed(() => {
  if (typeof window === 'undefined') return '#666666'
  return getComputedStyle(document.documentElement)
    .getPropertyValue('--color-dashboard-chart-secondary')
    .trim() || '#666666'
})

const loadWeeklyData = async () => {
  try {
    const today = new Date()
    const weekDays: DayData[] = []
    
    // Generate last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      weekDays.push({
        date: dateStr,
        dayLabel: date.toLocaleDateString('en-US', { weekday: 'short' }),
        notes: 0,
        sessions: 0
      })
    }
    
    // Load notes data
    const notes = await db.notes.getAll()
    notes.forEach(note => {
      const noteDate = new Date(note.created_at || '').toISOString().split('T')[0]
      const dayIndex = weekDays.findIndex(day => day.date === noteDate)
      if (dayIndex !== -1) {
        weekDays[dayIndex].notes++
      }
    })
    
    // Load sessions data
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const sessions = await db.timer.getSessionsByDateRange(
      sevenDaysAgo.toISOString().split('T')[0],
      today.toISOString().split('T')[0]
    )
    
    sessions.filter(s => s.is_completed === 1).forEach(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      const dayIndex = weekDays.findIndex(day => day.date === sessionDate)
      if (dayIndex !== -1) {
        weekDays[dayIndex].sessions++
      }
    })
    
    weekData.value = weekDays
  } catch (error) {
    console.error('Failed to load weekly activity data:', error)
  }
}

onMounted(() => {
  loadWeeklyData()
})

defineExpose({
  refresh: loadWeeklyData
})
</script>

<style scoped>
.weekly-activity-chart {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 12px;
  padding: 20px;
  height: 200px;
  transition: all 0.2s ease;
}

.weekly-activity-chart:hover {
  border-color: var(--color-border-hover);
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
  text-align: center;
  letter-spacing: -0.025em;
}

.chart-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
}

.chart-svg {
  flex: 1;
}

.day-label {
  font-size: 11px;
  fill: var(--color-text-secondary);
  font-family: system-ui, -apple-system, sans-serif;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--color-border-secondary);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  border: 1px solid var(--color-border-secondary);
}

@media (max-width: 768px) {
  .weekly-activity-chart {
    padding: 16px;
    height: 180px;
    border-radius: 10px;
  }

  .chart-title {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .chart-container {
    height: calc(100% - 32px);
  }

  .chart-legend {
    gap: 16px;
    margin-top: 10px;
  }

  .legend-item {
    font-size: 0.75rem;
    gap: 6px;
  }

  .legend-color {
    width: 10px;
    height: 10px;
    border-radius: 2px;
  }
}

@media (max-width: 480px) {
  .weekly-activity-chart {
    padding: 12px;
    height: 160px;
  }

  .chart-title {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }
}
</style>
