{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "compounds": [
    {
      "name": "Debug App",
      "preLaunchTask": "Before Debug",
      "configurations": [
        "Debug Main Process",
        "Debug Renderer Process"
      ],
      "presentation": {
        "hidden": false,
        "group": "",
        "order": 1
      },
      "stopAll": true
    }
  ],
  "configurations": [
    {
      "name": "Debug Main Process",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron",
      "windows": {
        "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron.cmd"
      },
      "runtimeArgs": [
        "--remote-debugging-port=9229",
        "."
      ],
      "envFile": "${workspaceFolder}/.vscode/.debug.env",
      "console": "integratedTerminal"
    },
    {
      "name": "Debug Renderer Process",
      "port": 9229,
      "request": "attach",
      "type": "chrome",
      "timeout": 60000,
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceRoot}/node_modules/**",
        "${workspaceRoot}/dist-electron/**",
        // Skip files in host(VITE_DEV_SERVER_URL)
        "http://127.0.0.1:3344/**"
      ]
    },
  ]
}
