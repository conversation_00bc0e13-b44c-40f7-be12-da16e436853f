<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-overlay">
      <div class="delete-modal">
        <div class="close-icon" @click="$emit('close')">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>

        <div class="modal-header">
          <h1 class="modal-title">{{ mode === 'edit' ? 'Edit Link' : 'Add Link' }}</h1>
          <div class="modal-subtitle">
            <div v-if="mode === 'new' && !hasSelection" class="modal-input-group">
              <label for="linkText">Link Text</label>
              <input
                id="linkText"
                v-model="localLinkText"
                type="text"
                placeholder="Text to display"
                @keyup.enter="$emit('confirm', { url: localLinkUrl, text: localLinkText })"
                ref="linkTextInput"
              >
            </div>
            <div class="modal-input-group">
              <label for="linkUrl">Link URL</label>
              <input
                id="linkUrl"
                v-model="localLinkUrl"
                type="text"
                placeholder="https://"
                @keyup.enter="$emit('confirm', { url: localLinkUrl, text: localLinkText })"
                ref="urlInput"
              >
            </div>
          </div>
        </div>

        <div class="divider"></div>

        <div class="modal-footer">
          <button class="btn btn-cancel" @click="$emit('cancel')">
            Cancel
          </button>
          <button class="btn btn-primary" @click="$emit('confirm', { url: localLinkUrl, text: localLinkText })">
            {{ mode === 'edit' ? 'Update' : 'Add' }}
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue';

export default defineComponent({
  name: 'InsertLinkModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String as () => 'edit' | 'new',
      default: 'new'
    },
    linkUrl: {
      type: String,
      default: ''
    },
    linkText: {
      type: String,
      default: ''
    },
    hasSelection: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'confirm', 'cancel'],
  setup(props, { emit }) {
    // Local reactive copies of props for v-model
    const localLinkUrl = ref(props.linkUrl);
    const localLinkText = ref(props.linkText);
    
    // Template refs
    const urlInput = ref<HTMLInputElement | null>(null);
    const linkTextInput = ref<HTMLInputElement | null>(null);

    // Watch for prop changes to update local values
    watch(() => props.linkUrl, (newValue) => {
      localLinkUrl.value = newValue;
    });

    watch(() => props.linkText, (newValue) => {
      localLinkText.value = newValue;
    });

    // Focus management when modal becomes visible
    watch(() => props.visible, async (isVisible) => {
      if (isVisible) {
        await nextTick();
        
        // Focus appropriate input based on mode and selection
        if (props.mode === 'new' && !props.hasSelection) {
          // Focus link text input for new links without selection
          linkTextInput.value?.focus();
        } else {
          // Focus URL input for edit mode or when there's a selection
          urlInput.value?.focus();
        }
      }
    });

    return {
      localLinkUrl,
      localLinkText,
      urlInput,
      linkTextInput
    };
  }
});
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  isolation: isolate;
  padding: 20px;
  box-sizing: border-box;
}

/* Main Modal Container */
.delete-modal {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  width: 520px;
  max-width: 100%;
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  position: relative;
}

/* Close Button */
.close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-icon:hover {
  background-color: var(--color-nav-item-hover);
}

.close-icon img {
  width: 24px;
  height: 24px;
}

/* Modal Header */
.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

/* Divider */
.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  font-family: 'Montserrat', sans-serif;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-cancel:hover {
  background-color: var(--color-btn-secondary-hover);
  border-color: var(--color-border-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

/* Input Groups */
.modal-input-group {
  margin-bottom: 16px;
}

.modal-input-group:last-child {
  margin-bottom: 0;
}

.modal-input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.modal-input-group input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid var(--color-input-border);
  font-size: 15px;
  box-sizing: border-box;
  font-family: inherit;
  transition: all 0.2s ease;
  background-color: var(--color-input-bg);
  color: var(--color-input-text);
}

.modal-input-group input:focus {
  outline: none;
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.2);
}

.modal-input-group input::placeholder {
  color: var(--color-input-placeholder);
}
</style>
