# Database Transaction Implementation for Sync Manifest Generation

## Problem Analysis

The `generateManifestFromDatabase` method in `manifest-manager.ts` currently runs three separate queries:
1. Books query
2. Folders query  
3. Notes query

This creates potential race conditions where user actions (creating/updating/deleting items) between queries could result in an inconsistent manifest state.

## Solution: Transaction-Wrapped Manifest Generation

### Current Transaction Patterns in Codebase

After analyzing the codebase, I found several transaction implementation patterns:

1. **Settings API Pattern** (`settings-api.ts`):
```typescript
// Begin transaction
await new Promise<void>((resolve, reject) => {
    db.run('BEGIN TRANSACTION', (err) => (err ? reject(err) : resolve()));
});

// Execute operations...

// Commit transaction
await new Promise<void>((resolve, reject) => {
    db.run('COMMIT', (err) => (err ? reject(err) : resolve()));
});
```

2. **Serialize Pattern** (`database-api.ts`):
```typescript
const db: Database = getDatabase();
return new Promise((resolve, reject) => {
    db.serialize(async () => {
        try {
            await dbRun('BEGIN TRANSACTION');
            // Execute operations...
            await dbRun('COMMIT');
            resolve(result);
        } catch (error) {
            await dbRun('ROLLBACK');
            reject(error);
        }
    });
});
```

## Recommended Implementation

### Option 1: Transaction Helper Function (Recommended)

Create a reusable transaction helper in `database-api.ts`:

```typescript
// Add to database-api.ts
export const withTransaction = async <T>(
    operation: (db: Database) => Promise<T>
): Promise<T> => {
    const db: Database = getDatabase();
    
    return new Promise((resolve, reject) => {
        db.serialize(async () => {
            try {
                await dbRun('BEGIN TRANSACTION');
                const result = await operation(db);
                await dbRun('COMMIT');
                resolve(result);
            } catch (error) {
                try {
                    await dbRun('ROLLBACK');
                } catch (rollbackError) {
                    console.error('Error rolling back transaction:', rollbackError);
                }
                reject(error);
            }
        });
    });
};
```

### Option 2: Direct Implementation in Manifest Manager

Modify the `generateManifestFromDatabase` method to use transactions directly.

## Files to Modify

1. **`electron/main/database/database-api.ts`** - Add transaction helper
2. **`electron/main/api/sync-logic/manifest-manager.ts`** - Update manifest generation method

## Benefits

1. **Data Consistency**: All three queries see the same database state
2. **Isolation**: Read operations are isolated from concurrent writes
3. **Reliability**: Prevents partial/inconsistent manifest generation
4. **Reusability**: Transaction helper can be used for other sync operations

## Implementation Priority

**High Priority** - This directly affects sync reliability and data integrity. Should be implemented before deploying sync functionality to production.

## Testing Considerations

1. Test manifest generation under concurrent database operations
2. Verify transaction rollback on database errors
3. Test performance impact of read transactions
4. Ensure proper cleanup on transaction failures

## SQLite Transaction Support

✅ **Confirmed**: The codebase uses sqlite3 with full transaction support
✅ **Pattern Established**: Multiple existing examples of transaction usage
✅ **Infrastructure Ready**: Database connection and helper functions already support transactions