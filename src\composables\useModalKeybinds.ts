// Modal keybinds composable
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useModalKeybinds() {
  const isActive = ref(false)

  // Modal functions (to be passed from modal components)
  let confirmModal: () => void = () => console.log('✅ Confirm modal')
  let cancelModal: () => void = () => console.log('❌ Cancel modal')
  let navigateModalFields: (direction: 'forward' | 'backward') => void = (direction) => console.log(`🧭 Navigate modal fields ${direction}`)

  // Register modal-specific keybinds
  const registerModalKeybinds = () => {
    console.log('🔲 Registering modal keybinds...')

    // Modal confirmation
    globalKeybindManager.register({
      key: 'enter',
      handler: (context) => {
        if (context.modalOpen && !context.editorFocused) {
          // Only trigger if not in a textarea or multi-line input
          const activeElement = document.activeElement as HTMLElement
          if (activeElement && (activeElement.tagName === 'TEXTAREA' || 
              (activeElement.tagName === 'INPUT' && activeElement.getAttribute('type') === 'text'))) {
            return // Let the native behavior handle it
          }
          confirmModal()
        }
      },
      description: 'Confirm modal action',
      category: KeybindCategory.MODAL,
      priority: 'high',
      enabled: true,
      context: { modalOpen: true, editorFocused: false }
    })

    // Modal cancellation (handled by global escape, but we can add specific logic)
    globalKeybindManager.register({
      key: 'escape',
      handler: (context) => {
        if (context.modalOpen) {
          cancelModal()
        }
      },
      description: 'Cancel/close modal',
      category: KeybindCategory.MODAL,
      priority: 'high',
      enabled: true,
      context: { modalOpen: true }
    })

    // Tab navigation in modals
    globalKeybindManager.register({
      key: 'tab',
      handler: (context, event) => {
        if (context.modalOpen) {
          // Let the browser handle tab navigation, but we can add custom logic if needed
          navigateModalFields('forward')
          // Don't prevent default for tab - let it work naturally
        }
      },
      description: 'Navigate to next field',
      category: KeybindCategory.MODAL,
      priority: 'medium',
      enabled: true,
      context: { modalOpen: true }
    })

    globalKeybindManager.register({
      key: 'shift+tab',
      handler: (context, event) => {
        if (context.modalOpen) {
          // Let the browser handle shift+tab navigation, but we can add custom logic if needed
          navigateModalFields('backward')
          // Don't prevent default for shift+tab - let it work naturally
        }
      },
      description: 'Navigate to previous field',
      category: KeybindCategory.MODAL,
      priority: 'medium',
      enabled: true,
      context: { modalOpen: true }
    })

    console.log('✅ Modal keybinds registered')
  }

  // Unregister modal keybinds
  const unregisterModalKeybinds = () => {
    console.log('🗑️ Unregistering modal keybinds...')
    
    // Unregister all modal keybinds to prevent memory leaks
    globalKeybindManager.unregister('enter')
    globalKeybindManager.unregister('escape')
    globalKeybindManager.unregister('tab')
    globalKeybindManager.unregister('shift+tab')
  }

  // Activate modal keybinds
  const activate = () => {
    if (!isActive.value) {
      registerModalKeybinds()
      isActive.value = true
      console.log('🟢 Modal keybinds activated')
    }
  }

  // Deactivate modal keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterModalKeybinds()
      isActive.value = false
      console.log('🔴 Modal keybinds deactivated')
    }
  }

  // Setup functions (to be called from modal components)
  const setupModalFunctions = (functions: {
    confirmModal?: () => void
    cancelModal?: () => void
    navigateModalFields?: (direction: 'forward' | 'backward') => void
  }) => {
    if (functions.confirmModal) confirmModal = functions.confirmModal
    if (functions.cancelModal) cancelModal = functions.cancelModal
    if (functions.navigateModalFields) navigateModalFields = functions.navigateModalFields
    
    console.log('🔧 Modal functions configured')
  }

  // Focus management utilities
  const focusFirstInput = () => {
    const modal = document.querySelector('.modal-overlay')
    if (modal) {
      const firstInput = modal.querySelector('input, textarea, select, button') as HTMLElement
      if (firstInput) {
        firstInput.focus()
      }
    }
  }

  const focusLastInput = () => {
    const modal = document.querySelector('.modal-overlay')
    if (modal) {
      const inputs = modal.querySelectorAll('input, textarea, select, button')
      const lastInput = inputs[inputs.length - 1] as HTMLElement
      if (lastInput) {
        lastInput.focus()
      }
    }
  }

  const getModalInputs = () => {
    const modal = document.querySelector('.modal-overlay')
    if (modal) {
      return Array.from(modal.querySelectorAll('input, textarea, select, button')) as HTMLElement[]
    }
    return []
  }

  // Auto-activate when mounted
  onMounted(() => {
    activate()
  })

  // Cleanup on unmount
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupModalFunctions,
    focusFirstInput,
    focusLastInput,
    getModalInputs,
    registerModalKeybinds,
    unregisterModalKeybinds
  }
}