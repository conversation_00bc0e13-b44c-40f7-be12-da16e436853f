<template>
  <Teleport to="body">
    <div class="export-modal-overlay">
    <div class="export-modal">
      <div class="close-icon" @click="$emit('close')">
        <img src="/icons/close-icon.svg" alt="Close" />
      </div>

      <div class="modal-header">
        <h1 class="modal-title">Export Folder</h1>
        <p class="modal-subtitle">Export "{{ folderName }}" and its contents</p>
      </div>

      <div class="divider"></div>

      <div class="modal-content">
        <div class="file-type-header">
          <img src="/icons/file-type-icon.svg" class="file-icon" alt="File" />
          <span class="file-type-label">Export Format</span>
        </div>

        <div class="file-type-separator"></div>

        <div class="file-options">
          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'pdf' }"
            @click="selectFormat('pdf')"
          >
            <div class="file-icon-container">
              <img src="/icons/pdf-icon.svg" alt="PDF" class="format-icon" />
              <span>PDF</span>
            </div>
          </div>

          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'md' }"
            @click="selectFormat('md')"
          >
            <div class="file-icon-container">
              <img src="/icons/md-icon.svg" alt="Markdown" class="format-icon" />
              <span>Markdown</span>
            </div>
          </div>

          <div
            class="file-option-container"
            :class="{ 'selected': selectedFormat === 'noti' }"
            @click="selectFormat('noti')"
          >
            <div class="file-icon-container">
              <img src="/icons/noti-icon.svg" alt="NOTI" class="format-icon" />
              <span>Noti</span>
            </div>
          </div>
        </div>

        <div class="export-options">
          <div class="export-option">
            <input type="checkbox" id="includeSubfolders" v-model="includeSubfolders" />
            <label for="includeSubfolders">Include subfolders</label>
          </div>

          <div class="export-option">
            <input type="checkbox" id="includeNotes" v-model="includeNotes" />
            <label for="includeNotes">Include notes</label>
          </div>
        </div>
      </div>

      <div class="divider"></div>

      <div class="modal-footer">
        <button class="btn btn-cancel" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn btn-export" @click="exportFolder" :disabled="!selectedFormat || isExporting">
          <span v-if="isExporting" class="loading-spinner"></span>
          <img v-if="!isExporting" src="/icons/export-icon.svg" class="export-icon" alt="Export" />
          {{ isExporting ? 'Preparing export...' : 'Export' }}
        </button>
      </div>
    </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useElectronAPI } from '../../useElectronAPI';

export default defineComponent({
  name: 'ExportFolderModal',
  props: {
    folderId: {
      type: Number,
      required: true
    },
    folderName: {
      type: String,
      default: 'Untitled Folder'
    }
  },
  emits: ['close', 'export-start', 'export-complete'],
  setup(props, { emit }) {
    const db = useElectronAPI();
    const selectedFormat = ref('');
    const includeSubfolders = ref(true);
    const includeNotes = ref(true);
    const isExporting = ref(false);

    const selectFormat = (format: string) => {
      selectedFormat.value = format;
    };

    const exportFolder = async () => {
      if (isExporting.value) return;

      isExporting.value = true;

      // Emit export start event to show progress overlay
      emit('export-start', {
        format: selectedFormat.value,
        itemCount: 1,
        itemType: 'Folder'
      });

      try {
        if (!selectedFormat.value) {
          throw new Error('No export format selected');
        }

        console.log(`Exporting folder ${props.folderId} as ${selectedFormat.value}`);
        console.log(`Include subfolders: ${includeSubfolders.value}`);
        console.log(`Include notes: ${includeNotes.value}`);

        // Create the folder item for export
        const folderItem = {
          id: props.folderId,
          type: 'folder' as const,
          name: props.folderName
        };

        // Set up export options
        const options = {
          includeSubfolders: includeSubfolders.value,
          includeNotes: includeNotes.value
        };

        // Call the backend API to perform the export
        const result = await db.notes.exportMultiple([folderItem], selectedFormat.value, options);

        console.log('Export result:', result);

        emit('export-complete', {
          success: true,
          format: selectedFormat.value,
          folderId: props.folderId,
          message: result
        });
        emit('close');

      } catch (error: any) {
        console.error('Export failed:', error);
        emit('export-complete', {
          success: false,
          error: error.message || 'Unknown error during export'
        });
        emit('close');
      } finally {
        isExporting.value = false;
      }
    };

    return {
      selectedFormat,
      includeSubfolders,
      includeNotes,
      isExporting,
      selectFormat,
      exportFolder
    };
  }
});
</script>

<style scoped>
.export-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.export-modal {
  background-color: var(--color-modal-bg);
  border-radius: 12px;
  width: 500px;
  max-width: 95%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--color-card-shadow);
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.close-icon:hover {
  opacity: 1;
}

.close-icon img {
  width: 16px;
  height: 16px;
}

.modal-header {
  padding: 24px 32px;
  text-align: left;
}

.modal-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 8px;
  font-family: 'Montserrat', sans-serif;
}

.modal-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--color-text-secondary);
  font-family: 'Montserrat', sans-serif;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-content {
  padding: 24px 32px;
}

.file-type-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.file-type-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  font-family: 'Montserrat', sans-serif;
}

.file-type-separator {
  width: 100%;
  height: 1px;
  background-color: var(--color-border-primary);
  margin-bottom: 20px;
}

.file-options {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.file-option-container {
  flex: 1;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--color-card-bg);
}

.file-option-container:hover {
  border-color: var(--color-border-hover);
  background-color: var(--color-nav-item-hover);
}

.file-option-container.selected {
  border-color: var(--color-primary);
  background-color: var(--color-nav-item-active);
}

.file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.format-icon {
  width: 48px;
  height: 48px;
  opacity: 0.8;
}

.file-icon-container span {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-family: 'Montserrat', sans-serif;
  text-align: center;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.export-option {
  display: flex;
  align-items: center;
  gap: 10px;
}

.export-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--color-primary);
}

.export-option label {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-family: 'Montserrat', sans-serif;
}

.modal-footer {
  padding: 16px 32px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Montserrat', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  border: 1px solid var(--color-btn-secondary-border);
  color: var(--color-btn-secondary-text);
}

.btn-cancel:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-export {
  background-color: var(--color-btn-primary-bg);
  border: none;
  color: var(--color-btn-primary-text);
}

.btn-export:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
}

.btn-export:disabled {
  background-color: var(--color-btn-primary-bg);
  opacity: 0.5;
  cursor: not-allowed;
}

.export-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-btn-primary-text);
  border-radius: 50%;
  border-top-color: transparent;
  opacity: 0.3;
  animation: spin 1s ease-in-out infinite;
  margin-right: 6px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
