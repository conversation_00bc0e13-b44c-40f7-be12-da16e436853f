/* Animation utilities for folders expansion/collapse */
@keyframes expandFolderContents {
  0% {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  70% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 1;
    max-height: 2000px; /* Large value to accommodate various content sizes */
    transform: translateY(0);
  }
}

@keyframes collapseFolderContents {
  0% {
    opacity: 1;
    max-height: 2000px;
    transform: translateY(0);
  }
  30% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
}

@keyframes rotateArrow {
  from {
    transform: rotate(-90deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes rotateArrowBack {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-90deg);
  }
}

.folder-children-enter-active {
  animation: expandFolderContents 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  overflow: hidden;
  transform-origin: top;
  will-change: opacity, max-height, transform;
}

.folder-children-leave-active {
  animation: collapseFolderContents 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  overflow: hidden;
  transform-origin: top;
  will-change: opacity, max-height, transform;
}

.arrow-rotate-enter-active {
  animation: rotateArrow 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  will-change: transform;
}

.arrow-rotate-leave-active {
  animation: rotateArrowBack 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
  will-change: transform;
}
