# Frontend Bug Fixes Batch 1

## Files Modified
- `src/stores/settingsStore.ts` (already fixed)
- `src/components/notes/NoteEditor.vue`
- `src/components/settings/DiscordSettings.vue`

## Bugs Fixed

### 1. Error Swallowing in Settings Store (medium-4) ✅
**Status**: Already fixed in the codebase

**Problem**: 
- `saveSettingToDatabase` and `saveAllSettingsToDatabase` were catching errors but not re-throwing them
- This made settings appear saved in the UI when database persistence actually failed

**Solution**:
- Both functions already have `throw error` after `console.error` in their catch blocks
- This ensures errors propagate up to calling code for proper handling

### 2. Duplicate Event Listeners in NoteEditor (medium-5) ✅
**Status**: Fixed

**Problem**:
- Editor watch was calling `activateKeybinds()` without deactivating previous listeners
- This caused memory leaks as event listeners accumulated with each editor change

**Solution**:
```javascript
// Added deactivateKeybinds() call before activating new ones
watch(editor, (newEditor) => {
  if (newEditor) {
    // Deactivate previous keybinds to prevent duplicate event listeners
    deactivateKeybinds();
    
    // Setup editor with keybinds
    setupEditor(newEditor);
    // ... rest of the setup
  }
});
```

### 3. Discord Settings Race Condition (medium-6) ✅
**Status**: Fixed

**Problem**:
- UI was updating immediately while async Discord operations could fail
- This showed Discord as "enabled" in the UI while it was actually disabled

**Solution**:
```javascript
async function handleToggleEnabled(event: Event) {
  const target = event.target as HTMLInputElement
  const enabled = target.checked
  
  // Store the previous value for rollback
  const previousEnabled = settingsStore.settings.discordRichPresenceEnabled

  try {
    // Perform async operations first
    if (enabled) {
      await initializeDiscord()
    } else {
      await db.discord.setEnabled(false)
      await db.discord.clearActivity()
    }
    await checkDiscordStatus()
    
    // Only update settings after successful async operations
    await settingsStore.updateSetting('discordRichPresenceEnabled', enabled)
  } catch (error) {
    console.error('Failed to toggle Discord:', error)
    
    // Rollback the checkbox state on failure
    target.checked = previousEnabled
  }
}
```

## Testing Recommendations

1. **Settings Store**: 
   - Test saving settings with a simulated database error
   - Verify that errors are properly caught and handled by calling code

2. **NoteEditor**:
   - Monitor browser DevTools memory usage
   - Switch between multiple notes and verify event listener count doesn't increase
   - Check that keybindings work correctly after switching notes

3. **Discord Settings**:
   - Test toggling Discord with Discord app not running
   - Verify checkbox state correctly reflects actual Discord connection status
   - Test rapid toggling to ensure no race conditions

## Impact
- Improved error handling and reliability in settings persistence
- Fixed memory leak in note editor
- Consistent UI state with actual Discord connection status