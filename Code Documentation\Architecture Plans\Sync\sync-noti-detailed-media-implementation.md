# Sync System .noti Format - Detailed Media Implementation

## Overview

This document details the specific implementation of .noti format for sync operations with focus on media handling, simplified content structure, and database integration.

## Simplified .noti Format for Sync

### Structure for Sync Operations
```json
{
  "version": "1.0",
  "type": "noti-note",
  "schema": "https://noti.app/schemas/note/v1.0",
  "metadata": {
    "id": 123,
    "title": "Note Title",
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T14:30:00.000Z",
    "last_viewed_at": "2024-01-15T14:30:00.000Z",
    "type": "text",
    "color": "#ff0000",
    "folder_id": 456,
    "book_id": 789
  },
  "content": {
    "html": "<p>Full rich HTML content from TipTap editor</p><img src=\"noti-media://embedded_media_1\">",
    "markdown": "Note title preview text...", // First ~50 words for list display
    "plain_text": "Note title preview text for search"
  },
  "media": [
    {
      "id": "embedded_media_1",
      "file_name": "screenshot.png",
      "file_type": "image/png",
      "file_size": 102400,
      "original_path": "noti-media://path/to/original/file",
      "embedded": true,
      "data": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    }
  ]
}
```

### Key Simplifications
1. **HTML Content**: Full rich content with embedded media references
2. **Markdown Content**: Only first ~50 words for preview in notes list
3. **Plain Text**: Simple text extraction for search functionality
4. **No Statistics**: Remove word count, character count, reading time
5. **No Integrity Hash**: Simplify format for sync operations
6. **No Export Metadata**: Remove app version tracking

## Media Implementation Deep Dive

### 1. Export Process: Database → .noti File

#### Step 1: Extract Note Content and Media
```typescript
// In unified-sync-engine.ts exportNote method
private async exportNote(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
  const note = await getNoteById(item.id);
  
  // Get all media files associated with this note
  const mediaFiles = await mediaApi.getMediaFilesByNoteId(note.id);
  
  // Process each media file
  const embeddedMedia: EmbeddedMedia[] = [];
  let processedHtmlContent = note.html_content || '';
  
  for (const mediaFile of mediaFiles) {
    // Read the actual file from disk
    const fileBuffer = await fs.readFile(mediaFile.file_path);
    const base64Data = fileBuffer.toString('base64');
    
    // Create embedded media entry
    const embeddedId = `embedded_media_${mediaFile.id}`;
    embeddedMedia.push({
      id: embeddedId,
      file_name: mediaFile.file_name,
      file_type: mediaFile.file_type,
      file_size: mediaFile.file_size,
      original_path: mediaFile.file_path,
      embedded: true,
      data: base64Data
    });
    
    // Replace noti-media URLs in HTML with embedded references
    const mediaUrl = filePathToMediaUrl(mediaFile.file_path);
    processedHtmlContent = processedHtmlContent.replace(
      new RegExp(escapeRegExp(mediaUrl), 'g'),
      `noti-media://${embeddedId}`
    );
  }
  
  // Create simplified markdown preview (first 50 words)
  const markdownPreview = this.createMarkdownPreview(note.content || note.title);
  
  // Create .noti structure
  const notiData = {
    version: "1.0",
    type: "noti-note",
    schema: "https://noti.app/schemas/note/v1.0",
    metadata: {
      id: note.id,
      title: note.title,
      created_at: note.created_at,
      updated_at: note.updated_at,
      last_viewed_at: note.last_viewed_at,
      type: note.type || 'text',
      color: note.color,
      folder_id: note.folder_id,
      book_id: note.book_id
    },
    content: {
      html: processedHtmlContent,
      markdown: markdownPreview,
      plain_text: this.extractPlainText(processedHtmlContent)
    },
    media: embeddedMedia
  };
  
  // Write .noti file
  const notiPath = path.join(directory, folderItem.path, `${sanitizeNoteTitle(note.title)}.noti`);
  await fileOperations.writeFileAtomic(notiPath, JSON.stringify(notiData, null, 2));
}

private createMarkdownPreview(content: string): string {
  // Extract first 50 words for preview
  const words = content.split(/\s+/).slice(0, 50);
  return words.join(' ') + (words.length >= 50 ? '...' : '');
}

private extractPlainText(htmlContent: string): string {
  // Simple HTML tag removal for plain text
  return htmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
}
```

#### Step 2: Media URL Replacement Strategy
```typescript
// Replace various noti-media URL formats with embedded references
private replaceMediaUrls(htmlContent: string, mediaFile: any, embeddedId: string): string {
  const patterns = [
    // Full file path
    `noti-media://${mediaFile.file_path.replace(/\\/g, '/')}`,
    // Just filename
    `noti-media://${mediaFile.file_name}`,
    // Encoded paths
    `noti-media://${encodeURIComponent(mediaFile.file_path)}`,
    // Any path ending with the filename
    new RegExp(`noti-media://.*?${escapeRegExp(mediaFile.file_name)}`, 'g')
  ];
  
  let processedContent = htmlContent;
  for (const pattern of patterns) {
    if (typeof pattern === 'string') {
      processedContent = processedContent.replace(new RegExp(escapeRegExp(pattern), 'g'), `noti-media://${embeddedId}`);
    } else {
      processedContent = processedContent.replace(pattern, `noti-media://${embeddedId}`);
    }
  }
  
  return processedContent;
}
```

### 2. Import Process: .noti File → Database

#### Step 1: Parse .noti File and Extract Media
```typescript
// In unified-sync-engine.ts importNote method
private async importNote(item: ManifestItem, directory: string): Promise<void> {
  const notiPath = path.join(directory, item.path);
  
  // Read and parse .noti file
  const notiContent = await fileOperations.readFileAtomic(notiPath);
  const notiData = JSON.parse(notiContent);
  
  // Extract content and metadata
  const htmlContent = notiData.content.html;
  const markdownContent = notiData.content.markdown; // This is just preview text
  const metadata = notiData.metadata || {};
  
  // Determine relationships
  let folderId: number | null = null;
  let bookId: number | null = null;
  
  if (item.relationships) {
    if (item.relationships.bookId) {
      const localBookId = this.importIdMapping.get(item.relationships.bookId);
      if (localBookId) bookId = localBookId;
    }
    if (item.relationships.folderId) {
      const localFolderId = this.importIdMapping.get(item.relationships.folderId);
      if (localFolderId) folderId = localFolderId;
    }
  }

  await withTransaction(async () => {
    const noteTitle = item.name || path.basename(notiPath, '.noti');
    
    // Check if note exists
    let existingNote: Note | null = null;
    const mappedId = this.importIdMapping.get(item.id);
    if (mappedId) {
      existingNote = await this.noteExistsById(mappedId);
    }
    
    if (!existingNote) {
      existingNote = await this.noteExists(noteTitle, folderId, bookId);
    }
    
    let note: Note;
    if (existingNote) {
      // Update existing note with imported content
      await updateNote(existingNote.id!, {
        title: noteTitle,
        content: markdownContent, // Store preview text as markdown content
        html_content: null, // Will be set after media restoration
        folder_id: folderId,
        book_id: bookId,
        type: metadata.type || existingNote.type,
        color: metadata.color || existingNote.color,
        last_viewed_at: metadata.last_viewed_at || existingNote.last_viewed_at
      });
      note = existingNote;
    } else {
      // Create new note
      note = await createNote({
        title: noteTitle,
        content: markdownContent, // Store preview text as markdown content
        html_content: null, // Will be set after media restoration
        folder_id: folderId,
        book_id: bookId,
        type: metadata.type || 'text',
        color: metadata.color || null,
        last_viewed_at: metadata.last_viewed_at
      });
    }

    // Process embedded media
    if (notiData.media && notiData.media.length > 0) {
      const restoredHtmlContent = await this.restoreEmbeddedMedia(
        htmlContent, 
        notiData.media, 
        note.id!
      );
      
      // Update note with restored HTML content
      await updateNote(note.id!, {
        html_content: restoredHtmlContent
      });
    } else {
      // No media, just set the HTML content
      await updateNote(note.id!, {
        html_content: htmlContent
      });
    }

    // Track mapping
    this.importIdMapping.set(item.id, note.id!);
  });
}
```

#### Step 2: Media Restoration Process
```typescript
private async restoreEmbeddedMedia(
  htmlContent: string, 
  embeddedMedia: EmbeddedMedia[], 
  noteId: number
): Promise<string> {
  let restoredHtmlContent = htmlContent;
  
  for (const media of embeddedMedia) {
    try {
      // Convert base64 back to buffer
      const fileBuffer = Buffer.from(media.data, 'base64');
      
      // Save media file to media storage and database
      const savedMediaFile = await saveMediaFile(
        noteId,
        fileBuffer,
        media.file_name,
        media.file_type,
        null, // book_id
        false // is_cover
      );
      
      // Create the new noti-media URL for the restored file
      const newMediaUrl = filePathToMediaUrl(savedMediaFile.file_path);
      
      // Replace embedded reference with actual media URL
      const embeddedReference = `noti-media://${media.id}`;
      restoredHtmlContent = restoredHtmlContent.replace(
        new RegExp(escapeRegExp(embeddedReference), 'g'),
        newMediaUrl
      );
      
      console.log(`Restored media: ${media.file_name} -> ${newMediaUrl}`);
      
    } catch (error) {
      console.error(`Failed to restore media ${media.file_name}:`, error);
      // Remove broken media references from HTML
      const embeddedReference = `noti-media://${media.id}`;
      restoredHtmlContent = restoredHtmlContent.replace(
        new RegExp(`<img[^>]*src="${escapeRegExp(embeddedReference)}"[^>]*>`, 'g'),
        `<span style="color: red;">[Missing media: ${media.file_name}]</span>`
      );
    }
  }
  
  return restoredHtmlContent;
}
```

### 3. Database Integration

#### Media Files Table Integration
```sql
-- The media_files table structure (already exists)
CREATE TABLE media_files (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  note_id INTEGER,
  book_id INTEGER,
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  is_cover BOOLEAN DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
  FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

#### Media Restoration Flow
1. **Parse .noti file** → Extract embedded media array
2. **For each media item**:
   - Decode base64 data to buffer
   - Generate unique filename (timestamp + original name)
   - Save buffer to media storage directory
   - Insert record into `media_files` table with `note_id`
   - Get new `noti-media://` URL from saved file path
   - Replace embedded reference in HTML with new URL
3. **Update note** → Save restored HTML content to database

### 4. File Operations Updates

#### Updated writeNote method
```typescript
// In file-operations.ts
async writeNote(notePath: string, notiData: any): Promise<void> {
  try {
    const validatedPath = this.syncDirectory 
      ? this.validatePath(notePath, this.syncDirectory)
      : path.normalize(notePath);
    
    const dir = path.dirname(validatedPath);
    
    // Ensure directory exists
    await this.ensurePath(dir);
    
    // Write .noti file as JSON
    await this.writeFileAtomic(validatedPath, JSON.stringify(notiData, null, 2));
    
  } catch (error) {
    throw new SyncError(
      ErrorCode.FILE_WRITE_ERROR,
      `Failed to write .noti file at ${notePath}: ${(error as Error).message}`
    );
  }
}
```

#### Updated readNote method
```typescript
// In file-operations.ts
async readNote(notePath: string): Promise<any> {
  try {
    const validatedPath = this.syncDirectory 
      ? this.validatePath(notePath, this.syncDirectory)
      : path.normalize(notePath);
    
    // Read the .noti file content
    const content = await this.readFileAtomic(validatedPath);
    
    // Parse JSON
    return JSON.parse(content);
    
  } catch (error) {
    throw new SyncError(
      ErrorCode.FILE_READ_ERROR,
      `Failed to read .noti file at ${notePath}: ${(error as Error).message}`
    );
  }
}
```

## Example Complete Flow

### Export Example
1. **Note in Database**: 
   - `content`: "This is my note with an image"
   - `html_content`: `<p>This is my note with an image</p><img src="noti-media://C:/Users/<USER>/media/12345-screenshot.png">`
   - Associated media file in `media_files` table

2. **Export Process**:
   - Read media file from disk → Convert to base64
   - Replace `noti-media://C:/Users/<USER>/media/12345-screenshot.png` with `noti-media://embedded_media_1`
   - Create markdown preview: "This is my note with..."
   - Save as `.noti` file with embedded media

### Import Example
1. **.noti File Content**:
   - HTML: `<p>This is my note with an image</p><img src="noti-media://embedded_media_1">`
   - Media array with base64 data for `embedded_media_1`

2. **Import Process**:
   - Create note with preview text as markdown content
   - Decode base64 → Save to media storage → Insert into `media_files` table
   - Replace `noti-media://embedded_media_1` with `noti-media://C:/Users/<USER>/media/67890-screenshot.png`
   - Update note with restored HTML content

This approach ensures complete media portability while maintaining the existing media system architecture within the application.
