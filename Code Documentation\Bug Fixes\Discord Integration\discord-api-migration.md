# Discord RPC API Migration Fix

## Files Modified
- `src/stores/timerStore.ts` - Updated Discord integration calls
- `src/stores/settingsStore.ts` - Updated Discord initialization
- `electron/main/ipc-handlers.ts` - Updated IPC handlers for new API
- `electron/preload/api-bridge.ts` - Updated API bridge methods
- `src/useElectronAPI.ts` - Updated TypeScript interfaces
- `src/types/electron-api.d.ts` - Updated type definitions
- `src/types/mock-api.ts` - Updated mock API implementation

## What Was Done
Fixed the Discord Rich Presence integration after the API customization by migrating from the old complex API to the new simplified API.

## Problem
After customizing the Discord RPC to be simple and professional, the timer store was still calling the old methods:
- `setStudySession()` - No longer exists
- `setIdle(options)` - Now takes no parameters
- Complex session/timer data structures - Simplified to activity types

This caused errors when starting/stopping the timer:
```
TypeError: discordRpc.setStudySession is not a function
```

## Solution

### 1. Updated Timer Store Discord Integration
**Before:**
```typescript
await db.discord.setStudySession(sessionData, timerData, {
  showDetails: settingsStore.settings.discordShowSessionDetails,
  showTimer: settingsStore.settings.discordShowTimer,
  // ... many options
})
```

**After:**
```typescript
if (state.value.activeSession && state.value.isRunning) {
  await db.discord.setActivity({ type: 'timer' })
} else {
  await db.discord.setIdle()
}
```

### 2. Updated Settings Store Initialization
**Before:**
```typescript
await db.discord.setIdle({
  customIdleMessage: settings.value.discordCustomIdleMessage,
  showAppName: settings.value.discordShowAppName
})
```

**After:**
```typescript
await db.discord.setIdle()
```

### 3. Updated IPC Handlers
**Removed old handlers:**
- `discord:setStudySession`
- `discord:setReading` 
- `discord:setNoteTaking`

**Added new handlers:**
- `discord:setActivity` - Single method for all activities
- `discord:updateSettings` - For privacy controls
- Updated `discord:setIdle` - No parameters

### 4. Updated API Interfaces
**New DiscordAPI interface:**
```typescript
export interface DiscordAPI {
  initialize: () => Promise<boolean>;
  setEnabled: (enabled: boolean) => Promise<boolean>;
  setActivity: (activityData: any) => Promise<boolean>;
  setIdle: () => Promise<boolean>;
  updateSettings: (settings: any) => Promise<boolean>;
  clearActivity: () => Promise<boolean>;
  getStatus: () => Promise<{ connected: boolean; enabled: boolean; settings: any }>;
  destroy: () => Promise<boolean>;
  testConnection: () => Promise<boolean>;
}
```

## Benefits
1. **Consistency** - All components now use the same simplified API
2. **Reliability** - No more method not found errors
3. **Maintainability** - Single activity method instead of multiple specialized ones
4. **Privacy** - Settings-based control over what information is shared

## Testing
- Timer start/stop now works without Discord errors
- Activity correctly shows "In focus session" when timer is running
- Idle state is set when timer is not active
- No more console errors about missing methods

## Activity Tracking Implementation

### 1. Added Discord Activity Composable
Created `src/composables/useDiscordActivity.ts` for centralized activity management:
```typescript
export function useDiscordActivity() {
  return {
    setNoteTakingActivity,
    setBookWritingActivity,
    setTimerActivity,
    setSettingsActivity,
    updateDiscordSettings
  }
}
```

### 2. Integrated Activity Tracking
**NotesView.vue:**
- Shows "Taking notes" when user selects/edits notes
- Shows "Writing about [Book Name]" when note is linked to a book
- Respects privacy settings for book name display

**SettingsView.vue:**
- Shows "Configuring app" when user enters settings

**TimerStore.ts:**
- Shows "In focus session" only when timer is actively running
- No longer immediately shows idle when timer stops

### 3. Automatic Idle Detection
- **3-minute timeout**: Shows "Idle" after 3 minutes of no user activity
- **Activity tracking**: Updates timestamp on any Discord activity
- **Smart detection**: Only goes idle when user is truly inactive

### 4. Updated Settings UI
Replaced complex settings with simple privacy controls:
- ✅ Show Note Taking
- ✅ Show Book Writing
- ✅ Show Book Names (privacy control)
- ✅ Show Timer Sessions
- ✅ Show Settings Activity

## User Experience
**Before:** Immediately showed "Idle" when timer stopped, even if user was active
**After:** Shows current activity and only goes idle after 3 minutes of true inactivity

**Before:** Complex settings with many unused options
**After:** Simple privacy controls that match actual functionality

## Future Integration
Other components can easily integrate Discord activities:
```typescript
// For note-taking
await db.discord.setActivity({ type: 'notes' })

// For book writing
await db.discord.setActivity({
  type: 'book',
  bookName: 'The Great Gatsby'
})

// For settings
await db.discord.setActivity({ type: 'settings' })
```
