x64:
  firstOrDefaultFilePatterns:
    - '!**/node_modules'
    - '!build{,/**/*}'
    - '!build-output/0.8.0{,/**/*}'
    - dist
    - dist-electron
    - package.json
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - dist
    - dist-electron
nsis:
  script: "!include \"C:\\Users\\<USER>\\Desktop\\Noti\\Noti\\node_modules\\app-builder-lib\\templates\\nsis\\include\\StdUtils.nsh\"\n!addincludedir \"C:\\Users\\<USER>\\Desktop\\Noti\\Noti\\node_modules\\app-builder-lib\\templates\\nsis\\include\"\n!macro _isUpdated _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"updated\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isUpdated `\"\" isUpdated \"\"`\n\n!macro _isForceRun _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"force-run\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isForceRun `\"\" isForceRun \"\"`\n\n!macro _isKeepShortcuts _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"keep-shortcuts\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isKeepShortcuts `\"\" isKeepShortcuts \"\"`\n\n!macro _isNoDesktopShortcut _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"no-desktop-shortcut\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isNoDesktopShortcut `\"\" isNoDesktopShortcut \"\"`\n\n!macro _isDeleteAppData _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"delete-app-data\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isDeleteAppData `\"\" isDeleteAppData \"\"`\n\n!macro _isForAllUsers _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"allusers\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isForAllUsers `\"\" isForAllUsers \"\"`\n\n!macro _isForCurrentUser _a _b _t _f\n  ${StdUtils.TestParameter} $R9 \"currentuser\"\n  StrCmp \"$R9\" \"true\" `${_t}` `${_f}`\n!macroend\n!define isForCurrentUser `\"\" isForCurrentUser \"\"`\n\n!macro addLangs\n  !insertmacro MUI_LANGUAGE \"English\"\n  !insertmacro MUI_LANGUAGE \"German\"\n  !insertmacro MUI_LANGUAGE \"French\"\n  !insertmacro MUI_LANGUAGE \"SpanishInternational\"\n  !insertmacro MUI_LANGUAGE \"SimpChinese\"\n  !insertmacro MUI_LANGUAGE \"TradChinese\"\n  !insertmacro MUI_LANGUAGE \"Japanese\"\n  !insertmacro MUI_LANGUAGE \"Korean\"\n  !insertmacro MUI_LANGUAGE \"Italian\"\n  !insertmacro MUI_LANGUAGE \"Dutch\"\n  !insertmacro MUI_LANGUAGE \"Danish\"\n  !insertmacro MUI_LANGUAGE \"Swedish\"\n  !insertmacro MUI_LANGUAGE \"Norwegian\"\n  !insertmacro MUI_LANGUAGE \"Finnish\"\n  !insertmacro MUI_LANGUAGE \"Russian\"\n  !insertmacro MUI_LANGUAGE \"Portuguese\"\n  !insertmacro MUI_LANGUAGE \"PortugueseBR\"\n  !insertmacro MUI_LANGUAGE \"Polish\"\n  !insertmacro MUI_LANGUAGE \"Ukrainian\"\n  !insertmacro MUI_LANGUAGE \"Czech\"\n  !insertmacro MUI_LANGUAGE \"Slovak\"\n  !insertmacro MUI_LANGUAGE \"Hungarian\"\n  !insertmacro MUI_LANGUAGE \"Arabic\"\n  !insertmacro MUI_LANGUAGE \"Turkish\"\n  !insertmacro MUI_LANGUAGE \"Thai\"\n  !insertmacro MUI_LANGUAGE \"Vietnamese\"\n!macroend\n\n!addplugindir /x86-unicode \"C:\\Users\\<USER>\\AppData\\Local\\electron-builder\\Cache\\nsis\\nsis-resources-3.4.1\\plugins\\x86-unicode\"\n!include \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\t-0KmY09\\2-messages.nsh\"\n\n!include \"common.nsh\"\n!include \"extractAppPackage.nsh\"\n\n# https://github.com/electron-userland/electron-builder/issues/3972#issuecomment-505171582\nCRCCheck off\nWindowIcon Off\nAutoCloseWindow True\nRequestExecutionLevel ${REQUEST_EXECUTION_LEVEL}\n\nFunction .onInit\n  !ifndef SPLASH_IMAGE\n    SetSilent silent\n  !endif\n\n  !insertmacro check64BitAndSetRegView\nFunctionEnd\n\nFunction .onGUIInit\n  InitPluginsDir\n\n  !ifdef SPLASH_IMAGE\n    File /oname=$PLUGINSDIR\\splash.bmp \"${SPLASH_IMAGE}\"\n    BgImage::SetBg $PLUGINSDIR\\splash.bmp\n    BgImage::Redraw\n  !endif\nFunctionEnd\n\nSection\n  !ifdef SPLASH_IMAGE\n    HideWindow\n  !endif\n\n  StrCpy $INSTDIR \"$PLUGINSDIR\\app\"\n  !ifdef UNPACK_DIR_NAME\n    StrCpy $INSTDIR \"$TEMP\\${UNPACK_DIR_NAME}\"\n  !endif\n\n  RMDir /r $INSTDIR\n  SetOutPath $INSTDIR\n\n  !ifdef APP_DIR_64\n    !ifdef APP_DIR_ARM64\n      !ifdef APP_DIR_32\n        ${if} ${IsNativeARM64}\n          File /r \"${APP_DIR_ARM64}\\*.*\"\n        ${elseif} ${RunningX64}\n          File /r \"${APP_DIR_64}\\*.*\"\n        ${else}\n          File /r \"${APP_DIR_32}\\*.*\"\n        ${endIf}\n      !else\n        ${if} ${IsNativeARM64}\n          File /r \"${APP_DIR_ARM64}\\*.*\"\n        ${else}\n          File /r \"${APP_DIR_64}\\*.*\"\n        {endIf}\n      !endif\n    !else\n      !ifdef APP_DIR_32\n        ${if} ${RunningX64}\n          File /r \"${APP_DIR_64}\\*.*\"\n        ${else}\n          File /r \"${APP_DIR_32}\\*.*\"\n        ${endIf}\n      !else\n        File /r \"${APP_DIR_64}\\*.*\"\n      !endif\n    !endif\n  !else\n    !ifdef APP_DIR_32\n      File /r \"${APP_DIR_32}\\*.*\"\n    !else\n      !insertmacro extractEmbeddedAppPackage\n    !endif\n  !endif\n\n  System::Call 'Kernel32::SetEnvironmentVariable(t, t)i (\"PORTABLE_EXECUTABLE_DIR\", \"$EXEDIR\").r0'\n  System::Call 'Kernel32::SetEnvironmentVariable(t, t)i (\"PORTABLE_EXECUTABLE_FILE\", \"$EXEPATH\").r0'\n  System::Call 'Kernel32::SetEnvironmentVariable(t, t)i (\"PORTABLE_EXECUTABLE_APP_FILENAME\", \"${APP_FILENAME}\").r0'\n  ${StdUtils.GetAllParameters} $R0 0\n\n  !ifdef SPLASH_IMAGE\n    BgImage::Destroy\n  !endif\n\n\tExecWait \"$INSTDIR\\${APP_EXECUTABLE_FILENAME} $R0\" $0\n  SetErrorLevel $0\n\n  SetOutPath $EXEDIR\n\tRMDir /r $INSTDIR\nSectionEnd\n"
