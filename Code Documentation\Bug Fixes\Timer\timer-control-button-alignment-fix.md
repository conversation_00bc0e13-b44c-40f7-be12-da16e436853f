# Timer Control Button Alignment Fix

## Issue Description
The timer control buttons in PomodoroTimer.vue had an unstable layout where buttons would shift horizontally when the "End Session" button appeared or disappeared. Additionally, the play/pause button was not properly aligned with the colon separator (:) in the timer display.

## Root Cause Analysis
1. **Layout Shift Issue**: The end session button used `v-if="sessionActive"` which completely removed/added the button from the DOM, causing all other buttons to shift positions due to the flexbox `justify-content: center` layout.

2. **Alignment Issue**: The timer display used a 3-column CSS Grid (`1fr auto 1fr`) with the colon in the center, but the action controls used a 4-column grid with fixed widths, causing misalignment between the play button and the colon separator.

## Files Modified
- `src/components/timer/PomodoroTimer.vue`

## Changes Implemented

### 1. Fixed Layout Stability
**Before:**
```html
<img v-if="sessionActive" src="/icons/end-session-icon.svg" class="end-session-icon" ... />
```

**After:**
```html
<img v-show="sessionActive" src="/icons/end-session-icon.svg" class="end-session-icon" ... />
```

- Changed `v-if` to `v-show` to keep the button in the DOM but hide it visually when not needed
- This prevents layout shifts when the session state changes

### 2. Improved Button Alignment
**Before:**
```css
.action-controls {
  display: grid;
  grid-template-columns: 40px 70px 50px 40px;
  width: 240px;
  justify-items: center;
  gap: 15px;
}
```

**After:**
```css
.action-controls {
  display: grid;
  grid-template-columns: 1fr 70px 1fr;
  width: fit-content;
  justify-items: center;
  gap: 15px;
  position: relative;
}
```

- Changed to 3-column grid layout matching the timer display structure
- Play button now sits in the center column, aligned with the colon separator
- Used `1fr` for side columns to create flexible spacing

### 3. Positioned Side Buttons
```css
.restart-button {
  grid-column: 1;
  justify-self: end;
}

.skip-button {
  grid-column: 3;
  justify-self: start;
}
```

- Restart button positioned at the end of the first column
- Skip button positioned at the start of the third column
- Creates balanced spacing around the central play button

### 4. Absolute Positioning for End Session Button
```css
.end-session-icon {
  position: absolute;
  right: -70px;
  top: 50%;
  transform: translateY(-50%);
}
```

- Positioned absolutely to avoid interfering with the main grid layout
- Maintains consistent positioning regardless of session state
- Uses `v-show` for smooth visibility transitions

## Benefits
1. **Stable Layout**: Buttons maintain consistent positions regardless of session state
2. **Perfect Alignment**: Play/pause button is now horizontally aligned with the timer's colon separator
3. **Visual Consistency**: No more jarring layout shifts when sessions start/end
4. **Responsive Design**: Layout adapts properly to different screen sizes
5. **Smooth Transitions**: End session button appears/disappears without affecting other button positions

## Testing Recommendations
1. Start a session and verify the play button aligns with the colon in the timer
2. End a session and confirm no layout shifts occur
3. Test on different screen sizes to ensure responsive behavior
4. Verify all button interactions work correctly
5. Check that button hover effects and transitions remain intact

## Technical Notes
- The solution uses CSS Grid for precise control over button positioning
- `v-show` maintains DOM structure while controlling visibility
- Absolute positioning isolates the end session button from the main layout flow
- The 3-column grid structure mirrors the timer display for perfect alignment
