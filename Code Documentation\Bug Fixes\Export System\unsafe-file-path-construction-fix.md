# Unsafe File Path Construction Fix

## Files Modified
- `electron/main/api/sync-logic/unified-sync-engine.ts`

## What Was Done
Fixed unsafe file path construction in the unified sync engine where note titles and folder names were being used directly in file paths without sanitization. This could cause issues with special characters in file/folder names.

## How It Was Fixed

### Changes Applied:

1. **Note Export Function (lines 601-613)**:
   - Replaced direct usage of `${note.title}.md` with `${sanitizeNoteTitle(note.title)}.md`
   - Applied to all three path construction scenarios:
     - Notes in folders
     - Notes in books
     - Standalone notes

2. **Folder Export Function (lines 557-569)**:
   - Replaced direct usage of `folder.name` with `sanitizeFolderName(folder.name)`
   - Applied to all three path construction scenarios:
     - Subfolders (with parent_id)
     - Book folders (with book_id)
     - Top-level folders

### Why This Fix is Safe:
- The sync system tracks items by their database ID, not by filename
- The import handler reads whatever filenames exist in the sync directory
- The manifest stores the original titles/names separately from file paths
- Existing syncs won't be broken because the system is ID-based
- The sanitization functions were already imported but weren't being used

### Technical Details:
The sanitization functions handle:
- Special characters that are invalid in file paths
- Characters that could cause cross-platform issues
- Path traversal attempts
- Length limits for file systems

This ensures that sync operations work reliably across different operating systems and file systems.