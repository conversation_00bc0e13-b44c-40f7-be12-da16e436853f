# BackupSettings Theme Consistency Fix

## Files Modified
- `src/components/settings/BackupSettings.vue`

## What Was Done
Fixed multiple styling issues in the BackupSettings component that were causing improper appearance in both light and dark themes. The component was using inconsistent CSS variables and hardcoded colors that didn't adapt properly to theme changes.

## Issues Identified
1. **Inconsistent border colors**: Using `--color-border-primary` instead of appropriate input-specific colors
2. **Wrong background colors**: Using generic secondary backgrounds instead of input-specific backgrounds
3. **Hardcoded toggle slider colors**: White background that didn't work in dark mode
4. **Notification styling**: Not using proper theme-aware status colors
5. **Icon visibility issues**: SVG and image icons not adapting properly to theme changes
6. **Disabled button styling**: Using border colors instead of proper button colors

## How It Was Fixed

### Border and Background Consistency
- Replaced `--color-border-primary` with `--color-input-border` for form elements
- Replaced `--color-bg-secondary` with `--color-input-bg` for input containers
- Updated all section containers (location, toggle, format options, etc.) to use consistent input styling

### Toggle Slider Improvements
```css
/* Before */
.toggle-slider:before {
  background-color: white; /* Hardcoded white */
}

/* After */
.toggle-slider:before {
  background-color: var(--color-bg-primary); /* Theme-aware */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Better visibility */
}
```

### Notification System
- Updated notification backgrounds to use proper status color variables:
  - `--color-success-bg` and `--color-success-text` for success notifications
  - `--color-error-bg` and `--color-error-text` for error notifications
- Changed notification message color to `inherit` for proper theme adaptation

### Button and Icon Styling
- **SVG Icons**: Fixed sync button SVG icons to use `color: inherit` to properly inherit button text color
- **Image Icons**: Implemented theme-specific CSS filters for the import button icon:
  - Light theme: `invert(100%)` to make icon white on dark button background
  - Dark theme: `invert(13%)` to make icon dark on light button background
- Fixed disabled button styling to use `--color-btn-secondary-bg` instead of border colors
- Added opacity to disabled states for better visual feedback

### Icon Implementation Details
```css
/* SVG icons inherit button text color */
.sync-icon {
  color: inherit;
}

/* Image icons use theme-specific filters */
.theme-light .import-button .button-icon {
  filter: brightness(0) saturate(100%) invert(100%);
}

.theme-dark .import-button .button-icon {
  filter: brightness(0) saturate(100%) invert(13%);
}
```

### Hover State Improvements
- Updated format option hover to use `--color-card-hover-bg` for consistent hover behavior
- Ensured all interactive elements use proper theme-aware hover colors

## Technical Details

### CSS Variables Used
- `--color-input-bg`: Input field backgrounds
- `--color-input-border`: Input field borders  
- `--color-input-placeholder`: Placeholder text
- `--color-card-hover-bg`: Hover state backgrounds
- `--color-success-bg/text`: Success notification styling
- `--color-error-bg/text`: Error notification styling
- `--color-btn-secondary-bg`: Secondary button backgrounds
- `--icon-filter`: Theme-aware icon filtering

### Visual Improvements
1. **Better contrast**: All text and borders now have proper contrast in both themes
2. **Consistent styling**: All form elements use the same visual treatment
3. **Improved accessibility**: Better visual feedback for interactive states
4. **Theme coherence**: All elements properly adapt between light and dark modes

## Result
The BackupSettings component now has consistent, theme-aware styling that works properly in both light and dark modes. All form elements, buttons, notifications, icons, and interactive states properly adapt to theme changes without any visual inconsistencies or accessibility issues.

### Specific Improvements
1. **Icon Visibility**: Both SVG and image icons are now clearly visible in both themes
2. **Better contrast**: All text and borders now have proper contrast in both themes
3. **Consistent styling**: All form elements use the same visual treatment
4. **Improved accessibility**: Better visual feedback for interactive states
5. **Theme coherence**: All elements properly adapt between light and dark modes