// scripts/populateTimerTestData.ts
import sqlite3 from 'sqlite3';
import path from 'node:path';
import fs from 'node:fs';
import os from 'node:os';

// Types
type Database = sqlite3.Database;

// Function to get the actual Noti database path
function getNotiDbPath(): string {
    let appDataPath: string;

    // Determine the user's AppData path based on the platform
    if (process.platform === 'win32') {
        appDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'noti');
    } else if (process.platform === 'darwin') {
        appDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'noti');
    } else {
        // Linux and others
        appDataPath = path.join(os.homedir(), '.config', 'noti');
    }

    return path.join(appDataPath, 'noti-database.sqlite');
}

// Categories for test sessions
const categories = ['Work', 'Study', 'Personal', 'Exercise', 'Reading', 'General'];

// Session names for variety
const sessionNames = [
    'Deep Work Session',
    'Study Time',
    'Project Focus',
    'Reading Session',
    'Code Review',
    'Planning Session',
    'Research Time',
    'Writing Session',
    'Learning Session',
    'Creative Work'
];

// Function to get random element from array
function getRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
}

// Function to get random number between min and max
function getRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Function to create a timer session
function createTimerSession(db: Database, sessionData: {
    session_name: string;
    category: string;
    start_time: string;
    duration: number;
    pomodoro_cycles_completed: number;
    is_completed: number;
    is_user_session: number;
}): Promise<number> {
    return new Promise((resolve, reject) => {
        const sql = `INSERT INTO timer_sessions 
            (session_name, category, start_time, duration, pomodoro_cycles_completed, is_completed, is_user_session, session_type, focus)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'work', ?)`;
        
        const focus = `Focus on ${sessionData.session_name.toLowerCase()}`;
        
        db.run(sql, [
            sessionData.session_name,
            sessionData.category,
            sessionData.start_time,
            sessionData.duration,
            sessionData.pomodoro_cycles_completed,
            sessionData.is_completed,
            sessionData.is_user_session,
            focus
        ], function(err) {
            if (err) {
                reject(err);
                return;
            }
            resolve(this.lastID);
        });
    });
}

// Function to create pomodoro cycles for a session
function createPomodoroCycles(db: Database, sessionId: number, cycleCount: number): Promise<void> {
    return new Promise((resolve, reject) => {
        if (cycleCount === 0) {
            resolve();
            return;
        }

        let completed = 0;
        let hasError = false;

        for (let i = 0; i < cycleCount; i++) {
            const sql = `INSERT INTO pomodoro_cycles 
                (session_id, cycle_type, duration, completed, start_time, end_time)
                VALUES (?, 'pomodoro', 1500, 1, datetime('now', '-${cycleCount - i} hours'), datetime('now', '-${cycleCount - i - 1} hours'))`;
            
            db.run(sql, [sessionId], (err) => {
                if (err && !hasError) {
                    hasError = true;
                    reject(err);
                    return;
                }
                
                completed++;
                if (completed === cycleCount && !hasError) {
                    resolve();
                }
            });
        }
    });
}

async function populateTimerTestData() {
    const dbPath = getNotiDbPath();
    console.log(`Using Noti database: ${dbPath}`);

    if (!fs.existsSync(dbPath)) {
        console.error('Noti database not found. Please run the application first to create the database.');
        process.exit(1);
    }

    const db = new sqlite3.Database(dbPath);

    try {
        console.log('Creating timer test data...');

        // Create sessions for the last 14 days
        const today = new Date();
        const sessionsCreated = [];

        for (let dayOffset = 13; dayOffset >= 0; dayOffset--) {
            const date = new Date(today);
            date.setDate(date.getDate() - dayOffset);
            
            // Create 1-4 sessions per day (random)
            const sessionsPerDay = getRandomNumber(1, 4);
            
            for (let sessionIndex = 0; sessionIndex < sessionsPerDay; sessionIndex++) {
                // Random start time during the day
                const startHour = getRandomNumber(8, 20);
                const startMinute = getRandomNumber(0, 59);
                
                const sessionStart = new Date(date);
                sessionStart.setHours(startHour, startMinute, 0, 0);
                
                // Random session duration (15 minutes to 3 hours)
                const durationMinutes = getRandomNumber(15, 180);
                const durationSeconds = durationMinutes * 60;
                
                // Random pomodoro count (0-6)
                const pomodoroCount = getRandomNumber(0, 6);
                
                const sessionData = {
                    session_name: getRandomElement(sessionNames),
                    category: getRandomElement(categories),
                    start_time: sessionStart.toISOString(),
                    duration: durationSeconds,
                    pomodoro_cycles_completed: pomodoroCount,
                    is_completed: 1,
                    is_user_session: 1
                };

                try {
                    const sessionId = await createTimerSession(db, sessionData);
                    console.log(`Created session: ${sessionData.session_name} (${sessionData.category}) - ${durationMinutes}min, ${pomodoroCount} pomodoros`);
                    
                    // Create pomodoro cycles for this session
                    if (pomodoroCount > 0) {
                        await createPomodoroCycles(db, sessionId, pomodoroCount);
                    }
                    
                    sessionsCreated.push(sessionId);
                } catch (error) {
                    console.error('Error creating session:', error);
                }
            }
        }

        console.log(`\nSuccessfully created ${sessionsCreated.length} timer sessions with test data.`);
        console.log('You can now view the charts in the Timer section of the application.');

    } catch (error) {
        console.error('Error populating timer test data:', error);
    } finally {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            } else {
                console.log('Database connection closed.');
            }
        });
    }
}

// Run the script
populateTimerTestData().catch(console.error);
