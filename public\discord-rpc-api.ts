// Discord Rich Presence API for Noti application
import { Client } from 'discord-rpc';

// Discord application ID for Noti (you'll need to create this on Discord Developer Portal)
const CLIENT_ID = '1381419258738770061'; // Replace with actual Discord app ID

export interface DiscordActivity {
  state?: string;
  details?: string;
  startTimestamp?: number;
  endTimestamp?: number;
  largeImageKey?: string;
  largeImageText?: string;
}

export interface DiscordRPCSettings {
  enabled: boolean;
  showNoteTaking: boolean;
  showBookWriting: boolean;
  showBookNames: boolean;
  showTimer: boolean;
  showSettings: boolean;
  showTimerDetails: boolean;
  showPomodoroCount: boolean;
  showSessionName: boolean;
  customIdleMessage: string;
  customActiveMessage: string;
  idleTimeout: number; // in minutes
}

export interface ActivityData {
  type: 'notes' | 'book' | 'timer' | 'settings' | 'idle';
  bookName?: string;
  timerType?: 'pomodoro' | 'shortBreak' | 'longBreak';
  timeRemaining?: number; // seconds
  pomodoroCount?: number;
  sessionName?: string;
  sessionCategory?: string;
  totalFocusTime?: number; // seconds
  isRunning?: boolean;
}

class DiscordRichPresence {
  private client: Client | null = null;
  private isConnected = false;
  private settings: DiscordRPCSettings = {
    enabled: false,
    showNoteTaking: true,
    showBookWriting: true,
    showBookNames: true,
    showTimer: true,
    showSettings: true,
    showTimerDetails: true,
    showPomodoroCount: true,
    showSessionName: true,
    customIdleMessage: '',
    customActiveMessage: '',
    idleTimeout: 3 // 3 minutes default
  };
  private currentActivity: DiscordActivity | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private lastActivityTime = Date.now();
  private appStartTime = Date.now(); // Persistent timestamp for the entire app session
  private idleCheckInterval: NodeJS.Timeout | null = null;
  private get IDLE_TIMEOUT() {
    return this.settings.idleTimeout * 60 * 1000; // Convert minutes to milliseconds
  }

  constructor() {
    console.log('🎮 [DiscordRPC] Initializing Discord Rich Presence...');
    console.log('🎮 [DiscordRPC] App start time set to:', new Date(this.appStartTime).toISOString());
    this.startIdleCheck();
  }

  /**
   * Start idle checking interval
   */
  private startIdleCheck(): void {
    this.idleCheckInterval = setInterval(() => {
      if (this.settings.enabled && Date.now() - this.lastActivityTime > this.IDLE_TIMEOUT) {
        this.setIdle();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Update last activity time
   */
  private updateLastActivity(): void {
    this.lastActivityTime = Date.now();
  }

  /**
   * Initialize Discord RPC client
   */
  async initialize(): Promise<boolean> {
    console.log('🎮 [DiscordRPC] Initialize called, current state:', {
      hasClient: !!this.client,
      isConnected: this.isConnected,
      enabled: this.settings.enabled,
      clientId: CLIENT_ID
    });

    if (this.client && this.isConnected) {
      console.log('🎮 [DiscordRPC] Client already initialized and connected');
      return true;
    }

    // If we have a client but not connected, destroy it first
    if (this.client && !this.isConnected) {
      console.log('🎮 [DiscordRPC] Client exists but not connected, destroying...');
      try {
        await this.client.destroy();
      } catch (error) {
        console.log('🎮 [DiscordRPC] Error destroying old client:', error);
      }
      this.client = null;
    }

    try {
      console.log('🎮 [DiscordRPC] Creating new Discord client...');
      this.client = new Client({ transport: 'ipc' });

      this.client.on('ready', () => {
        console.log('🎮 [DiscordRPC] Connected to Discord!');
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Set initial activity if we have one, otherwise show active state
        if (this.currentActivity && this.settings.enabled) {
          console.log('🎮 [DiscordRPC] Setting initial activity...');
          this.updateActivity(this.currentActivity);
        } else if (this.settings.enabled) {
          console.log('🎮 [DiscordRPC] Setting initial active state...');
          this.setActiveState();
        }
      });

      this.client.on('disconnected', () => {
        console.log('🎮 [DiscordRPC] Disconnected from Discord');
        this.isConnected = false;
        this.attemptReconnect();
      });

      console.log('🎮 [DiscordRPC] Attempting to login with client ID:', CLIENT_ID);
      await this.client.login({ clientId: CLIENT_ID });
      console.log('🎮 [DiscordRPC] Login successful');
      return true;
    } catch (error) {
      console.error('🎮 [DiscordRPC] Failed to initialize:', error);

      // Handle specific error types
      if (error.message?.includes('RPC_CONNECTION_TIMEOUT')) {
        console.log('🎮 [DiscordRPC] Connection timeout - Discord may not be running or RPC may be disabled');
      } else if (error.message?.includes('ENOENT')) {
        console.log('🎮 [DiscordRPC] Discord not found - please make sure Discord is installed and running');
      }

      this.client = null;
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Attempt to reconnect to Discord
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('🎮 [DiscordRPC] Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    
    console.log(`🎮 [DiscordRPC] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(async () => {
      try {
        if (this.client) {
          await this.client.destroy();
        }
        this.client = null;
        await this.initialize();
      } catch (error) {
        console.error('🎮 [DiscordRPC] Reconnection failed:', error);
      }
    }, delay);
  }

  /**
   * Update settings
   */
  updateSettings(newSettings: Partial<DiscordRPCSettings>): void {
    const oldIdleTimeout = this.settings.idleTimeout;
    this.settings = { ...this.settings, ...newSettings };
    console.log('🎮 [DiscordRPC] Settings updated:', this.settings);

    // If idle timeout changed, restart idle check with new interval
    if (newSettings.idleTimeout && newSettings.idleTimeout !== oldIdleTimeout) {
      console.log('🎮 [DiscordRPC] Idle timeout changed, restarting idle check');
      this.stopIdleCheck();
      this.startIdleCheck();
    }
  }

  /**
   * Stop idle checking interval
   */
  private stopIdleCheck(): void {
    if (this.idleCheckInterval) {
      clearInterval(this.idleCheckInterval);
      this.idleCheckInterval = null;
    }
  }

  /**
   * Enable or disable Discord Rich Presence
   */
  async setEnabled(enabled: boolean): Promise<void> {
    console.log(`🎮 [DiscordRPC] Setting enabled: ${enabled}, current state:`, {
      isConnected: this.isConnected,
      hasClient: !!this.client,
      currentActivity: !!this.currentActivity
    });
    this.settings.enabled = enabled;

    if (enabled) {
      if (!this.isConnected) {
        console.log('🎮 [DiscordRPC] Not connected, initializing...');
        await this.initialize();
      }
      // Restore current activity if we have one
      if (this.currentActivity && this.isConnected) {
        console.log('🎮 [DiscordRPC] Restoring current activity...');
        this.updateActivity(this.currentActivity);
      } else if (this.isConnected) {
        console.log('🎮 [DiscordRPC] No current activity, setting active state...');
        this.setActiveState();
      }
    } else {
      console.log('🎮 [DiscordRPC] Disabled, clearing activity...');
      await this.clearActivity();
    }
  }

  /**
   * Update Discord activity (optimized for responsiveness)
   */
  private updateActivity(activity: DiscordActivity): void {
    // Store activity immediately for responsiveness
    this.currentActivity = activity;
    this.updateLastActivity();

    // Early return if not ready
    if (!this.client || !this.isConnected || !this.settings.enabled) {
      console.log('🎮 [DiscordRPC] Cannot update activity - not ready:', {
        hasClient: !!this.client,
        isConnected: this.isConnected,
        enabled: this.settings.enabled
      });
      return;
    }

    // Fire and forget for better performance
    this.client.setActivity(activity).then(() => {
      console.log('🎮 [DiscordRPC] Activity updated:', activity.details);
    }).catch((error: any) => {
      console.error('🎮 [DiscordRPC] Failed to update activity:', error);
      // If we get an error, mark as disconnected and try to reconnect
      if (error.message?.includes('RPC_CONNECTION_TIMEOUT') || error.message?.includes('ENOENT')) {
        console.log('🎮 [DiscordRPC] Connection lost, marking as disconnected');
        this.isConnected = false;
      }
    });
  }

  /**
   * Clear Discord activity
   */
  async clearActivity(): Promise<void> {
    if (!this.client || !this.isConnected) {
      return;
    }

    try {
      await this.client.clearActivity();
      this.currentActivity = null;
      console.log('🎮 [DiscordRPC] Activity cleared');
    } catch (error) {
      console.error('🎮 [DiscordRPC] Failed to clear activity:', error);
    }
  }

  /**
   * Set activity based on current user action
   */
  async setActivity(activityData: ActivityData): Promise<void> {
    // Quick early returns for better performance
    if (!this.settings.enabled) return;

    // Check if this activity type should be shown
    switch (activityData.type) {
      case 'notes':
        if (!this.settings.showNoteTaking) return;
        break;
      case 'book':
        if (!this.settings.showBookWriting) return;
        break;
      case 'timer':
        if (!this.settings.showTimer) return;
        break;
      case 'settings':
        if (!this.settings.showSettings) return;
        break;
    }

    // Pre-build activity object for faster execution
    const activity: DiscordActivity = {
      largeImageKey: 'noti-logo',
      largeImageText: 'Noti - Smart Note-Taking & Study Companion',
      startTimestamp: this.appStartTime, // Use persistent app start time
      details: this.getActivityDetails(activityData),
      state: this.getActivityState(activityData)
    };

    // Update activity immediately without waiting
    this.updateActivity(activity);
  }

  /**
   * Get activity details string (enhanced with rich information)
   */
  private getActivityDetails(activityData: ActivityData): string {
    switch (activityData.type) {
      case 'notes':
        return 'Taking notes';
      case 'book':
        return this.settings.showBookNames && activityData.bookName
          ? `Writing about ${activityData.bookName}`
          : 'Writing about a book';
      case 'timer':
        if (this.settings.showSessionName && activityData.sessionName) {
          return `Focus session: ${activityData.sessionName}`;
        }
        return this.getTimerActivityDetails(activityData);
      case 'settings':
        return 'Configuring app';
      case 'idle':
        return this.settings.customIdleMessage || 'Idle';
      default:
        return this.settings.customActiveMessage || 'Using Noti';
    }
  }

  /**
   * Get activity state string (additional information line)
   */
  private getActivityState(activityData: ActivityData): string | undefined {
    switch (activityData.type) {
      case 'timer':
        return this.getTimerStateDetails(activityData);
      case 'book':
        if (this.settings.showSessionName && activityData.sessionCategory) {
          return `Category: ${activityData.sessionCategory}`;
        }
        break;
      case 'notes':
        if (activityData.sessionName && this.settings.showSessionName) {
          return `Session: ${activityData.sessionName}`;
        }
        break;
    }
    return undefined;
  }

  /**
   * Get detailed timer activity information
   */
  private getTimerActivityDetails(activityData: ActivityData): string {
    const timerTypeMap = {
      'pomodoro': 'Focus Session',
      'shortBreak': 'Short Break',
      'longBreak': 'Long Break'
    };

    const timerTypeName = timerTypeMap[activityData.timerType || 'pomodoro'];

    if (this.settings.showTimerDetails && activityData.timeRemaining !== undefined) {
      const minutes = Math.floor(activityData.timeRemaining / 60);
      const seconds = activityData.timeRemaining % 60;
      const timeStr = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      const status = activityData.isRunning ? 'Running' : 'Paused';
      return `${timerTypeName} (${timeStr} - ${status})`;
    }

    return `In ${timerTypeName.toLowerCase()}`;
  }

  /**
   * Get timer state details for the state line
   */
  private getTimerStateDetails(activityData: ActivityData): string | undefined {
    const parts: string[] = [];

    if (this.settings.showPomodoroCount && activityData.pomodoroCount !== undefined) {
      parts.push(`${activityData.pomodoroCount} pomodoros completed`);
    }

    if (this.settings.showTimerDetails && activityData.totalFocusTime !== undefined) {
      const hours = Math.floor(activityData.totalFocusTime / 3600);
      const minutes = Math.floor((activityData.totalFocusTime % 3600) / 60);
      if (hours > 0) {
        parts.push(`${hours}h ${minutes}m total focus`);
      } else if (minutes > 0) {
        parts.push(`${minutes}m total focus`);
      }
    }

    return parts.length > 0 ? parts.join(' • ') : undefined;
  }

  /**
   * Set active state (when app is open but no specific activity)
   */
  setActiveState(): void {
    console.log('🎮 [DiscordRPC] setActiveState called, enabled:', this.settings.enabled);
    if (!this.settings.enabled) {
      console.log('🎮 [DiscordRPC] setActiveState: Not enabled, returning');
      return;
    }

    const activity: DiscordActivity = {
      largeImageKey: 'noti-logo',
      largeImageText: 'Noti - Smart Note-Taking & Study Companion',
      details: this.settings.customActiveMessage || 'Using Noti',
      startTimestamp: this.appStartTime // Use persistent app start time
    };

    console.log('🎮 [DiscordRPC] Setting active state:', activity);
    this.updateActivity(activity);
  }

  /**
   * Set idle activity
   */
  async setIdle(): Promise<void> {
    console.log('🎮 [DiscordRPC] setIdle called, enabled:', this.settings.enabled);
    if (!this.settings.enabled) {
      console.log('🎮 [DiscordRPC] setIdle: Not enabled, returning');
      return;
    }

    const activity: DiscordActivity = {
      largeImageKey: 'noti-logo',
      largeImageText: 'Noti - Smart Note-Taking & Study Companion',
      details: this.settings.customIdleMessage || 'Idle',
      startTimestamp: this.appStartTime // Use persistent app start time
    };

    console.log('🎮 [DiscordRPC] Setting idle activity:', activity);
    this.updateActivity(activity);
  }

  /**
   * Test Discord connection with a simple activity
   */
  async testConnection(): Promise<boolean> {
    console.log('🎮 [DiscordRPC] Testing Discord connection...');

    if (!this.settings.enabled) {
      console.log('🎮 [DiscordRPC] Discord RPC is not enabled');
      return false;
    }

    if (!this.isConnected) {
      console.log('🎮 [DiscordRPC] Not connected, attempting to initialize...');
      const success = await this.initialize();
      if (!success) {
        console.log('🎮 [DiscordRPC] Failed to initialize Discord connection');
        return false;
      }
    }

    try {
      const testActivity: DiscordActivity = {
        largeImageKey: 'noti-logo',
        largeImageText: 'Noti - Smart Note-Taking & Study Companion',
        details: 'Testing Connection',
        startTimestamp: this.appStartTime // Use persistent app start time
      };

      this.updateActivity(testActivity);
      console.log('🎮 [DiscordRPC] Test activity set successfully!');
      return true;
    } catch (error) {
      console.error('🎮 [DiscordRPC] Test connection failed:', error);
      return false;
    }
  }

  /**
   * Destroy the Discord RPC client
   */
  async destroy(): Promise<void> {
    console.log('🎮 [DiscordRPC] Destroying client...');

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.idleCheckInterval) {
      clearInterval(this.idleCheckInterval);
      this.idleCheckInterval = null;
    }

    if (this.client) {
      try {
        await this.client.destroy();
      } catch (error) {
        console.error('🎮 [DiscordRPC] Error destroying client:', error);
      }
      this.client = null;
    }

    this.isConnected = false;
    this.currentActivity = null;
  }

  /**
   * Get connection status and settings
   */
  getStatus(): { connected: boolean; enabled: boolean; settings: DiscordRPCSettings } {
    return {
      connected: this.isConnected,
      enabled: this.settings.enabled,
      settings: this.settings
    };
  }
}

// Export singleton instance
export default new DiscordRichPresence();
