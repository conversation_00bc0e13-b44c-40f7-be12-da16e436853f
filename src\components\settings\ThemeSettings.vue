<template>
  <div class="theme-container">
    <div class="theme-header">
      Theme Settings
    </div>
    <div class="theme-divider"></div>
    <div class="theme-subtitle">Choose Theme</div>
    <div class="theme-options-wrapper">
      <!-- Core Themes (easily expandable for future color themes) -->
      <div class="theme-options">
        <div class="theme-column">
          <div
            class="theme-card"
            :class="{ 'selected': currentTheme === 'light' }"
            @click="selectTheme('light')"
          >
            <div class="theme-preview">
              <div class="preview-header light-header">
                <div class="preview-nav-bar light-nav"></div>
                <div class="preview-content-area light-content"></div>
              </div>
              <div class="preview-divider"></div>
              <div class="theme-label">Light Mode</div>
            </div>
          </div>
        </div>
        <div class="theme-column">
          <div
            class="theme-card dark-theme"
            :class="{ 'selected': currentTheme === 'dark' }"
            @click="selectTheme('dark')"
          >
            <div class="theme-preview">
              <div class="preview-header dark-header">
                <div class="preview-nav-bar dark-nav"></div>
                <div class="preview-content-area dark-content"></div>
              </div>
              <div class="preview-divider"></div>
              <div class="theme-label">Dark Mode</div>
            </div>
          </div>
        </div>

      </div>

      <!-- Future color themes can be added here as additional rows -->
      <!--
      <div class="theme-options theme-options-additional">
        <div class="theme-column">
          <div class="theme-card blue-theme" :class="{ 'selected': currentTheme === 'blue' }" @click="selectTheme('blue')">
            <div class="theme-preview">
              <div class="preview-header blue-header">
                <div class="preview-nav-bar blue-nav"></div>
                <div class="preview-content-area blue-content"></div>
              </div>
              <div class="preview-divider"></div>
              <div class="theme-label">Blue</div>
            </div>
          </div>
        </div>
        Add more theme columns here...
      </div>
      -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useSettingsStore } from '../../stores/settingsStore'
import type { ThemeType } from '../../utils/themeUtils'

// Theme settings component for selecting application themes
const settingsStore = useSettingsStore()

// Get current theme from store
const currentTheme = computed(() => settingsStore.currentTheme)

// Function to select a theme
function selectTheme(theme: ThemeType) {
  settingsStore.updateSetting('theme', theme)
}
</script>

<style scoped>
.theme-container {
  border-radius: 16px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-primary);
  display: flex;
  padding: 32px;
  flex-direction: column;
  align-items: start;
  font-family: 'Montserrat', sans-serif;
}

@media (max-width: 991px) {
  .theme-container {
    padding: 20px;
  }
}

.theme-header {
  color: var(--color-text-primary);
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.theme-divider {
  background-color: var(--color-border-primary);
  align-self: stretch;
  display: flex;
  margin-top: 24px;
  flex-shrink: 0;
  height: 1px;
}

@media (max-width: 991px) {
  .theme-divider {
    max-width: 100%;
  }
}

.theme-subtitle {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}

.theme-options-wrapper {
  margin-top: 19px;
  width: 413px;
  max-width: 100%;
}

.theme-options {
  gap: 20px;
  display: flex;
}

/* Future theme rows can use these classes */
.theme-options-additional {
  margin-top: 20px;
}

@media (max-width: 991px) {
  .theme-options {
    flex-direction: column;
    align-items: stretch;
    gap: 0px;
  }
}

.theme-column {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  line-height: normal;
  width: 33%;
  margin-left: 0px;
}

@media (max-width: 991px) {
  .theme-column {
    width: 100%;
  }
}

.theme-card {
  border-radius: 12px;
  background-color: var(--color-bg-secondary);
  border: 2px solid var(--color-border-primary);
  display: flex;
  margin-left: auto;
  margin-right: auto;
  padding-bottom: 10px;
  flex-direction: column;
  align-items: stretch;
  width: 125px;
  height: 125px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-card:hover {
  border-color: var(--color-border-hover);
  transform: translateY(-2px);
  box-shadow: var(--color-card-hover-shadow) 0 4px 12px;
}

.theme-card.selected {
  border-color: var(--color-primary);
  box-shadow: var(--color-card-hover-shadow) 0 4px 12px;
}

@media (max-width: 991px) {
  .theme-card {
    margin-top: 19px;
  }
}

.dark-theme {
  padding-bottom: 13px;
}



.theme-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-header {
  border-radius: 12px 12px 0px 0px;
  background-color: var(--color-bg-secondary);
  padding: 14px 11px;
}

.light-header {
  background-color: #f5f5f5;
}

.dark-header {
  background-color: #6b6b6b;
}



.preview-nav-bar {
  border-radius: 5px;
  background-color: var(--color-btn-secondary-bg);
  display: flex;
  flex-shrink: 0;
  height: 15px;
}

.light-nav {
  background-color: #D9D9D9;
}

.dark-nav {
  background-color: #262626;
}



.preview-content-area {
  border-radius: 10px;
  background-color: var(--color-card-bg);
  display: flex;
  margin-top: 5px;
  flex-shrink: 0;
  height: 48px;
}

.light-content {
  background-color: #ffffff;
}

.dark-content {
  background-color: #121212;
}



/* Future theme preview styles can be added here */
/*
.blue-header { background-color: #1e40af; }
.blue-nav { background-color: #3b82f6; }
.blue-content { background-color: #f8fafc; }

.green-header { background-color: #166534; }
.green-nav { background-color: #22c55e; }
.green-content { background-color: #f7fdf7; }

.purple-header { background-color: #581c87; }
.purple-nav { background-color: #8b5cf6; }
.purple-content { background-color: #faf7ff; }

.contrast-header { background-color: #000000; }
.contrast-nav { background-color: #333333; }
.contrast-content { background-color: #ffffff; border: 2px solid #000000; }
*/

.preview-divider {
  background-color: var(--color-border-primary);
  display: flex;
  flex-shrink: 0;
  height: 1px;
}

.theme-label {
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin: 0;
  padding: 8px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 20px;
}
</style>
