# OpenLibrary API Timeout Error Fix

## Files Modified
- `electron/main/api/books-api.ts`

## Section of App
- Books management / OpenLibrary integration
- Add book functionality

## Issue Description
Users were experiencing timeout errors when adding certain books from OpenLibrary search results. The error occurred specifically when fetching author details from the OpenLibrary API:

```
Failed to fetch author details: AxiosError: timeout of 5000ms exceeded
```

The timeout was happening at the author details endpoint (e.g., `https://openlibrary.org/authors/OL2745568A.json`) which sometimes takes longer than 5 seconds to respond.

## Root Cause
1. **Aggressive timeout**: Author requests had a 5-second timeout which was too short for some OpenLibrary API responses
2. **No retry logic**: Failed requests were not retried, leading to immediate failures
3. **Poor error handling**: Timeout errors prevented the entire book addition process from completing
4. **No fallback mechanism**: When author details failed, no fallback data was used

## Solution Implemented

### 1. Increased Timeout Duration
- **Before**: 5000ms (5 seconds) for author requests  
- **After**: 10000ms (10 seconds) for author requests
- Work details requests already had 10-second timeout

### 2. Added Retry Logic
- Implemented retry mechanism with 2 attempts for author requests
- Added 1-second delay between retry attempts
- Graceful handling when retries fail

### 3. Improved Error Classification
- Added `isTimeoutError()` helper function to distinguish timeout vs other network errors
- Better error logging with specific error types (timeout vs network error)

### 4. Enhanced Fallback Mechanisms
- When author detail requests fail, the book addition continues with available data
- Added fallback logic to extract basic author information from author keys when detailed requests fail
- Book addition process is now more resilient to API failures

### 5. Better User Experience
- Books can now be added even when some OpenLibrary API requests timeout
- More informative error messages for debugging
- Graceful degradation - users get basic book info instead of complete failure

## Code Changes

### Timeout and Retry Logic
```typescript
// Try with retry logic
for (let attempt = 1; attempt <= 2; attempt++) {
  try {
    const authorResponse = await axios.get<OpenLibraryAuthorDetails>(authorUrl, {
      timeout: 10000, // Increased from 5000ms to 10000ms
      headers: {
        'User-Agent': 'Noti/1.0 (https://github.com/noti-app/noti)'
      }
    });
    return authorResponse.data.name;
  } catch (error) {
    if (attempt === 1) {
      console.warn(`Failed to fetch author details (timeout) on attempt ${attempt}, retrying:`, error);
      await new Promise(resolve => setTimeout(resolve, 1000));
    } else {
      console.warn(`Failed to fetch author details (timeout) after ${attempt} attempts:`, error);
      return null;
    }
  }
}
```

### Error Classification
```typescript
const isTimeoutError = (error: any): boolean => {
  return error?.code === 'ECONNABORTED' || 
         error?.message?.includes('timeout') ||
         error?.code === 'ETIMEDOUT';
};
```

### Fallback Mechanism
```typescript
// If no author names were found, try to extract basic names from author keys as fallback
if (authorNames.length === 0 && work.authors && work.authors.length > 0) {
  console.log('Using author keys as fallback for author names');
  authorNames = work.authors.map(author => {
    const authorKey = author.author.key.split('/').pop() || 'Unknown Author';
    return authorKey.replace(/^OL|A$/g, '') || 'Unknown Author';
  });
}
```

## Testing
The fix can be tested by:
1. Searching for books that previously caused timeout errors (like "Bible")
2. Attempting to add books with multiple authors
3. Testing with slow network connections
4. Verifying that books are added successfully even when some author requests fail

## Expected Outcome
- Reduced timeout errors when adding books from OpenLibrary
- More resilient book addition process
- Better user experience with graceful error handling
- Books can be added with basic information even when detailed API requests fail 