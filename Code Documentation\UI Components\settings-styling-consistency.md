# Settings Components Styling Consistency

## Files Modified
- `src/components/settings/BackupSettings.vue`
- `src/components/settings/DiscordSettings.vue`

## What Was Done
Updated BackupSettings and DiscordSettings components to match the consistent styling template established by ThemeSettings.vue.

## How It Was Fixed/Implemented

### Template Design Pattern (from ThemeSettings.vue)
- **Container**: `border-radius: 16px`, `padding: 32px` (20px on mobile)
- **Header**: `font-size: 28px`, `font-weight: 600`, `margin: 0`
- **Divider**: `margin-top: 24px`, `align-self: stretch`, proper responsive handling
- **Subtitle**: `font-size: 16px`, `font-weight: 500`, `margin-top: 20px`
- **Responsive**: Consistent mobile breakpoints and padding adjustments

### BackupSettings.vue Changes
1. **Container Border Radius**: Changed from `12px` to `16px`
2. **Container Padding**: Changed from `20px` to `32px` (20px on mobile)
3. **Header Font Size**: Changed from `22px` to `28px`
4. **Header Margin**: Changed from `margin-bottom: 8px` to `margin: 0`
5. **Divider Margin**: Changed from `margin-top: 16px` to `margin-top: 24px`
6. **Divider Structure**: Updated to match ThemeSettings pattern with `align-self: stretch`
7. **Section Margin**: Changed from `margin-top: 16px` to `margin-top: 20px`
8. **Subtitle Font Size**: Changed from `14px` to `16px`
9. **Mobile Responsive**: Added proper mobile padding (20px) to match template

### DiscordSettings.vue Changes
1. **Header Font Size**: Changed from `20px` to `28px`
2. **Header Margin**: Changed from `margin-bottom: 8px` to `margin: 0`
3. **Divider Structure**: Updated to match ThemeSettings pattern:
   - Changed from `width: 100%` to `align-self: stretch`
   - Changed from `margin-bottom: 24px` to `margin-top: 24px`
   - Added `display: flex` and `flex-shrink: 0`
4. **Subtitle Margin**: Changed from `margin: 20px 0 16px 0` to `margin-top: 20px`
5. **Mobile Responsive**: Added proper mobile padding (20px) to match template

### Consistency Benefits
- **Visual Harmony**: All settings components now have identical header sizes, spacing, and container styling
- **User Experience**: Consistent visual hierarchy and spacing creates a more professional interface
- **Maintainability**: Standardized styling patterns make future updates easier
- **Responsive Design**: Uniform mobile breakpoints and padding adjustments across all components

### Design System Alignment
The changes ensure all settings components follow the established design system:
- Consistent typography scale (28px headers, 16px subtitles)
- Uniform spacing system (24px divider margins, 20px section spacing)
- Standardized container styling (16px border radius, 32px padding)
- Proper responsive behavior across all screen sizes

This update maintains the user's preference for clean, minimalist design while ensuring visual consistency across the entire settings interface.
