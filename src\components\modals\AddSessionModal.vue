<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Add Session</h3>
          <div class="close-button" @click="handleClose">
            <img src="/icons/close-icon.svg" alt="Close" />
          </div>
        </div>
        <div class="form-container">
          <div class="form-content">
            <div class="session-info-container">
              <!-- Session Name Field -->
              <div class="form-field">
                <label>Session Name *</label>
                <input
                  v-model="sessionName"
                  type="text"
                  placeholder="e.g., Deep Work Session, Study Time, Project Work"
                  maxlength="100"
                  @blur="validateSessionName"
                  :class="{ 'input-error': sessionNameError }"
                />
                <small v-if="sessionNameError" class="error-message">{{ sessionNameError }}</small>
              </div>

              <!-- Focus Description Field -->
              <div class="form-field">
                <label>Focus Description</label>
                <textarea
                  v-model="sessionFocus"
                  placeholder="What will you be working on? (optional)"
                  maxlength="500"
                  rows="3"
                ></textarea>
                <div class="character-count">{{ sessionFocus.length }}/500</div>
              </div>

              <!-- Category Field -->
              <div class="form-field">
                <label>Category (optional)</label>
                <div
                  class="category-selector"
                  ref="categorySelector"
                  @click.stop="toggleCategoryDropdown"
                >
                  <span class="category-text">{{ selectedCategory }}</span>
                  <img
                    src="/icons/dropdown-arrow.svg"
                    class="dropdown-icon"
                    :class="{ 'open': showCategoryDropdown }"
                    alt="Select category"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer-right">
            <button class="btn btn-secondary" @click="handleClose">Cancel</button>
            <button
              class="btn btn-primary"
              @click="handleStartSession"
              :disabled="!isValid"
            >
              <img
                src="/icons/plus-icon.svg"
                class="button-icon"
                alt="Add session"
              />
              Add Session
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Category dropdown - teleported for better positioning -->
    <div
      v-if="showCategoryDropdown"
      class="category-dropdown"
      ref="categoryDropdown"
      :style="{
        position: 'fixed',
        top: dropdownPosition.top + 'px',
        left: dropdownPosition.left + 'px',
        width: dropdownPosition.width + 'px',
        maxHeight: dropdownPosition.maxHeight + 'px'
      }"
    >
      <div
        v-for="(category, index) in categories"
        :key="index"
        class="category-option"
        @click="selectCategory(category)"
      >
        {{ category }}
      </div>
      <!-- Add custom category option with improved click handling -->
      <div
        class="category-option add-new-category"
        @click="showAddNewCategoryInput"
      >
        <span class="new-category-text">+ Add New Category</span>
      </div>
      <!-- Custom category input -->
      <div v-if="showCustomCategoryInput" class="custom-category-input-container">
        <input
          type="text"
          v-model="newCategoryName"
          placeholder="Type category name..."
          class="custom-category-input"
          ref="customCategoryInput"
          @keyup.enter="addCustomCategory"
          @keyup.escape="cancelCustomCategory"
        />
        <div class="category-input-actions">
          <button
            class="category-input-button cancel"
            @click="cancelCustomCategory"
          >
            Cancel
          </button>
          <button
            class="category-input-button add"
            @click="addCustomCategory"
            :disabled="!newCategoryName.trim()"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';

export default defineComponent({
  name: 'AddSessionModal',
  emits: ['close', 'start-session'],
  setup(_, { emit }) {
    // Session data
    const sessionName = ref('');
    const sessionNameError = ref('');
    const sessionFocus = ref('');
    const selectedCategory = ref('General');
    const showCategoryDropdown = ref(false);
    const showCustomCategoryInput = ref(false);
    const newCategoryName = ref('');
    const customCategoryInput = ref<HTMLInputElement | null>(null);
    const categorySelector = ref<HTMLElement | null>(null);
    const categoryDropdown = ref<HTMLElement | null>(null);

    // Dropdown positioning state
    const dropdownPosition = ref({
      top: 0,
      left: 0,
      width: 0,
      maxHeight: 250,
      showAbove: false
    });

    // Predefined categories
    const categories = ref([
      'General',
      'Study',
      'Work',
      'Reading',
      'Writing',
      'Personal Project',
      'Other'
    ]);
    
    // Session name validation
    const validateSessionName = () => {
      if (!sessionName.value.trim()) {
        sessionNameError.value = 'Session name is required';
        return false;
      }
      if (sessionName.value.length > 100) {
        sessionNameError.value = 'Session name must be 100 characters or less';
        return false;
      }
      sessionNameError.value = '';
      return true;
    };

    // Form validation
    const isValid = computed(() => {
      return sessionName.value.trim().length > 0 &&
             sessionName.value.length <= 100;
    });

    // Event handlers following the established modal pattern
    const handleClose = () => {
      emit('close');
    };

    const handleStartSession = () => {
      if (!isValid.value || !validateSessionName()) return;

      emit('start-session', {
        sessionName: sessionName.value.trim(),
        focus: sessionFocus.value.trim(),
        category: selectedCategory.value
      });

      // Reset form
      sessionName.value = '';
      sessionNameError.value = '';
      sessionFocus.value = '';
      selectedCategory.value = 'General';
      showCustomCategoryInput.value = false;
    };

    // Legacy method for backward compatibility
    const onClose = () => {
      handleClose();
    };

    // Calculate dropdown position with viewport boundary detection
    const calculateDropdownPosition = () => {
      if (!categorySelector.value) return;
      
      const selectorRect = categorySelector.value.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      
      // Calculate available space above and below
      const spaceBelow = viewportHeight - selectorRect.bottom;
      const spaceAbove = selectorRect.top;
      
      // Determine if dropdown should appear above or below
      const preferredMaxHeight = 250;
      const minSpaceRequired = Math.min(preferredMaxHeight, 150); // Minimum viable height
      
      let showAbove = false;
      let maxHeight = preferredMaxHeight;
      
      if (spaceBelow < minSpaceRequired && spaceAbove > spaceBelow) {
        // Show above if there's more space above
        showAbove = true;
        maxHeight = Math.min(preferredMaxHeight, spaceAbove - 10); // 10px padding from viewport edge
      } else {
        // Show below (default)
        maxHeight = Math.min(preferredMaxHeight, spaceBelow - 10);
      }
      
      // Update dropdown position
      dropdownPosition.value = {
        top: showAbove ? selectorRect.top - maxHeight - 5 : selectorRect.bottom + 5,
        left: selectorRect.left,
        width: selectorRect.width,
        maxHeight,
        showAbove
      };
    };
    
    const toggleCategoryDropdown = () => {
      // If showing custom input, hide dropdown instead of toggling
      if (showCustomCategoryInput.value) {
        showCustomCategoryInput.value = false;
        return;
      }
      
      if (!showCategoryDropdown.value) {
        // Calculate position before showing
        calculateDropdownPosition();
      }
      
      showCategoryDropdown.value = !showCategoryDropdown.value;
    };
    
    const selectCategory = (category: string) => {
      selectedCategory.value = category;
      showCategoryDropdown.value = false;
    };
    
    // Legacy method for backward compatibility
    const startSession = () => {
      handleStartSession();
    };
    
    // Custom category methods with improved click handling
    const showAddNewCategoryInput = (event: Event) => {
      // Prevent event propagation to avoid conflicts
      event.preventDefault();
      event.stopPropagation();
      
      // Show the custom input with visual feedback
      showCustomCategoryInput.value = true;
      
      // Focus the input after DOM update
      nextTick(() => {
        if (customCategoryInput.value) {
          customCategoryInput.value.focus();
        }
      });
    };
    
    const addCustomCategory = () => {
      const categoryName = newCategoryName.value.trim();
      if (categoryName) {
        // Don't add duplicates
        if (!categories.value.includes(categoryName)) {
          categories.value.push(categoryName);
        }
        
        // Select the new category
        selectedCategory.value = categoryName;
        
        // Hide inputs and reset
        showCustomCategoryInput.value = false;
        showCategoryDropdown.value = false;
        newCategoryName.value = '';
      }
    };
    
    const cancelCustomCategory = () => {
      newCategoryName.value = '';
      showCustomCategoryInput.value = false;
    };
    
    // Focus custom category input when shown
    watch(showCustomCategoryInput, async (newVal) => {
      if (newVal) {
        await nextTick();
        if (customCategoryInput.value) {
          customCategoryInput.value.focus();
        }
      }
    });
      // Close dropdown when clicking outside
    const closeDropdownOnClickOutside = (event: MouseEvent) => {
      // Get clicked element
      const target = event.target as HTMLElement;
      
      // Check if clicked element is inside the dropdown or selector
      const isClickInDropdown = target.closest('.category-dropdown');
      const isClickInSelector = target.closest('.category-selector');
      
      if (showCategoryDropdown.value && !isClickInDropdown && !isClickInSelector) {
        showCategoryDropdown.value = false;
      }
      
      // Handle custom category input
      if (showCustomCategoryInput.value) {
        const isClickInCustomInput = target.closest('.custom-category-input-container');
        if (!isClickInCustomInput) {
          showCustomCategoryInput.value = false;
        }
      }
    };
    
    // Window resize handler for dropdown repositioning
    const handleWindowResize = () => {
      if (showCategoryDropdown.value) {
        calculateDropdownPosition();
      }
    };

    // Escape key handler
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // If dropdown is open, close it first
        if (showCategoryDropdown.value) {
          showCategoryDropdown.value = false;
        } else if (showCustomCategoryInput.value) {
          showCustomCategoryInput.value = false;
        } else {
          // Close the modal
          emit('close');
        }
      }
    };

    // Add global event listeners
    onMounted(() => {
      document.addEventListener('keydown', handleEscapeKey);
      window.addEventListener('click', closeDropdownOnClickOutside);
      window.addEventListener('resize', handleWindowResize);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleEscapeKey);
      window.removeEventListener('click', closeDropdownOnClickOutside);
      window.removeEventListener('resize', handleWindowResize);
    });
    
    return {
      sessionName,
      sessionNameError,
      sessionFocus,
      selectedCategory,
      categories,
      showCategoryDropdown,
      showCustomCategoryInput,
      newCategoryName,
      customCategoryInput,
      categorySelector,
      categoryDropdown,
      dropdownPosition,
      isValid,
      validateSessionName,
      handleClose,
      handleStartSession,
      onClose, // Legacy support
      toggleCategoryDropdown,
      selectCategory,
      startSession, // Legacy support
      showAddNewCategoryInput,
      addCustomCategory,
      cancelCustomCategory
    };
  }
});
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

/* Modal Content */
.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  width: 550px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: 90vh;
}

/* Modal Header */
.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

/* Form Container */
.form-container {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.form-content {
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 20px;
  position: relative;
  background-color: var(--color-bg-tertiary);
}

.session-info-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.form-field label {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.form-field input,
.form-field select,
.form-field textarea {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  outline: none;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
  background-color: var(--color-input-bg);
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
}

.form-field textarea {
  resize: none;
  min-height: 80px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

.form-field textarea::-webkit-scrollbar {
  width: 8px;
}

.form-field textarea::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.form-field textarea::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: 4px;
  border: 2px solid var(--color-scrollbar-track);
}

.form-field textarea::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-thumb-hover);
}

.form-field .input-error {
  border-color: var(--color-error);
  background-color: rgba(229, 57, 53, 0.05);
}

.form-field .error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-top: 2px;
  display: block;
}

.character-count {
  color: var(--color-text-tertiary);
  font-size: 12px;
  text-align: right;
  margin-top: 4px;
}

.category-selector {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.category-selector:hover {
  border-color: var(--color-input-focus);
}

.category-selector:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
  outline: none;
}

.category-text {
  flex: 1;
  text-align: left;
}

.dropdown-icon {
  width: 12px;
  height: 12px;
  transition: transform 0.2s ease;
  margin-left: 8px;
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.category-dropdown {
  border-radius: 5px;
  background-color: var(--color-modal-bg);
  border-color: var(--color-border-primary);
  border-style: solid;
  border-width: 1px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  z-index: 10002;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
  isolation: isolate;
}

.category-dropdown::-webkit-scrollbar {
  width: 8px;
}

.category-dropdown::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.category-dropdown::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.category-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.category-option {
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.category-option:hover {
  background-color: var(--color-nav-item-hover);
}

.add-new-category {
  color: var(--color-text-primary);
  font-weight: 500;
  border-top: 1px solid var(--color-border-secondary);
  transition: all 0.2s ease;
}

.add-new-category:hover {
  background-color: var(--color-nav-item-hover) !important;
  transform: translateY(-1px);
}

.add-new-category:active {
  transform: translateY(0);
  background-color: var(--color-btn-secondary-hover) !important;
}

.new-category-text {
  display: flex;
  align-items: center;
}

.custom-category-input-container {
  padding: 10px;
  border-top: 1px solid var(--color-border-secondary);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.custom-category-input {
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-size: 14px;
  padding: 8px;
  border: 1px solid var(--color-input-border);
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  outline: none;
  transition: border-color 0.2s ease;
}

.custom-category-input:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
}

.category-input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.category-input-button {
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-style: solid;
  border-width: 1px;
}

.category-input-button.cancel {
  background-color: transparent;
  border-color: var(--color-btn-secondary-border);
  color: var(--color-btn-secondary-text);
  padding: 8px 16px;
  font-size: 14px;
  min-height: 36px;
}

.category-input-button.cancel:hover {
  background-color: var(--color-nav-item-hover);
  border-color: var(--color-border-hover);
}

.category-input-button.add {
  background-color: var(--color-btn-primary-bg);
  border-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  padding: 8px 16px;
  font-size: 14px;
  min-height: 36px;
}

.category-input-button.add:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

.category-input-button.add:active:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

.category-input-button.add:focus:not(:disabled) {
  background-color: var(--color-btn-primary-bg);
  border-color: var(--color-btn-primary-bg);
  outline: none;
}

.category-input-button.add:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Footer */
.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-modal-border);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.modal-footer-right {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 10px 24px;
  min-width: 120px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .form-container,
  .modal-footer {
    padding: 16px 20px;
  }

  .modal-header h3 {
    font-size: 24px;
  }

  .form-content {
    padding: 16px;
  }
}

@media (max-width: 640px) {
  .modal-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: 100vh;
  }

  .modal-header,
  .form-container,
  .modal-footer {
    padding: 12px 16px;
  }

  .form-content {
    padding: 12px;
  }

  .session-info-container {
    gap: 12px;
  }

  .modal-footer-right {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
  }
}
</style>
