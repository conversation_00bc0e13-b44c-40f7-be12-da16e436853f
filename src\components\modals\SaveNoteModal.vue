<template>
  <div class="save-note-modal">
    <div class="close-icon" @click="$emit('close')">
      <img src="/icons/close-icon.svg" alt="Close" />
    </div>
    
    <div class="modal-header">
      <h1 class="modal-title">Save Note</h1>
      <p class="modal-subtitle">
        <template v-if="notes.length > 1">
          Select a location to save {{notes.length}} notes
        </template>
        <template v-else>
          Select a location to save your note
        </template>
      </p>
    </div>
    
    <div class="divider"></div>
    
    <div class="folder-section">
      <div class="file-type-header">
        <img src="/icons/folder-icon.svg" class="folder-icon-header" alt="Folder" />
        <span class="folder-name">Folder Name</span>
      </div>
      
      <div class="file-type-separator"></div>
      
      <!-- Scrollable folder list with custom scroll behavior -->
      <div class="folders-container" ref="foldersContainer" @wheel="handleScroll">
        <!-- Root folder option -->
        <div class="folder-item" 
             :class="{ 'selected': selectedFolderId === null }"
             @click="selectFolder(null)">
          <img src="/icons/folder-icon.svg" class="folder-icon" alt="Root Folder" />
          <span class="folder-label">Root</span>
        </div>
        
        <!-- Folder list -->
        <div v-for="folder in folders" 
             :key="folder.id" 
             class="folder-item"
             :class="{ 'selected': selectedFolderId === folder.id }"
             @click="selectFolder(folder.id)">
          <img src="/icons/folder-icon.svg" class="folder-icon" alt="Folder" />
          <span class="folder-label">{{ folder.name }}</span>
        </div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="modal-footer">
      <button class="btn btn-new-folder" @click="$emit('newFolder')">
        <img src="/icons/plus-icon.svg" class="new-folder-icon" alt="New Folder" />
        New Folder
      </button>
      
      <div class="action-buttons">
        <button class="btn btn-cancel" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn btn-save" @click="save" :disabled="!canSave">
          <img src="/icons/save-icon.svg" class="save-icon" alt="Save" />
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, computed } from 'vue'

interface Note {
  id?: number;
  title: string;
  content?: string;
  folder_id?: number | null;
}

interface Folder {
  id: number;
  name: string;
  parent_id?: number | null;
}

export default defineComponent({
  name: 'SaveNoteModal',
  props: {
    notes: {
      type: Array as PropType<Note[]>,
      required: true
    },
    folders: {
      type: Array as PropType<Folder[]>,
      default: () => [
        { id: 1, name: 'Getting Started' },
        { id: 2, name: 'Projects' },
        { id: 3, name: 'Books' },
        { id: 4, name: 'Personal' },
        { id: 5, name: 'Work' },
        { id: 6, name: 'Work' },
        { id: 7, name: 'Work' },
        { id: 8, name: 'Work' },
        { id: 9, name: 'Work' },
        { id: 10, name: 'Work' },
        { id: 11, name: 'Work' }
      ]
    }
  },
  emits: ['close', 'save', 'newFolder'],
  setup(props, { emit }) {
    // Set the initial selected folder to the first note's current folder (or null if different folders)
    const getInitialFolderId = () => {
      if (props.notes.length === 0) return null;
      if (props.notes.length === 1) return props.notes[0].folder_id ?? null;
      
      // For multiple notes, check if they're all in the same folder
      const firstFolder = props.notes[0].folder_id ?? null;
      const allSameFolder = props.notes.every(note => (note.folder_id ?? null) === firstFolder);
      return allSameFolder ? firstFolder : null;
    };
    
    const selectedFolderId = ref<number | null>(getInitialFolderId());
    const foldersContainer = ref<HTMLElement | null>(null);
    
    const canSave = computed(() => {
      // Allow saving if a folder is selected and it's different from the current state
      if (props.notes.length === 0) return false;
      if (props.notes.length === 1) {
        return selectedFolderId.value !== (props.notes[0].folder_id ?? null);
      }
      
      // For multiple notes, allow saving if we have a selection
      return selectedFolderId.value !== getInitialFolderId();
    });
    
    const selectFolder = (folderId: number | null) => {
      selectedFolderId.value = folderId;
    };
    
    const save = () => {
      emit('save', selectedFolderId.value);
    };
    
    // Updated scroll handler for faster response
    const handleScroll = (event: WheelEvent) => {
      if (!foldersContainer.value) return;
      
      // Don't prevent default for fast scrolling
      const isFastScroll = Math.abs(event.deltaY) > 50;
      
      if (!isFastScroll) {
        // For slow scrolls, we still want controlled scrolling
        event.preventDefault();
        
        // For gentle scrolls, move exactly one item
        const folderItemHeight = 56;
        foldersContainer.value.scrollBy({
          top: Math.sign(event.deltaY) * folderItemHeight,
          behavior: 'smooth'
        });
      }
      // For fast scrolls, let the native scrolling happen (much faster)
      // No need to do anything as we're not preventing default
    };
    
    return {
      selectedFolderId,
      foldersContainer,
      canSave,
      selectFolder,
      save,
      handleScroll
    };
  }
})
</script>

<style scoped>
.save-note-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-shadow);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: #E3E3E3;
  width: 100%;
}

.folder-section {
  padding: 40px 40px 0 40px;
}

.file-type-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #777777;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.folder-icon-header {
  width: 24px;
  height: 24px;
}

.file-type-separator {
  height: 1px;
  background-color: #CCC7C7;
  width: 100%;
  margin-bottom: 24px;
}

/* Scrollable container for folder items */
.folders-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 40px;
  padding-right: 10px; /* Space for scrollbar */
  scroll-snap-type: y proximity; /* Proximity for more natural scrolling */
}

/* Custom scrollbar styling */
.folders-container::-webkit-scrollbar {
  width: 8px;
}

.folders-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0 8px 8px 0;
}

.folders-container::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 4px;
}

.folders-container::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.folder-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid #E3E3E3;
  color: #777777;
  transition: background-color 0.2s ease;
  border-radius: 8px;
  height: 56px; /* Fixed height for predictable scrolling */
  box-sizing: border-box;
  scroll-snap-align: start;
}

.folder-item:hover {
  color: #4A4A4A;
  background-color: #f5f5f5;
}

.folder-item.selected {
  color: #4A4A4A;
  font-weight: 500;
  background-color: #f0f0f0;
}

.folder-icon {
  width: 24px;
  height: 24px;
}

.folder-label {
  font-size: 16px;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 24px 40px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: 1px solid #E3E3E3;
  background-color: #F8F8F8;
  color: #4A4A4A;
}

.btn-new-folder {
  background-color: white;
}

.btn-save {
  background-color: #4A4A4A;
  color: white;
  border: none;
}

.btn-save:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.new-folder-icon, .folder-icon {
  width: 16px;
  height: 16px;
}

.save-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}
</style>