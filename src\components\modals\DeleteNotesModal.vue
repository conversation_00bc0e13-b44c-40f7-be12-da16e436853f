<template>
  <div class="delete-modal">
    <div class="close-icon" @click="$emit('close')">
      <img src="/icons/close-icon.svg" alt="Close" />
    </div>
    
    <div class="modal-header">
      <h1 class="modal-title">Delete {{ multipleNotes ? 'Notes' : 'Note' }}</h1>
      <p class="modal-subtitle" v-if="!multipleNotes">
        Are you sure you want to delete "{{ noteTitle }}"?
      </p>
      <p class="modal-subtitle" v-else>
        Are you sure you want to delete {{ notesCount }} {{ notesCount === 1 ? 'note' : 'notes' }}?
      </p>
      <div class="notes-list" v-if="multipleNotes && noteTitles.length > 0">
        <ul>
          <li v-for="(title, index) in displayedTitles" :key="index">{{ title }}</li>
        </ul>
        <p v-if="noteTitles.length > maxDisplayed" class="more-notes">
          ...and {{ noteTitles.length - maxDisplayed }} more
        </p>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="modal-footer">
      <button class="btn btn-cancel" @click="$emit('cancel')">
        Cancel
      </button>
      <button class="btn btn-delete" @click="$emit('delete')">
        <img src="/icons/trash-icon.svg" class="delete-icon" alt="Delete" />
        Delete {{ multipleNotes ? 'All' : '' }}
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'DeleteNotesModal',
  props: {
    noteTitle: {
      type: String,
      default: ''
    },
    noteTitles: {
      type: Array as () => string[],
      default: () => []
    },
    notesCount: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    const multipleNotes = computed(() => props.notesCount > 1);
    const maxDisplayed = 5;
    const displayedTitles = computed(() => {
      return props.noteTitles.slice(0, maxDisplayed);
    });

    return {
      multipleNotes,
      maxDisplayed,
      displayedTitles
    };
  },
  emits: ['close', 'delete', 'cancel']
})
</script>

<style scoped>
.delete-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-hover-shadow);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
  line-height: 1.5;
}

.notes-list {
  margin-top: 24px;
  max-height: 200px;
  overflow-y: auto;
  padding: 16px;
  background-color: var(--color-bg-tertiary);
  border-radius: 10px;
  border: 1px solid var(--color-border-primary);
}

/* Custom scrollbar styling for notes list */
.notes-list::-webkit-scrollbar {
  width: 8px;
}

.notes-list::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.notes-list::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.notes-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.notes-list ul {
  margin: 0;
  padding-left: 24px;
}

.notes-list li {
  margin-bottom: 8px;
  color: var(--color-text-primary);
  font-size: 16px;
}

.more-notes {
  font-style: italic;
  color: var(--color-text-secondary);
  font-size: 16px;
  margin: 12px 0 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-delete {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
}

.delete-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}
</style>
