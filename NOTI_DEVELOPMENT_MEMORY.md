# Noti Development Memory - Critical Information

## 🚨 ALWAYS REMEMBER

### 1. IPC is MANDATORY
```typescript
// ✅ CORRECT
await window.electronAPI.db.notes.create(noteData)

// ❌ WRONG - Direct database access
db.run('INSERT INTO notes...')
```

### 2. Backup System is Complex - Test Thoroughly
- Components: change-detector → backup-engine → storage-manager → state-validator
- Common bugs: Double processing, event timing, cleanup failures
- Check `processedNoteIds` to prevent duplicates

### 3. Timer State Resets on Navigation (By Design)
- Sessions save at cycle completion only
- Use `shouldAutoStart` parameter explicitly
- State doesn't persist across view changes

### 4. CSS Variables Only for Colors
```css
/* ✅ CORRECT */
color: var(--text-color);
background: var(--bg-color);

/* ❌ WRONG */
color: #000000;
background: white;
```

### 5. Memory Leak Checklist
- [ ] Clear timeouts in `onUnmounted`
- [ ] Destroy Chart.js instances
- [ ] Remove event listeners
- [ ] Cancel pending API requests

### 6. Common Bug Patterns
- **Race Conditions**: Folder creation + auto-backup, book addition + cover download
- **Modal Z-index**: Must be > 10000
- **Search Performance**: Always debounce
- **Event Timing**: Database hooks fire before operations complete

### 7. Pre-Commit Checklist
- [ ] Run `npm run build` - MANDATORY
- [ ] Test in both light/dark themes
- [ ] Test with empty and populated database
- [ ] Document changes in `Code Documentation/`

## 🔧 Quick Reference

### Adding New Feature
1. Create API module: `electron/main/api/feature-api.ts`
2. Add IPC handler: `electron/main/ipc-handlers.ts`
3. Expose in: `electron/preload/api-bridge.ts`
4. Add types: `src/types/electron-api.d.ts`
5. Use: `window.electronAPI.db.feature.method()`

### File Paths
- **Media**: Use `noti-media://` protocol
- **Bash**: Quote paths with spaces
- **Main Process**: Always absolute paths

### Special Folders
- "Books" folder is immutable (created on init)
- Book-folder link via `book_id` field

### Database Patterns
- Use transactions for multi-table ops
- CASCADE DELETE on foreign keys
- Don't manually update `updated_at`

## 🐛 Debug First Checks

1. **UI Not Updating?** → Check theme variables
2. **IPC Not Working?** → Check handler exists
3. **Backup Issues?** → Check event timing
4. **Build Failing?** → Run `vue-tsc --noEmit`
5. **Modal Behind Others?** → Check z-index > 10000

## 📝 Documentation Required
Every change needs docs in `Code Documentation/`:
- Bug fixes → `Bug Fixes/[Component]/`
- Features → `Feature Implementations/[Component]/`
- Analysis → `Code Analysis/`

Format: `kebab-case-description.md`