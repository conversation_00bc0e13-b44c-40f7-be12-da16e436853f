<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Session Details</h3>
          <div class="close-button" @click="handleClose">
            <img src="/icons/close-icon.svg" alt="Close" />
          </div>
        </div>

        <div class="form-container">
          <div class="form-content">
            <div class="session-info-container">
              <!-- Session Title -->
              <div class="session-title-section">
                <h4 class="session-title">{{ sessionData.sessionName || 'Unnamed Session' }}</h4>
                <div class="status-badge" :class="{ 'status-active': sessionData.isActive, 'status-finished': !sessionData.isActive }">
                  {{ sessionData.isActive ? 'Active' : 'Finished' }}
                </div>
              </div>

              <!-- Main session details -->
              <div class="form-section">
                <div class="form-row">
                  <div class="form-field">
                    <label>Category</label>
                    <div class="field-display">{{ sessionData.category || 'No Category' }}</div>
                  </div>

                  <div class="form-field" v-if="sessionData.focus">
                    <label>Focus Area</label>
                    <div class="field-display">{{ sessionData.focus }}</div>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-field">
                    <label>Date</label>
                    <div class="field-display">{{ formattedDate }}</div>
                  </div>

                  <div class="form-field">
                    <label>Start Time</label>
                    <div class="field-display">{{ formattedStartTime }}</div>
                  </div>
                </div>

                <div class="form-row" v-if="sessionData.endTime">
                  <div class="form-field">
                    <label>End Time</label>
                    <div class="field-display">{{ formattedEndTime }}</div>
                  </div>

                  <div class="form-field">
                    <label>Duration</label>
                    <div class="field-display">{{ sessionDuration }}</div>
                  </div>
                </div>

                <!-- Performance metrics -->
                <div class="metrics-section">
                  <div class="metric-card">
                    <div class="metric-icon">
                      <svg width="24" height="24" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.0275 5.24047C13.8695 5.02682 13.5683 4.98178 13.3546 5.13963C13.141 5.29763 13.0958 5.59885 13.2538 5.81253C13.9718 6.78357 14.3513 7.91363 14.3513 9.08063C14.3513 12.3654 11.3481 15.0378 7.65678 15.0378C3.96544 15.0378 0.962344 12.3654 0.962344 9.0806C0.962344 7.25644 1.91009 5.53044 3.51313 4.40338L4.32928 5.02278L3.76716 6.85878C3.71359 7.03366 3.76387 7.22375 3.89681 7.34938C4.02978 7.475 4.22247 7.51425 4.394 7.45091L7.65713 6.24466L10.9203 7.45091C10.9746 7.47097 11.031 7.48078 11.087 7.48078C11.2078 7.48078 11.3266 7.43522 11.4175 7.34938C11.5504 7.22382 11.6007 7.03363 11.5471 6.85878L10.9848 5.02278L13.1612 3.371C13.3136 3.25535 13.383 3.06038 13.3379 2.87444C13.2928 2.68853 13.1419 2.54694 12.9535 2.51385C12.3403 2.40622 11.682 2.37813 10.9981 2.43028C9.73313 2.52685 8.67819 2.87816 8.13778 3.09053V1.57541L9.22463 0.887753C9.44916 0.745691 9.51603 0.448503 9.37397 0.223941C9.23194 -0.000590593 8.93475 -0.0674656 8.71016 0.0745969L7.39941 0.903909C7.26003 0.992097 7.17553 1.14553 7.17553 1.3105V3.09035C6.63119 2.87672 5.57697 2.52653 4.316 2.43025C4.09297 2.41316 3.87309 2.4045 3.66253 2.4045H3.65166C3.20563 2.4045 2.77087 2.44116 2.35962 2.51353C2.17122 2.54663 2.02038 2.68822 1.97534 2.87419C1.93031 3.06013 1.99975 3.25503 2.15209 3.37066L2.71306 3.79641C1.00275 5.10482 0 7.0366 0 9.08063C9.375e-05 12.896 3.43491 16 7.65681 16C11.8787 16 15.3135 12.896 15.3135 9.08063C15.3135 7.70619 14.8688 6.37832 14.0275 5.24047ZM4.24284 3.38969C5.81606 3.50978 7.05328 4.079 7.14697 4.12297L7.44659 4.2686C7.57916 4.33297 7.73388 4.33313 7.86656 4.26894L8.16788 4.12307C8.18056 4.117 9.45381 3.51319 11.0715 3.38969C11.241 3.37675 11.4098 3.36925 11.5745 3.36732L10.1357 4.45925C9.97497 4.58122 9.90741 4.7905 9.9665 4.98338L10.3426 6.21138L7.82403 5.28038C7.71641 5.2406 7.59806 5.2406 7.49038 5.28038L4.97169 6.21144L5.34769 4.98335C5.40678 4.79047 5.33916 4.58122 5.17847 4.45925L3.73944 3.36716C3.90222 3.369 4.07119 3.37657 4.24284 3.38969Z" fill="#4A4A4A"/>
                        <path d="M7.65676 13.5944C4.7613 13.5944 2.40564 11.5695 2.40564 9.08061C2.40564 8.81489 2.62105 8.59949 2.88676 8.59949C3.15248 8.59949 3.36789 8.81489 3.36789 9.08061C3.36789 11.039 5.29189 12.6322 7.65676 12.6322C7.92248 12.6322 8.13789 12.8476 8.13789 13.1133C8.13789 13.379 7.92248 13.5944 7.65676 13.5944Z" fill="#4A4A4A"/>
                        <path d="M3.65183 2.88577C3.85699 2.88549 4.06618 2.89377 4.27946 2.91005C6.00393 3.04167 7.34287 3.68358 7.35487 3.6892L7.6569 3.83599L7.95821 3.69011C7.9714 3.68358 9.3104 3.0417 11.0349 2.91002C11.6836 2.86052 12.2956 2.88686 12.8704 2.98777L10.4265 4.84258L11.0871 6.99967L7.65715 5.7318L4.22721 6.99967L4.88758 4.84258L2.44312 2.98745C2.82937 2.91955 3.23221 2.88577 3.65183 2.88577Z" fill="#4A4A4A"/>
                      </svg>
                    </div>
                    <div class="metric-content">
                      <div class="metric-label">Pomodoro Cycles</div>
                      <div class="metric-value">{{ formattedPomodoroCount }}</div>
                    </div>
                  </div>

                  <div class="metric-card">
                    <div class="metric-icon">
                      <svg width="24" height="24" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.66699 1.33337H9.33366" stroke="#4A4A4A" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M8 3.33337V6.00004" stroke="#4A4A4A" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M8.00033 14.6667C10.9458 14.6667 13.3337 12.2789 13.3337 9.33333C13.3337 6.38781 10.9458 4 8.00033 4C5.05481 4 2.66699 6.38781 2.66699 9.33333C2.66699 12.2789 5.05481 14.6667 8.00033 14.6667Z" stroke="#4A4A4A" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                    </div>
                    <div class="metric-content">
                      <div class="metric-label">Total Focus Time</div>
                      <div class="metric-value">{{ formattedFocusTime }}</div>
                      <div class="metric-subtext">{{ formattedFocusTimeDetailed }}</div>
                    </div>
                  </div>
                </div>

                <!-- Productivity insights -->
                <div class="insights-section" v-if="!sessionData.isActive">
                  <div class="insights-header">
                    <h5>Session Insights</h5>
                  </div>
                  <div class="insights-grid">
                    <div class="insight-item">
                      <div class="insight-label">Productivity Score</div>
                      <div class="insight-value" :class="productivityScoreClass">{{ productivityScore }}%</div>
                    </div>
                    <div class="insight-item">
                      <div class="insight-label">Average per Pomodoro</div>
                      <div class="insight-value">{{ averageTimePerPomodoro }}</div>
                    </div>
                    <div class="insight-item">
                      <div class="insight-label">Session Efficiency</div>
                      <div class="insight-value">{{ sessionEfficiency }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer-left">
            <button class="btn btn-danger" @click="handleDelete">
              <img src="/icons/trash-icon.svg" class="delete-icon" alt="Delete" />
              Delete Session
            </button>
          </div>
          <div class="modal-footer-right">
            <button class="btn btn-secondary" @click="handleClose">Close</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <div v-if="showDeleteConfirmation" class="confirmation-overlay" @click="cancelDelete">
      <div class="confirmation-dialog" @click.stop>
        <div class="confirmation-header">
          <h4>Delete Session</h4>
        </div>
        <div class="confirmation-content">
          <p>Are you sure you want to delete "{{ sessionData.sessionName || 'Unnamed Session' }}"?</p>
          <p class="warning-text">This action cannot be undone.</p>
        </div>
        <div class="confirmation-footer">
          <button class="btn btn-secondary" @click="cancelDelete">Cancel</button>
          <button class="btn btn-danger" @click="confirmDelete">
            <img src="/icons/trash-icon.svg" class="delete-icon" alt="Delete" />
            Delete
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, computed, PropType, ref } from 'vue';

interface SessionData {
  id?: number;
  sessionName: string;
  focus?: string | null;
  category: string;
  date: Date;
  endTime?: Date | null;
  totalFocusTime: number;
  pomodoroCount: number;
  isActive: boolean;
}

export default defineComponent({
  name: 'SessionDetailsModal',
  props: {
    sessionData: {
      type: Object as PropType<SessionData>,
      required: true
    }
  },
  emits: ['close', 'delete'],
  setup(props, { emit }) {
    // State for confirmation dialog
    const showDeleteConfirmation = ref(false);

    // Format the date as "DD Month YYYY"
    const formattedDate = computed(() => {
      const options: Intl.DateTimeFormatOptions = {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      };
      return props.sessionData.date.toLocaleDateString('en-US', options);
    });

    // Format start time
    const formattedStartTime = computed(() => {
      return props.sessionData.date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    });

    // Format end time (if available)
    const formattedEndTime = computed(() => {
      if (!props.sessionData.endTime) return '';
      return props.sessionData.endTime.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    });

    // Format focus time (in seconds) to a readable format
    const formattedFocusTime = computed(() => {
      const totalMinutes = Math.floor(props.sessionData.totalFocusTime / 60);
      if (totalMinutes < 60) {
        return `${totalMinutes}m`;
      } else {
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        if (minutes === 0) {
          return `${hours}h`;
        } else {
          return `${hours}h ${minutes}m`;
        }
      }
    });

    // Detailed focus time breakdown
    const formattedFocusTimeDetailed = computed(() => {
      const totalMinutes = Math.floor(props.sessionData.totalFocusTime / 60);
      const totalSeconds = props.sessionData.totalFocusTime % 60;
      
      if (totalMinutes < 60) {
        return `${totalMinutes} minutes, ${totalSeconds} seconds`;
      } else {
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        return `${hours} hour${hours !== 1 ? 's' : ''}, ${minutes} minute${minutes !== 1 ? 's' : ''}, ${totalSeconds} second${totalSeconds !== 1 ? 's' : ''}`;
      }
    });

    // Calculate session duration - total time from session creation to completion
    const sessionDuration = computed(() => {
      if (!props.sessionData.endTime) return 'Ongoing';

      // Calculate total elapsed time from session start to end (includes breaks, pauses, etc.)
      const startTime = props.sessionData.date.getTime();
      const endTime = props.sessionData.endTime.getTime();
      const durationMs = endTime - startTime;
      const durationMinutes = Math.floor(durationMs / (1000 * 60));

      if (durationMinutes < 60) {
        return `${durationMinutes} minutes`;
      } else {
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        if (minutes === 0) {
          return `${hours} hour${hours !== 1 ? 's' : ''}`;
        } else {
          return `${hours} hour${hours !== 1 ? 's' : ''}, ${minutes} minute${minutes !== 1 ? 's' : ''}`;
        }
      }
    });

    // Format pomodoro count with proper pluralization
    const formattedPomodoroCount = computed(() => {
      if (props.sessionData.pomodoroCount === 1) {
        return `${props.sessionData.pomodoroCount} Pomodoro completed`;
      } else {
        return `${props.sessionData.pomodoroCount} Pomodoros completed`;
      }
    });

    // Calculate productivity insights
    const productivityScore = computed(() => {
      if (props.sessionData.isActive || props.sessionData.pomodoroCount === 0) return 0;

      // Calculate based on focus time vs expected time (25 min per pomodoro)
      const expectedTime = props.sessionData.pomodoroCount * 25 * 60; // 25 minutes per pomodoro in seconds
      const actualFocusTime = props.sessionData.totalFocusTime;
      const score = Math.min(100, Math.round((actualFocusTime / expectedTime) * 100));
      return score;
    });

    const productivityScoreClass = computed(() => {
      const score = productivityScore.value;
      if (score >= 80) return 'score-excellent';
      if (score >= 60) return 'score-good';
      if (score >= 40) return 'score-fair';
      return 'score-poor';
    });

    const averageTimePerPomodoro = computed(() => {
      if (props.sessionData.pomodoroCount === 0) return '0m';

      const avgSeconds = props.sessionData.totalFocusTime / props.sessionData.pomodoroCount;
      const avgMinutes = Math.round(avgSeconds / 60);
      return `${avgMinutes}m`;
    });

    const sessionEfficiency = computed(() => {
      if (!props.sessionData.endTime || props.sessionData.isActive) return 'N/A';

      const totalSessionTime = props.sessionData.endTime.getTime() - props.sessionData.date.getTime();
      const totalSessionMinutes = totalSessionTime / (1000 * 60);
      const focusMinutes = props.sessionData.totalFocusTime / 60;
      const efficiency = Math.round((focusMinutes / totalSessionMinutes) * 100);
      return `${efficiency}%`;
    });

    const handleClose = () => {
      emit('close');
    };

    const handleDelete = () => {
      showDeleteConfirmation.value = true;
    };

    const confirmDelete = () => {
      emit('delete', props.sessionData.id);
      showDeleteConfirmation.value = false;
    };

    const cancelDelete = () => {
      showDeleteConfirmation.value = false;
    };

    return {
      showDeleteConfirmation,
      formattedDate,
      formattedStartTime,
      formattedEndTime,
      formattedFocusTime,
      formattedFocusTimeDetailed,
      formattedPomodoroCount,
      sessionDuration,
      productivityScore,
      productivityScoreClass,
      averageTimePerPomodoro,
      sessionEfficiency,
      handleClose,
      handleDelete,
      confirmDelete,
      cancelDelete
    };
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 650px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: 90vh;
}

.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

.form-container {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.form-content {
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 20px;
  position: relative;
  background-color: var(--color-bg-secondary);
}

.session-info-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
}

.session-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border-primary);
  margin-bottom: 8px;
}

.session-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text-primary);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: var(--color-nav-item-active);
  color: var(--color-primary);
}

.status-finished {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-tertiary);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.form-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.field-display {
  padding: 12px 16px;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  background-color: var(--color-card-bg);
  font-size: 14px;
  color: var(--color-text-primary);
  min-height: 20px;
  display: flex;
  align-items: center;
}

.metrics-section {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.metric-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  background-color: var(--color-card-bg);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--color-nav-item-active);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 2px;
}

.metric-subtext {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.modal-footer-left {
  display: flex;
  gap: 16px;
}

.modal-footer-right {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 10px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
}

.btn-danger:hover {
  background-color: #B71C1C;
}

.delete-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

/* Removed .metric-svg styles to allow icons to use their original colors */

/* Insights Section */
.insights-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-primary);
}

.insights-header {
  margin-bottom: 16px;
}

.insights-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.insight-item {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-secondary);
}

.insight-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.insight-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-text-primary);
}

.score-excellent {
  color: var(--color-success);
}

.score-good {
  color: #4A90E2;
}

.score-fair {
  color: var(--color-warning);
}

.score-poor {
  color: var(--color-error);
}

/* Confirmation Dialog Styles */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11000;
}

.confirmation-dialog {
  background-color: var(--color-modal-bg);
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 400px;
  max-width: 90%;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
}

.confirmation-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border-primary);
}

.confirmation-header h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.confirmation-content {
  padding: 20px 24px;
}

.confirmation-content p {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: var(--color-text-primary);
  line-height: 1.4;
}

.confirmation-content p:last-child {
  margin-bottom: 0;
}

.warning-text {
  color: var(--color-error) !important;
  font-size: 14px !important;
  font-weight: 500;
}

.confirmation-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .metrics-section {
    flex-direction: column;
  }

  .session-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .insights-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 640px) {
  .modal-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: 100vh;
  }

  .modal-header,
  .form-container,
  .modal-footer {
    padding: 16px 20px;
  }

  .modal-header h3 {
    font-size: 24px;
  }

  .session-title {
    font-size: 20px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 16px;
  }

  .modal-footer-right {
    width: 100%;
  }

  .btn {
    width: 100%;
  }
}
</style>