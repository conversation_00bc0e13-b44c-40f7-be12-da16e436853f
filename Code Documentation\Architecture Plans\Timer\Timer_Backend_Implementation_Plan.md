# Timer Backend Implementation Plan

## Executive Summary

After analyzing the Timer page frontend implementation and existing backend infrastructure, I've identified what's already implemented and what needs to be completed to fully integrate the Timer functionality. The backend API and database structure are **already implemented**, but the frontend is not connected to the backend.

## Current State Analysis

### ✅ Already Implemented (Backend)

1. **Database Tables**: 
   - `timer_sessions` table with all required fields
   - `timer_settings` table with configuration options

2. **API Layer**: Complete timer API in `electron/main/api/timer-api.ts`
   - Session management (start, end, get, delete)
   - Statistics and reporting
   - Settings management

3. **IPC Handlers**: All timer IPC channels registered in `electron/main/ipc-handlers.ts`
   - Timer sessions: start, end, getSession, etc.
   - Timer settings: get, update, reset

4. **Database Schema**: Tables created in `electron/main/database/database.ts`

### ❌ Missing Implementation

1. **Frontend API Bridge**: Timer API not exposed in `electron/preload/api-bridge.ts`
2. **Type Definitions**: Timer interfaces not in `src/types/electron-api.d.ts`
3. **Frontend Integration**: TimerView.vue uses mock data instead of real API
4. **Session Management**: No real session persistence or retrieval

## Implementation Plan

### Phase 1: API Bridge & Type Definitions

#### 1.1 Add Timer Types to Frontend (`src/types/electron-api.d.ts`)

**File**: `src/types/electron-api.d.ts`
**Action**: Add timer-related interfaces

```typescript
// Timer related interfaces
export interface TimerSession {
  id: number;
  start_time: string; // ISO 8601 format
  end_time?: string | null;
  duration?: number | null; // Duration in seconds
  session_type: string; // e.g., 'work', 'break'
  is_completed: 0 | 1;
  created_at: string;
  updated_at?: string;
  // Frontend-specific fields
  focus?: string;
  category?: string;
  totalFocusTime?: number;
  pomodoroCount?: number;
}

export interface TimerStats {
  total_sessions: number;
  total_duration: number | null;
  work_sessions: number;
  work_duration: number | null;
  break_sessions: number;
  break_duration: number | null;
}

export interface TimerSettings {
  id: number;
  work_duration: number; // seconds
  short_break_duration: number; // seconds
  long_break_duration: number; // seconds
  long_break_interval: number; // number of work sessions before long break
  auto_start_breaks: 0 | 1;
  auto_start_work: 0 | 1;
  created_at: string;
  updated_at: string;
}

export interface TimerAPI {
  // Timer sessions
  start: (sessionType?: string) => Promise<TimerSession>;
  end: (sessionId: number) => Promise<TimerSession>;
  getSession: (sessionId: number) => Promise<TimerSession>;
  getSessionsByDateRange: (startDate: string, endDate: string) => Promise<TimerSession[]>;
  getTodaySessions: () => Promise<TimerSession[]>;
  getStatsByDateRange: (startDate: string, endDate: string) => Promise<TimerStats>;
  deleteSession: (sessionId: number) => Promise<{ success: boolean; id: number }>;
  
  // Timer settings
  getSettings: () => Promise<TimerSettings>;
  updateSettings: (settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>) => Promise<TimerSettings>;
  resetSettings: () => Promise<TimerSettings>;
}
```

#### 1.2 Update Global Window Interface

**File**: `src/types/electron-api.d.ts`
**Action**: Add timer to global window interface

```typescript
declare global {
  interface Window {
    db: {
      notes: NotesAPI;
      folders: FoldersAPI;
      recentItems: RecentItemsAPI;
      media: MediaAPI;
      books: BooksAPI;
      timer: TimerAPI; // Add this line
    }
  }
}
```

#### 1.3 Add Timer API to Bridge

**File**: `electron/preload/api-bridge.ts`
**Action**: Add timer API section

```typescript
// Timer API
timer: {
  // Timer sessions
  start: (sessionType?: string) => ipcRenderer.invoke('timer:start', sessionType),
  end: (sessionId: number) => ipcRenderer.invoke('timer:end', sessionId),
  getSession: (sessionId: number) => ipcRenderer.invoke('timer:getSession', sessionId),
  getSessionsByDateRange: (startDate: string, endDate: string) =>
    ipcRenderer.invoke('timer:getSessionsByDateRange', startDate, endDate),
  getTodaySessions: () => ipcRenderer.invoke('timer:getTodaySessions'),
  getStatsByDateRange: (startDate: string, endDate: string) =>
    ipcRenderer.invoke('timer:getStatsByDateRange', startDate, endDate),
  deleteSession: (sessionId: number) => ipcRenderer.invoke('timer:deleteSession', sessionId),

  // Timer settings
  getSettings: () => ipcRenderer.invoke('timer:getSettings'),
  updateSettings: (settingsUpdates: any) => ipcRenderer.invoke('timer:updateSettings', settingsUpdates),
  resetSettings: () => ipcRenderer.invoke('timer:resetSettings')
}
```

#### 1.4 Update useElectronAPI

**File**: `src/useElectronAPI.ts`
**Action**: Add TimerAPI to interface

```typescript
import type { NotesAPI, FoldersAPI, RecentItemsAPI, BooksAPI, TimerAPI } from './types/electron-api';

export interface ElectronAPI {
  notes: NotesAPI;
  folders: FoldersAPI;
  recentItems: RecentItemsAPI;
  books: BooksAPI;
  timer: TimerAPI; // Add this line
}
```

### Phase 2: Enhanced Backend API

#### 2.1 Extend Timer Session Model

**File**: `electron/main/api/timer-api.ts`
**Action**: Add support for focus and category fields

The current backend only supports basic session tracking. We need to extend it to support:
- Focus description (what the user is working on)
- Category classification
- Better integration with the frontend session model

**Required Changes**:
1. Add `focus` and `category` columns to `timer_sessions` table
2. Update API functions to handle these fields
3. Add migration logic for existing installations

#### 2.2 Database Schema Updates

**File**: `electron/main/database/database.ts`
**Action**: Add missing columns to timer_sessions table

```sql
ALTER TABLE timer_sessions ADD COLUMN focus TEXT;
ALTER TABLE timer_sessions ADD COLUMN category TEXT;
ALTER TABLE timer_sessions ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

### Phase 3: Frontend Integration

#### 3.1 Update TimerView.vue

**File**: `src/views/TimerView.vue`
**Action**: Replace mock data with real API calls

**Key Changes**:
1. Remove mock data and implement real data loading
2. Connect session creation to backend
3. Implement session persistence
4. Add error handling
5. Implement real statistics calculation

#### 3.2 Update PomodoroTimer.vue

**File**: `src/components/timer/PomodoroTimer.vue`
**Action**: Integrate with backend session management

**Key Changes**:
1. Start real timer sessions when timer starts
2. End sessions when timer completes or is manually ended
3. Load and save timer settings
4. Persist timer state across app restarts

#### 3.3 Update AddSessionModal.vue

**File**: `src/components/modals/AddSessionModal.vue`
**Action**: Connect to backend session creation

**Key Changes**:
1. Create actual timer sessions in database
2. Handle category persistence
3. Validate session data

### Phase 4: Advanced Features

#### 4.1 Session History and Statistics

**Implementation Requirements**:
1. Real-time statistics calculation
2. Date range filtering
3. Session search functionality
4. Export capabilities

#### 4.2 Settings Persistence

**Implementation Requirements**:
1. Load settings on app startup
2. Save settings changes immediately
3. Settings validation
4. Reset to defaults functionality

#### 4.3 Session State Management

**Implementation Requirements**:
1. Persist active sessions across app restarts
2. Handle incomplete sessions
3. Session recovery mechanisms

## Technical Considerations

### Data Flow Architecture

```
Frontend (TimerView.vue) 
    ↓
useElectronAPI() 
    ↓
electron/preload/api-bridge.ts 
    ↓
IPC Channels 
    ↓
electron/main/ipc-handlers.ts 
    ↓
electron/main/api/timer-api.ts 
    ↓
SQLite Database
```

### Error Handling Strategy

1. **Frontend**: User-friendly error messages
2. **IPC Layer**: Proper error propagation
3. **Backend**: Detailed logging and graceful degradation
4. **Database**: Transaction rollback and data integrity

### Performance Considerations

1. **Lazy Loading**: Load session history on demand
2. **Caching**: Cache frequently accessed settings
3. **Debouncing**: Debounce settings updates
4. **Pagination**: Implement pagination for large session lists

## Implementation Priority

### High Priority (Core Functionality)
1. API Bridge setup (Phase 1)
2. Basic session management (Phase 3.1, 3.2)
3. Settings persistence (Phase 4.2)

### Medium Priority (Enhanced UX)
1. Session history and statistics (Phase 4.1)
2. Advanced session management (Phase 4.3)
3. Database schema enhancements (Phase 2)

### Low Priority (Nice to Have)
1. Export functionality
2. Advanced analytics
3. Session templates
4. Notification system

## Testing Strategy

### Unit Tests
- Timer API functions
- Session validation
- Settings management

### Integration Tests
- Frontend-backend communication
- Database operations
- IPC channel functionality

### User Acceptance Tests
- Complete timer workflows
- Settings persistence
- Session recovery
- Error scenarios

## Migration Strategy

For existing installations:
1. Check for missing database columns
2. Add columns with default values
3. Migrate existing data if necessary
4. Update schema version

## Detailed Implementation Steps

### Step 1: Database Schema Enhancement

**File**: `electron/main/database/database.ts`

Add migration logic to handle missing columns in existing installations:

```typescript
// Add after timer_sessions table creation
db.run(`ALTER TABLE timer_sessions ADD COLUMN focus TEXT`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add focus column:', err.message);
  }
});

db.run(`ALTER TABLE timer_sessions ADD COLUMN category TEXT`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add category column:', err.message);
  }
});

db.run(`ALTER TABLE timer_sessions ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add updated_at column:', err.message);
  }
});
```

### Step 2: Enhanced Timer API Functions

**File**: `electron/main/api/timer-api.ts`

Update the `startTimerSession` function to support focus and category:

```typescript
export const startTimerSession = async (
  sessionType: string = 'work',
  focus?: string,
  category?: string
): Promise<TimerSession> => {
  if (typeof sessionType !== 'string' || sessionType.trim() === '') {
    throw new Error('Session type must be a non-empty string.');
  }
  try {
    const startTime = new Date().toISOString();
    const result = await dbRun(
      'INSERT INTO timer_sessions (start_time, session_type, focus, category, is_completed, created_at, updated_at) VALUES (?, ?, ?, ?, 0, ?, ?)',
      [startTime, sessionType, focus || null, category || null, startTime, startTime]
    );

    const newSession = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [result.id]);
    if (!newSession) {
      throw new Error('Failed to retrieve the timer session after creation.');
    }
    return newSession;
  } catch (error: any) {
    console.error('Error starting timer session:', error);
    throw new Error(`Failed to start timer session: ${error.message}`);
  }
};
```

### Step 3: Frontend Session Interface

**File**: `src/views/TimerView.vue`

Replace the mock Session interface with the backend-compatible one:

```typescript
// Remove the local Session interface and import from types
import type { TimerSession, TimerStats } from '../types/electron-api';

// Update the session management functions
const startSession = async (sessionData: { focus: string; category: string }) => {
  try {
    // Start a real timer session in the backend
    const session = await db.timer.start('work', sessionData.focus, sessionData.category);

    activeSession.value = {
      ...session,
      focus: sessionData.focus,
      category: sessionData.category,
      date: new Date(session.start_time),
      totalFocusTime: 0,
      pomodoroCount: 0
    };

    showAddSessionModal.value = false;
  } catch (error) {
    console.error('Failed to start session:', error);
    // Handle error appropriately
  }
};

const endSession = async (sessionStats: { totalFocusTime: number; pomodoroCount: number }) => {
  if (activeSession.value?.id) {
    try {
      // End the session in the backend
      await db.timer.end(activeSession.value.id);

      // Add to completed sessions
      const completedSession = {
        ...activeSession.value,
        totalFocusTime: sessionStats.totalFocusTime,
        pomodoroCount: sessionStats.pomodoroCount
      };

      completedSessions.value.unshift(completedSession);
      activeSession.value = null;

      // Refresh statistics
      await loadStats();
    } catch (error) {
      console.error('Failed to end session:', error);
    }
  }
};
```

### Step 4: Statistics Integration

**File**: `src/views/TimerView.vue`

Implement real statistics loading:

```typescript
const loadStats = async () => {
  try {
    // Get today's date for statistics
    const today = new Date().toISOString().split('T')[0];
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Load today's sessions for current week count
    const todaySessions = await db.timer.getTodaySessions();
    const weekStats = await db.timer.getStatsByDateRange(weekAgo, today);

    // Calculate total statistics (you might want to cache this or limit the range)
    const allTimeStats = await db.timer.getStatsByDateRange('2020-01-01', today);

    stats.value = {
      totalSessions: allTimeStats.total_sessions,
      totalPomodoros: Math.floor((allTimeStats.work_duration || 0) / 1500), // Assuming 25min pomodoros
      totalFocusTime: formatDuration(allTimeStats.work_duration || 0),
      sessionsThisWeek: weekStats.total_sessions
    };
  } catch (error) {
    console.error('Failed to load statistics:', error);
  }
};

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
};
```

### Step 5: Settings Integration

**File**: `src/components/timer/PomodoroTimer.vue`

Load and save timer settings:

```typescript
// Add to setup function
const loadSettings = async () => {
  try {
    const settings = await db.timer.getSettings();
    pomodoroTime.value = settings.work_duration;
    shortBreakTime.value = settings.short_break_duration;
    longBreakTime.value = settings.long_break_duration;
    longBreakInterval.value = settings.long_break_interval;
    autoStartBreaks.value = Boolean(settings.auto_start_breaks);
    autoStartPomodoros.value = Boolean(settings.auto_start_work);

    // Update current timer if it's a pomodoro
    if (timerType.value === 'pomodoro') {
      timeLeft.value = pomodoroTime.value;
    }
  } catch (error) {
    console.error('Failed to load timer settings:', error);
  }
};

const updateSettings = async (newSettings: {
  pomodoroTime: number;
  shortBreakTime: number;
  longBreakTime: number;
  longBreakInterval: number;
  autoStartBreaks: boolean;
  autoStartPomodoros: boolean;
}) => {
  try {
    await db.timer.updateSettings({
      work_duration: newSettings.pomodoroTime,
      short_break_duration: newSettings.shortBreakTime,
      long_break_duration: newSettings.longBreakTime,
      long_break_interval: newSettings.longBreakInterval,
      auto_start_breaks: newSettings.autoStartBreaks ? 1 : 0,
      auto_start_work: newSettings.autoStartPomodoros ? 1 : 0
    });

    // Update local values
    pomodoroTime.value = newSettings.pomodoroTime;
    shortBreakTime.value = newSettings.shortBreakTime;
    longBreakTime.value = newSettings.longBreakTime;
    longBreakInterval.value = newSettings.longBreakInterval;
    autoStartBreaks.value = newSettings.autoStartBreaks;
    autoStartPomodoros.value = newSettings.autoStartPomodoros;

    // Update current timer according to its type
    if (timerType.value === 'pomodoro') {
      timeLeft.value = pomodoroTime.value;
    } else if (timerType.value === 'shortBreak') {
      timeLeft.value = shortBreakTime.value;
    } else if (timerType.value === 'longBreak') {
      timeLeft.value = longBreakTime.value;
    }

    // Reset timer if running
    if (isRunning.value) {
      isRunning.value = false;
      stopTimer();
    }
  } catch (error) {
    console.error('Failed to update timer settings:', error);
  }
};

// Load settings on component mount
onMounted(() => {
  loadSettings();
});
```

### Step 6: Session History Loading

**File**: `src/views/TimerView.vue`

Replace mock session loading with real data:

```typescript
const loadSessions = async () => {
  try {
    // Load recent sessions (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];

    const sessions = await db.timer.getSessionsByDateRange(thirtyDaysAgo, today);

    // Transform backend sessions to frontend format
    completedSessions.value = sessions
      .filter(session => session.is_completed === 1)
      .map(session => ({
        id: session.id,
        focus: session.focus || 'Unnamed Session',
        category: session.category || 'No Category',
        date: new Date(session.start_time),
        totalFocusTime: session.duration || 0,
        pomodoroCount: Math.floor((session.duration || 0) / 1500) // Estimate pomodoros
      }));
  } catch (error) {
    console.error('Failed to load sessions:', error);
  }
};
```

## File Modification Summary

### Files to Modify:

1. **`src/types/electron-api.d.ts`** - Add timer interfaces and API definitions
2. **`electron/preload/api-bridge.ts`** - Add timer API bridge
3. **`src/useElectronAPI.ts`** - Add TimerAPI to interface
4. **`electron/main/database/database.ts`** - Add database migrations
5. **`electron/main/api/timer-api.ts`** - Enhance API functions
6. **`src/views/TimerView.vue`** - Replace mock data with real API calls
7. **`src/components/timer/PomodoroTimer.vue`** - Integrate settings and session management

### New Files to Create:
- None (all functionality can be added to existing files)

## Testing Checklist

- [ ] Timer sessions can be started and ended
- [ ] Session data persists in database
- [ ] Statistics are calculated correctly
- [ ] Settings are loaded and saved properly
- [ ] Session history displays real data
- [ ] Error handling works correctly
- [ ] App restart preserves timer state
- [ ] Database migrations work on existing installations

## Conclusion

The Timer functionality has a solid backend foundation but requires frontend integration to become fully functional. The implementation should be done in phases, starting with the API bridge and basic functionality, then expanding to advanced features. The existing backend API is well-designed and should handle most requirements with minimal modifications.

The key insight is that most of the heavy lifting is already done in the backend - the main task is connecting the frontend components to use the real API instead of mock data, and enhancing the backend slightly to support the additional fields (focus, category) that the frontend expects.
