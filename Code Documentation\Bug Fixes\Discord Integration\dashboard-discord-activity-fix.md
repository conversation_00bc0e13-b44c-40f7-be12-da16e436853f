# Dashboard Discord Activity Fix

## Files Modified
- `src/composables/useDiscordActivity.ts` - Fixed setDashboardActivity function

## What Was Done
Fixed the Discord Rich Presence activity for the dashboard so it shows "Using Noti" instead of incorrectly setting the status to "Idle" when users are actively viewing the dashboard.

## How It Was Fixed

### Problem
The `setDashboardActivity()` function was incorrectly calling:
```typescript
db.discord.setActivity({ type: 'idle' })
```

This caused <PERSON>rd to show the user as "Idle" when they were actively using the dashboard, which was confusing and incorrect.

### Solution
Changed the function to use the proper active state method:
```typescript
db.discord.setActiveState()
```

### Before vs After

**Before:**
```typescript
function setDashboardActivity() {
  // ... validation code ...
  
  console.log('🎮 [Discord] Setting dashboard activity')
  // This was wrong - sets to idle when user is active
  db.discord.setActivity({ type: 'idle' }).catch((error: any) => {
    console.error('🎮 [Discord] Failed to set dashboard activity:', error)
  })
}
```

**After:**
```typescript
function setDashboardActivity() {
  // ... validation code ...
  
  console.log('🎮 [Discord] Setting dashboard activity')
  // Use setActiveState to show "Using Noti" instead of idle
  db.discord.setActiveState().catch((error: any) => {
    console.error('🎮 [Discord] Failed to set dashboard activity:', error)
  })
}
```

## Technical Details

### What `setActiveState()` Does
- Shows "Using Noti" as the Discord activity
- Uses the app's logo as the large image
- Sets the large image text to "Noti - Smart Note-Taking & Study Companion"
- Uses the persistent app start timestamp for accurate session duration
- Respects user's custom active message if configured

### Activity Types Available
- `'notes'` - For note-taking activities
- `'book'` - For book writing activities  
- `'timer'` - For focus/timer sessions
- `'settings'` - For app configuration
- `'idle'` - For actual idle state (automatic after inactivity)
- **Active State** - For general app usage (what dashboard should use)

### When This Fix Applies
- User navigates to the dashboard
- Dashboard component mounts and calls `setDashboardActivity()`
- Discord Rich Presence is enabled in settings
- Discord client is connected and available

## User Experience Impact

**Before Fix:**
- User opens dashboard → Discord shows "Idle"
- Confusing because user is actively using the app
- Made it appear the user wasn't engaged with the application

**After Fix:**
- User opens dashboard → Discord shows "Using Noti"
- Accurate representation of user activity
- Consistent with the app's active state behavior
- Shows proper session duration from app start time

## Related Components

This fix affects the Discord activity flow:
1. **Dashboard View** (`src/views/DashboardView.vue`) - Calls `setDashboardActivity()` on mount
2. **Discord RPC API** (`public/discord-rpc-api.ts`) - Handles the `setActiveState()` method
3. **IPC Handlers** (`electron/main/ipc-handlers.ts`) - Bridges the call to Discord RPC
4. **Settings Store** - Controls whether Discord Rich Presence is enabled

## Testing
To verify the fix:
1. Enable Discord Rich Presence in settings
2. Navigate to the dashboard
3. Check Discord - should show "Using Noti" instead of "Idle"
4. Activity should include proper app logo and description
5. Session duration should reflect time since app started
