// Composable for tracking Discord Rich Presence activity
import { useElectronAPI } from '../useElectronAPI'
import { useSettingsStore } from '../stores/settingsStore'

/**
 * Composable for managing Discord Rich Presence activities
 */
export function useDiscordActivity() {

  /**
   * Set Discord activity for note-taking
   */
  function setNoteTakingActivity() {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      console.log('🎮 [Discord] API check:', { hasDB: !!db, hasDiscord: !!db?.discord, enabled: settingsStore.settings.discordRichPresenceEnabled })

      // Quick early returns for better performance
      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) {
        console.log('🎮 [Discord] Skipping note-taking activity - disabled or API unavailable')
        return
      }

      console.log('🎮 [Discord] Setting note-taking activity')
      // Fire and forget for better responsiveness
      db.discord.setActivity({ type: 'notes' }).catch((error: any) => {
        console.error('🎮 [Discord] Failed to set note-taking activity:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to set note-taking activity:', error)
    }
  }

  /**
   * Set Discord activity for book writing
   */
  function setBookWritingActivity(bookName?: string) {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) return

      console.log('🎮 [Discord] Setting book writing activity:', bookName)
      // Fire and forget for better responsiveness
      db.discord.setActivity({
        type: 'book',
        bookName: bookName
      }).catch((error: any) => {
        console.error('🎮 [Discord] Failed to set book writing activity:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to set book writing activity:', error)
    }
  }

  /**
   * Set Discord activity for timer/focus session
   */
  function setTimerActivity() {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) return

      console.log('🎮 [Discord] Setting timer activity')
      // Fire and forget for better responsiveness
      db.discord.setActivity({ type: 'timer' }).catch((error: any) => {
        console.error('🎮 [Discord] Failed to set timer activity:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to set timer activity:', error)
    }
  }

  /**
   * Set Discord activity for settings/configuration
   */
  function setSettingsActivity() {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      console.log('🎮 [Discord] Settings activity - API check:', { hasDB: !!db, hasDiscord: !!db?.discord, enabled: settingsStore.settings.discordRichPresenceEnabled })

      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) {
        console.log('🎮 [Discord] Skipping settings activity - disabled or API unavailable')
        return
      }

      console.log('🎮 [Discord] Setting settings activity')
      // Fire and forget for better responsiveness
      db.discord.setActivity({ type: 'settings' }).catch((error: any) => {
        console.error('🎮 [Discord] Failed to set settings activity:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to set settings activity:', error)
    }
  }

  /**
   * Set Discord activity for dashboard viewing
   */
  function setDashboardActivity() {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) {
        return
      }

      console.log('🎮 [Discord] Setting dashboard activity')
      // Use setActiveState to show "Using Noti" instead of idle
      db.discord.setActiveState().catch((error: any) => {
        console.error('🎮 [Discord] Failed to set dashboard activity:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to set dashboard activity:', error)
    }
  }

  /**
   * Update Discord settings (privacy controls)
   */
  function updateDiscordSettings(settings: {
    showNoteTaking?: boolean
    showBookWriting?: boolean
    showBookNames?: boolean
    showTimer?: boolean
    showSettings?: boolean
  }) {
    try {
      const db = useElectronAPI()
      const settingsStore = useSettingsStore()

      if (!settingsStore.settings.discordRichPresenceEnabled || !db?.discord) return

      console.log('🎮 [Discord] Updating Discord settings:', settings)
      // Fire and forget for better responsiveness
      db.discord.updateSettings(settings).catch((error: any) => {
        console.error('🎮 [Discord] Failed to update Discord settings:', error)
      })
    } catch (error) {
      console.error('🎮 [Discord] Failed to update Discord settings:', error)
    }
  }

  return {
    setNoteTakingActivity,
    setBookWritingActivity,
    setTimerActivity,
    setSettingsActivity,
    setDashboardActivity,
    updateDiscordSettings
  }
}
