# Cover Image Fit Change

## Files Changed
- `src/components/books/BookCard.vue`

## Section
- Books/BookCard component

## Issue
User requested to change how cover images are displayed in book cards. They wanted the top and bottom edges of cover images to perfectly align with the container edges while maintaining the aspect ratio of the images, even if that results in blank spaces on the sides.

## Solution
Changed the `background-size` CSS property from `'cover'` to `'auto 100%'` in the `coverImageStyle` computed property.

### Before:
- Used `backgroundSize: 'cover'` which maintains aspect ratio but can crop parts of the image
- Images would fill the container completely but parts might be cut off on top/bottom or sides

### After:
- Uses `backgroundSize: 'auto 100%'` which maintains aspect ratio while making height 100% of container
- Top and bottom of cover images are perfectly aligned with container edges
- Images maintain their original aspect ratio without distortion
- May have blank spaces on the sides if image is narrower than container

## Changes Made
In the `coverImageStyle` computed property, both conditional returns now use:
```javascript
backgroundSize: 'auto 100%'
```
instead of:
```javascript
backgroundSize: 'cover'
``` 