<template>
  <div class="delete-modal">
    <div class="close-icon" @click="$emit('close')">
      <img src="/icons/close-icon.svg" alt="Close" />
    </div>
    
    <div class="modal-header">
      <h1 class="modal-title">Delete Book</h1>
      <p class="modal-subtitle">
        Are you sure you want to delete "{{ bookTitle }}"?
      </p>
      <p class="modal-warning">
        This action cannot be undone. All notes associated with this book will remain, but they will no longer be linked to the book.
      </p>
    </div>
    
    <div class="divider"></div>
    
    <div class="modal-footer">
      <button class="btn btn-cancel" @click="$emit('cancel')">
        Cancel
      </button>
      <button class="btn btn-delete" @click="$emit('delete')">
        <img src="/icons/trash-icon.svg" class="delete-icon" alt="Delete" />
        Delete
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'DeleteBookModal',
  props: {
    bookTitle: {
      type: String,
      required: true
    },
    bookId: {
      type: Number,
      required: true
    }
  },
  emits: ['close', 'delete', 'cancel']
})
</script>

<style scoped>
.delete-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-shadow);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0 0 12px 0;
}

.modal-warning {
  color: var(--color-error);
  font-size: 16px;
  font-weight: 400;
  margin: 16px 0 0 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-delete {
  background-color: var(--color-error);
  color: var(--color-btn-primary-text);
}

.delete-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1); /* Makes the icon white */
}
</style>
