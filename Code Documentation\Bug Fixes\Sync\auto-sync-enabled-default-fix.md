# Auto-Sync Enabled by Default Fix

## Files Modified
- `/electron/main/api/sync-logic/sync-api.ts`
- `/src/components/settings/BackupSettings.vue`

## What Was Done
Fixed auto-sync to be enabled by default and corrected frontend-backend property name mismatches in sync configuration.

## Issues Found
1. Auto-sync was not enabled by default when no setting existed in the database
2. <PERSON>end was sending incorrect property names to backend sync API:
   - Sending `autoSyncEnabled` instead of `autoSync`
   - Sending `syncDirectory` instead of `syncPath`

## How It Was Fixed

### 1. Backend Auto-Sync Default (sync-api.ts)
Modified the `loadSettings()` method to:
- Check if `autoSyncEnabled` setting exists
- If not, create it with value `true` by default
- Enable auto-sync by default if sync directory is configured
- Update sync status to reflect auto-sync state

```typescript
// Set auto-sync to true by default if not set
if (!autoSyncEnabled) {
  await setSetting('autoSyncEnabled', true, 'sync');
  autoSyncEnabled = { value: true };
}

// Enable auto-sync by default if sync directory is configured
const shouldEnableAutoSync = autoSyncEnabled?.value !== false; // Default to true
```

### 2. Frontend Property Name Fixes (BackupSettings.vue)
Fixed property names in sync configuration calls:
- Changed `autoSyncEnabled` to `autoSync`
- Changed `syncDirectory` to `syncPath`

This ensures the frontend sends the correct property names that match the `SyncConfig` interface expected by the backend.

## Result
- Auto-sync is now enabled by default when the app starts
- If a sync directory is configured, auto-sync will activate automatically
- UI correctly reflects the auto-sync state
- Frontend and backend are properly synchronized with correct property names