/**
 * Integration tests for .noti format implementation in sync system
 * Tests the complete workflow from export to import with media handling
 */

const path = require('path');
const fs = require('fs').promises;
const { fileOperations } = require('../../electron/main/api/sync-logic/file-operations');
const { 
  embedMediaFiles, 
  restoreEmbeddedMedia, 
  createMarkdownPreview, 
  extractPlainText 
} = require('../../electron/main/api/sync-logic/media-utils');

describe('Sync System .noti Format Integration', () => {
  let testDir;
  
  beforeEach(async () => {
    // Create temporary test directory
    testDir = path.join(__dirname, 'temp', `test-${Date.now()}`);
    await fs.mkdir(testDir, { recursive: true });
    fileOperations.setSyncDirectory(testDir);
  });
  
  afterEach(async () => {
    // Clean up test directory
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to clean up test directory:', error);
    }
    fileOperations.clearSyncDirectory();
  });

  describe('File Operations', () => {
    test('should write and read .noti files correctly', async () => {
      const testNotiData = {
        version: "1.0",
        type: "noti-note",
        schema: "https://noti.app/schemas/note/v1.0",
        metadata: {
          id: 123,
          title: "Test Note",
          created_at: "2024-01-15T10:30:00.000Z",
          updated_at: "2024-01-15T14:30:00.000Z",
          type: "text"
        },
        content: {
          html: "<p>Test HTML content</p>",
          markdown: "Test markdown preview",
          plain_text: "Test plain text"
        },
        media: []
      };

      const testPath = path.join(testDir, 'test-note.noti');
      
      // Write .noti file
      await fileOperations.writeNote(testPath, testNotiData);
      
      // Verify file exists
      const fileExists = await fileOperations.exists(testPath);
      expect(fileExists).toBe(true);
      
      // Read .noti file
      const readData = await fileOperations.readNote(testPath);
      
      // Verify data integrity
      expect(readData.version).toBe(testNotiData.version);
      expect(readData.metadata.id).toBe(testNotiData.metadata.id);
      expect(readData.metadata.title).toBe(testNotiData.metadata.title);
      expect(readData.content.html).toBe(testNotiData.content.html);
      expect(readData.content.markdown).toBe(testNotiData.content.markdown);
      expect(readData.media).toEqual(testNotiData.media);
    });

    test('should validate .noti file structure', async () => {
      const invalidNotiData = {
        version: "1.0",
        // Missing required fields
      };

      const testPath = path.join(testDir, 'invalid-note.noti');
      
      // Should throw error for invalid structure
      await expect(fileOperations.writeNote(testPath, invalidNotiData))
        .rejects.toThrow('Invalid .noti data structure');
    });
  });

  describe('Media Utilities', () => {
    test('should create markdown preview correctly', () => {
      const longText = 'This is a test note with many words that should be truncated to only the first fifty words for the preview functionality to work correctly and not overwhelm the notes list display with too much content that would make the interface cluttered and hard to read for users browsing their notes.';
      
      const preview = createMarkdownPreview(longText);
      const words = preview.split(' ');
      
      // Should be 50 words plus ellipsis
      expect(words.length).toBeLessThanOrEqual(51); // 50 words + "..."
      expect(preview.endsWith('...')).toBe(true);
    });

    test('should extract plain text from HTML', () => {
      const htmlContent = '<p>This is <strong>bold</strong> text with <em>emphasis</em> and <a href="#">links</a>.</p>';
      const plainText = extractPlainText(htmlContent);
      
      expect(plainText).toBe('This is bold text with emphasis and links.');
      expect(plainText).not.toContain('<');
      expect(plainText).not.toContain('>');
    });

    test('should handle empty content gracefully', () => {
      expect(createMarkdownPreview('')).toBe('');
      expect(createMarkdownPreview(null)).toBe('');
      expect(extractPlainText('')).toBe('');
      expect(extractPlainText(null)).toBe('');
    });
  });

  describe('Round-trip Integrity', () => {
    test('should maintain data integrity through export/import cycle', async () => {
      const originalData = {
        version: "1.0",
        type: "noti-note",
        schema: "https://noti.app/schemas/note/v1.0",
        metadata: {
          id: 456,
          title: "Round Trip Test",
          created_at: "2024-01-15T10:30:00.000Z",
          updated_at: "2024-01-15T14:30:00.000Z",
          type: "text",
          color: "#ff0000"
        },
        content: {
          html: "<p>Original HTML content with <strong>formatting</strong></p>",
          markdown: "Original markdown preview",
          plain_text: "Original plain text"
        },
        media: []
      };

      const testPath = path.join(testDir, 'roundtrip-test.noti');
      
      // Export (write)
      await fileOperations.writeNote(testPath, originalData);
      
      // Import (read)
      const importedData = await fileOperations.readNote(testPath);
      
      // Verify complete data integrity
      expect(importedData).toEqual(originalData);
    });
  });

  describe('Error Handling', () => {
    test('should handle corrupted .noti files gracefully', async () => {
      const corruptedPath = path.join(testDir, 'corrupted.noti');
      
      // Write invalid JSON
      await fs.writeFile(corruptedPath, '{ invalid json content }');
      
      // Should throw meaningful error
      await expect(fileOperations.readNote(corruptedPath))
        .rejects.toThrow('Invalid .noti file format');
    });

    test('should handle missing files gracefully', async () => {
      const missingPath = path.join(testDir, 'missing.noti');
      
      // Should throw file not found error
      await expect(fileOperations.readNote(missingPath))
        .rejects.toThrow('File not found');
    });
  });

  describe('Path Validation', () => {
    test('should validate paths within sync directory', async () => {
      const validPath = path.join(testDir, 'valid-note.noti');
      const invalidPath = path.join('..', 'invalid-note.noti');
      
      const testData = {
        version: "1.0",
        type: "noti-note",
        schema: "https://noti.app/schemas/note/v1.0",
        metadata: { id: 1, title: "Test", created_at: "2024-01-01T00:00:00.000Z", updated_at: "2024-01-01T00:00:00.000Z", type: "text" },
        content: { html: "", markdown: "", plain_text: "" },
        media: []
      };
      
      // Valid path should work
      await expect(fileOperations.writeNote(validPath, testData))
        .resolves.not.toThrow();
      
      // Invalid path should be rejected
      await expect(fileOperations.writeNote(invalidPath, testData))
        .rejects.toThrow('Path traversal attempt detected');
    });
  });
});

// Mock functions for testing without actual database
jest.mock('../../electron/main/api/media-api', () => ({
  getMediaFilesByNoteId: jest.fn().mockResolvedValue([]),
  saveMediaFile: jest.fn().mockResolvedValue({ id: 1, file_path: '/test/path' }),
  filePathToMediaUrl: jest.fn().mockReturnValue('noti-media://test/path')
}));
