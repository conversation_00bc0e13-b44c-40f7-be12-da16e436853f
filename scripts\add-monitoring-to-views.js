/**
 * <PERSON><PERSON><PERSON> to automatically add page load monitoring to all view components
 * 
 * This script will:
 * 1. Add the usePageLoadMonitoring import to each view
 * 2. Add the autoRecordPageMounted call to the setup function
 * 
 * Run with: node scripts/add-monitoring-to-views.js
 */

const fs = require('fs')
const path = require('path')

const viewsDir = path.join(__dirname, '../src/views')
const viewFiles = [
  'DashboardView.vue',
  'NotesView.vue', 
  'BooksView.vue',
  'FoldersView.vue',
  'TimerView.vue',
  'SettingsView.vue'
]

function addMonitoringToView(filePath, routeName) {
  console.log(`Processing ${routeName}...`)
  
  let content = fs.readFileSync(filePath, 'utf8')
  
  // Check if monitoring is already added
  if (content.includes('usePageLoadMonitoring')) {
    console.log(`  ✅ ${routeName} already has monitoring`)
    return
  }
  
  // Find the script tag and setup function
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/)
  if (!scriptMatch) {
    console.log(`  ❌ Could not find script tag in ${routeName}`)
    return
  }
  
  let scriptContent = scriptMatch[1]
  
  // Add import statement
  const importRegex = /(import.*from.*['"][^'"]*['"])/g
  const imports = scriptContent.match(importRegex) || []
  
  if (imports.length > 0) {
    const lastImport = imports[imports.length - 1]
    const importIndex = scriptContent.indexOf(lastImport) + lastImport.length
    const newImport = `\nimport { usePageLoadMonitoring } from '../composables/usePageLoadMonitoring'`
    scriptContent = scriptContent.slice(0, importIndex) + newImport + scriptContent.slice(importIndex)
  }
  
  // Find setup function and add monitoring
  const setupMatch = scriptContent.match(/setup\(\)\s*{/)
  if (setupMatch) {
    const setupIndex = scriptContent.indexOf(setupMatch[0]) + setupMatch[0].length
    const monitoringCode = `\n    // Page load monitoring\n    const { autoRecordPageMounted } = usePageLoadMonitoring()\n    autoRecordPageMounted('${routeName}')\n`
    scriptContent = scriptContent.slice(0, setupIndex) + monitoringCode + scriptContent.slice(setupIndex)
  } else {
    console.log(`  ⚠️  Could not find setup function in ${routeName}`)
    return
  }
  
  // Replace the script content in the original file
  const newContent = content.replace(/<script[^>]*>[\s\S]*?<\/script>/, `<script lang="ts">${scriptContent}</script>`)
  
  // Write back to file
  fs.writeFileSync(filePath, newContent, 'utf8')
  console.log(`  ✅ Added monitoring to ${routeName}`)
}

function main() {
  console.log('🔧 Adding page load monitoring to view components...\n')
  
  viewFiles.forEach(fileName => {
    const filePath = path.join(viewsDir, fileName)
    const routeName = fileName.replace('View.vue', '').replace('.vue', '')
    
    if (fs.existsSync(filePath)) {
      addMonitoringToView(filePath, routeName)
    } else {
      console.log(`  ❌ File not found: ${fileName}`)
    }
  })
  
  console.log('\n✅ Monitoring setup complete!')
  console.log('\n📊 Usage:')
  console.log('1. Navigate between pages using the sidebar')
  console.log('2. Open browser console and run: pagePerf.report()')
  console.log('3. Download CSV data with: pagePerf.download()')
  console.log('4. Clear data with: pagePerf.clear()')
}

if (require.main === module) {
  main()
}
