// Notes view keybinds composable
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useNotesKeybinds() {
  const isActive = ref(false)

  // Core note functions (to be passed from NotesView)
  let createNewNote: () => void = () => console.log('📝 Create new note')
  let saveCurrentNote: () => void = () => console.log('💾 Save current note')
  let focusSearchBar: () => void = () => console.log('🔍 Focus search bar')
  let deleteSelectedNotes: () => void = () => console.log('🗑️ Delete selected notes')
  let importNote: () => void = () => console.log('📥 Import note')
  let selectAllNotes: () => void = () => console.log('📋 Select all notes')
  let navigateNoteList: (direction: 'up' | 'down') => void = (direction) => console.log(`🧭 Navigate ${direction}`)
  let openSelectedNote: () => void = () => console.log('📖 Open selected note')

  // Multi-selection functions
  let toggleNoteSelection: (event: MouseEvent) => void = () => console.log('🔘 Toggle note selection')
  let rangeSelectNotes: (event: MouseEvent) => void = () => console.log('📏 Range select notes')

  // Register notes-specific keybinds
  const registerNotesKeybinds = () => {
    console.log('📝 Registering notes keybinds...')

    // Core notes actions
    globalKeybindManager.register({
      key: 'ctrl+n',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen) {
          createNewNote()
        }
      },
      description: 'Create new note',
      category: KeybindCategory.NOTES,
      priority: 'high',
      enabled: true,
      context: { view: 'notes', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+s',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen) {
          saveCurrentNote()
        }
      },
      description: 'Save current note',
      category: KeybindCategory.NOTES,
      priority: 'high',
      enabled: true,
      context: { view: 'notes', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+f',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          focusSearchBar()
        }
      },
      description: 'Focus search bar',
      category: KeybindCategory.NOTES,
      priority: 'high',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'delete',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          deleteSelectedNotes()
        }
      },
      description: 'Delete selected notes',
      category: KeybindCategory.NOTES,
      priority: 'high',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+i',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          importNote()
        }
      },
      description: 'Import note',
      category: KeybindCategory.NOTES,
      priority: 'medium',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    // Selection and navigation
    globalKeybindManager.register({
      key: 'ctrl+a',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          selectAllNotes()
        }
      },
      description: 'Select all notes',
      category: KeybindCategory.NOTES,
      priority: 'medium',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'arrowup',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          navigateNoteList('up')
        }
      },
      description: 'Navigate up in note list',
      category: KeybindCategory.NOTES,
      priority: 'medium',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'arrowdown',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          navigateNoteList('down')
        }
      },
      description: 'Navigate down in note list',
      category: KeybindCategory.NOTES,
      priority: 'medium',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'enter',
      handler: (context) => {
        if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
          openSelectedNote()
        }
      },
      description: 'Open selected note',
      category: KeybindCategory.NOTES,
      priority: 'medium',
      enabled: true,
      context: { view: 'notes', modalOpen: false, editorFocused: false }
    })

    console.log('✅ Notes keybinds registered')
  }

  // Unregister notes keybinds
  const unregisterNotesKeybinds = () => {
    console.log('🗑️ Unregistering notes keybinds...')
    
    globalKeybindManager.unregister('ctrl+n')
    globalKeybindManager.unregister('ctrl+s') 
    globalKeybindManager.unregister('ctrl+f')
    globalKeybindManager.unregister('delete')
    globalKeybindManager.unregister('ctrl+i')
    globalKeybindManager.unregister('ctrl+a')
    globalKeybindManager.unregister('arrowup')
    globalKeybindManager.unregister('arrowdown')
    globalKeybindManager.unregister('enter')
  }

  // Activate notes keybinds
  const activate = () => {
    if (!isActive.value) {
      registerNotesKeybinds()
      isActive.value = true
      console.log('🟢 Notes keybinds activated')
    }
  }

  // Deactivate notes keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterNotesKeybinds()
      isActive.value = false
      console.log('🔴 Notes keybinds deactivated')
    }
  }

  // Setup functions (to be called from NotesView)
  const setupNoteFunctions = (functions: {
    createNewNote?: () => void
    saveCurrentNote?: () => void
    focusSearchBar?: () => void
    deleteSelectedNotes?: () => void
    importNote?: () => void
    selectAllNotes?: () => void
    navigateNoteList?: (direction: 'up' | 'down') => void
    openSelectedNote?: () => void
    toggleNoteSelection?: (event: MouseEvent) => void
    rangeSelectNotes?: (event: MouseEvent) => void
  }) => {
    if (functions.createNewNote) createNewNote = functions.createNewNote
    if (functions.saveCurrentNote) saveCurrentNote = functions.saveCurrentNote
    if (functions.focusSearchBar) focusSearchBar = functions.focusSearchBar
    if (functions.deleteSelectedNotes) deleteSelectedNotes = functions.deleteSelectedNotes
    if (functions.importNote) importNote = functions.importNote
    if (functions.selectAllNotes) selectAllNotes = functions.selectAllNotes
    if (functions.navigateNoteList) navigateNoteList = functions.navigateNoteList
    if (functions.openSelectedNote) openSelectedNote = functions.openSelectedNote
    if (functions.toggleNoteSelection) toggleNoteSelection = functions.toggleNoteSelection
    if (functions.rangeSelectNotes) rangeSelectNotes = functions.rangeSelectNotes
    
    console.log('🔧 Notes functions configured')
  }

  // Auto-activate when mounted (if currently in notes view)
  onMounted(() => {
    // Check current route and activate if in notes view
    const currentPath = window.location.hash.slice(1) || window.location.pathname
    if (currentPath.includes('/notes')) {
      activate()
    }
  })

  // Cleanup on unmount
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupNoteFunctions,
    registerNotesKeybinds,
    unregisterNotesKeybinds
  }
}