# Book Cover Download and Storage Fix

## Files Modified
- `electron/main/api/books-api.ts`
- `electron/main/ipc-handlers.ts` 
- `electron/preload/api-bridge.ts`
- `src/types/electron-api.d.ts`

## Section
Books Management / Media Files System

## Issue Description
The application had several issues with book cover handling:

1. **Startup Cover Loss**: When a book was added and the app closed before the cover download completed, only the `cover_url` was saved. On next startup, the app would request the cover again but wouldn't download it to the `media_files` table.

2. **Manual Book Cover Upload**: When users manually added a book with a cover image, the cover was stored as a data URL in the `cover_url` field but never converted to a proper file in the `media_files` table.

3. **Missing Cover Check**: There was no mechanism to check for books that had `cover_url` entries but no corresponding files in `media_files` and retry downloading them.

## Root Cause Analysis
- The cover download process in `createBookWithValidation` could fail silently or be interrupted during app shutdown
- Manual book uploads used data URLs that weren't being processed into proper media files
- No startup routine existed to check and fix missing cover files
- The frontend correctly prioritized `cover_media_url` over `cover_url`, but missing files meant fallback to external URLs every time

## Solution Implemented

### 1. Enhanced Cover Download Logic
- **Data URL Detection**: Added `isDataUrl()` helper to detect base64 image data
- **Data URL Processing**: Added `dataUrlToBuffer()` to convert base64 data to Buffer
- **Improved `createBookWithValidation()`**: Now handles both external URLs and data URLs properly
- **URL Cleanup**: Data URLs are cleared from `cover_url` after successful conversion to prevent confusion

### 2. Missing Cover Detection and Recovery
- **New Function**: `checkAndDownloadMissingCovers()` that:
  - Queries for books with `cover_url` but no `is_cover=1` entry in `media_files`
  - Downloads missing covers in batches (3 at a time) to avoid overwhelming the system
  - Handles both external URLs and data URLs
  - Provides detailed logging of success/failure counts
  
### 3. Startup Integration
- **Background Processing**: The cover check runs automatically on app startup without blocking initialization
- **Error Resilience**: Failures don't prevent app startup
- **IPC Handler**: Added `books:checkAndDownloadMissingCovers` for manual triggering from frontend

### 4. API Enhancements
- **Type Safety**: Updated TypeScript interfaces to include new function
- **Preload Bridge**: Made function available to frontend via `window.db.books.checkAndDownloadMissingCovers()`

## Technical Details

### Database Query Used
```sql
SELECT b.id, b.title, b.cover_url 
FROM books b 
LEFT JOIN media_files m ON b.id = m.book_id AND m.is_cover = 1 
WHERE b.cover_url IS NOT NULL 
AND b.cover_url != '' 
AND m.id IS NULL
```

### Cover Processing Flow
1. **Manual Upload**: Data URL → Buffer → media_files → clear cover_url
2. **Online Book**: External URL → Download → media_files → keep cover_url as fallback
3. **Recovery**: Missing files detected → Download/Process → media_files

### Display Priority (BookCard.vue)
1. `cover_media_url` (from media_files) - **Primary**
2. `cover_url` (external URL) - **Fallback**
3. Default placeholder - **No cover available**

## Testing Scenarios Addressed
1. ✅ Add book from OpenLibrary → App closes during download → Next startup recovers cover
2. ✅ Manual book with uploaded cover → Cover properly saved to media_files
3. ✅ Existing books with missing covers → Automatically detected and downloaded
4. ✅ Batch processing prevents API rate limiting
5. ✅ Data URLs are cleaned up after successful processing
6. ✅ External URLs remain as fallback for redundancy

## Performance Considerations
- **Batch Processing**: Downloads happen in groups of 3 with 1-second delays
- **Background Execution**: Startup isn't blocked by cover downloads
- **Error Handling**: Individual failures don't stop the entire process
- **Logging**: Comprehensive feedback for monitoring and debugging

## Future Improvements
- Could add user notification when covers are being downloaded
- Might implement retry logic for failed downloads
- Could add manual trigger button in the UI for users to force cover check 