<template>  <div class="note-card" 
       :class="{ 'active': isActive, 'selected': isSelected }" 
       @click="handleClick"
       @contextmenu.prevent="handleRightClick">
    <div class="note-card-content">
      <h3 class="note-title">{{ title }}</h3>
      <p class="note-excerpt">{{ excerpt }}</p>
      <div class="note-meta">
        <span class="note-date">{{ date }}</span>
        <span class="note-path">{{ path }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'NoteCard',  props: {
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    path: {
      type: String,
      default: ''
    },    isActive: {
      type: Boolean,
      default: false
    }
  },emits: ['select', 'multiSelect', 'rightClick'],
  setup(props, { emit }) {
    // Create a computed property to extract a clean excerpt from content
    const excerpt = computed(() => {
      // Strip HTML tags if present
      const plainText = props.content.replace(/<[^>]*>/g, '');
      // Return a truncated version (about 100 characters)
      return plainText.length > 100 
        ? plainText.substring(0, 100) + '...' 
        : plainText;
    });    // Handle click with potential modifiers for multi-select
    const handleClick = (event: MouseEvent) => {
      if (event.ctrlKey || event.metaKey || event.shiftKey) {
        // Multi-select mode - emit with modifiers
        emit('multiSelect', {
          ctrlKey: event.ctrlKey || event.metaKey,
          shiftKey: event.shiftKey
        });
      } else {
        // Regular selection
        emit('select');
      }
    };

    // Handle right-click for context menu
    const handleRightClick = (event: MouseEvent) => {
      emit('rightClick', event);
    };    return {
      excerpt,
      handleClick,
      handleRightClick
    };
  }
});
</script>

<style scoped>
.note-card {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border-primary);
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Add selection indicator on the left side */
.note-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.note-card.selected::before {
  background-color: var(--color-primary);
}

.checkbox-container {
  margin-right: 12px;
  margin-top: 2px;
  position: relative;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.note-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox {
  position: absolute;
  top: 0;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: var(--color-input-bg);
  border: 1px solid var(--color-input-border);
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.8;
  transform: scale(0.9);
}

.checkbox-container:hover .custom-checkbox {
  transform: scale(1);
  opacity: 1;
}

.note-checkbox:checked ~ .custom-checkbox {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.custom-checkbox:after {
  content: "";
  position: absolute;
  display: none;
}

.note-checkbox:checked ~ .custom-checkbox:after {
  display: block;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.note-checkbox:focus ~ .custom-checkbox {
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.2);
}

.note-card:hover {
  background-color: var(--color-nav-item-hover);
}

.note-card.active {
  background-color: var(--color-nav-item-active);
  border-left: 3px solid var(--color-primary);
  padding-left: 17px; /* Compensate for the left border */
}

.note-card.selected {
  background-color: var(--color-nav-item-active);
  padding-left: 20px; /* Keep normal padding since we use ::before instead */
}

.note-card.selected.active {
  background-color: var(--color-nav-item-active);
}

.note-card-content {  width: 100%;
  flex: 1;
  padding-left: 0;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Montserrat', sans-serif;
}

.note-excerpt {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  line-height: 1.4;
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-muted);
  width: 100%;
  font-family: 'Montserrat', sans-serif;
}

.note-date {
  font-weight: 500;
  color: var(--color-text-tertiary);
}

.note-path {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  color: var(--color-text-muted);
  font-style: italic;
}
</style>