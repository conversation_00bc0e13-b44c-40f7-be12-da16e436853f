<template>
  <div class="folder-navigator">
    <!-- Breadcrumb navigation - only show if showBreadcrumb prop is true -->
    <div v-if="showBreadcrumb !== false" class="breadcrumb-container" style="position: relative; z-index: 100;">
      <!-- Only show breadcrumb when no folders are selected -->
      <template v-if="selectedItems.length === 0">
        <span class="breadcrumb-item" @click="handleRootClick">
          {{ rootFolderName }}
        </span>
        <template v-if="shouldTruncatePath">
          <span class="breadcrumb-separator">/</span>
          <span
            class="breadcrumb-item truncated"
            @click="toggleFullPathDropdown($event)"
            title="Click to see full path"
          >
            <!-- CORRECT: This calculation is right - keep it as is -->
            ...{{ breadcrumbPath.length - 2 }} more...
          </span>
          <span class="breadcrumb-separator">/</span>
          <span
            class="breadcrumb-item current"
            @click="handleBreadcrumbClick(breadcrumbPath.length - 1)"
          >
            {{ breadcrumbPath[breadcrumbPath.length - 1]?.name || '' }}
          </span>
        </template>
        <template v-else>
          <template v-for="(folder, index) in breadcrumbPath.slice(1)" :key="folder.id">
            <span class="breadcrumb-separator">/</span>
            <span
              class="breadcrumb-item"
              :class="{'current': index === breadcrumbPath.length - 2}"
              @click="handleBreadcrumbClick(index + 1)"
              :title="folder.name"
            >
              {{ folder.name }}
            </span>
          </template>
        </template>
      </template>
      <!-- Dropdown for full breadcrumb path when it's truncated -->
      <div class="breadcrumb-expand" v-if="showFullPathDropdown" @click.stop>
        <div class="breadcrumb-dropdown" @click.stop>
          <div class="dropdown-content">
            <span
              class="breadcrumb-dropdown-item"
              @click.stop="handleRootClick(); showFullPathDropdown = false"
            >
              <span class="dropdown-level-indicator"></span>
              {{ rootFolderName }}
            </span>
            <template v-for="(folder, index) in breadcrumbPath.slice(1)" :key="folder.id">
              <span
                class="breadcrumb-dropdown-item"
                :class="{
                  'current': index === breadcrumbPath.length - 2,
                  'hover-effect': true
                }"
                @click.stop="handleBreadcrumbClick(index + 1); showFullPathDropdown = false"
              >
                <span class="dropdown-level-indicator"></span>
                {{ folder.name }}
              </span>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Folder list container -->
    <div
      class="folder-list-container"
      :class="{ 'selection-mode': selectedItems.length > 0 }"
      v-if="!isFolderEmpty"
      @click.self="handleContainerClick"
    >
      <ul class="folder-list">
        <li
          v-for="item in currentItems"
          :key="item.id === null ? `null-${Math.random()}` : item.id"
          class="folder-list-item"
          :class="{
            'selected': isSelected(item.id),
            'cursor-pointer': item.type === 'folder' && selectedItems.length === 0
          }"
          @click="handleItemClick(item, $event)"
        >
          <!-- Folder icon -->
          <div class="item-icon">
            <FolderIcon
              v-if="item.type === 'folder'"
              :color="item.color || undefined"
              :size="16"
              :isOpen="false"
            />

            <!-- Note icon -->
            <img
              v-else-if="item.type === 'note'"
              src="/icons/notes-icon.svg"
              alt="Note"
            />

            <!-- File icon -->
            <img
              v-else
              src="/icons/file-icon.svg"
              alt="File"
            />
          </div>

          <!-- Item name -->
          <span class="item-name">
            {{ item.type === 'folder' ? item.name : item.title }}
          </span>

          <!-- Rename Icon (visible on hover for folders, except protected Books folder) -->
          <img
            v-if="item.type === 'folder' && !isProtectedFolder(item)"
            src="/icons/rename-icon.svg"
            class="rename-icon"
            alt="Rename"
            @click.stop="requestRename(item)"
          />

          <!-- Note count for folders -->
          <span v-if="item.type === 'folder' && item.notesCount !== undefined" class="item-count">
            {{ item.notesCount }}
          </span>

          <!-- Right arrow for folders -->
          <img
            v-if="item.type === 'folder'"
            src="/icons/dropdown-arrow-icon.svg"
            class="chevron-right"
            alt="Open"
          />
        </li>
      </ul>
    </div>

    <!-- Empty folder message -->
    <div class="empty-folder-message" v-else>
      <div class="empty-folder-content">
        <FolderIcon :color="undefined" :size="48" :isOpen="false" class="empty-folder-icon" />
        <p>"{{ getCurrentFolderName() }}" is empty.</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, PropType, onMounted, onBeforeUnmount } from 'vue';
import { FolderWithMeta } from '../../types/electron-api';
import { NavigatorItem } from '../../types/components';
import { getFolderIconStyle, getFolderIconSrc } from '../../utils/colorUtils';
import FolderIcon from '../icons/FolderIcon.vue';

export default defineComponent({
  name: 'FolderNavigator',
  components: {
    FolderIcon
  },
  props: {
    folderHierarchy: {
      type: Array as PropType<FolderWithMeta[] | null>,
      required: true
    },
    selectedItemId: {
      type: [Number, String] as PropType<number | string | null>,
      default: null
    },
    rootFolderName: {
      type: String,
      default: 'All Folders'
    },
    currentSelectedFolder: {
      type: Object as PropType<FolderWithMeta | null>,
      default: null
    },
    isModalOpen: {
      type: Boolean,
      default: false
    },
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    isProtectedFolderSelected: {
      type: Boolean,
      default: false
    }
  }, emits: [
    'selectItem',
    'toggleFolder',
    'renameFolderRequest',
    'selectionChange',
    'deleteItems',
    'createFolder'
  ], setup(props, { emit }) {    // State
    const breadcrumbPath = ref<FolderWithMeta[]>([]);
    const currentFolderId = ref<number | null>(null);
    const currentItems = ref<NavigatorItem[]>([]);
    const selectedItems = ref<(string | number)[]>([]);
    const lastSelectedItem = ref<NavigatorItem | null>(null);
    const showFullPathDropdown = ref<boolean>(false);

    // Store the dropdown close listener to ensure proper cleanup
    const dropdownCloseListener = ref<((evt: MouseEvent) => void) | null>(null);

    // Helper function to clean up dropdown listener
    const cleanupDropdownListener = () => {
      if (dropdownCloseListener.value) {
        document.removeEventListener('click', dropdownCloseListener.value);
        dropdownCloseListener.value = null;
      }
    };

    // Helper function to check if a folder is protected (Books folder)
    const isProtectedFolder = (item: NavigatorItem): boolean => {
      if (!item || item.type !== 'folder') return false;
      // Check if this is the "Books" root folder
      return item.name === 'Books' && item.parent_id === null;
    };

    // Create a root folder object
    const getRootFolder = (): FolderWithMeta => ({
      id: -1, // Using -1 to represent the root
      name: props.rootFolderName,
      children: props.folderHierarchy || []
    });

    // Initialize breadcrumb path with root folder
    breadcrumbPath.value = [getRootFolder()];

    // Watch for changes in selectedItems to debug multi-selection issues
    watch(selectedItems, (newValue, oldValue) => {
      console.log('selectedItems changed:', {
        from: oldValue,
        to: newValue,
        length: newValue.length
      });

      // If selection is cleared, ensure lastSelectedItem is also cleared
      if (newValue.length === 0) {
        lastSelectedItem.value = null;
      }
    }, { deep: true });

    // Computed property to determine if breadcrumb path should be truncated
    const shouldTruncatePath = computed(() => {
      return breadcrumbPath.value.length > 3; // Truncate if more than 3 levels
    });

    // Computed property to determine if the current folder is truly empty
    // A folder is empty only if it has no subfolders AND no notes
    const isFolderEmpty = computed(() => {
      // Check if there are any subfolders
      const hasSubfolders = currentItems.value.length > 0;

      // Check if the current folder has notes
      const currentFolder = breadcrumbPath.value[breadcrumbPath.value.length - 1];
      const hasNotes = currentFolder && typeof currentFolder.notesCount === 'number' && currentFolder.notesCount > 0;

      // Folder is empty only if it has neither subfolders nor notes
      return !hasSubfolders && !hasNotes;
    });

    // Handle root folder click
    const handleRootClick = () => {
      // Reset to root folder
      breadcrumbPath.value = [getRootFolder()];
      currentFolderId.value = null;
      updateCurrentItems();

      // Emit selection event for root folder
      emit('selectItem', { id: null, name: props.rootFolderName, type: 'folder' });
    };

    // Computed property to handle breadcrumb display logic
    const displayedBreadcrumbs = computed(() => {
      const path = breadcrumbPath.value;

      // Always show at least the root and current folder
      if (path.length <= 3) {
        // If 3 or fewer items, show all
        return path.map((folder: FolderWithMeta, index: number) => ({
          folder,
          index,
          truncated: false
        }));
      }

      // Otherwise, show first, last, and a truncated indicator in the middle
      return [
        // Root folder
        { folder: path[0], index: 0, truncated: false },
        // Middle truncated indicator - show count of hidden items
        { folder: { id: -999, name: `...${path.length - 2} more...` } as FolderWithMeta, index: -1, truncated: true },
        // Current folder (last item)
        { folder: path[path.length - 1], index: path.length - 1, truncated: false }
      ];
    });

    // Toggle full breadcrumb path dropdown
    const toggleFullPathDropdown = (event?: MouseEvent) => {
      if (event) {
        event.stopPropagation(); // Prevent the event from bubbling up
        event.preventDefault(); // Prevent any default behavior
      }

      // Clean up any existing listener before toggling
      cleanupDropdownListener();

      // Toggle dropdown visibility
      showFullPathDropdown.value = !showFullPathDropdown.value;

      // Position the dropdown if opening it
      if (showFullPathDropdown.value && event) {
        // Get the truncated element that was clicked
        const truncatedElement = event.currentTarget as HTMLElement;
        if (truncatedElement) {
          // Get position information
          const rect = truncatedElement.getBoundingClientRect();

          // Get the dropdown element (after the DOM has updated)
          setTimeout(() => {
            const dropdownElement = document.querySelector('.breadcrumb-expand') as HTMLElement;
            if (dropdownElement) {
              // Position the dropdown below the truncated item
              dropdownElement.style.position = 'fixed';
              dropdownElement.style.top = `${rect.bottom + 2}px`;
              dropdownElement.style.left = `${rect.left}px`;
              // No need to set width, as it's already defined in CSS
            }
          }, 0);
        }

        // Add event listener to close dropdown when clicking outside
        setTimeout(() => {
          const closeDropdown = (evt: MouseEvent) => {
            // Check if the click is outside the dropdown and truncated item
            const target = evt.target as HTMLElement;
            const isClickInsideDropdown = target.closest('.breadcrumb-dropdown') !== null;
            const isClickOnTruncated = target.closest('.breadcrumb-item.truncated') !== null;

            if (!isClickInsideDropdown && !isClickOnTruncated) {
              showFullPathDropdown.value = false;
              cleanupDropdownListener(); // Use the helper function
            }
          };

          // Store the listener reference and add it to document
          dropdownCloseListener.value = closeDropdown;
          document.addEventListener('click', closeDropdown);
        }, 100);
      }
    };

    // Check if an item is selected
    const isSelected = (id: number | string | null) => {
      // If id is null, or there are no selected items, return false
      if (id === null || selectedItems.value.length === 0) {
        return false;
      }

      // Check if this item is in the current selection
      const selected = selectedItems.value.includes(id);

      return selected;
    };

    // Process folder hierarchy when it changes
    const processHierarchy = () => {
      if (!props.folderHierarchy) {
        breadcrumbPath.value = [];
        currentItems.value = [];
        return;
      }

      // Initialize with the root folder if path is empty
      if (breadcrumbPath.value.length === 0) {
        breadcrumbPath.value = [getRootFolder()];
        currentFolderId.value = null;
      } else {
        // Rebuild the path to ensure it has current data
        rebuildPath();
      }

      // Update the current items based on the selected folder
      updateCurrentItems();
    };

    // Rebuild the breadcrumb path using the current hierarchy
    const rebuildPath = () => {
      if (breadcrumbPath.value.length === 0) {
        breadcrumbPath.value = [getRootFolder()];
        return;
      }

      const rootFolder = getRootFolder();
      const newPath: FolderWithMeta[] = [rootFolder];

      // Skip the root folder (first item)
      for (let i = 1; i < breadcrumbPath.value.length; i++) {
        const pathItem = breadcrumbPath.value[i];
        let parentFolder = newPath[newPath.length - 1];

        // Find the folder in the current hierarchy
        const matchedFolder = findFolderById(parentFolder.children || [], pathItem.id);

        if (matchedFolder) {
          newPath.push(matchedFolder);
        } else {
          // If folder not found, reset to root
          breadcrumbPath.value = [rootFolder];
          break;
        }
      }

      breadcrumbPath.value = newPath;
    };

    // Find a folder by ID in a folder array
    const findFolderById = (folders: FolderWithMeta[], id: number): FolderWithMeta | undefined => {
      for (const folder of folders) {
        if (folder.id === id) {
          return folder;
        }

        if (folder.children && folder.children.length > 0) {
          const foundInChildren = findFolderById(folder.children, id);
          if (foundInChildren) {
            return foundInChildren;
          }
        }
      }

      return undefined;
    };
    // Update the current items based on the selected folder
    const updateCurrentItems = () => {
      if (breadcrumbPath.value.length === 0) {
        // If breadcrumb path is empty, initialize with root folder
        breadcrumbPath.value = [getRootFolder()];
      }

      const currentFolder = breadcrumbPath.value[breadcrumbPath.value.length - 1];

      // Ensure currentFolder is valid
      if (!currentFolder) {
        console.error('Current folder is undefined in updateCurrentItems');
        // Reset to root folder
        breadcrumbPath.value = [getRootFolder()];
        return updateCurrentItems(); // Try again with reset path
      }

      currentFolderId.value = currentFolder.id !== -1 ? currentFolder.id : null;

      // Ensure children is always an array, even if undefined
      const children = currentFolder.children || [];

      // Convert folders to NavigatorItems
      const folderItems: NavigatorItem[] = children.map((folder: FolderWithMeta) => ({
        id: folder.id,
        name: folder.name,
        type: 'folder' as const,
        children: folder.children?.map((child: FolderWithMeta) => ({
          id: child.id,
          name: child.name,
          type: 'folder' as const,
          book_id: child.book_id,
          color: child.color
        })),
        notesCount: typeof folder.notesCount === 'number' ? folder.notesCount : 0,
        parent_id: folder.parent_id,
        book_id: folder.book_id,
        color: folder.color
      }));

      currentItems.value = folderItems;
    };    // Update navigator path when a folder is selected from outside the navigator
    const updateNavigatorPath = (folder: FolderWithMeta) => {
      try {
        if (!folder) {
          // Reset to root if folder is null
          breadcrumbPath.value = [getRootFolder()];
          currentFolderId.value = null;
          updateCurrentItems();
          return;
        }

        console.log('Updating navigator path for external folder selection:', folder.name);

        // Always start with the root folder
        const rootFolder = getRootFolder();
        const path: FolderWithMeta[] = [rootFolder];

        // Add the selected folder if it's not the root
        if (folder.id !== null && folder.id !== undefined) {
          // Find all parent folders to build complete path
          let currentParentId = folder.parent_id;
          let depth = 0;
          const maxDepth = 20; // Prevent infinite loops

          // Array to collect parent folders in reverse order
          const parentFolders: FolderWithMeta[] = [];

          // Recursively find a folder in the hierarchy
          const findFolderById = (folders: FolderWithMeta[] | null, id: number): FolderWithMeta | null => {
            if (!folders || !Array.isArray(folders)) return null;

            for (const f of folders) {
              if (f.id === id) return f;

              if (f.children && f.children.length > 0) {
                const found = findFolderById(f.children, id);
                if (found) return found;
              }
            }

            return null;
          };

          // First, try to find the current folder in the hierarchy to get the most up-to-date version
          const updatedFolder = findFolderById(props.folderHierarchy, folder.id);

          // Use the updated folder if found, otherwise use the provided folder
          const folderToUse = updatedFolder || folder;

          // Build the path by going up the parent chain
          while (currentParentId !== null && currentParentId !== undefined && depth < maxDepth) {
            const parentFolder = findFolderById(props.folderHierarchy, currentParentId);

            if (parentFolder) {
              parentFolders.unshift(parentFolder);
              currentParentId = parentFolder.parent_id;
            } else {
              console.warn(`Parent folder with ID ${currentParentId} not found in hierarchy`);
              break;
            }

            depth++;
          }

          // Add all parent folders to the path
          path.push(...parentFolders);

          // Finally add the current folder
          path.push(folderToUse);
        }

        // Update the breadcrumb path
        breadcrumbPath.value = path;

        // Set the current folder ID
        currentFolderId.value = folder.id;

        // Update the current items display
        updateCurrentItems();

        console.log('Navigator path updated:', path.map(f => f.name).join(' > '));
      } catch (error) {
        console.error('Error updating navigator path:', error);
        // Recover gracefully
        breadcrumbPath.value = [getRootFolder()];
        updateCurrentItems();
      }
    };

    // Watch for changes in the folder hierarchy
    watch(() => props.folderHierarchy, () => {
      processHierarchy();
    }, { immediate: true });    // Handle item click with enhanced shift+click functionality
    // Handle item click with enhanced shift+click functionality
    const handleItemClick = (item: NavigatorItem, event: MouseEvent) => {
      if (!item) {
        console.warn('handleItemClick called with undefined item');
        return;
      }

      console.log('handleItemClick called with:', {
        itemId: item.id,
        itemType: item.type,
        shiftKey: event.shiftKey,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        currentSelection: [...selectedItems.value]
      });

      // Close dropdown if it's open
      if (showFullPathDropdown.value) {
        showFullPathDropdown.value = false;
        cleanupDropdownListener();
      }

      // Check if we're in selection mode (have selected items)
      const isSelectionMode = selectedItems.value.length > 0;

      // Handle folder click in selection mode - prioritize selection over navigation
      if (item.type === 'folder' && isSelectionMode) {
        // In selection mode, folders should be selected, not navigated into

        // Check if we need to handle multi-selection with modifier keys
        if (event.shiftKey && lastSelectedItem.value) {
          // Handle range selection
          handleRangeSelection(item, event);
        } else if (event.ctrlKey || event.metaKey) {
          // Toggle this item's selection
          toggleItemSelection(item);
        } else {
          // Simple selection without modifiers - check if already selected
          if (isSelected(item.id)) {
            // If already selected, unselect it
            updateSelection(selectedItems.value.filter(id => id !== item.id));

            // If selection is now empty, clear lastSelectedItem
            if (selectedItems.value.length === 0) {
              lastSelectedItem.value = null;
            }
          } else {
            // Not already selected, select just this folder
            if (item.id !== null) {
              updateSelection([item.id]);
              lastSelectedItem.value = item;
            }
          }
        }

        // DON'T emit selectItem when in selection mode - this prevents navigation
        // emit('selectItem', item); // <-- REMOVED THIS LINE
        return; // Skip navigation logic
      }

      // Handle folder click with modifier keys (even when not in selection mode)
      if (item.type === 'folder' && (event.ctrlKey || event.metaKey || event.shiftKey)) {
        // Start selection mode with modifier keys

        if (event.shiftKey && lastSelectedItem.value) {
          // Handle range selection
          handleRangeSelection(item, event);
        } else if (event.ctrlKey || event.metaKey) {
          // Toggle this item's selection
          toggleItemSelection(item);
        }

        // DON'T emit selectItem when using modifier keys - this prevents navigation
        return; // Skip navigation logic
      }

      // Handle normal click on folder without modifier keys (when not in selection mode)
      if (item.type === 'folder' && !event.ctrlKey && !event.metaKey && !event.shiftKey && !isSelectionMode) {
        // Normal folder navigation
        console.log('Navigating to folder:', item.id);
        // Find the folder in the hierarchy
        const currentFolder = breadcrumbPath.value[breadcrumbPath.value.length - 1];

        // Add null check for currentFolder and its children property
        if (!currentFolder || !currentFolder.children) {
          console.warn('Current folder or its children property is undefined, rebuildPath and try again');
          // Try to rebuild the path and retry
          rebuildPath();
          updateCurrentItems();

          // After rebuilding, check again
          const updatedCurrentFolder = breadcrumbPath.value[breadcrumbPath.value.length - 1];
          if (!updatedCurrentFolder || !updatedCurrentFolder.children) {
            console.error('Still unable to access folder hierarchy after rebuild');
            emit('toggleFolder', item); // Still emit the event to notify parent
            return;
          }
        }

        const nextFolder = currentFolder.children?.find((child: FolderWithMeta) => child.id === item.id);

        if (nextFolder) {
          // Navigate into the folder
          breadcrumbPath.value.push(nextFolder);
          updateCurrentItems();

          // Emit the toggle folder event
          emit('toggleFolder', item);

          // Clear any existing selection when navigating
          if (selectedItems.value.length > 0) {
            updateSelection([]);
          }

          // Emit select item event for the folder - this is OK for navigation
          emit('selectItem', item);
        }

        return; // Skip the remaining selection logic
      }

      // Handle multi-selection with Ctrl/Cmd key
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {
        toggleItemSelection(item);
        return;
      }

      // Handle range selection with Shift key
      if (event.shiftKey && lastSelectedItem.value) {
        handleRangeSelection(item, event);
        return;
      }

      // For non-folder items or single-click selection without modifiers
      if (item.id !== null) {
        // Check if we're clicking on an already selected item
        if (isSelected(item.id)) {
          // If clicking on an already selected item, unselect it
          const newSelection = selectedItems.value.filter(id => id !== item.id);
          updateSelection(newSelection);

          // If selection is now empty, clear lastSelectedItem
          if (newSelection.length === 0) {
            lastSelectedItem.value = null;
          } else {
            // Otherwise, update lastSelectedItem to another selected item
            const lastId = newSelection[newSelection.length - 1];
            const lastItem = currentItems.value.find((i: NavigatorItem) => i.id === lastId);
            if (lastItem) {
              lastSelectedItem.value = lastItem;
            }
          }
        } else {
          // Otherwise, select just this item (clearing previous selection)
          updateSelection([item.id]);
          lastSelectedItem.value = item;
        }
      } else {
        updateSelection([]);
        lastSelectedItem.value = null;
      }

      // Only emit selectItem for non-folder items or when not using modifier keys
      if (item.type !== 'folder') {
        emit('selectItem', item);
      }
    };
    // Helper function to toggle an item's selection
    const toggleItemSelection = (item: NavigatorItem) => {
      // Skip null IDs
      if (item.id === null) return;

      // Ctrl/Cmd click - toggle selection
      const index = selectedItems.value.indexOf(item.id);
      if (index === -1) {
        // Add to selection
        updateSelection([...selectedItems.value, item.id]);
        // Update last selected item
        lastSelectedItem.value = item;
      } else {
        // Remove from selection
        const newSelection = selectedItems.value.filter(id => id !== item.id);
        updateSelection(newSelection);

        // If we're removing the last selected item or the selection is now empty,
        // update lastSelectedItem accordingly
        if (lastSelectedItem.value?.id === item.id) {
          // If selection is now empty, clear lastSelectedItem
          if (newSelection.length === 0) {
            lastSelectedItem.value = null;
          } else {
            // Otherwise, set lastSelectedItem to the last item in the selection
            const lastId = newSelection[newSelection.length - 1];
            const lastItem = currentItems.value.find((i: NavigatorItem) => i.id === lastId);
            if (lastItem) {
              lastSelectedItem.value = lastItem;
            }
          }
        }
      }
    };

    // Clear all selections - to be called from parent component
    const clearSelection = () => {
      console.log('Clearing all selections in FolderNavigator');
      updateSelection([]);
      lastSelectedItem.value = null;

      // Also close dropdown if open and clean up listener
      if (showFullPathDropdown.value) {
        showFullPathDropdown.value = false;
        cleanupDropdownListener();
      }
    };

    // Helper function to handle range selection with Shift key
    const handleRangeSelection = (item: NavigatorItem, event: MouseEvent) => {
      // Shift-click - select range
      console.log('Shift+click detected with lastSelectedItem:', lastSelectedItem.value?.id);

      // Find indexes for selection range
      const lastItemIndex = currentItems.value.findIndex((i: NavigatorItem) => i.id === lastSelectedItem.value!.id);
      const currentItemIndex = currentItems.value.findIndex((i: NavigatorItem) => i.id === item.id);

      console.log('Range selection indexes:', { lastItemIndex, currentItemIndex });

      if (lastItemIndex !== -1 && currentItemIndex !== -1) {
        // Determine start and end for the range
        const start = Math.min(lastItemIndex, currentItemIndex);
        const end = Math.max(lastItemIndex, currentItemIndex);

        // Prepare selection array
        const newSelection = event.ctrlKey || event.metaKey
          ? [...selectedItems.value] // Keep existing selections if Ctrl/Cmd is also pressed
          : []; // Otherwise, start fresh
        // Add all items in range
        for (let i = start; i <= end; i++) {
          const itemId = currentItems.value[i].id;
          if (itemId !== null && !newSelection.includes(itemId)) {
            newSelection.push(itemId);
          }
        }

        // Update selection
        updateSelection(newSelection);

        // If selection became empty, clear lastSelectedItem
        if (newSelection.length === 0) {
          lastSelectedItem.value = null;
        } else {
          // Update lastSelectedItem to the current item being clicked
          lastSelectedItem.value = item;
        }
      }
    };

    // Request rename for an item
    const requestRename = (item: NavigatorItem) => {
      if (item.type === 'folder') {
        emit('renameFolderRequest', item);
      }
    };

    // Handle breadcrumb click
    const handleBreadcrumbClick = (index: number) => {
      // Don't do anything if clicking the current folder
      if (index === breadcrumbPath.value.length - 1) return;

      // Navigate to the selected breadcrumb level
      breadcrumbPath.value = breadcrumbPath.value.slice(0, index + 1);
      updateCurrentItems();

      // Emit the select item event for the current folder
      const currentFolderInNav = breadcrumbPath.value[breadcrumbPath.value.length - 1];

      // If currentFolderInNav.id is -1 (root), FoldersView expects id: null
      const emitId = currentFolderInNav.id === -1 ? null : currentFolderInNav.id;

      emit('selectItem', {
        id: emitId,
        name: currentFolderInNav.name,
        type: 'folder',
        // Ensure other FolderWithMeta properties are present if needed by selectFolder in FoldersView
        children: currentFolderInNav.children || [],
        notesCount: currentFolderInNav.notesCount || 0,
        childFoldersCount: currentFolderInNav.children?.length || 0,
        book_id: currentFolderInNav.book_id
      } as FolderWithMeta);
    };

    // Get the current folder name
    const getCurrentFolderName = (): string => {
      if (breadcrumbPath.value.length === 0) return props.rootFolderName;
      return breadcrumbPath.value[breadcrumbPath.value.length - 1].name;
    };    // Handle click outside to close dropdown
    const handleClickOutside = (event: MouseEvent) => {
      if (showFullPathDropdown.value) {
        const target = event.target as HTMLElement;
        const isClickInsideDropdown = target.closest('.breadcrumb-dropdown') !== null;
        const isClickOnTruncated = target.closest('.breadcrumb-item.truncated') !== null;

        if (!isClickInsideDropdown && !isClickOnTruncated) {
          showFullPathDropdown.value = false;
        }
      }
    };

    // Handle delete for selected items
    const handleDeleteSelected = () => {
      if (selectedItems.value.length > 0) {
        // Emit event with selected item IDs to parent component
        emit('deleteItems', selectedItems.value);
      }
    };

    // Handle keyboard events (Escape and Delete keys)
    const handleKeyboardEvents = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // If dropdown is open, close it first
        if (showFullPathDropdown.value) {
          showFullPathDropdown.value = false;
          cleanupDropdownListener(); // Clean up the listener
        }
        // Only clear selection if no modal is open
        else if (selectedItems.value.length > 0 && !props.isModalOpen) {
          console.log('Escape key pressed - clearing selection');
          updateSelection([]);
        }
        // If modal is open, do nothing (let the modal handle the escape key)
      } else if (e.key === 'Delete' && selectedItems.value.length > 0 && !props.isModalOpen) {
        // Check if any selected items are protected folders
        const hasProtectedFolder = selectedItems.value.some(itemId => {
          const item = currentItems.value.find(i => i.id === itemId);
          if (item && item.type === 'folder') {
            // Check if this is the "Books" root folder
            return item.name === 'Books' && item.parent_id === null;
          }
          return false;
        });

        // Only allow delete if no protected folders are selected
        if (!hasProtectedFolder && !props.isProtectedFolderSelected) {
          console.log('Delete key pressed - deleting selected items');
          handleDeleteSelected();
        } else {
          console.log('Delete key pressed - but protected folder is selected, ignoring');
        }
      }
    };
    // Handle container click
    const handleContainerClick = (event: MouseEvent) => {
      // Only handle direct clicks on the container (not bubbled events)
      if (event.target === event.currentTarget && selectedItems.value.length > 0) {
        console.log('Container clicked - clearing selection');
        updateSelection([]);
      }
    };    // Update selection with proper handling of lastSelectedItem and events
    const updateSelection = (newSelection: (string | number | null)[]) => {
      console.log('Updating selection from:', selectedItems.value, 'to:', newSelection);

      // Filter out any null values from the selection
      const filteredSelection = newSelection.filter(id => id !== null);

      // Store the previous selection for comparison
      const prevSelection = [...selectedItems.value];

      // Assign the filtered selection
      selectedItems.value = filteredSelection;

      // If selection is empty, clear lastSelectedItem
      if (filteredSelection.length === 0) {
        lastSelectedItem.value = null;
      }

      // Force a UI refresh on selection change by triggering a redraw
      // This is done at the Vue template level now with :class binding, rather than DOM manipulation

      // For items that were removed from selection, make sure they don't keep the selected styling
      if (prevSelection.length > 0) {
        prevSelection.forEach(id => {
          if (!filteredSelection.includes(id)) {
            console.log(`Ensuring item ${id} is no longer styled as selected`);
          }
        });
      }

      // Emit selection change
      emit('selectionChange', filteredSelection);
    };

    // Setup event listeners on mount
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleKeyboardEvents);
    });
    // Clean up event listeners before unmount
    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyboardEvents);
      // Clean up dropdown listener if it exists
      cleanupDropdownListener();
    });

    // Watch for changes in currentSelectedFolder to update path when selected externally
    // Moved here to ensure all functions are defined before it's used
    watch(() => props.currentSelectedFolder, (newFolder) => {
      console.log('FolderNavigator - currentSelectedFolder changed:', newFolder?.name || 'null');

      try {
        if (newFolder) {
          updateNavigatorPath(newFolder);
        } else {
          // If null, we're at root folder - initialize with root folder
          const rootFolder = getRootFolder();
          breadcrumbPath.value = [rootFolder];
          currentFolderId.value = null;
          updateCurrentItems();
        }
      } catch (error) {
        console.error('Error in currentSelectedFolder watcher:', error);
        // Recover gracefully by initializing with root folder
        breadcrumbPath.value = [getRootFolder()];
        updateCurrentItems();
      }
    }, { immediate: true });

    return {
      breadcrumbPath,
      currentFolderId,
      currentItems,
      selectedItems,
      isSelected,
      handleItemClick,
      handleBreadcrumbClick,
      getCurrentFolderName,
      requestRename,
      displayedBreadcrumbs, toggleFullPathDropdown,
      showFullPathDropdown,
      shouldTruncatePath,
      isFolderEmpty,
      handleRootClick,
      handleContainerClick,
      handleDeleteSelected,
      clearSelection,
      isProtectedFolder,
      getFolderIconStyle,
      getFolderIconSrc
    };
  }
});
</script>

<style scoped>
.folder-navigator {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-card-bg);
  border-radius: 8px;
}

/* Breadcrumb navigation - FIXED at top */
.breadcrumb-container {
  padding: 8px 12px;
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-secondary);
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
  z-index: 100;
  flex-shrink: 0;
  /* CRITICAL: Don't let it shrink */
}

/* Folder list */
.folder-list-container {
  flex: 1;
  /* CRITICAL: Take remaining space */
  overflow-y: auto;
  padding: 4px;
  /* Make sure it can actually scroll */
  min-height: 0;
  /* CRITICAL: Allow flexbox to shrink this */
}

.folder-list-container.selection-mode {
  background-color: var(--color-nav-item-active);
}

.folder-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.folder-list-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 2px;
  transition: background-color 0.15s ease;
  position: relative;
  user-select: none;
}

.folder-list-item:hover {
  background-color: var(--color-nav-item-hover);
}

/* Override hover when in selection mode to only show select style on selected items */
.selection-mode .folder-list-item:not(.selected):hover {
  background-color: var(--color-nav-item-hover);
  /* Lighter hover effect */
  opacity: 0.9;
}

.selection-mode {
  background-color: var(--color-nav-item-active);
  /* Subtle background change in selection mode */
}

.folder-list-item.selected {
  background-color: var(--color-nav-item-active);
  box-shadow: 0 0 0 1px var(--color-border-focus);
}

/* Add a subtle indicator to show items are selectable */
.folder-list-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

/* Make selection indicator more visible in selection mode */
.selection-mode .folder-list-item::before {
  width: 4px;
}

.folder-list-item.selected::before {
  background-color: var(--color-primary);
}

.cursor-pointer {
  cursor: pointer;
}

/* In selection mode all items should be selectable */
.selection-mode .folder-list-item {
  cursor: pointer;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.item-icon img {
  width: 20px;
  height: 20px;
}

.item-name {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 5px;
  /* Add some space before potential icons */
}

/* Rename Icon */
.rename-icon {
  width: 14px;
  height: 14px;
  margin-left: auto;
  /* Pushes it to the right, before count and chevron */
  margin-right: 8px;
  opacity: 0.6;
  cursor: pointer;
  display: none;
  /* Hidden by default */
}

.folder-list-item:hover .rename-icon {
  display: inline-block;
  /* Show on hover */
}

.rename-icon:hover {
  opacity: 1;
}

.item-count {
  background-color: var(--color-item-count-bg);
  color: var(--color-item-count-text);
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 12px;
  margin-left: 8px;
}

.chevron-right {
  width: 12px;
  height: 12px;
  transform: rotate(-90deg);
  opacity: 0.6;
  margin-left: 8px;
}

/* Empty folder message */
.empty-folder-message {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--color-text-tertiary);
  text-align: center;
}

.empty-folder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-folder-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.button-text {
  font-weight: 500;
}

/* Breadcrumb item styles */
.breadcrumb-item {
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.breadcrumb-item.truncated {
  color: var(--color-text-tertiary);
  cursor: pointer;
  text-decoration: underline dotted;
  background-color: var(--color-bg-secondary);
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin: 0 4px;
}

.breadcrumb-item.truncated:hover {
  background-color: var(--color-nav-item-hover);
}

.breadcrumb-item:not(.current):not(.truncated):hover {
  background-color: var(--color-nav-item-hover);
}

.breadcrumb-item.current {
  font-weight: 600;
  color: var(--color-text-primary);
}

.breadcrumb-separator {
  margin: 0 4px;
  color: var(--color-text-tertiary);
}

/* Dropdown for full breadcrumb path */
.breadcrumb-expand {
  position: fixed;
  z-index: 9999;
  animation: fadeIn 0.15s ease-in-out;
  pointer-events: auto;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.breadcrumb-dropdown {
  background-color: var(--color-modal-bg);
  border: 1px solid var(--color-modal-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--color-card-shadow);
  z-index: 1000;
  padding: 8px;
  display: flex;
  flex-direction: column;
  width: 250px;
  overflow: hidden;
  animation: dropdown-appear 0.2s ease-out forwards;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.breadcrumb-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  margin: 2px 0;
  transition: all 0.2s ease;
  color: var(--color-text-secondary);
  flex-shrink: 0;
  min-height: 36px;
}

.breadcrumb-dropdown-item:hover {
  background-color: var(--color-nav-item-hover);
  color: var(--color-text-primary);
  transform: translateX(2px);
}

.breadcrumb-dropdown-item.current {
  font-weight: 600;
  color: var(--color-primary);
  background-color: var(--color-nav-item-active);
}

.dropdown-level-indicator {
  display: inline-block;
  width: 16px;
  height: 16px;
  min-width: 16px;
  background: url('/icons/folder-icon.svg') no-repeat center;
  background-size: contain;
  margin-right: 8px;
  opacity: 0.7;
  flex-shrink: 0;
}

.dropdown-level-indicator:first-child::before {
  background-image: url('/icons/folder-open-icon.svg');
}

.breadcrumb-dropdown .dropdown-content {
  display: flex;
  flex-direction: column;
  max-height: 350px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  padding-right: 4px;
}

.breadcrumb-dropdown .dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.breadcrumb-dropdown .dropdown-content::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.breadcrumb-dropdown .dropdown-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-thumb-hover);
}

.breadcrumb-dropdown .dropdown-content::-webkit-scrollbar-track {
  background-color: var(--color-scrollbar-track);
  border-radius: 4px;
}
</style>