# Changelog

## [0.6.0] - 2025-05-30

### Added
- 📖 Book Details & Management
  - Added BookDetailsModal.vue for viewing, editing, rating, and deleting books
  - BookCard.vue now displays book rating and note count
  - Implemented ability to rate books and track reading progress
  - Linked notes to books via book_id; notes can be created/viewed directly from book details
  - Book-specific notes are displayed in the Book Details modal
- 📝 Notes Integration
  - Added support for creating notes linked to specific books
  - Notes for a book are searchable and accessible from the book details view
- 🎨 UI/UX Improvements
  - Enhanced BooksView and BookCard UI to show ratings, note counts, and improved interactions
  - Improved modal transitions and error handling for book operations

### Changed
- 🔄 Refactored notes and books database schema to support book_id relationship
- 🛠️ Improved IPC and API logic for book editing, deletion, and note linkage

### Fixed
- 🐞 Fixed issues with book cover updates, rating persistence, and note linkage

## [0.5.0] - 2025-05-24

### Added
- 📚 Book Tracking
  - Added books table to the database schema for storing book information
  - Implemented books-api.ts with all book-related CRUD and OpenLibrary integration
  - Book search, addition, and OpenLibrary API integration are handled in books-api.ts (no separate openlibrary-api file)
  - Book cover download and local storage implemented via media-api.ts and media_files table
  - Created BooksView.vue page and Book search UI based on Figma design
  - Book cover management and storage handled through media-api.ts and media_files table
- 🎨 UI/UX Improvements
  - Designed and implemented BooksView and Book search UI in Figma
  - Improved book addition flow and cover image handling
  - Added feedback and error handling for book search and addition

### Changed
- 🛠️ Refactored database and API layers to support book-related operations
- 📦 Improved file storage for book covers using media-api.ts and media_files table
- 🔄 Enhanced IPC communication for book operations

### Fixed
- 🐞 Various bug fixes and UI polish for book addition and cover download flows

## [0.4.0] - 2025-05-18

### Added
- 📁 Folder Organization  - Implemented Folder Create, Read, Update, Delete logic in folders-api.ts
  - Created logic to link Notes to Folders with folder_id relationship
  - Developed FoldersView.vue and FolderNavigator.vue components based on Figma designs
  - Added FolderContent.vue to display notes within selected folders
  - Implemented SingleFolder.vue component for individual folder display and management
  - Created FolderToolbar.vue for folder operation controls (create, rename, delete)
- 🔄 Note-Folder Integration
  - Implemented UI to assign notes to folders through an intuitive interface
  - Added ability to filter Notes list based on selected folder
  - Created drag-and-drop functionality for moving notes between folders
- 🎨 UI/UX Improvements
  - Enhanced folder tree visualization with proper indentation and hierarchy
  - Added folder icons with open/closed states
  - Implemented smooth transitions for folder operations

### Changed
- 🔄 Refactored note listing to work with folder filtering
- 📱 Improved sidebar navigation with better folder visibility
- 🛠️ Enhanced IPC communication for folder operations
- 📊 Updated database schema to better support folder relationships

## [0.3.0] - 2025-05-10

### Added
- 📝 Note Creation and Management  - Implemented Note Create, Read, Update, Delete logic in notes-api.ts
  - Created NotesView.vue page based on Figma designs
  - Integrated basic Note list display with data fetching via IPC
  - Implemented Tiptap rich text editor (NoteEditor.vue component)
  - Connected Note Editor to create/update IPC calls for seamless editing
- 🧪 Testing and Quality Assurance
  - Added unit tests for Note API CRUD functions
  - Implemented backend integration testing for notes functionality
- 🎨 UI/UX Improvements
  - Finalized NotesView and NoteEditor components based on Figma designs
  - Added smooth transitions between notes list and editor
  - Improved responsive layout for different screen sizes

### Changed
- 🔄 Enhanced IPC communication for note operations
- 📱 Improved note list refreshing after operations
- 🛠️ Refactored database API for better error handling

## [0.2.0] - 2025-05-05

### Added
- 🗄️ Database infrastructure with SQLite setup  - Implemented database.ts utility with SQLite connection & initialization
  - Defined and created initial database schema (Notes, Folders tables)
  - Implemented database-api.ts with basic CRUD functions for Notes & Folders
- 📝 API layer for core functionality  - Set up notes-api.ts & folders-api.ts skeletons with full CRUD operations
  - Created robust IPC handlers in main.ts for all Note/Folder operations
- 🧪 Database testing infrastructure
  - Added comprehensive tests for database connection and schema creation
- 🪟 Window styling improvements
  - Implemented custom title bar with window controls
  - Added route name & icon display in title bar
- 🧭 Enhanced sidebar navigation
  - Improved visual consistency
  - Added better hover and selection states

### Changed
- 🏗️ Moved to frameless window architecture for cleaner UI
- 🔄 Refactored IPC communication pattern for better type safety
- 📱 Improved responsive layout handling

## [0.1.0] - 2025-04-30

### Added
- 🚀 Project initialization with Electron, Vue 3, and TypeScript
- 🛠️ Configured build system using electron-builder and Vite
- 🪟 Setup basic window management in Electron main process
- 🧩 Implemented core application components:
  - Main layout structure with Vue components
  - SidebarNavigation component
  - Icons for all main sections (dashboard, notes, folders, books, timer, settings)
- 📋 Created placeholder views for all main application sections:
  - DashboardView
  - NotesView
  - FoldersView
  - BooksView
  - TimerView
  - SettingsView
- 🔍 Configured VS Code integration for debugging and development
- 🔄 Setup GitHub workflows for CI/CD
- 🧭 Implemented basic router configuration for navigation between views

### Infrastructure
- 📝 TypeScript configuration for both main and renderer processes
- ⚡ Vite build pipeline setup
- 📦 electron-builder configuration for packaging
- 👷 GitHub Actions for automated builds

### In Progress
- 📡 Main process to renderer process communication (IPC)
- 📝 Note creation and editing functionality
- 🗃️ Database integration