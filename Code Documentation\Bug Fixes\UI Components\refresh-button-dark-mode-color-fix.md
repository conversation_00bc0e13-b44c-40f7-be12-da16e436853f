# Refresh Button Dark Mode Color Fix

## Issue Description
The refresh button icon in the ChartSection component was not displaying with the correct color in dark mode. The icon appeared with incorrect contrast and didn't follow the established theme color system.

## Root Cause
The refresh button was using an external SVG file with `<img src="/icons/refresh-icon.svg">`. When using external SVG files with `<img>` tags, the `fill="currentColor"` attribute in the SVG cannot access CSS color properties from the parent HTML element. This means the icon remained black regardless of the theme.

## Files Modified
- `src/components/timer/ChartSection.vue` - Updated refresh button color styling

## Solution Implementation

### Before (External SVG with img tag)
```html
<img
  src="/icons/refresh-icon.svg"
  alt="Refresh"
  :class="{ 'rotating': isRefreshing }"
/>
```

```css
.refresh-button {
  color: var(--color-text-secondary);
}
```

### After (Inline SVG with theme variable)
```html
<svg
  width="16"
  height="16"
  viewBox="0 0 16 16"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  :class="{ 'rotating': isRefreshing }"
>
  <path
    d="M13.65 2.35C12.18 0.88 10.21 0 8 0C3.58 0 0 3.58 0 8s3.58 8 8 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L9 5h7V-2l-2.35 2.35z"
    fill="var(--color-icon-fill)"
  />
</svg>
```

```css
.refresh-button svg {
  width: 16px;
  height: 16px;
}
```

### Key Changes

1. **Inline SVG**: Converted from external SVG file to inline SVG element
2. **Direct Theme Variable**: Uses `fill="var(--color-icon-fill)"` directly in the SVG path
3. **Proper CSS Targeting**: Updated CSS selectors from `img` to `svg`
4. **Theme Consistency**: Now follows the same pattern as other SVG icons in DashboardView.vue

### CSS Variable Values
The `--color-icon-fill` variable provides proper contrast in both themes:

**Light Mode:**
```css
--color-icon-fill: #4A4A4A;
```

**Dark Mode:**
```css
--color-icon-fill: #E0E0E0;
```

## Testing
- ✅ Refresh button icon displays correctly in light mode
- ✅ Refresh button icon displays correctly in dark mode
- ✅ Icon maintains proper contrast in both themes
- ✅ Hover states work correctly
- ✅ Rotation animation during refresh works properly

## Benefits
1. **Proper Dark Mode Support**: Icon now displays with correct contrast in dark mode
2. **Theme Consistency**: Uses the same color system as other SVG icons
3. **Better Visibility**: Proper icon color ensures good readability
4. **Maintainable Code**: Uses centralized color variables from theme system

## Impact
This fix ensures that the refresh button icon follows the established theme color system and displays correctly in both light and dark modes, providing consistent visual experience across the timer analytics section.
