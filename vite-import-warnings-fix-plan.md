# Vite Import Warnings Fix Plan - Static Imports Solution

## Executive Summary

This document provides step-by-step instructions to fix Vite build warnings about mixed static/dynamic imports. The solution converts all dynamic imports to static imports, which is the most straightforward and maintainable approach for an Electron application.

## Files and Lines to Change

### File 1: `electron/main/api/sync-logic/manifest-manager.ts`

**Change 1.1** - Line 11
- **Current**: `import { dbAll, withReadTransaction } from '../../database/database-api';`
- **Change to**: `import { dbAll, withReadTransaction, dbGet } from '../../database/database-api';`
- **Description**: Add `dbGet` to the existing import statement

**Change 1.2** - Line 309
- **Current**: `const { dbGet } = await import('../../database/database-api');`
- **Change to**: DELETE THIS ENTIRE LINE
- **Description**: Remove the dynamic import as dbGet is now statically imported

**Change 1.3** - Line 1 (at the very top, after existing imports)
- **Current**: No import for unified-sync-engine
- **Add new line**: `import { unifiedSyncEngine } from './unified-sync-engine';`
- **Description**: Add static import for unifiedSyncEngine at the top of the file

**Change 1.4** - Line 144
- **Current**: `const { unifiedSyncEngine } = await import('./unified-sync-engine');`
- **Change to**: DELETE THIS ENTIRE LINE
- **Description**: Remove the dynamic import as unifiedSyncEngine is now statically imported

### File 2: `electron/main/api/folders-api.ts`

**Change 2.1** - After line 18 (in the import section)
- **Current**: No import for deleteNoteWithValidation
- **Add new line**: `import { deleteNoteWithValidation } from './notes-api';`
- **Description**: Add static import for deleteNoteWithValidation function

**Change 2.2** - Line 383
- **Current**: `const { deleteNoteWithValidation } = await import('./notes-api');`
- **Change to**: DELETE THIS ENTIRE LINE
- **Description**: Remove the dynamic import as deleteNoteWithValidation is now statically imported

## How to Apply the Changes

### Step 1: Open `manifest-manager.ts`

1. Locate line 11 where database-api is imported
2. Add `, dbGet` before the closing brace so it reads:
   ```typescript
   import { dbAll, withReadTransaction, dbGet } from '../../database/database-api';
   ```

3. Scroll to the top of the file and add this new import line after the existing imports:
   ```typescript
   import { unifiedSyncEngine } from './unified-sync-engine';
   ```

4. Find line 144 (search for "await import('./unified-sync-engine')")
5. Delete the entire line: `const { unifiedSyncEngine } = await import('./unified-sync-engine');`

6. Find line 309 (search for "await import('../../database/database-api')")
7. Delete the entire line: `const { dbGet } = await import('../../database/database-api');`

### Step 2: Open `folders-api.ts`

1. Locate the import section at the top (around line 18, after the database-api imports)
2. Add this new import line:
   ```typescript
   import { deleteNoteWithValidation } from './notes-api';
   ```

3. Find line 383 (search for "await import('./notes-api')")
4. Delete the entire line: `const { deleteNoteWithValidation } = await import('./notes-api');`

## What the Code Will Look Like After Changes

### manifest-manager.ts (top section)
```typescript
import * as crypto from 'crypto';
import * as path from 'path';
import { 
  SyncManifest, 
  ManifestItem, 
  DeletionRecord,
  LocalItem 
} from './types';
import { fileOperations } from './file-operations';
import { getSetting, setSetting } from '../settings-api';
import { dbAll, withReadTransaction, dbGet } from '../../database/database-api';
import { unifiedSyncEngine } from './unified-sync-engine';
```

### manifest-manager.ts (around line 309)
```typescript
// Check if book has a cover in media_files table and add coverImage metadata
try {
  // Line 309 removed - no more dynamic import here
  const mediaQuery = `SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1`;
  const mediaFile = await dbGet<any>(mediaQuery, [book.id]);
```

### manifest-manager.ts (around line 144)
```typescript
private async performInitialSync(backupPath: string): Promise<void> {
  try {
    // Line 144 removed - no more dynamic import here
    
    // Perform sync to create all physical files
    await unifiedSyncEngine.sync(backupPath);
```

### folders-api.ts (import section)
```typescript
import {
    createFolder,
    getAllFolders,
    // ... other imports ...
} from '../database/database-api';
import { sanitizeFilename } from '../../utils/filename-sanitizer';
import { notifyFolderChange } from '../database/database-hooks';
import { deleteNoteWithValidation } from './notes-api';
```

### folders-api.ts (around line 383)
```typescript
} else {
    // Delete all notes in the folder (user chose "Delete all notes")
    // Line 383 removed - no more dynamic import here
    
    for (const note of notesInFolder) {
        if (note.id !== undefined) {
            await deleteNoteWithValidation(note.id);
        }
    }
}
```

## Verification Steps

After applying all changes:

1. **Build the project**: Run `npm run build`
   - The three warnings about dynamic imports should no longer appear
   - The build should complete successfully with no TypeScript errors

2. **Test affected features**:
   - Test creating a new backup location (tests manifest-manager changes)
   - Test deleting a folder with notes inside (tests folders-api changes)
   - Test syncing files with book covers (tests database-api usage)

3. **Expected outcome**:
   - No more Vite warnings about mixed imports
   - All functionality remains the same
   - Slightly faster startup time due to no dynamic imports

## Why This Solution Works

The warnings occurred because Vite detected that the same modules were being imported both statically (at module load time) and dynamically (at runtime). Since the modules were already included in the bundle due to static imports elsewhere, the dynamic imports provided no benefit and only added complexity. By converting everything to static imports, we:

1. Eliminate the warnings
2. Simplify the code
3. Improve type checking
4. Maintain all existing functionality
5. Slightly improve performance by removing async import overhead