<template>
  <Teleport to="body">
    <div class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit Book</h3>
        <div class="close-button" @click="handleClose">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>
      </div>

      <div class="form-container">
        <div class="form-content">
          <div class="book-info-container">
            <!-- First row with cover and main info -->
            <div class="main-info-row">
              <div class="cover-column">
                <div class="cover-upload" @click="handleCoverUpload">
                  <div v-if="!formData.cover_url" class="upload-placeholder">
                    Upload Cover
                  </div>
                  <img v-else :src="formData.cover_url" alt="Book cover" class="cover-preview" />
                  <input ref="fileInput" type="file" accept="image/*" @change="handleFileChange" style="display: none;" />
                </div>
              </div>

              <div class="main-fields-column">
                <div class="form-field">
                  <label>Title</label>
                  <input type="text" v-model="formData.title" placeholder="e.g., The Great Gatsby" />
                </div>

                <div class="form-field">
                  <label>Author</label>
                  <input type="text" v-model="formData.author" placeholder="e.g., F. Scott Fitzgerald" />
                </div>

                <div class="form-field">
                  <label>ISBN</label>
                  <input
                    type="text"
                    v-model="formData.isbn"
                    @input="validateISBN"
                    placeholder="e.g., 978-3-16-148410-0"
                    :class="{'input-error': isbnError}"
                  />
                  <small v-if="isbnError" class="error-message">{{ isbnError }}</small>
                </div>
              </div>
            </div>

            <!-- Second set of rows that start under the cover -->
            <div class="form-section">
              <div class="form-row">
                <div class="form-field">
                  <label>Publication Year</label>
                  <input
                    type="text"
                    v-model="formData.publication_date"
                    @input="validatePublicationYear"
                    placeholder="Enter year (e.g., 2025 or -32 for BCE)"
                    :class="{'input-error': publicationYearError}"
                  />
                  <small v-if="publicationYearError" class="error-message">{{ publicationYearError }}</small>
                </div>

                <div class="form-field">
                  <label>Language</label>
                  <input type="text" v-model="formData.language" placeholder="e.g., English" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Genre</label>
                  <input type="text" v-model="formData.genres" placeholder="e.g., Fiction, Mystery, Romance" />
                </div>

                <div class="form-field">
                  <label>Pages</label>
                  <input
                    type="number"
                    min="0"
                    v-model="formData.page_count"
                    @input="validatePageCount"
                    placeholder="e.g., 320"
                    :class="{'input-error': pageCountError}"
                  />
                  <small v-if="pageCountError" class="error-message">{{ pageCountError }}</small>
                </div>
              </div>

              <div class="form-field full-width">
                <label>Your rating</label>
                <div class="rating-container">
                  <div class="rating-stars">
                    <img
                      v-for="star in 5"
                      :key="star"
                      @click="setRating(star)"
                      :src="star <= formData.rating ? '/icons/star-filled-icon.svg' : '/icons/star-icon.svg'"
                      class="star"
                      :class="{ 'star-filled': star <= formData.rating }"
                      width="16"
                      height="16"
                      alt="Star rating"
                    />
                  </div>
                </div>
              </div>

              <div class="form-field full-width">
                <label>Description</label>
                <textarea v-model="formData.description" rows="2" placeholder="e.g., A brief summary of the book's content or plot..."></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="modal-footer-left">
          <button class="btn btn-back" @click="handleGoBack">
            <img src="/icons/goback-icon.svg" alt="Go back" class="back-icon" />
            <span>Go Back</span>
          </button>
        </div>
        <div class="modal-footer-right">
          <button class="btn btn-secondary" @click="handleClose">Cancel</button>
          <button class="btn btn-primary" @click="handleSave" :disabled="!isFormValid || isAdding">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="add-icon">
              <path d="M8 0V16M0 8H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            <span v-if="isAdding">Saving...</span>
            <span v-else>Save</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import type { BookSearchResult } from '../../types/electron-api';

// Define the form data interface
interface BookFormData {
  title: string;
  author: string;
  publication_date: number | null;
  isbn: string;
  language: string;
  page_count: number | null;
  genres: string;
  rating: number;
  description: string;
  cover_url: string | null;
}

export default defineComponent({
  name: 'EditBookModal',
  props: {
    bookData: {
      type: Object as () => BookSearchResult,
      required: true
    }
  },
  emits: ['close', 'save-book', 'go-back', 'cover-changed'],
  setup(props, { emit }) {
    const fileInput = ref<HTMLInputElement | null>(null);

    // The current year for validation
    const currentYear = new Date().getFullYear();
    const publicationYearError = ref<string>('');
    const pageCountError = ref<string>('');
    const isbnError = ref<string>('');
    const isAdding = ref(false);

    // Track if user has changed the cover manually
    const userChangedCover = ref(false);

    // Store the original cover to ensure it doesn't get lost during edits
    const originalCover = ref<string | null>(null);

    // Extract data mapping logic into a pure function
    const mapBookDataToForm = (bookData: BookSearchResult): BookFormData => {
      return {
        title: bookData?.title || '',
        author: bookData?.author_name ? bookData.author_name.join(', ') : '',
        publication_date: bookData?.first_publish_year || null,
        isbn: bookData?.isbn_primary ||
              (Array.isArray(bookData?.isbn) && bookData.isbn.length > 0 ? bookData.isbn[0] : ''),
        language: (() => {
          // Handle both array and string formats for language
          if (Array.isArray(bookData?.language) && bookData.language.length > 0) {
            return bookData.language[0];
          } else if (typeof bookData?.language === 'string' && bookData.language) {
            return bookData.language;
          }
          return '';
        })(),
        page_count: (bookData as any)?.page_count || null,
        genres: bookData?.genres ||
                (Array.isArray(bookData?.subject) && bookData.subject.length > 0
                  ? bookData.subject.slice(0, 3).join(', ')
                  : ''),
        rating: (bookData as any)?.rating || 0,
        description: bookData?.description || '',
        cover_url: bookData?.cover_url || null
      };
    };

    // Use computed for base form data
    const baseFormData = computed(() => {
      return props.bookData ? mapBookDataToForm(props.bookData) : null;
    });

    // Initialize form data using the mapping function
    const formData = ref<BookFormData>(
      baseFormData.value || {
        title: '',
        author: '',
        publication_date: null,
        isbn: '',
        language: '',
        page_count: null,
        genres: '',
        rating: 0,
        description: '',
        cover_url: null
      }
    );

    // Set original cover on initialization
    if (props.bookData?.cover_url) {
      originalCover.value = props.bookData.cover_url;
    }

    // Simplified watcher using hybrid approach
    watch(baseFormData, (newBaseData) => {
      if (!newBaseData) return;

      // Update all fields with new data
      formData.value = { ...newBaseData };

      // Handle cover preservation logic
      if (userChangedCover.value) {
        // Preserve user's custom cover
        formData.value.cover_url = originalCover.value;
      } else {
        // Track new original cover
        originalCover.value = newBaseData.cover_url;
      }
    }, { immediate: true });

    const validatePublicationYear = () => {
      publicationYearError.value = '';

      if (!formData.value.publication_date) {
        return;
      }

      const yearValue = String(formData.value.publication_date).trim();
      const yearNum = Number(yearValue);

      if (isNaN(yearNum) || !Number.isInteger(yearNum)) {
        publicationYearError.value = 'Please enter a valid year';
        return;
      }

      if (yearNum > currentYear + 10) {
        publicationYearError.value = 'Year cannot be more than 10 years in the future';
      } else if (yearNum < -4000) {
        publicationYearError.value = 'Year cannot be before 4000 BCE';
      }
    };

    const validatePageCount = () => {
      pageCountError.value = '';

      if (!formData.value.page_count) {
        return;
      }

      const pageNum = Number(formData.value.page_count);

      if (isNaN(pageNum) || !Number.isInteger(pageNum) || pageNum <= 0) {
        pageCountError.value = 'Please enter a valid page count';
      } else if (pageNum > 50000) {
        pageCountError.value = 'Page count seems too high';
      }
    };

    const validateISBN = () => {
      isbnError.value = '';

      if (!formData.value.isbn || formData.value.isbn.trim() === '') {
        return;
      }

      const cleanISBN = formData.value.isbn.replace(/[-\s]/g, '');

      if (cleanISBN.length !== 10 && cleanISBN.length !== 13) {
        isbnError.value = 'ISBN must be 10 or 13 characters';
      } else if (!/^\d{9}[\dX]$/.test(cleanISBN) && !/^\d{13}$/.test(cleanISBN)) {
        isbnError.value = 'ISBN must contain only digits (and possibly "X" for ISBN-10)';
      }
    };

    const isFormValid = computed(() => {
      return formData.value.title.trim() !== '' &&
        !publicationYearError.value &&
        !pageCountError.value &&
        !isbnError.value;
    });

    const handleClose = () => {
      // If user changed cover but didn't save, emit the cover to parent for persistence
      if (userChangedCover.value && formData.value.cover_url) {
        emit('cover-changed', formData.value.cover_url);
      }

      emit('close');
    };

    const handleGoBack = () => {
      emit('go-back');
    };

    const handleSave = () => {
      if (formData.value.publication_date) {
        validatePublicationYear();
      }

      if (formData.value.page_count) {
        validatePageCount();
      }

      if (formData.value.isbn && formData.value.isbn.trim() !== '') {
        validateISBN();
      }

      if (isFormValid.value) {
        isAdding.value = true;

        const bookData = {
          title: formData.value.title.trim(),
          author: formData.value.author.trim() || null,
          isbn: formData.value.isbn.trim() || null,
          publication_date: formData.value.publication_date ? formData.value.publication_date.toString() : null,
          language: formData.value.language.trim() || null,
          page_count: formData.value.page_count || null,
          genres: formData.value.genres.trim() || null,
          rating: formData.value.rating || null,
          description: formData.value.description.trim() || null,
          cover_url: formData.value.cover_url || null
        };

        if (props.bookData && props.bookData.olid) {
          (bookData as any).olid = props.bookData.olid;
        }

        if (props.bookData) {
          const serializedOriginalData = {
            title: props.bookData.title,
            author_name: props.bookData.author_name ? [...props.bookData.author_name] : undefined,
            isbn: props.bookData.isbn ? [...props.bookData.isbn] : undefined,
            cover_i: props.bookData.cover_i,
            cover_edition_key: props.bookData.cover_edition_key,
            first_publish_year: props.bookData.first_publish_year,
            language: props.bookData.language ? [...props.bookData.language] : undefined,
            edition_count: props.bookData.edition_count,
            key: props.bookData.key,
            subject: props.bookData.subject ? [...props.bookData.subject] : undefined,
            publisher: props.bookData.publisher ? [...props.bookData.publisher] : undefined,
            publish_year: props.bookData.publish_year ? [...props.bookData.publish_year] : undefined,
            oclc: props.bookData.oclc ? [...props.bookData.oclc] : undefined,
            lccn: props.bookData.lccn ? [...props.bookData.lccn] : undefined,
            olid: props.bookData.olid,
            // Use original cover only if user hasn't changed it
            cover_url: userChangedCover.value ? null : originalCover.value,
            description: props.bookData.description,
            genres: props.bookData.genres,
            isbn_primary: props.bookData.isbn_primary
          };

          const enrichedData = {
            ...bookData,
            fromOpenLibrary: true,
            openLibraryData: serializedOriginalData,
            // Flag to indicate if user changed the cover
            userModifiedCover: userChangedCover.value
          };

          emit('save-book', enrichedData);
        } else {
          emit('save-book', bookData);
        }

        setTimeout(() => {
          isAdding.value = false;
          handleClose();
        }, 1500);
      }
    };

    const setRating = (rating: number) => {
      formData.value.rating = (formData.value.rating === rating) ? 0 : rating;
    };

    const handleCoverUpload = () => {
      fileInput.value?.click();
    };

    const handleFileChange = (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];

      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const newCoverUrl = e.target?.result as string;

          formData.value.cover_url = newCoverUrl;
          userChangedCover.value = true;

          // Emit the cover change to parent so it can track it
          emit('cover-changed', newCoverUrl);
        };
        reader.readAsDataURL(file);
      }
    };

    return {
      formData,
      fileInput,
      publicationYearError,
      pageCountError,
      isbnError,
      isAdding,
      isFormValid,
      handleClose,
      handleGoBack,
      handleSave,
      handleCoverUpload,
      handleFileChange,
      setRating,
      validatePublicationYear,
      validatePageCount,
      validateISBN,
      originalCover,
      userChangedCover
    };
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; /* Ensure it's on top */
}

.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  width: 650px; /* Increased from 600px to allow more space */
  max-width: 90%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden; /* For rounded corners */
  max-height: 90vh; /* Limit height to prevent modal from extending beyond viewport */
}

.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

.form-container {
  padding: 16px;
  flex: 1;
  overflow-y: hidden; /* Set back to hidden since we don't want scrollbars */
}

.form-content {
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 15px;
  position: relative;
  background-color: var(--color-bg-secondary);
}

.book-info-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
  margin-bottom: 0;
}

.main-info-row {
  display: flex;
  gap: 20px;
  width: 100%;
  align-items: stretch;
}

.cover-column {
  width: 135px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.main-fields-column {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-top: 5px;
}

.cover-upload {
  width: 135px;
  height: 100%;
  min-height: 192px;
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.cover-upload:hover {
  background-color: var(--color-nav-item-hover);
  transform: translateY(-1px);
}

.upload-placeholder {
  text-align: center;
  color: var(--color-text-primary);
  padding: 15px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-placeholder::before {
  content: "";
  display: block;
  width: 24px;
  height: 24px;
  background: url('/icons/image-icon.svg') no-repeat center center;
  background-size: contain;
  margin-bottom: 10px;
  opacity: 0.7;
}

.cover-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 3px;
  flex: 1;
  margin-bottom: 2px;
}

.form-field.full-width {
  width: 100%;
}

.form-field label {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.form-field input,
.form-field select,
.form-field textarea {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  outline: none;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
  background-color: var(--color-input-bg);
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.2);
}

.form-field textarea {
  resize: none;
  min-height: 65px;
  height: 65px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

.form-field textarea::-webkit-scrollbar {
  width: 8px;
}

.form-field textarea::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.form-field textarea::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: 4px;
  border: 2px solid var(--color-scrollbar-track);
}

.form-field textarea::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-thumb-hover);
}

.form-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.rating-container {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 6px 12px;
  background-color: var(--color-input-bg);
  width: 100%;
  box-sizing: border-box;
}

.rating-stars {
  display: flex;
  gap: 12px;
  padding: 2px 0;
}

.star {
  cursor: pointer;
  transition: all 0.2s;
  width: 20px;
  height: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.star:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
}

.form-field .input-error {
  border-color: var(--color-error);
  background-color: rgba(229, 57, 53, 0.05);
}

.form-field .error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-top: 2px;
  display: block;
}

.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-modal-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.modal-footer-left {
  display: flex;
}

.modal-footer-right {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 10px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-back {
  background-color: transparent;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  padding-left: 0;
  min-width: auto;
}

.btn-back:hover {
  color: var(--color-primary-hover);
}

.back-icon {
  width: 16px;
  height: 16px;
  filter: invert(30%);
  transition: filter 0.2s;
}

.btn-back:hover .back-icon {
  filter: invert(50%) sepia(80%) saturate(1000%) hue-rotate(180deg) brightness(100%) contrast(95%);
}

.add-icon {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}

@media (max-width: 640px) {
  .main-info-row {
    flex-direction: column;
    gap: 16px;
  }

  .cover-column {
    width: 100%;
  }

  .cover-upload {
    width: 100%;
    height: 250px;
    margin: 0 auto;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }
  .modal-footer {
    flex-direction: column;
    gap: 16px;
  }

  .modal-footer-left {
    width: 100%;
  }

  .modal-footer-right {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}
</style>