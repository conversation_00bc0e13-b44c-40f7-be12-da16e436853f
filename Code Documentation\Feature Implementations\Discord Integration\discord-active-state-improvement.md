# Discord RPC Active State Improvement

## Files Modified
- `public/discord-rpc-api.ts` - Added setActiveState method and improved initialization
- `src/stores/settingsStore.ts` - Updated to use active state on initialization
- `electron/main/ipc-handlers.ts` - Added setActiveState IPC handler
- `electron/preload/api-bridge.ts` - Added setActiveState API bridge
- `src/useElectronAPI.ts` - Updated DiscordAPI interface
- `src/types/electron-api.d.ts` - Updated type definitions
- `src/types/mock-api.ts` - Added mock implementation

## What Was Done
Improved Discord Rich Presence to show an "active" state when the app is first opened, instead of immediately showing "idle", and only switch to idle after 3 minutes of actual inactivity.

## Problem
Previously, when users opened the app:
1. Discord would immediately show "Idle" status
2. This was confusing because the user was actively using the app
3. Only specific activities (notes, timer, settings) would override the idle state

## Solution

### 1. Added Active State
**New Method: `setActiveState()`**
```typescript
setActiveState(): void {
  const activity: DiscordActivity = {
    largeImageKey: 'noti-logo',
    largeImageText: 'Noti - Smart Note-Taking & Study Companion',
    details: 'Using Noti',
    startTimestamp: Date.now()
  };
  this.updateActivity(activity);
}
```

### 2. Updated Initialization Flow
**Before:**
```typescript
// App opens → Immediately shows "Idle"
await db.discord.setIdle()
```

**After:**
```typescript
// App opens → Shows "Using Noti" (active state)
await db.discord.setActiveState()
```

### 3. Smart State Management
**App Startup:**
1. Discord initializes → Shows "Using Noti"
2. User performs specific activity → Shows specific activity
3. After 3 minutes of inactivity → Shows "Idle"

**Activity Flow:**
- **App Launch**: "Using Noti" (active)
- **Taking Notes**: "Taking notes" (specific activity)
- **Using Timer**: "In focus session" (specific activity)
- **In Settings**: "Configuring app" (specific activity)
- **3 Min Inactive**: "Idle" (automatic)

## Implementation Details

### 1. API Integration
Added `setActiveState` to all API layers:
- IPC handler: `discord:setActiveState`
- API bridge: `setActiveState()`
- Type definitions: Updated interfaces
- Mock API: Added mock implementation

### 2. Initialization Updates
**Settings Store:**
```typescript
// Initialize with active state instead of idle
await db.discord.setActiveState()
```

**Discord RPC Class:**
```typescript
// On connection, show active state if no specific activity
if (this.currentActivity) {
  this.updateActivity(this.currentActivity);
} else {
  this.setActiveState(); // New: Show active instead of idle
}
```

### 3. Idle Detection Unchanged
- Still automatically detects idle after 3 minutes
- Still updates activity timestamp on user interactions
- Still respects user privacy settings

## User Experience Improvements

### Before
1. **App Opens** → "Idle" (confusing)
2. **User Active** → Still "Idle" until specific activity
3. **Specific Activity** → Shows correct activity
4. **Stop Activity** → Back to "Idle" immediately

### After
1. **App Opens** → "Using Noti" (accurate)
2. **User Active** → "Using Noti" (shows engagement)
3. **Specific Activity** → Shows correct activity
4. **Stop Activity** → Back to "Using Noti"
5. **3 Min Inactive** → "Idle" (automatic)

## Benefits

### 1. More Accurate Status
- Shows user is actively using the app
- Reflects actual engagement state
- Professional appearance

### 2. Better User Experience
- No confusing "Idle" when actively using app
- Clear indication of app usage
- Smooth transitions between states

### 3. Maintains All Features
- ✅ 3-minute idle detection still works
- ✅ Privacy controls still respected
- ✅ Specific activities still override
- ✅ Performance optimizations maintained

## Discord Status Flow

```
App Launch → "Using Noti"
    ↓
User Activity → Specific Activity ("Taking notes", "In focus session", etc.)
    ↓
Activity Ends → "Using Noti"
    ↓
3 Minutes Inactive → "Idle"
    ↓
User Returns → "Using Noti"
```

## Result
Discord Rich Presence now accurately reflects user engagement:
- **Active when using app**: "Using Noti"
- **Specific when doing tasks**: Activity-specific messages
- **Idle only when truly inactive**: After 3 minutes of no interaction

This creates a more professional and accurate representation of user activity.
