<template>
  <div class="files">
    <div class="files-container">
      <div class="files-content">
        <div class="bottom-border"></div>
        <!-- Files Header -->
        <div class="files-header">
          <div class="bottom-border"></div>
          <div class="table-row header-row">
            <div class="table-cell check-cell" @click.stop.prevent="toggleSelectAll">
              <div 
                class="custom-checkbox" 
                :class="{ 'checked': allItemsSelected }"
              >
                <img 
                  v-show="allItemsSelected" 
                  src="/icons/check-icon.svg" 
                  class="check-icon" 
                  alt="Checked" 
                />
              </div>
            </div>
            <div class="table-cell name-cell">
              <img src="/icons/file-type-icon.svg" class="header-icon" alt="File" />
              <span>Name</span>
            </div>
            <div class="table-cell created-cell">Created</div>
            <div class="table-cell modified-cell">Modified</div>
            <div class="table-cell size-cell">File Size</div>
            <div class="table-cell type-cell">File Type</div>
          </div>
        </div>
        
        <div class="files-list">
          <div class="file-items" v-if="items.length > 0">            
            <!-- Folders -->            <div v-for="folder in folders" 
                :key="`folder-${folder.id}`"
                :class="{
                  'table-row': true,
                  'file-row': true,
                  'active': selectedItemId === folder.id && selectedItemType === 'folder',
                  'selected': isItemSelected('folder', folder.id)
                }"
                @click.ctrl.stop="toggleItemSelection('folder', folder.id, $event)"
                @click.shift.stop="toggleItemSelection('folder', folder.id, $event)"
            >
              <!-- Modified checkbox cell with improved event handling -->
              <!-- Modified check-cell for folder to prevent any navigation -->
<div class="table-cell check-cell" @click.stop.prevent="toggleItemSelection('folder', folder.id, $event)">
  <div 
    class="custom-checkbox"
    :class="{ 'checked': isItemSelected('folder', folder.id) }"
  >
    <img 
      v-show="isItemSelected('folder', folder.id)" 
      src="/icons/check-icon.svg" 
      class="check-icon" 
      alt="Checked" 
    />
  </div>
</div>
              
              <!-- Other cells - Handle navigation here -->
              <div 
                class="table-cell name-cell"
                @click="selectItem('folder', folder.id, $event)"
              >
                <FolderIcon :color="folder.color" :size="16" :isOpen="false" class="file-icon" />
                <span class="name">{{ folder.name }}</span>
                <span class="folder-item-count" :class="{ 'empty': folder.notesCount === 0 }">{{ folder.notesCount || 0 }}</span>
              </div>
              <div 
                class="table-cell created-cell"
                @click="selectItem('folder', folder.id, $event)"
              >
                {{ formatDate(folder.created_at || '') }}
              </div>
              <div 
                class="table-cell modified-cell"
                @click="selectItem('folder', folder.id, $event)"
              >
                {{ formatDate(folder.updated_at || '') }}
              </div>
              <div 
                class="table-cell size-cell"
                @click="selectItem('folder', folder.id, $event)"
              >
                elements
              </div>
              <div 
                class="table-cell type-cell"
                @click="selectItem('folder', folder.id, $event)"
              >
                N/A
              </div>
              <div class="bottom-border"></div>
            </div>
                
            <!-- Files/Notes -->            <div v-for="note in notes" 
                :key="`note-${note.id}`"
                :class="{
                  'table-row': true,
                  'file-row': true,
                  'active': selectedItemId === note.id && selectedItemType === 'note',
                  'selected': isItemSelected('note', note.id)
                }"
                @click.ctrl.stop="toggleItemSelection('note', note.id, $event)"
                @click.shift.stop="toggleItemSelection('note', note.id, $event)"
            >
              <!-- Modified checkbox cell with improved event handling -->
              <!-- Modified check-cell for note to prevent any navigation -->
<div class="table-cell check-cell" @click.stop.prevent="toggleItemSelection('note', note.id, $event)">
  <div 
    class="custom-checkbox"
    :class="{ 'checked': isItemSelected('note', note.id) }"
  >
    <img 
      v-show="isItemSelected('note', note.id)" 
      src="/icons/check-icon.svg"
      class="check-icon" 
      alt="Checked" 
    />
  </div>
</div>
              
              <!-- Other cells - Handle navigation here -->
              <div 
                class="table-cell name-cell"
                @click="selectItem('note', note.id, $event)"
              >
                <img src="/icons/file-icon.svg" class="file-icon" alt="File" />
                <span class="name">{{ note.title || note.name || 'Untitled Note' }}</span>
              </div>
              <div 
                class="table-cell created-cell"
                @click="selectItem('note', note.id, $event)"
              >
                {{ formatDate(note.created_at) }}
              </div>
              <div 
                class="table-cell modified-cell"
                @click="selectItem('note', note.id, $event)"
              >
                {{ formatDate(note.updated_at) }}
              </div>
              <div 
                class="table-cell size-cell"
                @click="selectItem('note', note.id, $event)"
              >
                {{ getFileSize(note.content) }}
              </div>
              <div 
                class="table-cell type-cell"
                @click="selectItem('note', note.id, $event)"
              >
                .noti
              </div>
              <div class="bottom-border"></div>
            </div>
          </div>
          <div v-else class="empty-content">
            <img src="/icons/folder-open-icon.svg" class="empty-icon" alt="Empty folder" />
            <p>This folder is empty</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch } from 'vue';
import { getFolderIconStyle, getFolderIconSrc } from '../../utils/colorUtils';
import FolderIcon from '../icons/FolderIcon.vue';

// Define types for folder and note items
interface BaseItem {
  id: number | string;
  type: string;
  created_at?: string;
  updated_at?: string;
}

interface FolderItem extends BaseItem {
  type: 'folder';
  name: string;
  notesCount?: number;
  parent_id?: number | null;
  color?: string | null;
}

interface NoteItem extends BaseItem {
  type: 'note' | 'file';
  title?: string;
  name?: string; // Some notes might use name instead of title
  content?: string;
  folder_id?: number | null;
}

type Item = FolderItem | NoteItem;

// Helper function to get the name/title of an item
function getItemName(item: Item): string {
  if (item.type === 'folder') {
    return item.name || '';
  } else {
    return item.title || item.name || '';
  }
}

export default defineComponent({
  name: 'FolderContent',
  components: {
    FolderIcon
  },
  props: {
    folderName: {
      type: String,
      default: 'Untitled Folder'
    },
    items: {
      type: Array as () => Item[],
      default: () => []
    },
    selectedItemId: {
      type: [Number, String],
      default: null
    },
    selectedItemType: {
      type: String,
      default: null
    },
    searchQuery: {
      type: String,
      default: ''
    },
    folderPath: {
      type: Array,
      default: () => []
    }
  },
  emits: [
    'selectItem', 
    'createItem', 
    'deleteFolder', 
    'renameFolder', 
    'moveFolder', 
    'exportFolder', 
    'importToFolder', 
    'sortFolder', 
    'search',
    'checkedItemsChange'
  ],
  setup(props, { emit }) {
    // Using reactive state for checked items
    const checkedItems = ref<{type: string, id: number | string}[]>([]);
    const lastSelectedItem = ref<{type: string, id: number | string} | null>(null);
    
    // Reset selection when items change
    watch(() => props.items, (newItems, oldItems) => {
      // Check if this is actually a meaningful change
      // Don't reset selection if it's just a re-render with the same items
      if (oldItems && oldItems.length === newItems.length) {
        // Compare item IDs to see if they're the same items
        const oldIds = oldItems.map(item => String(item.id)).sort().join(',');
        const newIds = newItems.map(item => String(item.id)).sort().join(',');
        
        if (oldIds === newIds) {
          return; // Exit early to preserve selection
        }
      }
      
      // Create a new empty array and update in one operation
      const emptyArray: {type: string, id: number | string}[] = [];
      checkedItems.value = emptyArray;
      lastSelectedItem.value = null;
      
      // Emit the change event
      emit('checkedItemsChange', emptyArray);
    }, { deep: true });

    const filteredItems = computed(() => {
      if (!props.searchQuery || props.searchQuery.trim() === '') {
        return props.items;
      }
      const query = props.searchQuery.toLowerCase().trim();
      return props.items.filter(item => {
        const itemName = getItemName(item);
        return itemName.toLowerCase().includes(query);
      });
    });

    const folderItems = computed<FolderItem[]>(() => {
      return filteredItems.value.filter((item): item is FolderItem => item.type === 'folder');
    });

    const noteItems = computed<NoteItem[]>(() => {
      return filteredItems.value.filter((item): item is NoteItem => 
        item.type === 'note' || item.type === 'file'
      );
    });

    const itemCount = computed(() => {
      return filteredItems.value.length;
    });

    const allItemsSelected = computed(() => {
      return filteredItems.value.length > 0 && checkedItems.value.length === filteredItems.value.length;
    });
    
    const isItemSelected = (type: string, id: number | string) => {
      return checkedItems.value.some(item => 
        item.type === type && String(item.id) === String(id)
      );
    };

    // Improved toggle item selection function
    const toggleItemSelection = (type: string, id: number | string, event: MouseEvent) => {
      // Stop all event propagation and default behavior
      if (event) {
        event.stopPropagation();
        event.preventDefault();
        // Explicitly stop any parent handlers
        if (event.cancelBubble !== undefined) event.cancelBubble = true;
      }

      // Get reference to all filtered items for range selection
      const allItems = filteredItems.value;
      const currentItem = { type, id };
      let newCheckedItems = [...checkedItems.value];
      
      // Find if the item is already selected
      const existingIndex = newCheckedItems.findIndex(item => 
        item.type === type && String(item.id) === String(id)
      );
      
      // Handle different selection modes
      if (event?.shiftKey && lastSelectedItem.value) {
        // SHIFT+CLICK: Select range of items
        // Find indices of last selected item and current item in the filteredItems
        const lastItemIndex = allItems.findIndex(item => 
          item.type === lastSelectedItem.value!.type && 
          String(item.id) === String(lastSelectedItem.value!.id)
        );
        
        const currentItemIndex = allItems.findIndex(item => 
          item.type === type && String(item.id) === String(id)
        );
        
        if (lastItemIndex !== -1 && currentItemIndex !== -1) {
          // Determine start and end indices for the range
          const startIndex = Math.min(lastItemIndex, currentItemIndex);
          const endIndex = Math.max(lastItemIndex, currentItemIndex);
          
          // Select all items in the range
          // First, create a set of existing ids for faster lookups
          const existingSelections = new Set(
            newCheckedItems.map(item => `${item.type}-${item.id}`)
          );
          
          // Add all items in the range that aren't already selected
          for (let i = startIndex; i <= endIndex; i++) {
            const item = allItems[i];
            const itemKey = `${item.type}-${item.id}`;
            
            if (!existingSelections.has(itemKey)) {
              newCheckedItems.push({ type: item.type, id: item.id });
            }
          }
        }
        
        // Update last selected item to the current one
        lastSelectedItem.value = currentItem;
      } 
      else if (event?.ctrlKey) {
        // CTRL+CLICK: Toggle individual selection without affecting others
        if (existingIndex >= 0) {
          // Already selected, remove it
          newCheckedItems.splice(existingIndex, 1);
        } else {
          // Not selected, add it
          newCheckedItems.push(currentItem);
        }
        
        // Update last selected item to the current one
        lastSelectedItem.value = currentItem;
      } 
      else {
        // REGULAR CLICK: Just toggle this item (and deselect others if not on checkbox)
        // If clicking directly on the checkbox, toggle just this item
        const target = event?.target as HTMLElement;
        const isCheckboxClick = target?.closest('.check-cell') || 
                               target?.closest('.custom-checkbox') || 
                               target?.classList.contains('check-icon');
        
        if (isCheckboxClick) {
          // Toggle just this item
          if (existingIndex >= 0) {
            // Already selected, remove it
            newCheckedItems.splice(existingIndex, 1);
          } else {
            // Not selected, add it
            newCheckedItems.push(currentItem);
          }
        } else {
          // Replace selection with just this item
          newCheckedItems = [currentItem];
        }
        
        // Update last selected item to the current one
        lastSelectedItem.value = currentItem;
      }
      
      // Update the state
      checkedItems.value = newCheckedItems;
      
      // Emit the change
      emit('checkedItemsChange', newCheckedItems);
      
      // Force a reactivity trigger (in case Vue isn't detecting the changes)
      checkedItems.value = [...newCheckedItems];
      
      // Prevent any navigation that might be triggered
      return false;
    };
    
    const toggleSelectAll = () => {
      // Simple toggle all implementation
      let newCheckedItems: {type: string, id: number | string}[] = [];
      
      if (!allItemsSelected.value) {
        // If not all selected, select all items
        newCheckedItems = filteredItems.value.map(item => ({ 
          type: item.type, 
          id: item.id 
        }));
      }
      // Otherwise leave the array empty to deselect all
      
      // Update state
      checkedItems.value = newCheckedItems;
      
      // Emit the event
      emit('checkedItemsChange', newCheckedItems);
    };
    
    const formatDate = (dateString?: string) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric', 
        month: 'short', 
        day: 'numeric'
      });
    };

    const getFileSize = (content?: string) => {
      if (!content) return '0 KB';
      const sizeInBytes = new TextEncoder().encode(content).length;
      if (sizeInBytes < 1024) {
        return `${sizeInBytes} B`;
      } else if (sizeInBytes < 1024 * 1024) {
        return `${(sizeInBytes / 1024).toFixed(1)} KB`;
      } else {
        return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
      }
    };
      const selectItem = (type: string, id: number | string, event?: MouseEvent) => {
      // Handle both navigation and selection with modifiers
      
      // Ignore if clicked on checkbox area
      if (event) {
        const target = event.target as HTMLElement;
        // Check if the click was anywhere near the checkbox
        if (target.closest('.check-cell') || 
            target.closest('.custom-checkbox') || 
            target.classList.contains('check-icon')) {
          return;
        }
        
        // If Ctrl or Shift key is pressed, let toggleItemSelection handle it
        // The @click events on the row will handle this
        if (event.ctrlKey || event.shiftKey) {
          // This is handled by the row's @click.ctrl and @click.shift handlers
          return; 
        }
      }
      
      // If it's a regular click, update the last selected item for future shift+click operations
      lastSelectedItem.value = { type, id };
      
      // Just emit select for navigation without changing selection state
      if (type === 'folder') {
        const folder = folderItems.value.find(f => String(f.id) === String(id));
        if (folder) {
          emit('selectItem', { type, id, name: folder.name, parent_id: folder.parent_id });
        } else {
          emit('selectItem', { type, id });
        }
      } else if (type === 'note') {
        const note = noteItems.value.find(n => String(n.id) === String(id));
        if (note) {
          emit('selectItem', { type, id, name: note.title || note.name || 'Untitled Note', parent_id: note.folder_id });
        } else {
          emit('selectItem', { type, id });
        }
      } else {
        emit('selectItem', { type, id });
      }
    };

    return {
      folders: folderItems, // Used in template as 'folders'
      notes: noteItems,     // Used in template as 'notes'
      itemCount,
      formatDate,
      getFileSize,
      selectItem,
      checkedItems, // For potential direct use or debugging, though template uses methods
      isItemSelected,
      toggleItemSelection,
      toggleSelectAll,
      allItemsSelected,
      getFolderIconStyle,
      getFolderIconSrc
    };
  }
});
</script>

<style scoped>
.files {
  background-color: var(--color-bg-primary);
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.files-container {
  background-color: var(--color-card-bg);
  border-radius: 15px;
  border: 1px solid var(--color-card-border);
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.files-content {
  height: calc(100% - 28px);
  position: relative;
  top: 28px;
  width: 100%;
}

.bottom-border {
  background-color: var(--color-border-primary);
  height: 1px;
  left: 0;
  position: absolute;
  bottom: 0;
  width: 100%;
  opacity: 0.7;
}

.files-header {
  height: 28px;
  left: 0;
  position: absolute;
  top: -28px;
  width: 100%;
  background-color: var(--color-bg-secondary);
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-sizing: border-box;
  z-index: 1;
}

.files-list {
  height: 100%;
  overflow-y: auto;
  position: relative;
  width: 100%;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}

.files-list::-webkit-scrollbar {
  width: 6px;
}

.files-list::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: 6px;
}

.files-list::-webkit-scrollbar-track {
  background-color: transparent;
}

.file-items {
  width: 100%;
  position: relative;
}

/* Table Structure */
.table-row {
  display: flex;
  width: 100%;
  align-items: center;
  box-sizing: border-box;
}

.header-row {
  height: 28px;
}

.file-row {
  height: 40px;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  border-bottom: 1px solid var(--color-border-secondary);
}

.file-row:hover {
  background-color: var(--color-nav-item-hover);
}

.file-row.active {
  background-color: var(--color-nav-item-hover);
}

.file-row.selected {
  background-color: var(--color-nav-item-active);
}

.file-row.selected:hover {
  background-color: var(--color-nav-item-active);
  opacity: 0.8;
}

.file-row.active.selected {
  background-color: var(--color-nav-item-active);
  opacity: 0.9;
}

.table-cell {
  display: flex;
  align-items: center;
  padding: 0 5px;
  overflow: hidden;
  box-sizing: border-box;
  flex-shrink: 0;
}

/* Cell sizes */
.check-cell {
  width: 45px;
  justify-content: center;
  padding-left: 15px;
  z-index: 5;
  cursor: pointer; /* Added cursor pointer for better UX */
}

.name-cell {
  min-width: 200px;
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 5px;
}

.created-cell {
  width: 120px;
  color: var(--color-text-secondary);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.modified-cell {
  width: 120px;
  color: var(--color-text-secondary);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.size-cell {
  width: 90px;
  color: var(--color-text-secondary);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.type-cell {
  width: 80px;
  color: var(--color-text-secondary);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;
  padding-right: 15px;
}

/* Header cells have bold font */
.header-row .table-cell {
  color: var(--color-text-secondary);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 700;
  text-align: left;
}

/* File icons */
.file-icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
  margin-right: 10px;
  flex-shrink: 0;
}

.header-icon {
  height: 16px;
  width: 16px;
  margin-right: 6px;
  opacity: 0.8;
}

/* Name text */
.name {
  white-space: nowrap;
  overflow: hidden;
}

/* Folder item count badge */
.folder-item-count {
  font-size: 12px;
  color: var(--color-item-count-text);
  background-color: var(--color-item-count-bg);
  border-radius: 10px;
  padding: 2px 8px;
  min-width: 24px;
  text-align: center;
  margin-left: 10px;
  font-weight: 500;
  display: inline-block;
  transition: opacity 0.2s, background-color 0.2s;
}

.folder-item-count.empty {
  /* Use same styling as regular counters for consistency */
  background-color: var(--color-item-count-bg);
  color: var(--color-item-count-text);
}

.table-cell {
  text-overflow: ellipsis;
  color: var(--color-table-cell);
  font-family: "Montserrat", Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

/* Improved Checkbox styles */
.custom-checkbox {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--color-border-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background-color: var(--color-card-bg);
  box-sizing: border-box;
  position: relative;
  z-index: 10;
  pointer-events: none; /* Important - let the parent handle clicks */
}

.custom-checkbox.checked {
  border-color: var(--color-text-secondary);
  background-color: var(--color-card-bg);
}

.check-icon {
  width: 12px;
  height: 12px;
  object-fit: contain;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none; /* Important - let the parent handle clicks */
}

.file-row:hover .custom-checkbox {
  border-color: var(--color-text-secondary);
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  padding: 40px 20px;
  color: var(--color-empty-state);
}

.empty-icon {
  width: 60px;
  height: 60px;
  opacity: 0.5;
  margin-bottom: 16px;
}

.create-item-btn {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background-color: var(--color-create-btn-bg);
  color: var(--color-btn-primary-text);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.create-item-btn:hover {
  background-color: var(--color-create-btn-hover);
}

.create-item-btn img {
  width: 12px;
  height: 12px;
  filter: brightness(0) invert(1);
}

.file-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
  margin-right: 20px;
}

.action-btn {
  padding: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: var(--color-action-hover);
}

.action-btn.danger:hover {
  background-color: var(--color-action-danger-hover);
}

.action-icon {
  width: 14px;
  height: 14px;
}
</style>