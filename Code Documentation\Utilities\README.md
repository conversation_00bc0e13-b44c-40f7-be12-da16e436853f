# Noti Scripts

This directory contains utility scripts for the Noti application.

## Test Data Population Script

The `populateTestData.ts` script allows you to populate either a test database or your actual Noti application database with sample folders and notes.

### Usage

```bash
# Basic usage (creates a test database with 3 top folders, 9 sub-folders, and 27 notes)
npm run populate-test-data -- 3

# Add test data to your actual Noti application database
npm run populate-real-db -- 3

# Reset test database and add test data
npm run populate-test-data -- 3 --reset

# Reset the actual Noti database and add test data (EXTREME CAUTION!)
npm run populate-real-db -- 3 --reset
```

### Command Line Options

- `<N>`: Number of items to create (creates N top folders, N sub-folders per top folder, and N notes per sub-folder)
- `--reset`: Reset the database before adding test data (deletes all existing data!)

### Scripts

- `npm run populate-test-data`: Creates test data in a separate test database
- `npm run populate-real-db`: Creates test data in your actual Noti application database

### Output

- When using `populate-real-db`, the script will write to your actual Noti application database (at the standard Electron app data location)
- With `populate-test-data`, a test database will be created at `./test-data/noti-test-data.sqlite`
- The script creates a log file at `./scripts/test_data_output.log`

### Safety Features

- The script includes warnings and delays when using `--reset` with the real database
- A 5-second delay is added before resetting the actual database to allow aborting with Ctrl+C

### Viewing Test Data

- Test database: Open `./test-data/noti-test-data.sqlite` with DB Browser for SQLite
- Actual database: Launch the Noti application after running the script

### Examples

1. **For development and testing:**
   ```bash
   npm run populate-test-data -- 5
   ```
   This creates a test database with 5 top folders, 25 sub-folders, and 125 notes.

2. **For adding sample data to your Noti application:**
   ```bash
   npm run populate-real-db -- 2
   ```
   This adds 2 top folders, 4 sub-folders, and 8 notes to your actual Noti database. 