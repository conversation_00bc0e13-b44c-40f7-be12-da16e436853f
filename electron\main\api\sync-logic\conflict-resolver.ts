import { ManifestItem, ConflictResolutionResult } from './types';

export class ConflictResolver {
  /**
   * Main conflict resolution method
   * Determines which version of an item should win in a sync conflict
   */
  resolveConflict(
    local: ManifestItem,
    remote: ManifestItem,
    deviceId: string
  ): ConflictResolutionResult {
    // Sanity check - ensure we're comparing the same item
    if (local.id !== remote.id || local.type !== remote.type) {
      throw new Error(`Cannot resolve conflict between different items: ${local.id} vs ${remote.id}`);
    }

    // Type-specific resolution
    switch (local.type) {
      case 'book':
        return this.resolveBookConflict(local, remote, deviceId);
      case 'folder':
        return this.resolveFolderConflict(local, remote, deviceId);
      case 'note':
        return this.resolveNoteConflict(local, remote, deviceId);
      default:
        // Default resolution for any future types
        return this.resolveByTimestamp(local, remote) || 
               this.resolveByDeviceId(local, remote, deviceId);
    }
  }

  /**
   * Compare timestamps - newer always wins
   * Returns null if timestamps are equal
   */
  private resolveByTimestamp(
    local: ManifestItem,
    remote: ManifestItem
  ): ConflictResolutionResult | null {
    const localTime = new Date(local.updated_at || local.modified).getTime();
    const remoteTime = new Date(remote.updated_at || remote.modified).getTime();

    if (localTime > remoteTime) {
      return {
        winner: local,
        source: 'local',
        merged: false
      };
    } else if (remoteTime > localTime) {
      return {
        winner: remote,
        source: 'remote',
        merged: false
      };
    }

    // Timestamps are equal
    return null;
  }

  /**
   * Device ID tiebreaker - used when timestamps are equal
   * Uses string comparison for consistent results
   */
  private resolveByDeviceId(
    local: ManifestItem,
    remote: ManifestItem,
    deviceId: string
  ): ConflictResolutionResult {
    // The device with the "higher" ID wins to ensure consistency
    // This is arbitrary but deterministic
    if (deviceId > (remote.device_id || '')) {
      return {
        winner: local,
        source: 'local',
        merged: false
      };
    } else {
      return {
        winner: remote,
        source: 'remote',
        merged: false
      };
    }
  }

  /**
   * Book-specific conflict resolution
   * Preserves OpenLibrary IDs when possible
   */
  private resolveBookConflict(
    local: ManifestItem,
    remote: ManifestItem,
    deviceId: string
  ): ConflictResolutionResult {
    // First try timestamp resolution
    const timestampResult = this.resolveByTimestamp(local, remote);
    if (timestampResult) {
      // Check if we need to merge OpenLibrary ID
      const localMeta = local.metadata || {};
      const remoteMeta = remote.metadata || {};
      
      // If one has OpenLibrary ID and other doesn't, merge it
      if (localMeta?.openlibrary_id && !remoteMeta?.openlibrary_id) {
        timestampResult.winner.metadata = this.mergeMetadata(local, remote);
        timestampResult.merged = true;
      } else if (!localMeta?.openlibrary_id && remoteMeta?.openlibrary_id) {
        timestampResult.winner.metadata = this.mergeMetadata(local, remote);
        timestampResult.merged = true;
      }
      
      return timestampResult;
    }

    // Fall back to device ID tiebreaker
    return this.resolveByDeviceId(local, remote, deviceId);
  }

  /**
   * Folder-specific conflict resolution
   * Currently uses standard resolution, but ready for folder-specific logic
   */
  private resolveFolderConflict(
    local: ManifestItem,
    remote: ManifestItem,
    deviceId: string
  ): ConflictResolutionResult {
    // For now, use standard resolution
    // In future, could merge child counts or other folder-specific metadata
    return this.resolveByTimestamp(local, remote) || 
           this.resolveByDeviceId(local, remote, deviceId);
  }

  /**
   * Note-specific conflict resolution
   * Currently picks winner by timestamp, but ready for content merging
   */
  private resolveNoteConflict(
    local: ManifestItem,
    remote: ManifestItem,
    deviceId: string
  ): ConflictResolutionResult {
    // For now, use standard resolution
    // In future, could implement content merging strategies:
    // - Three-way merge using a common ancestor
    // - Operational transformation for real-time collaboration
    // - Create conflict copies for user resolution
    return this.resolveByTimestamp(local, remote) || 
           this.resolveByDeviceId(local, remote, deviceId);
  }

  /**
   * Merge metadata intelligently
   * Preserves important fields from both versions
   */
  private mergeMetadata(
    local: ManifestItem,
    remote: ManifestItem
  ): Record<string, any> {
    const localMeta = local.metadata || {};
    const remoteMeta = remote.metadata || {};
    
    // Start with the newer metadata
    const localTime = new Date(local.updated_at || local.modified).getTime();
    const remoteTime = new Date(remote.updated_at || remote.modified).getTime();
    const baseMeta = localTime >= remoteTime ? { ...localMeta } : { ...remoteMeta };
    
    // Preserve important fields from both sides
    const merged: Record<string, any> = { ...baseMeta };
    
    // For books: preserve OpenLibrary ID if present
    if (local.type === 'book') {
      if (localMeta.openlibrary_id || remoteMeta.openlibrary_id) {
        merged.openlibrary_id = localMeta.openlibrary_id || remoteMeta.openlibrary_id;
      }
      // Preserve ISBN if present
      if (localMeta.isbn || remoteMeta.isbn) {
        merged.isbn = localMeta.isbn || remoteMeta.isbn;
      }
    }
    
    // For notes: preserve tags from both sides (union)
    if (local.type === 'note') {
      if (localMeta.tags || remoteMeta.tags) {
        const localTags = localMeta.tags || [];
        const remoteTags = remoteMeta.tags || [];
        merged.tags = Array.from(new Set([...localTags, ...remoteTags]));
      }
    }
    
    // For all types: preserve star status (true wins)
    if (localMeta.is_starred || remoteMeta.is_starred) {
      merged.is_starred = true;
    }
    
    return merged;
  }
}