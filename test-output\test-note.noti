{"version": "1.0", "type": "noti-note", "schema": "https://noti.app/schemas/note/v1.0", "metadata": {"id": 123, "title": "Test Note", "created_at": "2025-06-19T19:10:50.513Z", "updated_at": "2025-06-19T19:10:50.514Z", "type": "text", "color": "#ff0000", "folder_id": null, "book_id": null, "export": {"version": "1.0.0", "app_version": "1.0.0", "exported_at": "2025-06-19T19:10:50.514Z"}}, "content": {"html": "<h1>Test Note</h1><p>This is a <strong>test</strong> note with HTML content.</p>", "markdown": "# Test Note\n\nThis is a **test** note with HTML content.", "plain_text": "Test Note\n\nThis is a test note with HTML content.", "statistics": {"word_count": 10, "character_count": 45, "reading_time": 1}}, "media": [], "integrity": {"algorithm": "sha256", "content_hash": "dab00f3cd5242cc0443f274fcba671587336dc2ce8cf498937a1d45e8c5d9ec7"}}