import type { Note, Folder, FolderWithMeta, NotesAPI, FoldersAPI, RecentItemsAPI, Book, BookWithNoteCount, BookSearchResult, BooksAPI, TimerAPI, TimerSession, TimerStats, TimerSettings, PomodoroCycle } from './electron-api';

// This file provides mock implementations of the database API
// during development while we're still integrating with the backend

// Create mock data store
const mockNotes: Note[] = [];
const mockFolders: Folder[] = [];
const mockBooks: Book[] = [
  {
    id: 1,
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    isbn: '9780743273565',
    rating: 4,
    genres: 'Fiction, Classic Literature',
    publication_date: '1925',
    description: 'A classic American novel set in the Jazz Age.',
    page_count: 180,
    language: 'en',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    title: 'To Kill a Mockingbird',
    author: '<PERSON>',
    isbn: '9780060935467',
    rating: 5,
    genres: 'Fiction, Drama',
    publication_date: '1960',
    description: 'A novel about racial injustice and childhood innocence.',
    page_count: 281,
    language: 'en',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Unique ID generators
let nextNoteId = 1;
let nextFolderId = 1;
let nextBookId = 1;

// Function to safely check if we have access to the real Electron APIs
// This will be called on-demand to avoid early window.db access
const hasRealElectronAPI = (): boolean => {
  try {
    return typeof window !== 'undefined' && (
      (typeof window.db !== 'undefined' && window.db !== null) ||
      (typeof window.electronAPI !== 'undefined' && window.electronAPI !== null)
    );
  } catch (e) {
    return false;
  }
};

// Provide API proxies that either call the real APIs or use mock implementations
export const notesApi = {
  create: async (note: Note): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.create(note);
    }

    // Mock implementation
    const newNote: Note = {
      ...note,
      id: nextNoteId++,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockNotes.unshift(newNote);
    return newNote;
  },

  createForBook: async (bookId: number, customTitle?: string, folderId?: number | null): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.createForBook(bookId, customTitle, folderId);
    }

    // Mock implementation
    const book = mockBooks.find(b => b.id === bookId);
    if (!book) {
      throw new Error(`Book with id ${bookId} not found`);
    }

    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const noteTitle = customTitle && customTitle.trim()
      ? customTitle.trim()
      : `${book.title} - ${currentDate}`;

    const newNote: Note = {
      title: noteTitle,
      content: '',
      html_content: '<p></p>',
      book_id: bookId,
      folder_id: folderId,
      id: nextNoteId++,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockNotes.unshift(newNote);
    return newNote;
  },

  getAll: async (options?: any): Promise<Note[]> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.getAll(options);
    }

    // Mock implementation
    return [...mockNotes];
  },

  getById: async (id: number): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.getById(id);
    }

    // Mock implementation
    const note = mockNotes.find(n => n.id === id);
    if (!note) {
      throw new Error(`Note with id ${id} not found`);
    }
    return { ...note };
  },

  update: async (id: number, noteUpdates: Partial<Note>): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.update(id, noteUpdates);
    }

    // Mock implementation
    const index = mockNotes.findIndex(n => n.id === id);
    if (index === -1) {
      throw new Error(`Note with id ${id} not found`);
    }

    mockNotes[index] = {
      ...mockNotes[index],
      ...noteUpdates,
      updated_at: new Date().toISOString()
    };

    return { ...mockNotes[index] };
  },

  delete: async (id: number): Promise<void> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.delete(id);
    }

    // Mock implementation
    const index = mockNotes.findIndex(n => n.id === id);
    if (index === -1) {
      throw new Error(`Note with id ${id} not found`);
    }

    mockNotes.splice(index, 1);
  },

  getByFolderId: async (folderId: number): Promise<Note[]> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.getByFolderId(folderId);
    }

    // Mock implementation
    return mockNotes.filter(note => note.folder_id === folderId);
  },

  export: async (id: number, format: string): Promise<string> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.export(id, format);
    }

    // Mock implementation
    const note = mockNotes.find(n => n.id === id);
    if (!note) {
      throw new Error(`Note with id ${id} not found`);
    }

    // Simple export, in a real implementation this would convert to different formats
    return note.content || '';
  },

  exportMultiple: async (
    items: Array<{ id: number; type: 'folder' | 'note'; name: string }>,
    format: string,
    options?: { includeSubfolders?: boolean; includeNotes?: boolean }
  ): Promise<string> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.exportMultiple(items, format, options);
    }

    // Mock implementation
    console.log(`Mock: Exporting ${items.length} items as ${format}`);
    return `Mock export of ${items.length} items completed`;
  },

  import: async (content: string, format: string, title: string): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.import(content, format, title);
    }

    // Mock implementation - create a new note with the imported content
    const newNote: Note = {
      title,
      content,
      html_content: `<p>${content}</p>`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return notesApi.create(newNote);
  },

  linkToBook: async (noteId: number, bookId: number | null): Promise<Note> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.linkToBook(noteId, bookId);
    }

    // Mock implementation - find and update the note
    const note = mockNotes.find(n => n.id === noteId);
    if (!note) {
      throw new Error(`Note with id ${noteId} not found`);
    }

    note.book_id = bookId;
    note.updated_at = new Date().toISOString();
    return { ...note };
  },

  autoLinkInFolder: async (folderId: number): Promise<{ linked: number; errors: number }> => {
    if (hasRealElectronAPI()) {
      return window.db.notes.autoLinkInFolder(folderId);
    }

    // Mock implementation
    return { linked: 0, errors: 0 };
  }
};

export const foldersApi = {
  create: async (folder: Omit<Folder, 'id'>): Promise<Folder> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.create(folder);
    }

    // Mock implementation
    const newFolder: Folder = {
      ...folder,
      id: nextFolderId++,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockFolders.push(newFolder);
    return newFolder;
  },

  getAll: async (): Promise<Folder[]> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.getAll();
    }

    // Mock implementation
    return [...mockFolders];
  },

  getById: async (id: number): Promise<Folder> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.getById(id);
    }

    // Mock implementation
    const folder = mockFolders.find(f => f.id === id);
    if (!folder) {
      throw new Error(`Folder with id ${id} not found`);
    }
    return { ...folder };
  },

  update: async (id: number, folderUpdates: Partial<Folder>): Promise<Folder> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.update(id, folderUpdates);
    }

    // Mock implementation
    const index = mockFolders.findIndex(f => f.id === id);
    if (index === -1) {
      throw new Error(`Folder with id ${id} not found`);
    }

    mockFolders[index] = {
      ...mockFolders[index],
      ...folderUpdates,
      updated_at: new Date().toISOString()
    };

    return { ...mockFolders[index] };
  },

  delete: async (id: number): Promise<void> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.delete(id);
    }

    // Mock implementation
    const index = mockFolders.findIndex(f => f.id === id);
    if (index === -1) {
      throw new Error(`Folder with id ${id} not found`);
    }

    mockFolders.splice(index, 1);
  },

  getChildren: async (parentId: number | null): Promise<Folder[]> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.getChildren(parentId);
    }

    // Mock implementation
    return mockFolders.filter(folder => folder.parent_id === parentId);
  },

  getHierarchy: async (): Promise<FolderWithMeta[]> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.getHierarchy();
    }

    // Mock implementation - return simple hierarchy structure
    return mockFolders.map(folder => ({
      ...folder,
      notesCount: 0,
      childFoldersCount: 0,
      children: []
    }));
  },

  getInheritedBookId: async (parentId: number | null): Promise<number | null> => {
    if (hasRealElectronAPI()) {
      return window.db.folders.getInheritedBookId(parentId);
    }

    // Mock implementation - return null (no inherited book ID)
    return null;
  }
};

export const recentItemsApi = {
  addNote: async (noteId: number): Promise<void> => {
    if (hasRealElectronAPI()) {
      return window.db.recentItems.addNote(noteId);
    }

    // Mock implementation - doesn't do anything in mock mode
    return;
  },

  getRecentNotes: async (limit?: number): Promise<Note[]> => {
    if (hasRealElectronAPI()) {
      return window.db.recentItems.getRecentNotes(limit);
    }

    // Mock implementation - return most recently updated notes
    const recentNotes = [...mockNotes].sort((a, b) => {
      const aDate = new Date(a.updated_at || a.created_at || '');
      const bDate = new Date(b.updated_at || b.created_at || '');
      return bDate.getTime() - aDate.getTime();
    });

    return limit ? recentNotes.slice(0, limit) : recentNotes;
  }
};

export const booksApi: BooksAPI = {
  create: async (book: Partial<Book>, downloadCover?: boolean): Promise<Book> => {
    if (hasRealElectronAPI()) {
      return window.db.books.create(book, downloadCover);
    }

    // Mock implementation
    const newBook: Book = {
      ...book,
      id: nextBookId++,
      title: book.title || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as Book;

    mockBooks.push(newBook);
    return newBook;
  },

  getAll: async (): Promise<Book[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getAll();
    }
    return [...mockBooks];
  },

  getAllWithNoteCounts: async (): Promise<BookWithNoteCount[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getAllWithNoteCounts();
    }

    // Mock implementation with note counts
    return mockBooks.map(book => ({
      ...book,
      notesCount: Math.floor(Math.random() * 5),
      addedDate: new Date(book.created_at || '')
    }));
  },

  getBooksWithMetadata: async (): Promise<BookWithNoteCount[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getBooksWithMetadata();
    }
    return booksApi.getAllWithNoteCounts();
  },

  getById: async (id: number): Promise<Book> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getById(id);
    }

    const book = mockBooks.find(b => b.id === id);
    if (!book) {
      throw new Error(`Book with id ${id} not found`);
    }
    return { ...book };
  },

  getByIsbn: async (isbn: string): Promise<Book | null> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getByIsbn(isbn);
    }

    const book = mockBooks.find(b => b.isbn === isbn);
    return book ? { ...book } : null;
  },

  getByOlid: async (olid: string): Promise<Book | null> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getByOlid(olid);
    }

    const book = mockBooks.find(b => b.olid === olid);
    return book ? { ...book } : null;
  },

  update: async (id: number, book: Partial<Book>): Promise<Book> => {
    if (hasRealElectronAPI()) {
      return window.db.books.update(id, book);
    }

    const index = mockBooks.findIndex(b => b.id === id);
    if (index === -1) {
      throw new Error(`Book with id ${id} not found`);
    }

    mockBooks[index] = {
      ...mockBooks[index],
      ...book,
      updated_at: new Date().toISOString()
    };

    return { ...mockBooks[index] };
  },

  delete: async (id: number): Promise<{ success: boolean; id: number }> => {
    if (hasRealElectronAPI()) {
      return window.db.books.delete(id);
    }

    const index = mockBooks.findIndex(b => b.id === id);
    if (index === -1) {
      throw new Error(`Book with id ${id} not found`);
    }

    mockBooks.splice(index, 1);
    return { success: true, id };
  },

  getRecent: async (days?: number): Promise<Book[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getRecent(days);
    }

    // Mock implementation - return books created in the last X days
    const daysAgo = days || 14;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysAgo);

    return mockBooks.filter(book => {
      const createdDate = new Date(book.created_at || '');
      return createdDate > cutoffDate;
    });
  },

  search: async (searchTerm: string): Promise<Book[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.search(searchTerm);
    }

    // Mock implementation
    const query = searchTerm.toLowerCase();
    return mockBooks.filter(book =>
      book.title.toLowerCase().includes(query) ||
      (book.author && book.author.toLowerCase().includes(query)) ||
      (book.isbn && book.isbn.includes(query))
    );
  },

  searchOnline: async (query: string, limit: number = 10): Promise<BookSearchResult[]> => {
    if (hasRealElectronAPI()) {
      return window.db.books.searchOnline(query, limit);
    }

    // Mock search results
    return [];
  },

  searchHybrid: async (searchTerm: string, includeOnline?: boolean, onlineLimit?: number) => {
    if (hasRealElectronAPI()) {
      return window.db.books.searchHybrid(searchTerm, includeOnline, onlineLimit);
    }

    // Mock implementation
    const localResults = await booksApi.search(searchTerm);
    const onlineResults = includeOnline ? await booksApi.searchOnline(searchTerm, onlineLimit) : [];

    return {
      localResults,
      onlineResults
    };
  },

  getDetailsFromOpenLibrary: async (olid: string): Promise<Partial<Book>> => {
    if (hasRealElectronAPI()) {
      return window.db.books.getDetailsFromOpenLibrary(olid);
    }

    // Mock implementation
    return {
      title: `Book Details for ${olid}`,
      author: 'Mock Author',
      description: 'This is a mock description for the book.',
      genres: 'Fiction, Mock',
      publication_date: '2023',
      page_count: 300,
      language: 'en',
      olid
    };
  },

  addFromOpenLibrary: async (searchResult: BookSearchResult): Promise<Book> => {
    if (hasRealElectronAPI()) {
      return window.db.books.addFromOpenLibrary(searchResult);
    }

    // Mock adding a book from OpenLibrary
    const book: Book = {
      id: Date.now(),
      title: searchResult.title,
      author: searchResult.author_name ? searchResult.author_name.join(', ') : '',
      isbn: searchResult.isbn_primary || '',
      cover_url: searchResult.cover_url || '',
      publication_date: searchResult.first_publish_year?.toString() || '',
      description: searchResult.description || '',
      page_count: 0,
      current_page: 0,
      rating: 0,
      language: '',
      genres: searchResult.genres || '',
      olid: searchResult.olid || '',
      status: 'unread' as const,
      custom_fields: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockBooks.push(book);
    return book;
  },

  downloadCover: async (coverUrl: string, filename: string): Promise<string> => {
    if (hasRealElectronAPI()) {
      return window.db.books.downloadCover(coverUrl, filename);
    }

    // Mock implementation
    return 'mock-cover-data';
  },

  checkAndDownloadMissingCovers: async () => {
    // Mock function - does nothing in browser environment
    console.log('Mock: Checking and downloading missing covers...');
    return Promise.resolve();
  },

  ensureFoldersForAll: async () => {
    if (hasRealElectronAPI()) {
      return window.db.books.ensureFoldersForAll();
    }
    // Mock implementation
    return { created: 0, errors: 0, total: mockBooks.length };
  },

  getBooksWithoutFolders: async () => {
    if (hasRealElectronAPI()) {
      return window.db.books.getBooksWithoutFolders();
    }
    // Mock implementation
    return [];
  }
};

// Mock timer data
const mockTimerSessions: TimerSession[] = [];
let nextTimerSessionId = 1;

export const timerApi: TimerAPI = {
  // Timer sessions
  start: async (sessionType?: string, focus?: string, category?: string): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.start(sessionType, focus, category);
    }
    
    // Mock implementation
    const newSession: TimerSession = {
      id: nextTimerSessionId++,
      start_time: new Date().toISOString(),
      session_type: sessionType || 'work',
      is_completed: 0,
      created_at: new Date().toISOString(),
      focus: focus || null,
      category: category || null,
      session_name: null,
      is_user_session: 0,
      pomodoro_cycles_completed: 0
    };
    
    mockTimerSessions.push(newSession);
    return newSession;
  },

  end: async (sessionId: number): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.end(sessionId);
    }
    
    // Mock implementation
    const session = mockTimerSessions.find(s => s.id === sessionId);
    if (!session) {
      throw new Error(`Session with id ${sessionId} not found`);
    }
    
    const endTime = new Date().toISOString();
    const startTime = new Date(session.start_time);
    const duration = Math.floor((Date.now() - startTime.getTime()) / 1000);
    
    session.end_time = endTime;
    session.duration = duration;
    session.is_completed = 1;
    session.updated_at = endTime;
    
    return { ...session };
  },

  getSession: async (sessionId: number): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getSession(sessionId);
    }
    
    // Mock implementation
    const session = mockTimerSessions.find(s => s.id === sessionId);
    if (!session) {
      throw new Error(`Session with id ${sessionId} not found`);
    }
    return { ...session };
  },

  getSessionsByDateRange: async (startDate: string, endDate: string): Promise<TimerSession[]> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getSessionsByDateRange(startDate, endDate);
    }

    // Mock implementation
    return [...mockTimerSessions];
  },

  getTodaySessions: async (): Promise<TimerSession[]> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getTodaySessions();
    }
    
    // Mock implementation
    const today = new Date().toISOString().split('T')[0];
    return mockTimerSessions.filter(session =>
      session.start_time.startsWith(today)
    );
  },

  getStatsByDateRange: async (startDate: string, endDate: string): Promise<TimerStats> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getStatsByDateRange(startDate, endDate);
    }
    
    // Mock implementation
    const completedSessions = mockTimerSessions.filter(s => s.is_completed === 1);
    const workSessions = completedSessions.filter(s => s.session_type === 'work');
    const breakSessions = completedSessions.filter(s => s.session_type === 'break');
    
    return {
      total_sessions: completedSessions.length,
      total_duration: completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0),
      work_sessions: workSessions.length,
      work_duration: workSessions.reduce((sum, s) => sum + (s.duration || 0), 0),
      break_sessions: breakSessions.length,
      break_duration: breakSessions.reduce((sum, s) => sum + (s.duration || 0), 0),
      total_pomodoros: completedSessions.reduce((sum, s) => sum + (s.pomodoro_cycles_completed || 0), 0)
    };
  },

  deleteSession: async (sessionId: number): Promise<{ success: boolean; id: number }> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.deleteSession(sessionId);
    }
    
    // Mock implementation
    const index = mockTimerSessions.findIndex(s => s.id === sessionId);
    if (index === -1) {
      return { success: false, id: sessionId };
    }
    
    mockTimerSessions.splice(index, 1);
    return { success: true, id: sessionId };
  },

  // User sessions
  createUserSession: async (sessionName: string, focus?: string, category?: string): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.createUserSession(sessionName, focus, category);
    }
    
    // Mock implementation
    const newSession: TimerSession = {
      id: nextTimerSessionId++,
      start_time: new Date().toISOString(),
      session_type: 'work',
      is_completed: 0,
      created_at: new Date().toISOString(),
      focus: focus || null,
      category: category || 'General',
      session_name: sessionName,
      is_user_session: 1,
      pomodoro_cycles_completed: 0
    };
    
    mockTimerSessions.push(newSession);
    return newSession;
  },

  getActiveUserSession: async (): Promise<TimerSession | null> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getActiveUserSession();
    }
    
    // Mock implementation
    const activeSession = mockTimerSessions.find(s =>
      s.is_user_session === 1 && s.is_completed === 0
    );
    return activeSession ? { ...activeSession } : null;
  },

  endUserSession: async (sessionId: number): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.endUserSession(sessionId);
    }

    // Mock implementation - same as regular end
    return timerApi.end(sessionId);
  },

  updateSession: async (sessionId: number, updateData: {
    duration?: number;
    pomodoro_cycles_completed?: number;
    focus?: string;
    category?: string;
    session_name?: string;
  }): Promise<TimerSession> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.updateSession(sessionId, updateData);
    }

    // Mock implementation - find and update the session
    const sessionIndex = mockTimerSessions.findIndex(s => s.id === sessionId);
    if (sessionIndex === -1) {
      throw new Error(`Session with ID ${sessionId} not found.`);
    }

    const session = mockTimerSessions[sessionIndex];
    const updatedSession = {
      ...session,
      ...updateData,
      updated_at: new Date().toISOString()
    };

    mockTimerSessions[sessionIndex] = updatedSession;
    return { ...updatedSession };
  },

  // Pomodoro cycles
  startPomodoroInSession: async (sessionId: number, cycleType: string): Promise<PomodoroCycle> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.startPomodoroInSession(sessionId, cycleType);
    }
    
    // Mock implementation
    return {
      id: Date.now(),
      session_id: sessionId,
      cycle_type: cycleType as 'pomodoro' | 'short_break' | 'long_break',
      start_time: new Date().toISOString(),
      completed: 0,
      created_at: new Date().toISOString()
    };
  },

  completePomodoroInSession: async (sessionId: number, cycleId: number): Promise<PomodoroCycle> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.completePomodoroInSession(sessionId, cycleId);
    }

    // Mock implementation
    return {
      id: cycleId,
      session_id: sessionId,
      cycle_type: 'pomodoro',
      start_time: new Date().toISOString(),
      end_time: new Date().toISOString(),
      duration: 1500, // 25 minutes
      completed: 1,
      created_at: new Date().toISOString()
    };
  },

  cancelActiveCycle: async (sessionId: number, cycleId: number): Promise<void> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.cancelActiveCycle(sessionId, cycleId);
    }

    // Mock implementation - just log the cancellation
    console.log(`Mock: Canceling cycle ${cycleId} for session ${sessionId}`);
  },

  // Timer settings
  getSettings: async (): Promise<TimerSettings> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.getSettings();
    }
    
    // Mock implementation
    return {
      id: 1,
      work_duration: 1500, // 25 minutes
      short_break_duration: 300, // 5 minutes
      long_break_duration: 900, // 15 minutes
      long_break_interval: 4,
      auto_start_breaks: 1,
      auto_start_work: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  },

  updateSettings: async (settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>): Promise<TimerSettings> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.updateSettings(settingsUpdates);
    }
    
    // Mock implementation - return current settings
    return timerApi.getSettings();
  },

  resetSettings: async (): Promise<TimerSettings> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.resetSettings();
    }

    // Mock implementation - return default settings
    return timerApi.getSettings();
  },

  syncAllSessionPomodoroCounts: async (): Promise<{ updated: number; total: number }> => {
    if (hasRealElectronAPI()) {
      return window.db.timer.syncAllSessionPomodoroCounts();
    }

    // Mock implementation
    return { updated: 0, total: 0 };
  }
};


// Discord API mock
export const discordApi = {
  initialize: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.initialize();
    }

    // Mock implementation - always return true
    console.log('🎮 [MockAPI] Discord initialize called');
    return true;
  },

  setEnabled: async (enabled: boolean): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.setEnabled(enabled);
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord setEnabled called:', enabled);
    return true;
  },

  setActivity: async (activityData: any): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.setActivity(activityData);
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord setActivity called:', activityData);
    return true;
  },

  setActiveState: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.setActiveState();
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord setActiveState called');
    return true;
  },

  setIdle: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.setIdle();
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord setIdle called');
    return true;
  },

  updateSettings: async (settings: any): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.updateSettings(settings);
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord updateSettings called:', settings);
    return true;
  },

  clearActivity: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.clearActivity();
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord clearActivity called');
    return true;
  },

  getStatus: async (): Promise<{ connected: boolean; enabled: boolean; settings: any }> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.getStatus();
    }

    // Mock implementation
    return {
      connected: false,
      enabled: false,
      settings: {
        enabled: false,
        showNoteTaking: true,
        showBookWriting: true,
        showBookNames: true,
        showTimer: true,
        showSettings: true
      }
    };
  },

  destroy: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.destroy();
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord destroy called');
    return true;
  },

  testConnection: async (): Promise<boolean> => {
    if (hasRealElectronAPI()) {
      return window.db.discord.testConnection();
    }

    // Mock implementation
    console.log('🎮 [MockAPI] Discord testConnection called');
    return true;
  }
};

// Settings API mock
export const settingsApi = {
  get: async (key: string): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.get(key);
    }
    // Mock implementation
    return null;
  },

  getByCategory: async (category: string): Promise<any[]> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.getByCategory(category);
    }
    // Mock implementation
    return [];
  },

  getAll: async (): Promise<any[]> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.getAll();
    }
    // Mock implementation
    return [];
  },

  set: async (key: string, value: any, category?: string): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.set(key, value, category);
    }
    // Mock implementation
    return { key, value, category };
  },

  delete: async (key: string): Promise<{ success: boolean; key: string }> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.delete(key);
    }
    // Mock implementation
    return { success: true, key };
  },

  getActiveTheme: async (): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.getActiveTheme();
    }
    // Mock implementation
    return { id: 1, theme_name: 'light', is_active: 1 };
  },

  getAllThemes: async (): Promise<any[]> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.getAllThemes();
    }
    // Mock implementation
    return [
      { id: 1, theme_name: 'light', is_active: 1 },
      { id: 2, theme_name: 'dark', is_active: 0 }
    ];
  },

  createTheme: async (themeName: string): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.createTheme(themeName);
    }
    // Mock implementation
    return { id: Date.now(), theme_name: themeName, is_active: 0 };
  },

  setActiveTheme: async (themeId: number): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.setActiveTheme(themeId);
    }
    // Mock implementation
    return { id: themeId, theme_name: 'mock-theme', is_active: 1 };
  },

  deleteTheme: async (themeId: number): Promise<any> => {
    if (hasRealElectronAPI() && window.electronAPI?.settings) {
      return window.electronAPI.settings.deleteTheme(themeId);
    }
    // Mock implementation
    return { success: true, id: themeId };
  }
};


// Select folder function
export const selectFolder = async (): Promise<string | null> => {
  if (hasRealElectronAPI()) {
    return window.electronAPI.selectFolder();
  }

  // Mock implementation - return null (no folder selected)
  return null;
};

// Create a unified API object that mirrors the window.db structure
export const dbApi = {
  notes: notesApi,
  folders: foldersApi,
  recentItems: recentItemsApi,
  books: booksApi,
  timer: timerApi,
  discord: discordApi,
  settings: settingsApi,
  selectFolder: selectFolder
};