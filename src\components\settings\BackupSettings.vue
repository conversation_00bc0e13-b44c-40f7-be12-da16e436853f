<template>
  <div class="backup-container">
    <div class="backup-header-wrapper">
      <div class="backup-header">
        Backup
      </div>
      <div class="backup-master-toggle">
        <label class="toggle-switch">
          <input
            type="checkbox"
            v-model="masterBackupEnabled"
            @change="updateMasterBackup"
          >
          <span class="toggle-slider"></span>
        </label>
        <span class="toggle-label">{{ masterBackupEnabled ? 'On' : 'Off' }}</span>
      </div>
    </div>
    <div class="backup-divider"></div>

    <!-- Backup Settings Content (shown when enabled) -->
    <div v-if="masterBackupEnabled" class="backup-content">

    <!-- Backup Location Section -->
    <div class="backup-section">
      <div class="backup-subtitle">Backup Location</div>
      <div class="backup-location-wrapper">
        <div class="backup-location-display" v-if="backupLocation">
          <div class="location-path">{{ backupLocation }}</div>
          <button class="location-button change-button" @click="selectBackupLocation">
            Change
          </button>
        </div>
        <div class="backup-location-empty" v-else>
          <div class="empty-message">No backup location set</div>
          <button class="location-button add-button" @click="selectBackupLocation">
            Add Location
          </button>
        </div>
      </div>
      <div class="backup-location-help">
        <small class="help-text">
          Choose a folder you have write access to, such as Desktop, a custom folder you created, or an external drive.
          Avoid system folders like Documents, Program Files, or Windows.
        </small>
      </div>
    </div>

    <!-- Backup Format Section (Removed - defaulting to markdown format) -->



    <!-- Auto Backup Section -->
    <div class="backup-section">
      <div class="backup-subtitle">Auto Backup</div>
      <div class="backup-toggle-wrapper">
        <div class="toggle-description">
          Automatically backup your notes and folders
        </div>
        <div class="toggle-container">
          <label class="toggle-switch">
            <input
              type="checkbox"
              v-model="autoBackupEnabled"
              @change="updateAutoBackup"
              :disabled="!masterBackupEnabled"
            >
            <span class="toggle-slider" :class="{ 'disabled': !masterBackupEnabled }"></span>
          </label>
          <span class="toggle-label">{{ autoBackupEnabled ? 'On' : 'Off' }}</span>
        </div>
      </div>
    </div>

    <!-- Manual Sync Section -->
    <div class="backup-section">
      <div class="backup-subtitle">Manual Sync</div>
      <div class="sync-wrapper">
        <div class="sync-description">
          Manually sync your data with the selected location (bidirectional)
        </div>
        <div class="sync-controls">
          <div class="sync-status" v-if="isBackingUp || syncStatusMessage">
            {{ syncStatusMessage }}
          </div>
          <button
            class="sync-button"
            @click="performManualBackup"
            :disabled="!masterBackupEnabled || !backupLocation || isBackingUp"
          >
            <!-- Sync Icon (default/loading state) -->
            <img
              v-if="syncStage !== 'success'"
              src="/icons/sync-icon.svg"
              class="sync-icon"
              :class="{ 'rotating': isBackingUp }"
              width="16"
              height="16"
              alt="Sync"
            />
            <!-- Checkmark Icon (success state) -->
            <img
              v-if="syncStage === 'success'"
              src="/icons/check-icon.svg"
              class="sync-icon success"
              width="16"
              height="16"
              alt="Success"
            />
            <span v-if="!isBackingUp">Sync Now</span>
            <span v-else-if="syncStage === 'success'">Success</span>
            <span v-else>Syncing</span>
          </button>
        </div>
      </div>
      <!-- Last sync date displayed minimally under the manual sync container -->
      <div class="last-sync-info" v-if="lastBackupTime">
        <small class="last-sync-text">Last sync: {{ formatRelativeTime(lastBackupTime) }}</small>
      </div>
    </div>



    <!-- Notification Messages (Errors only) -->
    <div v-if="notification && notification.type === 'error'" class="notification error">
      <span class="notification-message">{{ notification.message }}</span>
      <button class="notification-close" @click="clearNotification">×</button>
    </div>

    </div> <!-- End backup-content -->

    <!-- TODO: Replace with unified sync modals -->
    <!-- <ImportBackupModal
      v-if="showImportBackupModal"
      @close="showImportBackupModal = false"
      @import-complete="handleImportComplete"
    /> -->

    <!-- <SyncHistoryModal
      v-if="showSyncHistoryModal"
      @close="showSyncHistoryModal = false"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from '../../stores/settingsStore'
// TODO: Import unified sync types when implemented
// import type { UnifiedSyncConfig, SyncResult } from '../../types/electron-api'
// import ImportBackupModal from '../modals/ImportBackupModal.vue'
// import SyncHistoryModal from '../modals/SyncHistoryModal.vue'

// Component state
const settingsStore = useSettingsStore()
const isBackingUp = ref(false)

const syncStage = ref<'idle' | 'checking' | 'syncing' | 'success'>('idle')
const syncStatusMessage = ref('')



const notification = ref<{ type: 'success' | 'error' | 'info'; message: string } | null>(null)

// Local state for backup settings since they're not in the main settings store yet
const masterBackupEnabled = ref(false)
const backupLocation = ref<string | null>(null)
const autoBackupEnabled = ref(true)

const lastBackupTime = ref<string | null>(null)

// Timer for updating relative time display
let updateTimer: NodeJS.Timeout | null = null



// Load backup settings from database on mount
async function loadBackupSettings() {
  try {
    const db = window.electronAPI
    
    // Load individual backup settings
    const settings = await db.settings.getAll()
    const settingsMap = new Map(settings.map((s: any) => [s.key, s.value]))
    
    masterBackupEnabled.value = settingsMap.get('backupEnabled') ?? false
    backupLocation.value = settingsMap.get('backupLocation') ?? null
    autoBackupEnabled.value = settingsMap.get('autoBackupEnabled') ?? true

    lastBackupTime.value = settingsMap.get('lastBackupTime') ?? null
    
    console.log('Loaded backup settings:', {
      masterBackupEnabled: masterBackupEnabled.value,
      backupLocation: backupLocation.value,
      autoBackupEnabled: autoBackupEnabled.value
    })
  } catch (error) {
    console.error('Failed to load backup settings:', error)
  }
}

// Save backup setting to database
async function saveBackupSetting(key: string, value: any) {
  try {
    await window.electronAPI.settings.set(key, value, 'backup')
    console.log(`Saved backup setting ${key}:`, value)
  } catch (error) {
    console.error(`Failed to save backup setting ${key}:`, error)
  }
}

// TODO: Implement unified sync location selection
async function selectBackupLocation() {
  try {
    console.log('Opening folder selection dialog...')

    const result = await window.electronAPI.selectFolder()
    if (result) {
      console.log('Selected sync location:', result)

      // Store the backup location
      backupLocation.value = result
      await saveBackupSetting('backupLocation', result)

      // CRITICAL FIX: Always configure sync path when location is selected
      // This triggers manifest initialization with current database state
      await window.electronAPI.sync.configure({
        syncPath: result,
        autoSync: autoBackupEnabled.value
      })

      showNotification('success', 'Backup location selected successfully')
    }
  } catch (error) {
    console.error('Error selecting sync location:', error)
    showNotification('error', 'Failed to select sync location')
  }
}

// TODO: Implement unified sync master toggle
async function updateMasterBackup() {
  try {
    console.log('Master backup toggled:', masterBackupEnabled.value)

    // Save the master backup state
    await saveBackupSetting('backupEnabled', masterBackupEnabled.value)

    // If enabling master backup and backup location exists, initialize it
    if (masterBackupEnabled.value && backupLocation.value) {
      await window.electronAPI.sync.configure({
        syncPath: backupLocation.value,
        autoSync: autoBackupEnabled.value
      })
    }

    // If disabling master backup, also disable auto backup
    if (!masterBackupEnabled.value && autoBackupEnabled.value) {
      autoBackupEnabled.value = false
      await saveBackupSetting('autoBackupEnabled', false)

      // Disable sync
      await window.electronAPI.sync.configure({
        autoSync: false
      })
    }

    showNotification('success', `Backup ${masterBackupEnabled.value ? 'enabled' : 'disabled'}`)
  } catch (error) {
    console.error('Error updating master sync:', error)
    showNotification('error', 'Failed to update sync settings')
  }
}

// TODO: Implement unified auto-sync toggle
async function updateAutoBackup() {
  try {
    console.log('Auto backup toggled:', autoBackupEnabled.value)
    
    // Save the auto backup state
    await saveBackupSetting('autoBackupEnabled', autoBackupEnabled.value)
    
    // Configure auto-sync if enabled
    if (autoBackupEnabled.value && backupLocation.value) {
      await window.electronAPI.sync.configure({
        autoSync: true,
        syncPath: backupLocation.value
      })
    } else {
      await window.electronAPI.sync.configure({
        autoSync: false
      })
    }
    
    showNotification('success', `Auto backup ${autoBackupEnabled.value ? 'enabled' : 'disabled'}`)
  } catch (error) {
    console.error('Error updating auto sync:', error)
    showNotification('error', 'Failed to update auto sync settings')
  }
}

// TODO: Implement unified manual sync
async function performManualBackup() {
  if (!masterBackupEnabled.value || !backupLocation.value || isBackingUp.value) return

  isBackingUp.value = true
  syncStage.value = 'checking'
  syncStatusMessage.value = 'Checking for changes...'

  try {
    console.log('Starting manual sync to:', backupLocation.value)

    syncStage.value = 'syncing'
    syncStatusMessage.value = 'Syncing...'

    // Perform sync using the new sync API
    const result = await window.electronAPI.sync.perform(backupLocation.value)

    if (result.success) {
      syncStage.value = 'success'
      const totalImported = result.imported.books + result.imported.folders + result.imported.notes
      const totalExported = result.exported.books + result.exported.folders + result.exported.notes
      
      if (totalImported > 0 || totalExported > 0) {
        syncStatusMessage.value = `Synced ${totalImported} imported, ${totalExported} exported`
        showNotification('success', `Sync completed: ${totalImported} items imported, ${totalExported} items exported`)
      } else {
        syncStatusMessage.value = 'Everything is up to date'
        showNotification('success', 'Everything is already synchronized')
      }
      
      // Refresh lastBackupTime from database (sync API will have updated it)
      await loadBackupSettings()
    } else {
      throw new Error(result.errors.join(', '))
    }
    
  } catch (error: any) {
    console.error('Manual sync failed:', error)
    syncStage.value = 'idle'
    syncStatusMessage.value = ''
    showNotification('error', `Sync failed: ${error.message || 'Unknown error'}`)
  } finally {
    // Reset to idle state after showing success for 2 seconds
    if (syncStage.value === 'success') {
      setTimeout(() => {
        isBackingUp.value = false
        syncStage.value = 'idle'
        syncStatusMessage.value = ''
      }, 2000)
    } else {
      isBackingUp.value = false
    }
  }
}

// Function to format relative time with dynamic units
function formatRelativeTime(timestamp: string): string {
  if (!timestamp) return 'Never'

  const time = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - time.getTime()

  // Convert to different units
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  const diffWeeks = Math.floor(diffDays / 7)
  const diffMonths = Math.floor(diffDays / 30)
  const diffYears = Math.floor(diffDays / 365)

  // Return appropriate format based on time elapsed
  if (diffSeconds < 60) {
    return diffSeconds <= 1 ? 'Just now' : `${diffSeconds} seconds ago`
  } else if (diffMinutes < 60) {
    return diffMinutes === 1 ? '1 minute ago' : `${diffMinutes} minutes ago`
  } else if (diffHours < 24) {
    return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`
  } else if (diffDays < 7) {
    return diffDays === 1 ? '1 day ago' : `${diffDays} days ago`
  } else if (diffWeeks < 4) {
    return diffWeeks === 1 ? '1 week ago' : `${diffWeeks} weeks ago`
  } else if (diffMonths < 12) {
    return diffMonths === 1 ? '1 month ago' : `${diffMonths} months ago`
  } else {
    return diffYears === 1 ? '1 year ago' : `${diffYears} years ago`
  }
}

// Notification functions
function showNotification(type: 'success' | 'error' | 'info', message: string) {
  notification.value = { type, message }
  // Auto-clear notification after 5 seconds
  setTimeout(() => {
    if (notification.value?.message === message) {
      notification.value = null
    }
  }, 5000)
}

function clearNotification() {
  notification.value = null
}









// Start timer to update relative time display
function startUpdateTimer() {
  if (updateTimer) {
    clearInterval(updateTimer)
  }

  // Update every 30 seconds to keep relative time current
  updateTimer = setInterval(() => {
    // Force reactivity update by reassigning the same value
    if (lastBackupTime.value) {
      lastBackupTime.value = lastBackupTime.value
    }
  }, 30000)
}

// Stop the update timer
function stopUpdateTimer() {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// Initialize component
onMounted(async () => {
  console.log('BackupSettings component mounted')

  // Load backup settings from database
  await loadBackupSettings()

  // Start the relative time update timer
  startUpdateTimer()

  // Check sync status on mount
  try {
    const status = await window.electronAPI.sync.getStatus()
    console.log('Current sync status:', status)

    // Update UI based on sync status
    if (status.state === 'syncing') {
      isBackingUp.value = true
      syncStage.value = 'syncing'
      syncStatusMessage.value = status.currentOperation || 'Syncing...'
    }

    // Update settings from sync status
    if (status.syncDirectory && status.syncDirectory !== backupLocation.value) {
      settingsStore.updateSetting('syncDirectory', status.syncDirectory)
    }
    if (status.autoSyncEnabled !== autoBackupEnabled.value) {
      settingsStore.updateSetting('autoSyncEnabled', status.autoSyncEnabled)
    }
  } catch (error) {
    console.error('Failed to get sync status:', error)
  }
})

// Cleanup on unmount
onUnmounted(() => {
  console.log('BackupSettings component unmounted')
  stopUpdateTimer()
})
</script>

<style scoped>
/* Apply Montserrat font to all elements */
.backup-container,
.backup-container * {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.backup-container {
  border-radius: 16px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  display: flex;
  padding: 32px;
  flex-direction: column;
  align-items: start;
}

@media (max-width: 991px) {
  .backup-container {
    padding: 20px;
  }
}

.backup-header-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.backup-header {
  color: var(--color-text-primary);
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.backup-master-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.backup-divider {
  background-color: var(--color-border-secondary);
  align-self: stretch;
  display: flex;
  margin-top: 24px;
  flex-shrink: 0;
  height: 1px;
}

@media (max-width: 991px) {
  .backup-divider {
    max-width: 100%;
  }
}

.backup-content {
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.backup-section {
  width: 100%;
  margin-top: 20px;
}

.backup-subtitle {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

/* Backup Location Styles */
.backup-location-wrapper {
  width: 100%;
}

.backup-location-display,
.backup-location-empty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-input-border);
  background-color: var(--color-input-bg);
}

.location-path {
  color: var(--color-text-primary);
  font-size: 13px;
  font-family: 'Courier New', monospace;
  flex: 1;
  margin-right: 12px;
  word-break: break-all;
}

.empty-message {
  color: var(--color-input-placeholder);
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.location-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.add-button {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.add-button:hover {
  background-color: var(--color-primary-hover);
}

.change-button {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.change-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.backup-location-help {
  margin-top: 6px;
}

.help-text {
  color: var(--color-text-secondary);
  font-size: 11px;
  line-height: 1.4;
  display: block;
}

/* Auto Backup Toggle Styles */
.backup-toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-input-border);
  background-color: var(--color-input-bg);
}

.toggle-description {
  color: var(--color-text-secondary);
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 22px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 22px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-bg-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-slider.disabled {
  background-color: var(--color-border-primary) !important;
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-label {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
  min-width: 22px;
}

/* Backup Format Styles */
.format-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.format-option {
  display: flex;
  align-items: flex-start;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid var(--color-input-border);
  background-color: var(--color-input-bg);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.format-option:hover {
  border-color: var(--color-primary);
  background-color: var(--color-card-hover-bg);
}

.format-option input[type="radio"] {
  margin-right: 10px;
  margin-top: 2px;
  accent-color: var(--color-primary);
  flex-shrink: 0;
}

.format-option-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.format-label {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 2px;
}

.format-description {
  color: var(--color-text-secondary);
  font-size: 11px;
}



/* Backup Status Styles */
.backup-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-input-border);
  background-color: var(--color-input-bg);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.status-time {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
}

.status-details {
  color: var(--color-text-secondary);
  font-size: 11px;
}

.view-history-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-btn-secondary-border);
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
}

.view-history-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

/* Manual Sync Styles */
.sync-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-input-border);
  background-color: var(--color-input-bg);
}

.sync-description {
  color: var(--color-text-secondary);
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.sync-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sync-status {
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
  min-width: 120px;
  text-align: right;
}

.sync-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
}

.sync-button:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

.sync-button:disabled {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.sync-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sync-icon.rotating {
  animation: rotate 1s linear infinite;
}

.sync-icon.success {
  filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}

/* Sync button icon styling for primary button */
.sync-button .sync-icon {
  /* Use theme-aware inverse filter for primary buttons */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light theme: primary button has dark bg, needs light icon */
:root .sync-button .sync-icon,
.theme-light .sync-button .sync-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* Dark theme: primary button has light bg, needs dark icon */
.theme-dark .sync-button .sync-icon {
  filter: brightness(0) saturate(100%) invert(13%) sepia(7%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);
}

/* Success state overrides theme filters */
.sync-icon.success {
  filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%) !important;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Last Sync Info Styles */
.last-sync-info {
  margin-top: 8px;
  padding: 0;
  /* Fixed height to prevent layout shifts */
  height: 16px;
  display: flex;
  align-items: center;
}

.last-sync-text {
  color: var(--color-text-secondary);
  font-size: 11px;
  font-weight: 400;
  opacity: 0.8;
  margin: 0;
  line-height: 1;
  /* Prevent text from causing layout shifts */
  white-space: nowrap;
}



/* Notification Styles */
.notification {
  margin-top: 12px;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-card-bg);
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification.success {
  border-left: 4px solid var(--color-success);
  background-color: var(--color-success-bg);
  color: var(--color-success-text);
}

.notification.error {
  border-left: 4px solid var(--color-error);
  background-color: var(--color-error-bg);
  color: var(--color-error-text);
}

.notification-message {
  font-size: 13px;
  font-weight: 500;
  flex: 1;
  color: inherit;
}

.notification-close {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  color: var(--color-text-secondary);
  opacity: 0.7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  margin-left: 12px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  opacity: 1;
  color: var(--color-text-primary);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .format-option {
    padding: 8px 10px;
  }

  .format-label {
    font-size: 12px;
  }

  .format-description {
    font-size: 10px;
  }

  .backup-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .view-history-button {
    align-self: stretch;
    text-align: center;
  }

  .sync-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sync-controls {
    justify-content: space-between;
  }

  .sync-status {
    text-align: left;
    min-width: auto;
  }
}
</style>
