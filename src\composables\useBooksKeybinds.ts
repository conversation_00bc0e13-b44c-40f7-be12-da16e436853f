// Books view keybinds composable
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useBooksKeybinds() {
  const isActive = ref(false)

  // Books functions (to be passed from BooksView)
  let openAddBookModal: () => void = () => console.log('📚 Open add book modal')
  let focusBookSearch: () => void = () => console.log('🔍 Focus book search')
  let openBookDetails: () => void = () => console.log('📖 Open book details')
  let deleteSelectedBook: () => void = () => console.log('🗑️ Delete selected book')
  let openBookNote: () => void = () => console.log('📝 Open book note')
  let createBookNote: () => void = () => console.log('➕ Create book note')

  // Register books-specific keybinds
  const registerBooksKeybinds = () => {
    console.log('📚 Registering books keybinds...')

    // Core books actions
    globalKeybindManager.register({
      key: 'ctrl+n',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen) {
          openAddBookModal()
        }
      },
      description: 'Add new book',
      category: KeybindCategory.BOOKS,
      priority: 'high',
      enabled: true,
      context: { view: 'books', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+f',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen && !context.editorFocused) {
          focusBookSearch()
        }
      },
      description: 'Focus book search',
      category: KeybindCategory.BOOKS,
      priority: 'high',
      enabled: true,
      context: { view: 'books', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'enter',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen && !context.editorFocused) {
          openBookDetails()
        }
      },
      description: 'Open book details',
      category: KeybindCategory.BOOKS,
      priority: 'medium',
      enabled: true,
      context: { view: 'books', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'delete',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen && !context.editorFocused) {
          deleteSelectedBook()
        }
      },
      description: 'Delete selected book',
      category: KeybindCategory.BOOKS,
      priority: 'medium',
      enabled: true,
      context: { view: 'books', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+o',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen) {
          openBookNote()
        }
      },
      description: 'Open book note',
      category: KeybindCategory.BOOKS,
      priority: 'medium',
      enabled: true,
      context: { view: 'books', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+n',
      handler: (context) => {
        if (context.view === 'books' && !context.modalOpen) {
          createBookNote()
        }
      },
      description: 'Create book note',
      category: KeybindCategory.BOOKS,
      priority: 'medium',
      enabled: true,
      context: { view: 'books', modalOpen: false }
    })

    console.log('✅ Books keybinds registered')
  }

  // Unregister books keybinds
  const unregisterBooksKeybinds = () => {
    console.log('🗑️ Unregistering books keybinds...')
    
    globalKeybindManager.unregister('ctrl+n')
    globalKeybindManager.unregister('ctrl+f')
    globalKeybindManager.unregister('enter')
    globalKeybindManager.unregister('delete')
    globalKeybindManager.unregister('ctrl+o')
    globalKeybindManager.unregister('ctrl+shift+n')
  }

  // Activate books keybinds
  const activate = () => {
    if (!isActive.value) {
      registerBooksKeybinds()
      isActive.value = true
      console.log('🟢 Books keybinds activated')
    }
  }

  // Deactivate books keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterBooksKeybinds()
      isActive.value = false
      console.log('🔴 Books keybinds deactivated')
    }
  }

  // Setup functions (to be called from BooksView)
  const setupBookFunctions = (functions: {
    openAddBookModal?: () => void
    focusBookSearch?: () => void
    openBookDetails?: () => void
    deleteSelectedBook?: () => void
    openBookNote?: () => void
    createBookNote?: () => void
  }) => {
    if (functions.openAddBookModal) openAddBookModal = functions.openAddBookModal
    if (functions.focusBookSearch) focusBookSearch = functions.focusBookSearch
    if (functions.openBookDetails) openBookDetails = functions.openBookDetails
    if (functions.deleteSelectedBook) deleteSelectedBook = functions.deleteSelectedBook
    if (functions.openBookNote) openBookNote = functions.openBookNote
    if (functions.createBookNote) createBookNote = functions.createBookNote
    
    console.log('🔧 Books functions configured')
  }

  // Auto-activate when mounted (if currently in books view)
  onMounted(() => {
    // Check current route and activate if in books view
    const currentPath = window.location.hash.slice(1) || window.location.pathname
    if (currentPath.includes('/books')) {
      activate()
    }
  })

  // Cleanup on unmount
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupBookFunctions,
    registerBooksKeybinds,
    unregisterBooksKeybinds
  }
}