# Vue Directive and Cloning Errors Fix

## Overview
Fixed two critical errors that were causing console warnings and import failures in the application:
1. Vue directive syntax error in BackupSettings.vue
2. Object cloning error in ReverseBackupModal.vue

## Files Modified
- `src/components/Settings/BackupSettings.vue`
- `src/components/modals/ReverseBackupModal.vue`

## Issues Fixed

### 1. Vue Directive Syntax Error (Critical)

**Problem:**
```
[Vue warn]: Failed to resolve directive: else"
```

**Location:** `BackupSettings.vue` line 113

**Root Cause:**
Malformed `v-else` directive with an extra quote character:
```vue
<span v-else">Sync Now</span>
```

**Solution:**
Removed the extra quote to fix the directive syntax:
```vue
<span v-else>Sync Now</span>
```

### 2. Object Cloning Error (Critical)

**Problem:**
```
Import failed: Error: An object could not be cloned.
```

**Location:** `ReverseBackupModal.vue` startImport function

**Root Cause:**
Vue reactive objects (refs) cannot be directly cloned by Electron's structured clone algorithm used in IPC communication. The `importConfig.value` object was being passed directly to the Electron API, causing the cloning failure.

**Solution:**
Convert the reactive object to a plain object before passing to the API:

**Before:**
```typescript
const result = await window.electronAPI.reverseBackup.startImport(
  selectedDirectory.value,
  importConfig.value  // ❌ Reactive object - cannot be cloned
)
```

**After:**
```typescript
// Convert reactive object to plain object to avoid cloning issues
const plainConfig = {
  importBooks: importConfig.value.importBooks,
  importNotes: importConfig.value.importNotes,
  importFolders: importConfig.value.importFolders,
  conflictResolution: importConfig.value.conflictResolution,
  preserveTimestamps: importConfig.value.preserveTimestamps,
  createBackup: importConfig.value.createBackup
}

const result = await window.electronAPI.reverseBackup.startImport(
  selectedDirectory.value,
  plainConfig  // ✅ Plain object - can be cloned
)
```

## Technical Details

### Vue Reactivity and Electron IPC
Vue 3's reactive objects contain internal properties and proxies that make them non-serializable for Electron's structured clone algorithm. When passing data to Electron's main process via IPC, all objects must be plain JavaScript objects without circular references or non-cloneable properties.

### Structured Clone Algorithm
Electron uses the structured clone algorithm for IPC communication, which cannot handle:
- Functions
- DOM elements
- Circular references
- Vue reactive proxies
- Symbols (except well-known symbols)

## Impact
- ✅ **Vue Directive Error**: Eliminated console warnings and potential rendering issues
- ✅ **Import Functionality**: Fixed reverse backup import process that was completely broken
- ✅ **User Experience**: Users can now successfully import backup directories without errors
- ✅ **Code Quality**: Improved error handling and data serialization practices

## Testing
Both fixes have been implemented and should resolve the console errors. The application should now:
1. Render BackupSettings.vue without Vue directive warnings
2. Successfully complete reverse backup imports without cloning errors

## Prevention
To prevent similar issues in the future:
1. Always validate Vue directive syntax during code reviews
2. Convert reactive objects to plain objects before passing to Electron APIs
3. Use TypeScript interfaces to ensure proper data structure when crossing IPC boundaries
4. Consider creating utility functions for reactive-to-plain object conversion for reusability
