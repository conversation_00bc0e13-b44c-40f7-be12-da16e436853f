<template>
  <div class="modal-overlay">
    <div class="modal-content">
      <h3>Name Folder</h3>
      <input
        v-model="folderName"
        type="text"
        class="name-input"
        @keyup.enter="confirmName"
        ref="nameInputRef"
        autofocus
      />

      <!-- Color selection section -->
      <div class="color-selection-section">
        <h4>Folder Color (Optional)</h4>
        <div class="color-options">
          <!-- Default color option -->
          <div
            class="color-option default-option"
            :class="{ 'selected': selectedColor === null }"
            @click="selectColor(null)"
            title="Default folder color"
          >
            <FolderIcon :color="undefined" :size="16" :isOpen="false" class="color-folder-icon" />
          </div>

          <!-- Predefined colors -->
          <div
            v-for="color in predefinedColors"
            :key="color.value"
            class="color-option"
            :class="{ 'selected': selectedColor === color.value }"
            :style="{ backgroundColor: color.value }"
            :title="color.name"
            @click="selectColor(color.value)"
          >
          </div>
        </div>
      </div>

      <div class="modal-buttons">
        <button @click="cancelName" class="btn btn-secondary">Cancel</button>
        <button @click="confirmName" class="btn btn-primary">Create</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from 'vue';
import FolderIcon from '../icons/FolderIcon.vue';

export default defineComponent({
  name: 'NameFolderModal',
  components: {
    FolderIcon
  },
  props: {
    initialName: {
      type: String,
      default: 'New Folder',
    },
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const folderName = ref(props.initialName);
    const nameInputRef = ref<HTMLInputElement | null>(null);
    const selectedColor = ref<string | null>(null);

    // Predefined colors (subset of the full palette for space efficiency)
    const predefinedColors = [
      { name: 'Red', value: '#E53E3E' },
      { name: 'Orange', value: '#FF8C00' },
      { name: 'Yellow', value: '#F6E05E' },
      { name: 'Green', value: '#38A169' },
      { name: 'Blue', value: '#3182CE' },
      { name: 'Purple', value: '#805AD5' },
      { name: 'Pink', value: '#ED64A6' },
      { name: 'Teal', value: '#319795' }
    ];

    const selectColor = (color: string | null) => {
      selectedColor.value = color;
    };

    const confirmName = () => {
      if (folderName.value.trim()) {
        emit('confirm', {
          name: folderName.value.trim(),
          color: selectedColor.value
        });
      }
    };

    const cancelName = () => {
      emit('cancel');
    };

    onMounted(() => {
      nextTick(() => {
        if (nameInputRef.value) {
          nameInputRef.value.focus();
          nameInputRef.value.select();
        }
      });
    });

    return {
      folderName,
      nameInputRef,
      selectedColor,
      predefinedColors,
      selectColor,
      confirmName,
      cancelName,
    };
  },
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; /* Ensure it's on top */
}

.modal-content {
  background-color: var(--color-modal-bg);
  padding: 32px; /* Increased padding */
  border-radius: 12px; /* Softer corners */
  box-shadow: 0 8px 24px var(--color-card-shadow); /* Enhanced shadow */
  width: 400px; /* Fixed width */
  max-width: 90%;
  font-family: 'Montserrat', sans-serif; /* Consistent font */
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 24px; /* Increased spacing */
  font-size: 24px; /* Larger title */
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center; /* Centered title */
}

.name-input {
  width: 100%;
  padding: 12px 16px; /* Comfortable padding */
  border: 1px solid var(--color-input-border);
  border-radius: 8px; /* Softer input corners */
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  margin-bottom: 24px; /* Spacing below input */
  box-sizing: border-box; /* Ensures padding doesn't expand width */
}

.name-input:focus {
  outline: none;
  border-color: var(--color-input-focus); /* Highlight focus */
  box-shadow: 0 0 0 2px var(--color-input-focus-shadow); /* Focus ring */
}

.modal-buttons {
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  gap: 12px; /* Space between buttons */
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

/* Color Selection Section */
.color-selection-section {
  margin-bottom: 24px;
}

.color-selection-section h4 {
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  font-family: 'Montserrat', sans-serif;
}

.color-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px var(--color-card-shadow);
}

.color-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px var(--color-card-hover-shadow);
  border-color: var(--color-primary);
}

.color-option.selected {
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px var(--color-card-hover-shadow);
}

.default-option {
  background-color: var(--color-btn-secondary-bg) !important;
  border-color: var(--color-border-primary) !important;
}

.default-option:hover {
  border-color: var(--color-primary) !important;
}

.default-option.selected {
  border-color: var(--color-primary) !important;
}

.color-folder-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}
</style>