# Discord RPC Connection Troubleshooting

## Files Modified
- `public/discord-rpc-api.ts` - Improved error handling and connection logic

## Issue
Discord RPC failing to connect with `RPC_CONNECTION_TIMEOUT` error.

## Common Causes & Solutions

### 1. Discord Not Running
**Error:** `RPC_CONNECTION_TIMEOUT`
**Cause:** Discord application is not running
**Solution:** 
- Start Discord desktop application
- Make sure Discord is fully loaded (not just starting up)
- Wait for Discord to be completely ready before opening Noti

### 2. Discord RPC Disabled
**Error:** `RPC_CONNECTION_TIMEOUT`
**Cause:** Rich Presence is disabled in Discord settings
**Solution:**
1. Open Discord
2. Go to Settings (gear icon)
3. Navigate to "Activity Privacy"
4. Enable "Display current activity as a status message"

### 3. Firewall/Antivirus Blocking
**Error:** `RPC_CONNECTION_TIMEOUT` or `ENOENT`
**Cause:** Security software blocking IPC connection
**Solution:**
- Add Discord to firewall exceptions
- Add Noti to firewall exceptions
- Temporarily disable antivirus to test

### 4. Discord Needs Restart
**Error:** `RPC_CONNECTION_TIMEOUT`
**Cause:** Discord RPC service is stuck
**Solution:**
1. Close Discord completely (check system tray)
2. Wait 10 seconds
3. Restart Discord
4. Wait for Discord to fully load
5. Open Noti

### 5. Multiple Discord Instances
**Error:** `RPC_CONNECTION_TIMEOUT`
**Cause:** Multiple Discord processes running
**Solution:**
1. Open Task Manager
2. End all Discord processes
3. Restart Discord normally
4. Open Noti

## Improved Error Handling

### Better Connection Logic
```typescript
// Now handles disconnected clients properly
if (this.client && !this.isConnected) {
  console.log('🎮 [DiscordRPC] Client exists but not connected, destroying...');
  await this.client.destroy();
  this.client = null;
}
```

### Specific Error Messages
```typescript
if (error.message?.includes('RPC_CONNECTION_TIMEOUT')) {
  console.log('🎮 [DiscordRPC] Connection timeout - Discord may not be running or RPC may be disabled');
} else if (error.message?.includes('ENOENT')) {
  console.log('🎮 [DiscordRPC] Discord not found - please make sure Discord is installed and running');
}
```

### Graceful Degradation
```typescript
// App continues working even if Discord fails
if (!this.client || !this.isConnected || !this.settings.enabled) {
  console.log('🎮 [DiscordRPC] Cannot update activity - not ready');
  return; // Silently continue without Discord
}
```

## Testing Steps

### 1. Basic Connection Test
1. Make sure Discord is running
2. Open Noti
3. Go to Settings → Discord Settings
4. Click "Test Connection" button
5. Should show success message

### 2. Activity Test
1. Enable Discord RPC in Noti settings
2. Go to Notes section
3. Check Discord profile - should show "Taking notes"
4. Switch to Timer - should show "In focus session"

### 3. Troubleshooting Test
1. Close Discord while Noti is running
2. Check console - should show connection timeout
3. Restart Discord
4. Discord should reconnect automatically

## Console Messages Guide

### Normal Operation
```
🎮 [DiscordRPC] Connected to Discord!
🎮 [DiscordRPC] Activity updated: Taking notes
```

### Connection Issues
```
🎮 [DiscordRPC] Failed to initialize: Error: RPC_CONNECTION_TIMEOUT
🎮 [DiscordRPC] Connection timeout - Discord may not be running or RPC may be disabled
```

### Recovery
```
🎮 [DiscordRPC] Attempting reconnection 1/3 in 2000ms
🎮 [DiscordRPC] Connected to Discord!
```

## User Instructions

### If Discord RPC Isn't Working:

1. **Check Discord is Running**
   - Look for Discord in system tray
   - Make sure it's fully loaded

2. **Check Discord Settings**
   - Settings → Activity Privacy
   - Enable "Display current activity as a status message"

3. **Restart Both Applications**
   - Close Discord completely
   - Close Noti
   - Start Discord first, wait for it to load
   - Start Noti

4. **Test Connection**
   - Go to Noti Settings → Discord Settings
   - Click "Test Connection"
   - Should show success message

5. **Check Firewall**
   - Add Discord and Noti to firewall exceptions
   - Temporarily disable antivirus to test

### Expected Behavior
- ✅ Discord shows "Using Noti" when app is open
- ✅ Shows specific activities when doing tasks
- ✅ Timer continues from app start time
- ✅ Goes idle after 3 minutes of inactivity

## Result
The Discord RPC integration now has better error handling and will:
- ✅ Provide clear error messages for common issues
- ✅ Continue working even if Discord connection fails
- ✅ Automatically attempt reconnection
- ✅ Give users actionable troubleshooting steps
