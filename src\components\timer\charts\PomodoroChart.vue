<template>
  <div class="chart-container">
    <h3 class="chart-title">Pomodoro Completion</h3>
    <div class="chart-wrapper">
      <Bar
        v-if="chartData"
        :data="chartData"
        :options="chartOptions"
        :height="200"
      />
      <div v-else class="chart-loading">Loading chart data...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Bar } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'
import { useElectronAPI } from '../../../useElectronAPI'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

interface Session {
  id: number
  start_time: string
  duration?: number | null
  category?: string | null
  session_name?: string | null
  is_completed: number
  pomodoro_cycles_completed?: number | null
}

const db = useElectronAPI()
const sessions = ref<Session[]>([])

const chartData = computed(() => {
  if (!sessions.value.length) return null

  // Get last 7 days
  const days = []
  const completedPomodoros = []
  const totalSessions = []
  const today = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    
    // Filter sessions for this day
    const daySessions = sessions.value.filter(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      return sessionDate === dateStr && session.is_completed === 1
    })
    
    // Calculate pomodoro metrics for this day
    const pomodoroCount = daySessions.reduce((total, session) => total + (session.pomodoro_cycles_completed || 0), 0)
    const sessionCount = daySessions.length
    
    days.push(date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }))
    completedPomodoros.push(pomodoroCount)
    totalSessions.push(sessionCount)
  }

  // Get theme-aware colors
  const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-primary').trim()
  const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary').trim()

  const primaryBg = primaryColor ? `${primaryColor}CC` : getComputedStyle(document.documentElement).getPropertyValue('--color-chart-primary-fallback').trim() || 'rgba(74, 74, 74, 0.8)' // 80% opacity
  const primaryBorder = primaryColor || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-primary-border-fallback').trim() || 'rgba(74, 74, 74, 1)'
  const secondaryBg = secondaryColor ? `${secondaryColor}99` : getComputedStyle(document.documentElement).getPropertyValue('--color-chart-secondary-fallback').trim() || 'rgba(136, 136, 136, 0.6)' // 60% opacity
  const secondaryBorder = secondaryColor || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-secondary-border-fallback').trim() || 'rgba(136, 136, 136, 1)'

  return {
    labels: days,
    datasets: [
      {
        label: 'Completed Pomodoros',
        data: completedPomodoros,
        backgroundColor: primaryBg,
        borderColor: primaryBorder,
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
      },
      {
        label: 'Total Sessions',
        data: totalSessions,
        backgroundColor: secondaryBg,
        borderColor: secondaryBorder,
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
      }
    ]
  }
})

const chartOptions = computed((): ChartOptions<'bar'> => {
  // Get theme-aware colors
  const textColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-text-fallback').trim() || '#4a4a4a'
  const tertiaryTextColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-tertiary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tertiary-text-fallback').trim() || '#888'
  const tooltipBg = getComputedStyle(document.documentElement).getPropertyValue('--color-bg-elevated').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-bg-fallback').trim() || 'rgba(0, 0, 0, 0.8)'
  const tooltipText = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-text-fallback').trim() || '#fff'
  const borderColor = getComputedStyle(document.documentElement).getPropertyValue('--color-border-secondary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-border-fallback').trim() || 'rgba(200, 200, 200, 0.3)'

  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: textColor,
          font: {
            family: 'Montserrat',
            size: 12,
            weight: 500
          },
          padding: 15,
          usePointStyle: true,
          pointStyle: 'rect'
        }
      },
      tooltip: {
        backgroundColor: tooltipBg,
        titleColor: tooltipText,
        bodyColor: tooltipText,
        borderColor: textColor,
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: true,
        callbacks: {
          afterBody: function(context) {
            const dataIndex = context[0].dataIndex
            const pomodoros = completedPomodoros.value[dataIndex] || 0
            const sessions = totalSessions.value[dataIndex] || 0

            if (sessions > 0) {
              const avgPomodoros = (pomodoros / sessions).toFixed(1)
              return [`Average: ${avgPomodoros} pomodoros per session`]
            }
            return []
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: tertiaryTextColor,
          font: {
            family: 'Montserrat',
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: borderColor,
          lineWidth: 1
        },
        ticks: {
          color: tertiaryTextColor,
          font: {
            family: 'Montserrat',
            size: 11
          },
          stepSize: 1
        }
      }
    }
  }
})

// Computed refs for tooltip callback
const completedPomodoros = computed(() => {
  if (!sessions.value.length) return []
  
  const today = new Date()
  const result = []
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    
    const daySessions = sessions.value.filter(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      return sessionDate === dateStr && session.is_completed === 1
    })
    
    const pomodoroCount = daySessions.reduce((total, session) => total + (session.pomodoro_cycles_completed || 0), 0)
    result.push(pomodoroCount)
  }
  
  return result
})

const totalSessions = computed(() => {
  if (!sessions.value.length) return []
  
  const today = new Date()
  const result = []
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    
    const daySessions = sessions.value.filter(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      return sessionDate === dateStr && session.is_completed === 1
    })
    
    result.push(daySessions.length)
  }
  
  return result
})

const loadChartData = async () => {
  try {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const today = new Date().toISOString().split('T')[0]
    const sessionData = await db.timer.getSessionsByDateRange(sevenDaysAgo, today)
    sessions.value = sessionData
  } catch (error) {
    console.error('Failed to load chart data:', error)
  }
}

onMounted(() => {
  loadChartData()
})

// Expose method to refresh data
defineExpose({
  refreshData: loadChartData
})
</script>

<style scoped>
.chart-container {
  background: var(--color-card-bg);
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  padding: 20px;
  margin-bottom: 15px;
}

.chart-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 15px;
  font-family: 'Montserrat', sans-serif;
}

.chart-wrapper {
  height: 200px;
  position: relative;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-tertiary);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
  }
  
  .chart-wrapper {
    height: 180px;
  }
}
</style>
