# Timer Backend: Enhanced Implementation Plan & Dashboard Strategy

## Document Purpose

This document serves as a comprehensive guide for enhancing the Timer application's backend. It incorporates the original "Timer Backend Implementation Plan," provides a detailed architectural review of that plan, and expands upon it with new strategies and recommendations. The primary goals of this enhanced plan are:

1.  To verify and validate the claims made in the original plan.
2.  To propose specific, actionable modifications to the database structure and APIs, ensuring the system is scalable, flexible, and prepared for future dashboard integration requiring rich statistical visualization.
3.  To recommend a suitable TypeScript charting library for generating highly aesthetic and interactive charts for the aforementioned dashboard.
4.  To consolidate all findings into a single, technically detailed roadmap for development.

This document is intended for technical stakeholders, including backend developers, frontend developers, and architects involved in the Timer application's development.

---

## Section 1: Original Backend Implementation Plan

**(The following is the content of the original `Timer_Backend_Implementation_Plan.md` document, included for context and as a baseline for the subsequent review and enhancements.)**

```markdown
# Timer Backend Implementation Plan

## Executive Summary

After analyzing the Timer page frontend implementation and existing backend infrastructure, I've identified what's already implemented and what needs to be completed to fully integrate the Timer functionality. The backend API and database structure are **already implemented**, but the frontend is not connected to the backend.

## Current State Analysis

### ✅ Already Implemented (Backend)

1.  **Database Tables**: 
    *   `timer_sessions` table with all required fields
    *   `timer_settings` table with configuration options

2.  **API Layer**: Complete timer API in `electron/main/api/timer-api.ts`
    *   Session management (start, end, get, delete)
    *   Statistics and reporting
    *   Settings management

3.  **IPC Handlers**: All timer IPC channels registered in `electron/main/ipc-handlers.ts`
    *   Timer sessions: start, end, getSession, etc.
    *   Timer settings: get, update, reset

4.  **Database Schema**: Tables created in `electron/main/database/database.ts`

### ❌ Missing Implementation

1.  **Frontend API Bridge**: Timer API not exposed in `electron/preload/api-bridge.ts`
2.  **Type Definitions**: Timer interfaces not in `src/types/electron-api.d.ts`
3.  **Frontend Integration**: TimerView.vue uses mock data instead of real API
4.  **Session Management**: No real session persistence or retrieval

## Implementation Plan

### Phase 1: API Bridge & Type Definitions

#### 1.1 Add Timer Types to Frontend (`src/types/electron-api.d.ts`)

**File**: `src/types/electron-api.d.ts`
**Action**: Add timer-related interfaces

```typescript
// Timer related interfaces
export interface TimerSession {
  id: number;
  start_time: string; // ISO 8601 format
  end_time?: string | null;
  duration?: number | null; // Duration in seconds
  session_type: string; // e.g., 'work', 'break'
  is_completed: 0 | 1;
  created_at: string;
  updated_at?: string;
  // Frontend-specific fields
  focus?: string;
  category?: string;
  totalFocusTime?: number;
  pomodoroCount?: number;
}

export interface TimerStats {
  total_sessions: number;
  total_duration: number | null;
  work_sessions: number;
  work_duration: number | null;
  break_sessions: number;
  break_duration: number | null;
}

export interface TimerSettings {
  id: number;
  work_duration: number; // seconds
  short_break_duration: number; // seconds
  long_break_duration: number; // seconds
  long_break_interval: number; // number of work sessions before long break
  auto_start_breaks: 0 | 1;
  auto_start_work: 0 | 1;
  created_at: string;
  updated_at: string;
}

export interface TimerAPI {
  // Timer sessions
  start: (sessionType?: string) => Promise<TimerSession>;
  end: (sessionId: number) => Promise<TimerSession>;
  getSession: (sessionId: number) => Promise<TimerSession>;
  getSessionsByDateRange: (startDate: string, endDate: string) => Promise<TimerSession[]>;
  getTodaySessions: () => Promise<TimerSession[]>;
  getStatsByDateRange: (startDate: string, endDate: string) => Promise<TimerStats>;
  deleteSession: (sessionId: number) => Promise<{ success: boolean; id: number }>;
  
  // Timer settings
  getSettings: () => Promise<TimerSettings>;
  updateSettings: (settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>) => Promise<TimerSettings>;
  resetSettings: () => Promise<TimerSettings>;
}
```

#### 1.2 Update Global Window Interface

**File**: `src/types/electron-api.d.ts`
**Action**: Add timer to global window interface

```typescript
declare global {
  interface Window {
    db: {
      notes: NotesAPI;
      folders: FoldersAPI;
      recentItems: RecentItemsAPI;
      media: MediaAPI;
      books: BooksAPI;
      timer: TimerAPI; // Add this line
    }
  }
}
```

#### 1.3 Add Timer API to Bridge

**File**: `electron/preload/api-bridge.ts`
**Action**: Add timer API section

```typescript
// Timer API
timer: {
  // Timer sessions
  start: (sessionType?: string) => ipcRenderer.invoke('timer:start', sessionType),
  end: (sessionId: number) => ipcRenderer.invoke('timer:end', sessionId),
  getSession: (sessionId: number) => ipcRenderer.invoke('timer:getSession', sessionId),
  getSessionsByDateRange: (startDate: string, endDate: string) =>
    ipcRenderer.invoke('timer:getSessionsByDateRange', startDate, endDate),
  getTodaySessions: () => ipcRenderer.invoke('timer:getTodaySessions'),
  getStatsByDateRange: (startDate: string, endDate: string) =>
    ipcRenderer.invoke('timer:getStatsByDateRange', startDate, endDate),
  deleteSession: (sessionId: number) => ipcRenderer.invoke('timer:deleteSession', sessionId),

  // Timer settings
  getSettings: () => ipcRenderer.invoke('timer:getSettings'),
  updateSettings: (settingsUpdates: any) => ipcRenderer.invoke('timer:updateSettings', settingsUpdates),
  resetSettings: () => ipcRenderer.invoke('timer:resetSettings')
}
```

#### 1.4 Update useElectronAPI

**File**: `src/useElectronAPI.ts`
**Action**: Add TimerAPI to interface

```typescript
import type { NotesAPI, FoldersAPI, RecentItemsAPI, BooksAPI, TimerAPI } from './types/electron-api';

export interface ElectronAPI {
  notes: NotesAPI;
  folders: FoldersAPI;
  recentItems: RecentItemsAPI;
  books: BooksAPI;
  timer: TimerAPI; // Add this line
}
```

### Phase 2: Enhanced Backend API

#### 2.1 Extend Timer Session Model

**File**: `electron/main/api/timer-api.ts`
**Action**: Add support for focus and category fields

The current backend only supports basic session tracking. We need to extend it to support:
- Focus description (what the user is working on)
- Category classification
- Better integration with the frontend session model

**Required Changes**:
1.  Add `focus` and `category` columns to `timer_sessions` table
2.  Update API functions to handle these fields
3.  Add migration logic for existing installations

#### 2.2 Database Schema Updates

**File**: `electron/main/database/database.ts`
**Action**: Add missing columns to timer_sessions table

```sql
ALTER TABLE timer_sessions ADD COLUMN focus TEXT;
ALTER TABLE timer_sessions ADD COLUMN category TEXT;
ALTER TABLE timer_sessions ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

### Phase 3: Frontend Integration

#### 3.1 Update TimerView.vue

**File**: `src/views/TimerView.vue`
**Action**: Replace mock data with real API calls

**Key Changes**:
1.  Remove mock data and implement real data loading
2.  Connect session creation to backend
3.  Implement session persistence
4.  Add error handling
5.  Implement real statistics calculation

#### 3.2 Update PomodoroTimer.vue

**File**: `src/components/timer/PomodoroTimer.vue`
**Action**: Integrate with backend session management

**Key Changes**:
1.  Start real timer sessions when timer starts
2.  End sessions when timer completes or is manually ended
3.  Load and save timer settings
4.  Persist timer state across app restarts

#### 3.3 Update AddSessionModal.vue

**File**: `src/components/modals/AddSessionModal.vue`
**Action**: Connect to backend session creation

**Key Changes**:
1.  Create actual timer sessions in database
2.  Handle category persistence
3.  Validate session data

### Phase 4: Advanced Features

#### 4.1 Session History and Statistics

**Implementation Requirements**:
1.  Real-time statistics calculation
2.  Date range filtering
3.  Session search functionality
4.  Export capabilities

#### 4.2 Settings Persistence

**Implementation Requirements**:
1.  Load settings on app startup
2.  Save settings changes immediately
3.  Settings validation
4.  Reset to defaults functionality

#### 4.3 Session State Management

**Implementation Requirements**:
1.  Persist active sessions across app restarts
2.  Handle incomplete sessions
3.  Session recovery mechanisms

## Technical Considerations

### Data Flow Architecture

```
Frontend (TimerView.vue) 
    ↓
useElectronAPI() 
    ↓
electron/preload/api-bridge.ts 
    ↓
IPC Channels 
    ↓
electron/main/ipc-handlers.ts 
    ↓
electron/main/api/timer-api.ts 
    ↓
SQLite Database
```

### Error Handling Strategy

1.  **Frontend**: User-friendly error messages
2.  **IPC Layer**: Proper error propagation
3.  **Backend**: Detailed logging and graceful degradation
4.  **Database**: Transaction rollback and data integrity

### Performance Considerations

1.  **Lazy Loading**: Load session history on demand
2.  **Caching**: Cache frequently accessed settings
3.  **Debouncing**: Debounce settings updates
4.  **Pagination**: Implement pagination for large session lists

## Implementation Priority

### High Priority (Core Functionality)
1.  API Bridge setup (Phase 1)
2.  Basic session management (Phase 3.1, 3.2)
3.  Settings persistence (Phase 4.2)

### Medium Priority (Enhanced UX)
1.  Session history and statistics (Phase 4.1)
2.  Advanced session management (Phase 4.3)
3.  Database schema enhancements (Phase 2)

### Low Priority (Nice to Have)
1.  Export functionality
2.  Advanced analytics
3.  Session templates
4.  Notification system

## Testing Strategy

### Unit Tests
- Timer API functions
- Session validation
- Settings management

### Integration Tests
- Frontend-backend communication
- Database operations
- IPC channel functionality

### User Acceptance Tests
- Complete timer workflows
- Settings persistence
- Session recovery
- Error scenarios

## Migration Strategy

For existing installations:
1.  Check for missing database columns
2.  Add columns with default values
3.  Migrate existing data if necessary
4.  Update schema version

## Detailed Implementation Steps

### Step 1: Database Schema Enhancement

**File**: `electron/main/database/database.ts`

Add migration logic to handle missing columns in existing installations:

```typescript
// Add after timer_sessions table creation
db.run(`ALTER TABLE timer_sessions ADD COLUMN focus TEXT`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add focus column:', err.message);
  }
});

db.run(`ALTER TABLE timer_sessions ADD COLUMN category TEXT`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add category column:', err.message);
  }
});

db.run(`ALTER TABLE timer_sessions ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Could not add updated_at column:', err.message);
  }
});
```

### Step 2: Enhanced Timer API Functions

**File**: `electron/main/api/timer-api.ts`

Update the `startTimerSession` function to support focus and category:

```typescript
export const startTimerSession = async (
  sessionType: string = 'work',
  focus?: string,
  category?: string
): Promise<TimerSession> => {
  if (typeof sessionType !== 'string' || sessionType.trim() === '') {
    throw new Error('Session type must be a non-empty string.');
  }
  try {
    const startTime = new Date().toISOString();
    const result = await dbRun(
      'INSERT INTO timer_sessions (start_time, session_type, focus, category, is_completed, created_at, updated_at) VALUES (?, ?, ?, ?, 0, ?, ?)',
      [startTime, sessionType, focus || null, category || null, startTime, startTime]
    );

    const newSession = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [result.id]);
    if (!newSession) {
      throw new Error('Failed to retrieve the timer session after creation.');
    }
    return newSession;
  } catch (error: any) {
    console.error('Error starting timer session:', error);
    throw new Error(`Failed to start timer session: ${error.message}`);
  }
};
```

### Step 3: Frontend Session Interface

**File**: `src/views/TimerView.vue`

Replace the mock Session interface with the backend-compatible one:

```typescript
// Remove the local Session interface and import from types
import type { TimerSession, TimerStats } from '../types/electron-api';

// Update the session management functions
const startSession = async (sessionData: { focus: string; category: string }) => {
  try {
    // Start a real timer session in the backend
    const session = await db.timer.start('work', sessionData.focus, sessionData.category);

    activeSession.value = {
      ...session,
      focus: sessionData.focus,
      category: sessionData.category,
      date: new Date(session.start_time),
      totalFocusTime: 0,
      pomodoroCount: 0
    };

    showAddSessionModal.value = false;
  } catch (error) {
    console.error('Failed to start session:', error);
    // Handle error appropriately
  }
};

const endSession = async (sessionStats: { totalFocusTime: number; pomodoroCount: number }) => {
  if (activeSession.value?.id) {
    try {
      // End the session in the backend
      await db.timer.end(activeSession.value.id);

      // Add to completed sessions
      const completedSession = {
        ...activeSession.value,
        totalFocusTime: sessionStats.totalFocusTime,
        pomodoroCount: sessionStats.pomodoroCount
      };

      completedSessions.value.unshift(completedSession);
      activeSession.value = null;

      // Refresh statistics
      await loadStats();
    } catch (error) {
      console.error('Failed to end session:', error);
    }
  }
};
```

### Step 4: Statistics Integration

**File**: `src/views/TimerView.vue`

Implement real statistics loading:

```typescript
const loadStats = async () => {
  try {
    // Get today's date for statistics
    const today = new Date().toISOString().split('T')[0];
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Load today's sessions for current week count
    const todaySessions = await db.timer.getTodaySessions();
    const weekStats = await db.timer.getStatsByDateRange(weekAgo, today);

    // Calculate total statistics (you might want to cache this or limit the range)
    const allTimeStats = await db.timer.getStatsByDateRange('2020-01-01', today);

    stats.value = {
      totalSessions: allTimeStats.total_sessions,
      totalPomodoros: Math.floor((allTimeStats.work_duration || 0) / 1500), // Assuming 25min pomodoros
      totalFocusTime: formatDuration(allTimeStats.work_duration || 0),
      sessionsThisWeek: weekStats.total_sessions
    };
  } catch (error) {
    console.error('Failed to load statistics:', error);
  }
};

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
};
```

### Step 5: Settings Integration

**File**: `src/components/timer/PomodoroTimer.vue`

Load and save timer settings:

```typescript
// Add to setup function
const loadSettings = async () => {
  try {
    const settings = await db.timer.getSettings();
    pomodoroTime.value = settings.work_duration;
    shortBreakTime.value = settings.short_break_duration;
    longBreakTime.value = settings.long_break_duration;
    longBreakInterval.value = settings.long_break_interval;
    autoStartBreaks.value = Boolean(settings.auto_start_breaks);
    autoStartPomodoros.value = Boolean(settings.auto_start_work);

    // Update current timer if it's a pomodoro
    if (timerType.value === 'pomodoro') {
      timeLeft.value = pomodoroTime.value;
    }
  } catch (error) {
    console.error('Failed to load timer settings:', error);
  }
};

const updateSettings = async (newSettings: {
  pomodoroTime: number;
  shortBreakTime: number;
  longBreakTime: number;
  longBreakInterval: number;
  autoStartBreaks: boolean;
  autoStartPomodoros: boolean;
}) => {
  try {
    await db.timer.updateSettings({
      work_duration: newSettings.pomodoroTime,
      short_break_duration: newSettings.shortBreakTime,
      long_break_duration: newSettings.longBreakTime,
      long_break_interval: newSettings.longBreakInterval,
      auto_start_breaks: newSettings.autoStartBreaks ? 1 : 0,
      auto_start_work: newSettings.autoStartPomodoros ? 1 : 0
    });

    // Update local values
    pomodoroTime.value = newSettings.pomodoroTime;
    shortBreakTime.value = newSettings.shortBreakTime;
    longBreakTime.value = newSettings.longBreakTime;
    longBreakInterval.value = newSettings.longBreakInterval;
    autoStartBreaks.value = newSettings.autoStartBreaks;
    autoStartPomodoros.value = newSettings.autoStartPomodoros;

    // Update current timer according to its type
    if (timerType.value === 'pomodoro') {
      timeLeft.value = pomodoroTime.value;
    } else if (timerType.value === 'shortBreak') {
      timeLeft.value = shortBreakTime.value;
    } else if (timerType.value === 'longBreak') {
      timeLeft.value = longBreakTime.value;
    }

    // Reset timer if running
    if (isRunning.value) {
      isRunning.value = false;
      stopTimer();
    }
  } catch (error) {
    console.error('Failed to update timer settings:', error);
  }
};

// Load settings on component mount
onMounted(() => {
  loadSettings();
});
```

### Step 6: Session History Loading

**File**: `src/views/TimerView.vue`

Replace mock session loading with real data:

```typescript
const loadSessions = async () => {
  try {
    // Load recent sessions (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];

    const sessions = await db.timer.getSessionsByDateRange(thirtyDaysAgo, today);

    // Transform backend sessions to frontend format
    completedSessions.value = sessions
      .filter(session => session.is_completed === 1)
      .map(session => ({
        id: session.id,
        focus: session.focus || 'Unnamed Session',
        category: session.category || 'No Category',
        date: new Date(session.start_time),
        totalFocusTime: session.duration || 0,
        pomodoroCount: Math.floor((session.duration || 0) / 1500) // Estimate pomodoros
      }));
  } catch (error) {
    console.error('Failed to load sessions:', error);
  }
};
```

## File Modification Summary

### Files to Modify:

1.  **`src/types/electron-api.d.ts`** - Add timer interfaces and API definitions
2.  **`electron/preload/api-bridge.ts`** - Add timer API bridge
3.  **`src/useElectronAPI.ts`** - Add TimerAPI to interface
4.  **`electron/main/database/database.ts`** - Add database migrations
5.  **`electron/main/api/timer-api.ts`** - Enhance API functions
6.  **`src/views/TimerView.vue`** - Replace mock data with real API calls
7.  **`src/components/timer/PomodoroTimer.vue`** - Integrate settings and session management

### New Files to Create:
- None (all functionality can be added to existing files)

## Testing Checklist

- [ ] Timer sessions can be started and ended
- [ ] Session data persists in database
- [ ] Statistics are calculated correctly
- [ ] Settings are loaded and saved properly
- [ ] Session history displays real data
- [ ] Error handling works correctly
- [ ] App restart preserves timer state
- [ ] Database migrations work on existing installations

## Conclusion

The Timer functionality has a solid backend foundation but requires frontend integration to become fully functional. The implementation should be done in phases, starting with the API bridge and basic functionality, then expanding to advanced features. The existing backend API is well-designed and should handle most requirements with minimal modifications.

The key insight is that most of the heavy lifting is already done in the backend - the main task is connecting the frontend components to use the real API instead of mock data, and enhancing the backend slightly to support the additional fields (focus, category) that the frontend expects.
```

---

## Section 2: Architectural Review and Enhancement Proposal

### 2.1 Introduction to the Review

This section provides an in-depth architectural review of the original "Timer Backend Implementation Plan" (hereafter referred to as the "Original Plan"). It verifies the claims made, identifies strengths and weaknesses, and proposes enhancements. The primary focus of these enhancements is to ensure the backend is robust, scalable, and adequately prepared for the future integration of a timer statistics dashboard, which will require flexible data querying and visually rich chart displays. This review also includes a recommendation for a TypeScript charting library suitable for this dashboard.

The overall process for this review and enhancement plan can be visualized as follows:

```mermaid
graph TD
    A[Start: Analyze User Request & Original Plan] --> B{Information Gathering & Verification};
    B --> B1[Review Original Plan Document];
    B --> B2[Review Vue Components (e.g., TimerView.vue)];
    B --> B3[Review Backend API (timer-api.ts)];
    B --> B4[Review Database Schema (database.ts)];
    B --> B5[Review IPC Handlers (ipc-handlers.ts)];
    B --> B6[Review Preload Bridge (api-bridge.ts)];
    B --> B7[Review Type Definitions (electron-api.d.ts)];

    B --> C{Synthesize Findings & Identify Gaps};
    C --> D{Develop Enhanced Plan (This Document)};
    D --> D1[Critique Original Plan];
    D --> D2[Propose DB Schema Modifications];
    D --> D3[Propose API Modifications];
    D --> D4[Recommend Charting Library (ECharts)];
    D --> D5[Outline Enhanced Frontend Integration];
    D --> D6[Define Future-Proofing Strategies];
    D --> F{User Review of Enhanced Plan};
    F -- Approval --> G[Proceed to Implementation Phase];
```

### 2.2 Verification of the Original Plan's Current State Analysis

A thorough review of the codebase was conducted to verify the claims made in the Original Plan's "Current State Analysis."

#### 2.2.1 Verification of "✅ Already Implemented (Backend)"

1.  **Database Tables (`timer_sessions`, `timer_settings`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** The file [`electron/main/database/database.ts`](electron/main/database/database.ts) shows the `CREATE TABLE IF NOT EXISTS timer_sessions` statement (lines 243-251) and `CREATE TABLE IF NOT EXISTS timer_settings` statement (lines 259-268).
    *   **Note:** The Original Plan states `timer_sessions` has "all required fields." While true for basic operation, this review later details necessary additions (`focus`, `category`, `updated_at`) for full feature support and dashboard integration, which the Original Plan also correctly identifies as a Phase 2 enhancement.

2.  **API Layer (Complete timer API in `electron/main/api/timer-api.ts`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** The file [`electron/main/api/timer-api.ts`](electron/main/api/timer-api.ts) contains a comprehensive set of exported functions for session management (`startTimerSession`, `endTimerSession`, `getTimerSession`, `deleteTimerSession`), statistics (`getTimerSessionsByDateRange`, `getTodayTimerSessions`, `getTimerStatsByDateRange`), and settings management (`getTimerSettings`, `updateTimerSettings`, `resetTimerSettings`).

3.  **IPC Handlers (All timer IPC channels registered in `electron/main/ipc-handlers.ts`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** The file [`electron/main/ipc-handlers.ts`](electron/main/ipc-handlers.ts) includes a `registerTimerHandlers` function (lines 464-556) which is called during `initializeIpcHandlers` (line 39). This function registers handlers for all corresponding API functions (e.g., `ipcMain.handle('timer:start', ...)`).

4.  **Database Schema (Tables created in `electron/main/database/database.ts`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** As noted in point 1, the `initDatabase` function in [`electron/main/database/database.ts`](electron/main/database/database.ts) executes the `CREATE TABLE` statements for `timer_sessions` and `timer_settings`.

#### 2.2.2 Verification of "❌ Missing Implementation"

1.  **Frontend API Bridge (Timer API not exposed in `electron/preload/api-bridge.ts`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** A review of [`electron/preload/api-bridge.ts`](electron/preload/api-bridge.ts) shows that while `notes`, `books`, `media`, `folders`, and `recentItems` APIs are exposed under `dbApi`, there is no entry for a `timer` API.

2.  **Type Definitions (Timer interfaces not in `src/types/electron-api.d.ts`):**
    *   **Verification:** Confirmed.
    *   **Evidence:** The file [`src/types/electron-api.d.ts`](src/types/electron-api.d.ts) does not currently contain the `TimerSession`, `TimerStats`, or `TimerAPI` interfaces. Furthermore, the global `Window.db` interface declaration (lines 188-196) is missing a `timer: TimerAPI;` property.

3.  **Frontend Integration (`TimerView.vue` uses mock data instead of real API):**
    *   **Verification:** Confirmed.
    *   **Evidence:** The `setup` function in [`src/views/TimerView.vue`](src/views/TimerView.vue) initializes `completedSessions` (line 111) and `stats` (line 101) with hardcoded mock data. Functions like `startSession` and `endSession` manipulate this local mock data and explicitly state that real implementation would involve database interaction (e.g., line 170: `// In a real implementation, you would save to a database`).

4.  **Session Management (No real session persistence or retrieval from frontend):**
    *   **Verification:** Confirmed.
    *   **Evidence:** This is a direct consequence of the missing API bridge, type definitions for the frontend, and the frontend using mock data. While the backend API (`timer-api.ts`) has the logic for persistence, the frontend cannot yet utilize it.

**Conclusion of Verification:** The Original Plan's "Current State Analysis" is accurate. The backend has foundational elements in place, but the critical links to the frontend are missing, which the Original Plan correctly identifies as the primary work needed for initial functionality.

### 2.3 Critique of the Original Backend Plan

#### 2.3.1 Strengths

*   **Clear Problem Definition:** The plan accurately identifies the core issue: a functional backend for the timer exists, but it's disconnected from the frontend.
*   **Phased Approach:** The proposed phases (API Bridge & Types, Enhanced Backend API, Frontend Integration, Advanced Features) provide a logical and manageable progression for development.
*   **Identification of Necessary Schema Changes:** The plan correctly notes the need to add `focus`, `category`, and `updated_at` columns to the `timer_sessions` table for richer functionality.
*   **File-Specific Actions:** The plan clearly lists which files need modification and outlines the key changes required in each, which is helpful for developers.
*   **Comprehensive Scope for Initial Integration:** The plan covers essential aspects like data flow, error handling, performance considerations (at a high level), testing, and migration.

#### 2.3.2 Weaknesses/Omissions (Primarily in the Context of Future Dashboard Requirements)

*   **Limited Foresight for Dashboard Statistics:** While "Phase 4.1 Session History and Statistics" is mentioned, the Original Plan primarily focuses on getting the current `TimerView.vue` functional. It doesn't delve deep into the specific data structures or API query capabilities that a separate, more advanced statistics dashboard would necessitate. The current `getTimerStatsByDateRange` is good for basic summaries but might be insufficient for complex dashboard visualizations requiring flexible aggregations.
*   **Scalability of Statistical Queries:** The plan mentions performance considerations like lazy loading but doesn't explicitly address how statistical queries on potentially large historical datasets will be optimized beyond basic API calls. Database indexing for statistics-heavy columns is not explicitly detailed.
*   **Charting Library Consideration:** This was outside the scope of the Original Plan but is a critical component of the current, broader task.
*   **API Design for Advanced Aggregation:** The existing API endpoints are suitable for their current purpose. However, for a dashboard that might need to slice and dice data in many ways (e.g., group by day/week/month/category, sum durations, count sessions per focus type), a more flexible aggregation API endpoint might be beneficial in the long run to avoid a proliferation of highly specific endpoints.

This enhanced plan aims to address these omissions by explicitly planning for dashboard integration from the database schema up through API design and frontend library selection.

### 2.4 Database Schema Enhancements for Dashboard Integration and Future-Proofing

The existing database schema for timer functionality is defined in [`electron/main/database/database.ts`](electron/main/database/database.ts). We will build upon this foundation.

#### 2.4.1 Recap of Original Timer-Related Schema (as per `database.ts`)

*   **`timer_sessions` table (lines 243-251):**
    *   `id INTEGER PRIMARY KEY AUTOINCREMENT`
    *   `start_time TIMESTAMP NOT NULL`
    *   `end_time TIMESTAMP`
    *   `duration INTEGER` (in seconds)
    *   `session_type TEXT` (e.g., 'work', 'break')
    *   `is_completed BOOLEAN DEFAULT 0`
    *   `created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`

*   **`timer_settings` table (lines 259-268):**
    *   `id INTEGER PRIMARY KEY AUTOINCREMENT`
    *   `work_duration INTEGER NOT NULL DEFAULT 1500`
    *   `short_break_duration INTEGER NOT NULL DEFAULT 300`
    *   `long_break_duration INTEGER NOT NULL DEFAULT 900`
    *   `long_break_interval INTEGER NOT NULL DEFAULT 4`
    *   `auto_start_breaks BOOLEAN DEFAULT 0`
    *   `auto_start_work BOOLEAN DEFAULT 0`
    *   `updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP` (Note: Original Plan's `TimerSettings` interface includes `created_at`, but the table schema in `database.ts` for `timer_settings` only has `updated_at`. This is a minor discrepancy; `updated_at` is sufficient for settings.)

#### 2.4.2 Implementing Original Plan's Schema Enhancements for `timer_sessions`

The Original Plan correctly identifies the need to add `focus`, `category`, and `updated_at` columns to the `timer_sessions` table. These are crucial for richer frontend features and provide essential dimensions for dashboard filtering and analysis.

**File to Modify:** [`electron/main/database/database.ts`](electron/main/database/database.ts) (within the `initDatabase` function, after the initial `CREATE TABLE timer_sessions` statement, or as part of a more structured migration system if one is developed later).

**Action:** Add `ALTER TABLE` statements with error handling to add the new columns if they don't already exist. This ensures idempotency for existing installations.

```typescript
// Inside initDatabase, after CREATE TABLE timer_sessions:

// Add 'focus' column to timer_sessions
db.run(`ALTER TABLE timer_sessions ADD COLUMN focus TEXT`, (err: Error | null) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Warning: Could not add focus column to timer_sessions:', err.message);
  } else if (!err) {
    console.log('Column "focus" added to timer_sessions or already exists.');
  }
});

// Add 'category' column to timer_sessions
db.run(`ALTER TABLE timer_sessions ADD COLUMN category TEXT`, (err: Error | null) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Warning: Could not add category column to timer_sessions:', err.message);
  } else if (!err) {
    console.log('Column "category" added to timer_sessions or already exists.');
  }
});

// Add 'updated_at' column to timer_sessions
// The Original Plan's TimerSession interface includes created_at and updated_at.
// The table schema has created_at. Adding updated_at for completeness.
db.run(`ALTER TABLE timer_sessions ADD COLUMN updated_at TIMESTAMP`, (err: Error | null) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.warn('Warning: Could not add updated_at column to timer_sessions:', err.message);
  } else if (!err) {
    console.log('Column "updated_at" added to timer_sessions or already exists.');
    // Optionally, populate existing rows' updated_at with created_at or current time if needed
    // db.run(`UPDATE timer_sessions SET updated_at = created_at WHERE updated_at IS NULL`);
  }
});
```

**Justification:**
*   `focus TEXT`: Allows users to describe what they worked on, providing valuable context for session history and dashboard analysis (e.g., "Time spent on 'Project X feature Y'").
*   `category TEXT`: Enables users to categorize sessions (e.g., "Work," "Study," "Personal Project"), which is a key dimension for filtering and aggregating statistics on the dashboard.
*   `updated_at TIMESTAMP`: Standard practice for tracking when a session record was last modified. Useful for auditing and potentially for incremental data synchronization scenarios in the future. The `endTimerSession` API should be updated to set this field.

#### 2.4.3 Recommended Indexing Strategy for `timer_sessions`

To ensure efficient querying for the dashboard and general statistics, especially as the number of sessions grows, appropriate database indexes are crucial.

**File to Modify:** [`electron/main/database/database.ts`](electron/main/database/database.ts) (within the `initDatabase` function, after table creations and alterations).

**Action:** Add `CREATE INDEX IF NOT EXISTS` statements.

```typescript
// Inside initDatabase, after table creations/alterations:

db.run('CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_time ON timer_sessions (start_time DESC)', (err: Error | null) => {
  if (err) console.error('Error creating index idx_timer_sessions_start_time:', err.message);
});

db.run('CREATE INDEX IF NOT EXISTS idx_timer_sessions_session_type ON timer_sessions (session_type)', (err: Error | null) => {
  if (err) console.error('Error creating index idx_timer_sessions_session_type:', err.message);
});

db.run('CREATE INDEX IF NOT EXISTS idx_timer_sessions_category ON timer_sessions (category)', (err: Error | null) => {
  if (err) console.error('Error creating index idx_timer_sessions_category:', err.message);
});

db.run('CREATE INDEX IF NOT EXISTS idx_timer_sessions_is_completed ON timer_sessions (is_completed)', (err: Error | null) => {
  if (err) console.error('Error creating index idx_timer_sessions_is_completed:', err.message);
});

// Composite index if frequently querying by date range and completion status
db.run('CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_completed ON timer_sessions (start_time DESC, is_completed)', (err: Error | null) => {
  if (err) console.error('Error creating index idx_timer_sessions_start_completed:', err.message);
});
```

**Justification:**
*   `idx_timer_sessions_start_time`: Essential for filtering sessions by date ranges (e.g., "last 7 days," "this month") and for ordering session history. `DESC` is often useful for recent items.
*   `idx_timer_sessions_session_type`: Speeds up queries that filter or group by session type (e.g., "total work time" vs. "total break time").
*   `idx_timer_sessions_category`: Critical for dashboard views that allow filtering or segmenting data by user-defined categories.
*   `idx_timer_sessions_is_completed`: Useful for quickly finding active or completed sessions.
*   `idx_timer_sessions_start_completed`: A composite index beneficial for queries that filter by both date and completion status, common in statistics.

#### 2.4.4 Optional Future Consideration: `project_id`

As per user feedback to focus on raw data flexibility for now, this is a low-priority item but worth noting for long-term vision.
*   **Column:** `project_id INTEGER NULLABLE, FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL` (assuming a `projects` table might exist or be created in the future).
*   **Justification:** If the application evolves to include a concept of "projects" or overarching tasks, linking timer sessions to these projects would enable powerful project-based time tracking and reporting on the dashboard.
*   **Current Stance:** Defer implementation unless a clear need arises. The current `category` and `focus` fields offer good flexibility for now.

### 2.5 API Enhancements for Dashboard Integration and Raw Data Accessibility

Modifications and considerations for [`electron/main/api/timer-api.ts`](electron/main/api/timer-api.ts) to support enhanced data capture and flexible retrieval for the dashboard.

#### 2.5.1 Modify `startTimerSession` (as per Original Plan, with justification)

**Action:** Update the function signature and `INSERT` statement to include `focus` and `category`.

```typescript
// In electron/main/api/timer-api.ts
export const startTimerSession = async (
  sessionType: string = 'work',
  focus?: string, // New parameter
  category?: string // New parameter
): Promise<TimerSession> => { // Ensure TimerSession interface here matches the one in types/electron-api.d.ts
  if (typeof sessionType !== 'string' || sessionType.trim() === '') {
    throw new Error('Session type must be a non-empty string.');
  }
  // Add validation for focus and category if necessary (e.g., length limits)
  try {
    const startTime = new Date().toISOString();
    const result = await dbRun(
      'INSERT INTO timer_sessions (start_time, session_type, focus, category, is_completed, created_at, updated_at) VALUES (?, ?, ?, ?, 0, ?, ?)',
      [startTime, sessionType, focus || null, category || null, startTime, startTime] // updated_at initially same as created_at
    );

    const newSession = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [result.id]);
    if (!newSession) {
      throw new Error('Failed to retrieve the timer session after creation.');
    }
    return newSession; // This newSession should include focus and category
  } catch (error: any) {
    console.error('Error starting timer session:', error);
    throw new Error(`Failed to start timer session: ${error.message}`);
  }
};
```

**Justification:** Capturing `focus` and `category` at session start enriches the dataset significantly, allowing for more detailed and meaningful statistics on the dashboard (e.g., time spent per category, common focus areas).

#### 2.5.2 Modify `endTimerSession`

**Action:** Ensure `updated_at` is set when a session is ended or modified. The Original Plan's SQL for `endTimerSession` (lines 123-130 of the plan document) already includes `updated_at = CURRENT_TIMESTAMP`, which is correct.

```typescript
// In electron/main/api/timer-api.ts (conceptual, ensure it matches actual implementation)
export const endTimerSession = async (sessionId: number): Promise<TimerSession> => {
    // ... (validation)
    try {
        const endTime = new Date().toISOString(); // Use consistent ISO string
        // Fetch start_time to calculate duration in JS for precision, if SQLite's julianday is problematic
        const existingSession = await dbGet<TimerSession>('SELECT start_time FROM timer_sessions WHERE id = ? AND is_completed = 0', [sessionId]);
        if (!existingSession) {
             // Handle session not found or already completed
             const currentSession = await getTimerSession(sessionId);
             if (!currentSession) throw new Error(`Timer session with ID ${sessionId} not found.`);
             if (currentSession.is_completed === 1) {
                 console.warn(`Timer session ${sessionId} was already completed.`);
                 return currentSession;
             }
             throw new Error(`Failed to end timer session ${sessionId}. Session not active.`);
        }
        
        const durationInSeconds = Math.round((new Date(endTime).getTime() - new Date(existingSession.start_time).getTime()) / 1000);

        const result = await dbRun(`
      UPDATE timer_sessions
      SET
        end_time = ?, 
        duration = ?,
        is_completed = 1,
        updated_at = ?
      WHERE id = ? AND is_completed = 0
    `, [endTime, durationInSeconds, endTime, sessionId]); // Use endTime for updated_at as well

        // ... (rest of the logic as in the existing timer-api.ts)
        if (result.changes === 0) {
            // This case should be largely covered by the pre-check, but as a fallback:
            const finalCheckSession = await getTimerSession(sessionId);
            if (!finalCheckSession) throw new Error(`Timer session with ID ${sessionId} not found after attempting to end.`);
            // If it was already completed, return it.
            if (finalCheckSession.is_completed === 1) return finalCheckSession;
            throw new Error(`Failed to end timer session ${sessionId}. No rows updated.`);
        }
        return await getTimerSession(sessionId);
    } catch (error: any) {
        console.error(`Error ending timer session ${sessionId}:`, error);
        throw new Error(`Failed to end timer session ${sessionId}: ${error.message}`);
    }
};
```
**Justification:** Keeps track of the last modification to the session, which is standard practice and useful for data integrity.

#### 2.5.3 Ensuring Raw Data Accessibility for Dashboard

The primary goal for now is to ensure all raw data can be queried flexibly.
*   **`getTimerSessionsByDateRange(startDate: string, endDate: string, category?: string, sessionType?: string)`:**
    *   **Action:** Enhance this existing function to optionally accept `category` and `sessionType` for filtering. Ensure it returns all fields, including `id`, `start_time`, `end_time`, `duration`, `session_type`, `is_completed`, `created_at`, `updated_at`, `focus`, and `category`.
    *   **SQL Modification:** The `WHERE` clause would need to be built dynamically to include optional filters.
    ```typescript
    // Conceptual update in electron/main/api/timer-api.ts
    export const getTimerSessionsByDateRange = async (
        startDate: string, 
        endDate: string, 
        filters?: { category?: string; sessionType?: string; focus?: string /* for text search */ }
    ): Promise<TimerSession[]> => {
        // ... (date validation)
        try {
            let query = 'SELECT * FROM timer_sessions WHERE date(start_time) BETWEEN date(?) AND date(?)';
            const params: any[] = [startDate, endDate];

            if (filters?.category) {
                query += ' AND category = ?';
                params.push(filters.category);
            }
            if (filters?.sessionType) {
                query += ' AND session_type = ?';
                params.push(filters.sessionType);
            }
            if (filters?.focus) { // Basic text search on focus
                query += ' AND focus LIKE ?';
                params.push(`%${filters.focus}%`);
            }
            query += ' ORDER BY start_time DESC';
            
            return await dbAll<TimerSession>(query, params);
        } catch (error: any) {
            // ... (error handling)
            throw error;
        }
    };
    ```
    *   **Justification:** Provides the frontend/dashboard with comprehensive raw data for client-side aggregation or for passing to charting libraries. Optional server-side filtering improves efficiency by reducing data transfer.

*   **`getTimerStatsByDateRange(startDate: string, endDate: string, category?: string)`:**
    *   **Action:** Similarly, enhance this to optionally filter by `category`.
    *   **SQL Modification:** The `WHERE` clause in the aggregate query would need to include the optional category filter.
    ```typescript
    // Conceptual update in electron/main/api/timer-api.ts
    export const getTimerStatsByDateRange = async (
        startDate: string, 
        endDate: string, 
        category?: string
    ): Promise<TimerStats> => {
        // ... (date validation)
        try {
            let query = `
              SELECT
                COUNT(*) as total_sessions,
                COALESCE(SUM(duration), 0) as total_duration,
                COUNT(CASE WHEN session_type = 'work' THEN 1 END) as work_sessions,
                COALESCE(SUM(CASE WHEN session_type = 'work' THEN duration ELSE 0 END), 0) as work_duration,
                COUNT(CASE WHEN session_type = 'break' THEN 1 END) as break_sessions,
                COALESCE(SUM(CASE WHEN session_type = 'break' THEN duration ELSE 0 END), 0) as break_duration
              FROM timer_sessions
              WHERE date(start_time) BETWEEN date(?) AND date(?) AND is_completed = 1`;
            const params: any[] = [startDate, endDate];

            if (category) {
                query += ' AND category = ?';
                params.push(category);
            }
            // The original query is fine, just add the category filter.
            const stats = await dbGet<TimerStats>(query, params);
            // ... (rest of the logic)
            return stats || { total_sessions: 0, total_duration: 0, work_sessions: 0, work_duration: 0, break_sessions: 0, break_duration: 0 };
        } catch (error: any) {
            // ... (error handling)
            throw error;
        }
    };
    ```
    *   **Justification:** Allows for more targeted summary statistics directly from the backend.

#### 2.5.4 Future API Endpoint Design for Advanced Statistics (`getAggregatedTimerData` - Conceptual)

While not for immediate implementation, designing with this future possibility in mind ensures the backend can adapt.

*   **Conceptual Signature:**
    `getAggregatedTimerData(options: AggregationOptions): Promise<AggregatedResult[]>`
*   **`AggregationOptions` Interface:**
    ```typescript
    interface AggregationOptions {
      date_from: string;
      date_to: string;
      time_unit?: 'hour' | 'day' | 'week' | 'month' | 'year'; // For grouping by time periods
      group_by_fields?: Array<'session_type' | 'category' | 'focus' /* potentially others */>;
      aggregations: Array<{
        field: 'duration' | 'id' /* or other numeric fields */;
        func: 'SUM' | 'COUNT' | 'AVG' | 'MIN' | 'MAX';
        as: string; // Alias for the result column
      }>;
      filters?: Array<{
        field: keyof TimerSession;
        operator: '=' | '!=' | '>' | '<' | 'LIKE' | 'IN';
        value: any;
      }>;
      order_by?: Array<{ field: string; direction: 'ASC' | 'DESC' }>;
      limit?: number;
      offset?: number;
    }
    ```
*   **Functionality:** This endpoint would dynamically build complex SQL queries based on the `AggregationOptions` to return highly customized aggregated data. This would involve significant dynamic SQL generation and careful validation to prevent SQL injection if string components are directly used (parameterization is key).
*   **Justification:** Provides ultimate flexibility for diverse and evolving dashboard requirements, minimizing the need to create numerous specific aggregation endpoints later. This is a common pattern in systems with rich analytical dashboards.
*   **Current Action:** No implementation now. The focus is on robust raw data storage and basic retrieval/aggregation. This concept informs the importance of good indexing and comprehensive raw data.

### 2.6 TypeScript Charting Library Recommendation for Dashboard

Based on the requirement for "really beautiful," interactive charts, strong Vue.js integration, and excellent TypeScript support, the following recommendation is made:

#### 2.6.1 Evaluation Criteria Recap
1.  **Vue.js Integration Efficiency**
2.  **Chart Type Variety**
3.  **Customization Capabilities**
4.  **Rendering Performance**
5.  **Visual Appeal ("Really Beautiful")**
6.  **Interactivity**
7.  **Documentation Quality**
8.  **Community Support & Maintenance**
9.  **TypeScript Support**
10. **Bundle Size**

#### 2.6.2 Top Recommendation: Apache ECharts (with `vue-echarts` wrapper)

*   **Website:** [https://echarts.apache.org/](https://echarts.apache.org/)
*   **Vue Wrapper:** `vue-echarts` ([https://github.com/ecomfe/vue-echarts](https://github.com/ecomfe/vue-echarts)) - typically uses `echarts` as a peer dependency.

*   **Justification:**
    *   **Visual Appeal & Customization:** ECharts is renowned for its ability to produce visually stunning and highly customizable charts. It offers a declarative way to define charts with extensive options for styling, themes, animations, and graphic elements, allowing for truly "beautiful" outputs.
    *   **Chart Type Variety:** Provides an incredibly rich set of chart types out-of-the-box, from common ones (line, bar, pie, scatter, doughnut) to more complex ones (candlestick, heatmap, graph, treemap, sunburst, funnel, gauge) and even 3D charts. This variety is excellent for diverse dashboard needs.
    *   **Interactivity:** Offers rich interactive features like tooltips, data zooming (`dataZoom`), legends, brushing for selection, and connecting multiple charts for coordinated interactions.
    *   **Rendering Power:** Supports both Canvas (default, for performance with large datasets) and SVG rendering (for better scalability and DOM interaction if needed), providing flexibility.
    *   **TypeScript Support:** ECharts has excellent first-class TypeScript support. Its option structures are well-typed, which greatly aids development in a TypeScript project.
    *   **Vue.js Integration:** `vue-echarts` is a popular and well-maintained wrapper that simplifies the integration of ECharts into Vue applications, including Vue 3 with the Composition API.
    *   **Documentation & Community:** Apache ECharts has comprehensive documentation (though it can be dense due to the library's richness) and a large, active community, particularly strong in Asia and growing globally.
    *   **Data Handling:** Efficiently handles large datasets and provides various ways to load and manage data.
    *   **Modularity:** ECharts is modular, allowing you to import only the charts and components you need, which can help manage bundle size.

*   **Considerations:**
    *   **Learning Curve:** Due to its power and extensive API, ECharts can have a steeper learning curve compared to simpler libraries.
    *   **Bundle Size:** While modular, the core can still be larger than very lightweight libraries. However, for a feature-rich dashboard, this is often an acceptable trade-off.

#### 2.6.3 Runner-Up: ApexCharts (with `vue3-apexcharts` wrapper)

*   **Website:** [https://apexcharts.com/](https://apexcharts.com/)
*   **Vue Wrapper:** `vue3-apexcharts` ([https://github.com/apexcharts/vue3-apexcharts](https://github.com/apexcharts/vue3-apexcharts))

*   **Justification as Runner-Up:**
    *   **Excellent Default Aesthetics:** ApexCharts often produces very modern and visually appealing charts with less configuration than ECharts might require for similar polish initially.
    *   **Good Interactivity & Vue 3 Support:** Offers good interactive features and has a dedicated Vue 3 wrapper.
    *   **SVG-Based:** Renders charts as SVG, which can be beneficial for crispness on high-DPI displays and easier CSS styling.
    *   **Simpler API (Potentially):** For common chart types, its API might feel more straightforward to get started with compared to ECharts.

*   **Why ECharts is Preferred for this Scenario:** Given the emphasis on "really beautiful" and the potential for diverse statistical visualizations on a dashboard, ECharts' superior customization depth, broader range of advanced chart types, and powerful data processing capabilities give it an edge for a comprehensive, future-proof dashboard solution. The user's positive inclination towards ECharts after looking into it also supports this choice.

#### 2.6.4 Brief Mention: Chart.js (with `vue-chartjs`)
*   A solid, popular library, especially for simpler charting needs. It's lightweight and has a large community. However, achieving the "really beautiful" aesthetic often requires more manual customization, and its feature set is less extensive than ECharts or ApexCharts for complex dashboard scenarios.

#### 2.6.5 Integration Notes for ECharts:
*   Install `echarts` and `vue-echarts`.
*   Import necessary ECharts modules (core, chart types, components like tooltip, legend, grid) to keep bundle size optimized.
    ```typescript
    import * as echarts from 'echarts/core';
    import { BarChart, LineChart, PieChart } from 'echarts/charts';
    import { TitleComponent, TooltipComponent, GridComponent, LegendComponent } from 'echarts/components';
    import { CanvasRenderer } from 'echarts/renderers';
    import VChart from 'vue-echarts';

    echarts.use([
      TitleComponent, TooltipComponent, GridComponent, LegendComponent,
      BarChart, LineChart, PieChart, CanvasRenderer
    ]);
    
    // In Vue component:
    // app.component('v-chart', VChart);
    ```
*   Utilize Vue's reactivity to update chart options dynamically.

### 2.7 Frontend Integration Plan (Enhanced)

This section refines the Original Plan's phases, integrating the new considerations for dashboard preparation and the ECharts recommendation.

#### Phase 1: API Bridge & Type Definitions (Critical First Step)

*   **Objective:** Enable frontend-backend communication for timer features.
*   **Files & Actions:**
    1.  **`src/types/electron-api.d.ts`:**
        *   Add `TimerSession`, `TimerStats`, and `TimerAPI` interfaces as defined in the Original Plan (Section 1.1). Ensure `TimerSession` includes `focus: string | null` and `category: string | null` (make them nullable if they can be empty, or non-nullable if always required from frontend).
        *   Add `timer: TimerAPI;` to the `Window.db` interface (Original Plan Section 1.2).
    2.  **`electron/preload/api-bridge.ts`:**
        *   Add the `timer` API object to `dbApi`, mapping frontend calls to `ipcRenderer.invoke` for each timer IPC channel (Original Plan Section 1.3). Ensure parameter types match the `TimerAPI` interface (e.g., `start` should accept optional `focus` and `category`).
        ```typescript
        // In electron/preload/api-bridge.ts, within dbApi:
        timer: {
          start: (sessionType?: string, focus?: string, category?: string) => 
            ipcRenderer.invoke('timer:start', sessionType, focus, category),
          end: (sessionId: number) => ipcRenderer.invoke('timer:end', sessionId),
          getSession: (sessionId: number) => ipcRenderer.invoke('timer:getSession', sessionId),
          getSessionsByDateRange: (
            startDate: string, 
            endDate: string, 
            filters?: { category?: string; sessionType?: string; focus?: string } // Updated signature
          ) => ipcRenderer.invoke('timer:getSessionsByDateRange', startDate, endDate, filters),
          getTodaySessions: () => ipcRenderer.invoke('timer:getTodaySessions'), // Potentially enhance with filters too
          getStatsByDateRange: (
            startDate: string, 
            endDate: string, 
            category?: string // Updated signature
          ) => ipcRenderer.invoke('timer:getStatsByDateRange', startDate, endDate, category),
          deleteSession: (sessionId: number) => ipcRenderer.invoke('timer:deleteSession', sessionId),
          getSettings: () => ipcRenderer.invoke('timer:getSettings'),
          updateSettings: (settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>) => 
            ipcRenderer.invoke('timer:updateSettings', settingsUpdates),
          resetSettings: () => ipcRenderer.invoke('timer:resetSettings')
        }
        ```
    3.  **`src/useElectronAPI.ts`:**
        *   Add `TimerAPI` to the `ElectronAPI` interface (Original Plan Section 1.4).
*   **Justification:** Unblocks all subsequent frontend work by establishing the communication channel and type safety.

#### Phase 2: Backend API and Database Enhancements (Data Foundation)

*   **Objective:** Enrich the data model and ensure backend support for new fields and efficient querying.
*   **Files & Actions:**
    1.  **`electron/main/database/database.ts`:**
        *   Implement schema migrations: Add `focus TEXT`, `category TEXT`, `updated_at TIMESTAMP` columns to `timer_sessions` (as detailed in Section 2.4.2).
        *   Implement indexing strategy for `timer_sessions` (as detailed in Section 2.4.3).
    2.  **`electron/main/api/timer-api.ts`:**
        *   Update `startTimerSession` to accept and store `focus` and `category` (as detailed in Section 2.5.1).
        *   Ensure `endTimerSession` correctly calculates duration and sets `updated_at` (as detailed in Section 2.5.2).
        *   Enhance `getTimerSessionsByDateRange` and `getTimerStatsByDateRange` to support optional filtering by new fields like `category` (as detailed in Section 2.5.3).
    3.  **`electron/main/ipc-handlers.ts`:**
        *   Update the corresponding IPC handlers (e.g., `timer:start`, `timer:getSessionsByDateRange`, `timer:getStatsByDateRange`) to pass through the new parameters (focus, category, filters) to the `timer-api.ts` functions.
*   **Justification:** Provides the necessary data richness and backend support for both the immediate `TimerView.vue` enhancements and the future dashboard.

#### Phase 3: Core Frontend Integration (Timer Functionality)

*   **Objective:** Connect existing Timer views and components to the live backend API.
*   **Files & Actions (as per Original Plan, now using real, enhanced API):**
    1.  **`src/views/TimerView.vue`:**
        *   Remove all mock data for sessions and stats.
        *   Implement `loadSessions` using `db.timer.getSessionsByDateRange` (fetching all new fields).
        *   Implement `loadStats` using `db.timer.getStatsByDateRange`.
        *   Modify `startSession` to call `db.timer.start(sessionType, focus, category)`.
        *   Modify `endSession` to call `db.timer.end(sessionId)`.
        *   Implement robust error handling for API calls.
        *   Ensure data transformation from backend `TimerSession` to frontend display format is correct.
    2.  **`src/components/timer/PomodoroTimer.vue`:**
        *   Integrate `db.timer.getSettings()` on mount and `db.timer.updateSettings()` when settings are changed by the user.
        *   Trigger `startSession` and `endSession` (from `TimerView.vue` via props/events or direct API calls if refactored) at appropriate lifecycle points of the pomodoro.
        *   Handle persistence of active timer state across app restarts (more complex, might involve storing active session ID and re-fetching on load).
    3.  **`src/components/modals/AddSessionModal.vue`:**
        *   Ensure it collects `focus` and `category` from the user.
        *   When "Start Session" is clicked, emit data to `TimerView.vue` to call the backend `startSession` function.
*   **Justification:** Makes the core timer functionality live and data-persistent.

#### Phase 4: Advanced Features & Initial Dashboard Setup

*   **Objective:** Implement advanced timer features and lay the groundwork for the statistics dashboard.
*   **Files & Actions:**
    1.  **Session History and Statistics (in `TimerView.vue` or new components):**
        *   Enhance display of session history using the richer data (focus, category).
        *   Implement date range filtering for session history and stats displayed in `TimerView.vue`.
        *   Consider basic search/filter functionality for session history within `TimerView.vue`.
    2.  **Settings Persistence & Validation:** Fully implement as per Original Plan.
    3.  **Session State Management (Robustness):** Fully implement as per Original Plan.
    4.  **Initial Dashboard Component Setup (`src/views/DashboardView.vue` or new dedicated components):**
        *   Create placeholder components for where charts will reside.
        *   Set up data fetching logic (using enhanced `getTimerSessionsByDateRange` or `getTimerStatsByDateRange`) to retrieve data needed for initial dashboard charts.
        *   Integrate **ECharts** (via `vue-echarts`):
            *   Install dependencies: `npm install echarts vue-echarts` or `yarn add echarts vue-echarts`.
            *   Perform basic ECharts setup (importing modules, registering the component).
            *   Implement 1-2 simple proof-of-concept charts (e.g., a bar chart of total work duration per day for the last week, a pie chart of session types) using fetched data to validate the ECharts integration and data flow.
*   **Justification:** Builds upon core functionality, improves user experience, and takes concrete steps towards realizing the statistics dashboard.

### 2.8 Technical Considerations (Expanded)

*   **Data Flow Architecture:** The diagram from the Original Plan remains valid.
*   **Error Handling Strategy:** Crucial at all layers.
    *   **Frontend:** User-friendly notifications for API errors. Use `try...catch` around all `ipcRenderer.invoke` calls.
    *   **Preload/IPC:** Ensure errors from main process are correctly propagated to renderer.
    *   **Backend API (`timer-api.ts`):** Specific error messages, console logging.
    *   **Database:** SQLite errors should be caught and translated into meaningful application errors. Transactions should be used for operations involving multiple writes to ensure atomicity (e.g., if creating a session involved writing to multiple tables).
*   **Performance Considerations:**
    *   **Database Indexing:** Already covered in Section 2.4.3. This is paramount for dashboard performance.
    *   **API Query Optimization:** Ensure backend API queries are efficient. Avoid N+1 query problems.
    *   **Lazy Loading:** For session history in `TimerView.vue` and potentially for some dashboard views if datasets become very large (implement pagination or infinite scroll).
    *   **Data Transfer:** Only fetch necessary data. For the dashboard, raw data is needed initially, but future aggregated endpoints will reduce transfer.
    *   **Frontend Rendering:** Efficiently render lists and charts. Virtual scrolling for very long lists of sessions. ECharts is generally performant but ensure chart options are optimized.
    *   **Debouncing/Throttling:** For frequent updates like settings changes or live search queries.
*   **Data Validation:** Implement validation at API boundaries (IPC handlers or start of API functions) for incoming data (e.g., session types, durations, IDs).

### 2.9 Implementation Priority (Reviewed for Dashboard Context)

Priorities from the Original Plan are generally sound. Enhancements for dashboard data (DB schema, API changes for focus/category) should be part of "Medium Priority" or integrated early if feasible, as they are foundational.

1.  **High Priority (Core Functionality & Basic Data):**
    *   Phase 1: API Bridge & Type Definitions.
    *   Phase 2: Backend API and Database Enhancements (specifically adding `focus`, `category`, `updated_at`, basic indexing, and updating `startTimerSession`).
    *   Phase 3: Core Frontend Integration (connecting `TimerView.vue`, `PomodoroTimer.vue`, `AddSessionModal.vue` to live API for basic operations).
    *   Settings Persistence (Original Plan Phase 4.2).

2.  **Medium Priority (Enhanced UX & Dashboard Foundation):**
    *   Advanced Database Indexing (Section 2.4.3).
    *   Enhanced API filtering capabilities (Section 2.5.3).
    *   Session History and Statistics in `TimerView.vue` (using real, enriched data).
    *   Advanced Session Management (Original Plan Phase 4.3).
    *   Initial Dashboard Component Setup with 1-2 basic ECharts (Phase 4, new item).

3.  **Low Priority (Nice to Have / Future Dashboard Iterations):**
    *   Export functionality.
    *   Advanced analytics/complex ECharts visualizations beyond initial setup.
    *   Conceptual `getAggregatedTimerData` API endpoint.
    *   Session templates, Notification system.

### 2.10 Testing Strategy (Expanded)

*   **Unit Tests:**
    *   `timer-api.ts` functions: Test all CRUD operations, statistics calculations, settings logic, including edge cases and validation for new fields (`focus`, `category`) and filters.
    *   Database interaction logic: Mock `sqlite3` or use an in-memory SQLite for testing database queries.
    *   Helper functions (e.g., duration formatting).
*   **Integration Tests:**
    *   IPC Handler -> API -> Database: Test the full flow for each timer operation.
    *   Frontend API Bridge (`useElectronAPI` calls) -> IPC Handler.
    *   Data integrity after migrations.
*   **End-to-End (E2E) Tests (using tools like Playwright or Spectron for Electron):**
    *   Simulate user workflows: Creating a session with focus/category, completing it, viewing it in history, checking stats in `TimerView.vue`.
    *   Testing settings changes and their effect on the timer.
    *   Basic dashboard data loading and chart rendering (once implemented).
*   **User Acceptance Tests (UAT):** As per Original Plan, expanded to include:
    *   Verification of dashboard data accuracy and chart usability.
    *   Correct filtering by category/focus on dashboard.

### 2.11 Migration Strategy (Focus on `timer_sessions` changes)

The `ALTER TABLE` statements with `IF NOT EXISTS` (implicitly handled by SQLite's `ALTER TABLE ADD COLUMN` not failing if column exists, but explicit checks in code are better) in `electron/main/database/database.ts` (Section 2.4.2) will handle adding `focus`, `category`, and `updated_at` to existing `timer_sessions` tables.
*   **Data Backfill (Optional):** For `updated_at`, consider if existing rows need this field backfilled (e.g., set to `created_at` or `end_time` if available).
    ```typescript
    // Potentially run once after adding updated_at column
    // db.run(`UPDATE timer_sessions SET updated_at = COALESCE(end_time, start_time) WHERE updated_at IS NULL`);
    ```
*   Schema versioning could be introduced if migrations become more complex in the future (e.g., a `schema_version` table). For now, the additive changes are manageable.

### 2.12 Detailed Implementation Steps (Consolidated & Enhanced Roadmap)

1.  **Step 1: Backend Foundation - Database & Core API**
    *   **Modify `electron/main/database/database.ts`:**
        *   Add `ALTER TABLE` statements for `timer_sessions` to include `focus TEXT`, `category TEXT`, `updated_at TIMESTAMP` (Section 2.4.2).
        *   Add `CREATE INDEX` statements for `timer_sessions` (Section 2.4.3).
    *   **Modify `electron/main/api/timer-api.ts`:**
        *   Update `startTimerSession` to accept and store `focus` and `category`.
        *   Verify/update `endTimerSession` to correctly set `duration` and `updated_at`.
        *   Implement basic filtering enhancements for `getTimerSessionsByDateRange` and `getTimerStatsByDateRange` (e.g., by category).
    *   **Modify `electron/main/ipc-handlers.ts`:**
        *   Update IPC handlers for `timer:start`, `timer:getSessionsByDateRange`, `timer:getStatsByDateRange` to accommodate new parameters/filters.

2.  **Step 2: Frontend Connectivity - API Bridge & Types**
    *   **Modify `src/types/electron-api.d.ts`:**
        *   Define `TimerSession` (with `focus`, `category`), `TimerStats`, `TimerAPI` interfaces.
        *   Add `timer: TimerAPI` to `Window.db`.
    *   **Modify `electron/preload/api-bridge.ts`:**
        *   Expose the `timer` API, mapping all `TimerAPI` functions (with updated signatures) to their respective IPC channels.
    *   **Modify `src/useElectronAPI.ts`:**
        *   Add `TimerAPI` to the `ElectronAPI` interface.

3.  **Step 3: Core Timer Frontend Integration**
    *   **Modify `src/components/modals/AddSessionModal.vue`:**
        *   Add input fields for `focus` and `category`.
        *   Emit these values when a session is to be started.
    *   **Modify `src/views/TimerView.vue`:**
        *   Refactor `startSession` to call `window.db.timer.start(sessionType, focus, category)`.
        *   Refactor `endSession` to call `window.db.timer.end(sessionId)`.
        *   Implement `loadSessions` using `window.db.timer.getSessionsByDateRange(...)`, displaying new `focus` and `category` fields.
        *   Implement `loadStats` using `window.db.timer.getStatsByDateRange(...)`.
        *   Remove all mock data. Implement error handling.
    *   **Modify `src/components/timer/PomodoroTimer.vue`:**
        *   Integrate `window.db.timer.getSettings()` and `window.db.timer.updateSettings()`.
        *   Ensure it correctly interacts with `TimerView.vue` or the API for starting/ending sessions.

4.  **Step 4: Testing & Refinement (Core Functionality)**
    *   Conduct thorough unit, integration, and E2E tests for all implemented timer features.
    *   Refine UI/UX based on testing.

5.  **Step 5: Initial Dashboard Setup & ECharts Integration**
    *   **Create/Modify `src/views/DashboardView.vue` (or new components):**
        *   Design a basic layout for displaying timer statistics.
        *   Implement data fetching logic using the enhanced timer API functions.
    *   **Integrate ECharts:**
        *   Install `echarts` and `vue-echarts`.
        *   Set up ECharts module imports.
        *   Implement 1-2 simple charts (e.g., daily work duration bar chart, session type pie chart) using the fetched data to demonstrate integration.
    *   **Testing:** Test data loading and basic chart rendering.

6.  **Step 6: Advanced Features & Dashboard Iteration**
    *   Implement remaining "Medium Priority" features (advanced session management, more complex dashboard charts, filters on dashboard).
    *   Iterate on dashboard design and functionality based on user feedback and evolving requirements.
    *   Consider implementing the conceptual `getAggregatedTimerData` API if dashboard needs become highly complex.

### 2.13 File Modification Summary (Updated)

*   **`electron/main/database/database.ts`**: Add new columns (`focus`, `category`, `updated_at`) and indexes to `timer_sessions`.
*   **`electron/main/api/timer-api.ts`**: Update `startTimerSession` for new fields; enhance `getTimerSessionsByDateRange`, `getTimerStatsByDateRange` for filtering; ensure `endTimerSession` updates `updated_at`.
*   **`electron/main/ipc-handlers.ts`**: Update relevant timer IPC handlers to pass new parameters.
*   **`src/types/electron-api.d.ts`**: Add `TimerSession`, `TimerStats`, `TimerAPI` interfaces; update `Window.db`.
*   **`electron/preload/api-bridge.ts`**: Expose `timer` API with updated function signatures.
*   **`src/useElectronAPI.ts`**: Add `TimerAPI` to `ElectronAPI` interface.
*   **`src/views/TimerView.vue`**: Remove mock data, integrate with live API for all timer operations, display new session fields.
*   **`src/components/timer/PomodoroTimer.vue`**: Integrate with settings API, manage session start/end calls.
*   **`src/components/modals/AddSessionModal.vue`**: Collect `focus` and `category` inputs.
*   **`src/views/DashboardView.vue` (or new files)**: Introduce ECharts for visualizing timer statistics.
*   **(Potentially other Vue components if stats/charts are distributed)**

### 2.14 Conclusion of Review and Path Forward

The Original Plan provides a solid starting point for integrating the timer's frontend with its existing backend. This enhanced plan builds upon that by:
*   Verifying its initial assessment.
*   Detailing necessary database and API modifications to not only support the planned frontend features (`focus`, `category`) but also to lay a robust foundation for a future statistics dashboard.
*   Recommending **ECharts** as the charting library for its power, visual appeal, and suitability for complex data visualization.
*   Providing a more detailed, phased implementation roadmap that incorporates these enhancements.

By following this enhanced plan, the Timer application will gain full frontend functionality and be well-positioned for future development of a comprehensive and insightful statistics dashboard. The emphasis on structured data capture, efficient querying through indexing, and a powerful charting library will ensure a scalable and maintainable solution.