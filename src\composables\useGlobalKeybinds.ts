// Global keybinds composable
import { onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useGlobalKeybinds() {
  const router = useRouter()

  // Global state management functions
  const toggleSidebar = () => {
    // Find the sidebar navigation component and toggle it
    const event = new CustomEvent('toggle-sidebar')
    document.dispatchEvent(event)
    console.log('🔄 Toggling sidebar')
  }

  const closeActiveModal = () => {
    // Close any open modals by dispatching escape event
    const event = new CustomEvent('close-modal')
    document.dispatchEvent(event)
    console.log('❌ Closing active modal')
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error enabling fullscreen: ${err.message}`)
      })
    } else {
      document.exitFullscreen().catch(err => {
        console.error(`Error exiting fullscreen: ${err.message}`)
      })
    }
  }

  // Navigation functions
  const navigateTo = (path: string) => {
    router.push(path).catch(err => {
      console.error(`Navigation error: ${err.message}`)
    })
  }

  // Register global keybinds
  const registerGlobalKeybinds = () => {
    console.log('🎹 Registering global keybinds...')

    // Global navigation shortcuts
    globalKeybindManager.register({
      key: 'ctrl+digit1',
      handler: () => navigateTo('/'),
      description: 'Navigate to Dashboard',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    globalKeybindManager.register({
      key: 'ctrl+digit2',
      handler: () => navigateTo('/notes'),
      description: 'Navigate to Notes',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    globalKeybindManager.register({
      key: 'ctrl+digit3',
      handler: () => navigateTo('/books'),
      description: 'Navigate to Books',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    globalKeybindManager.register({
      key: 'ctrl+digit4',
      handler: () => navigateTo('/folders'),
      description: 'Navigate to Folders',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    globalKeybindManager.register({
      key: 'ctrl+digit5',
      handler: () => navigateTo('/timer'),
      description: 'Navigate to Timer',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    globalKeybindManager.register({
      key: 'ctrl+digit6',
      handler: () => navigateTo('/settings'),
      description: 'Navigate to Settings',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    // Sidebar toggle
    globalKeybindManager.register({
      key: 'ctrl+`',
      handler: toggleSidebar,
      description: 'Toggle sidebar',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    // Modal and overlay controls
    globalKeybindManager.register({
      key: 'escape',
      handler: closeActiveModal,
      description: 'Close active modal',
      category: KeybindCategory.GLOBAL,
      priority: 'high',
      enabled: true
    })

    // Fullscreen toggle
    globalKeybindManager.register({
      key: 'f11',
      handler: toggleFullscreen,
      description: 'Toggle fullscreen',
      category: KeybindCategory.GLOBAL,
      priority: 'medium',
      enabled: true
    })

    console.log('✅ Global keybinds registered')
  }

  // Unregister global keybinds
  const unregisterGlobalKeybinds = () => {
    console.log('🗑️ Unregistering global keybinds...')
    
    globalKeybindManager.unregister('ctrl+digit1')
    globalKeybindManager.unregister('ctrl+digit2')
    globalKeybindManager.unregister('ctrl+digit3')
    globalKeybindManager.unregister('ctrl+digit4')
    globalKeybindManager.unregister('ctrl+digit5')
    globalKeybindManager.unregister('ctrl+digit6')
    globalKeybindManager.unregister('ctrl+`')
    globalKeybindManager.unregister('escape')
    globalKeybindManager.unregister('f11')
  }

  // Get current context
  const getCurrentContext = (): KeybindContext => {
    const currentRoute = router.currentRoute.value
    let view = currentRoute.name as string || 'dashboard'

    // Normalize view name to match our expected values
    view = view.toLowerCase()
    if (view === 'notes' || currentRoute.path.includes('/notes')) {
      view = 'notes'
    } else if (view === 'books' || currentRoute.path.includes('/books')) {
      view = 'books'
    } else if (view === 'folders' || currentRoute.path.includes('/folders')) {
      view = 'folders'
    } else if (view === 'timer' || currentRoute.path.includes('/timer')) {
      view = 'timer'
    } else if (view === 'settings' || currentRoute.path.includes('/settings')) {
      view = 'settings'
    } else {
      view = 'dashboard'
    }

    // Check for modals
    const modalOpen = !!document.querySelector('.modal-overlay')

    // Check for editor focus or search input focus
    const editorFocused = !!document.querySelector('.tiptap-editor:focus-within, .ProseMirror:focus-within')
    const searchInputFocused = !!document.querySelector('.search-bar:focus-within input:focus, input[placeholder*="Search"]:focus, input[placeholder*="search"]:focus')

    // Treat search input focus the same as editor focus for keybind purposes
    const inputFocused = editorFocused || searchInputFocused

    return {
      view: view as any,
      modalOpen,
      editorFocused: inputFocused,
      selectedItems: [], // This would be populated by view-specific composables
      currentRoute: currentRoute.path
    }
  }

  // Global keyboard event handler
  const handleGlobalKeyEvent = (event: KeyboardEvent) => {
    const context = getCurrentContext()
    if (process.env.NODE_ENV === 'development') {
      const searchInputFocused = !!document.querySelector('.search-bar:focus-within input:focus, input[placeholder*="Search"]:focus, input[placeholder*="search"]:focus')
      console.log(`🎹 Key event: ${event.key} in ${context.view}, modal: ${context.modalOpen}, editor: ${context.editorFocused}, search: ${searchInputFocused}`)
    }
    globalKeybindManager.handleKeyEvent(event, context)
  }

  // Event handler functions (store references for proper cleanup)
  const handleToggleSidebar = () => {
    // This would be handled by the SidebarNavigation component
    console.log('📡 Sidebar toggle event dispatched')
  }

  const handleCloseModal = () => {
    // This would be handled by modal components
    console.log('📡 Close modal event dispatched')
  }

  // Setup event listeners and keybinds
  const setup = () => {
    registerGlobalKeybinds()
    document.addEventListener('keydown', handleGlobalKeyEvent)
    
    // Listen for custom events
    document.addEventListener('toggle-sidebar', handleToggleSidebar)
    document.addEventListener('close-modal', handleCloseModal)
  }

  // Cleanup
  const cleanup = () => {
    unregisterGlobalKeybinds()
    document.removeEventListener('keydown', handleGlobalKeyEvent)
    document.removeEventListener('toggle-sidebar', handleToggleSidebar)
    document.removeEventListener('close-modal', handleCloseModal)
  }

  // Don't auto-setup in onMounted - let App.vue handle it

  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    registerGlobalKeybinds,
    unregisterGlobalKeybinds,
    getCurrentContext,
    setup,
    cleanup,
    // Expose utility functions for other composables
    toggleSidebar,
    closeActiveModal,
    toggleFullscreen,
    navigateTo
  }
}