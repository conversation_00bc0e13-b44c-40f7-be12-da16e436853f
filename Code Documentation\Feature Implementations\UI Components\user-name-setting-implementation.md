# User Name Setting Implementation

## Files Modified

- `src/stores/settingsStore.ts` - Added userName property to settings
- `src/components/settings/UserSettings.vue` - New settings component for user preferences
- `src/views/SettingsView.vue` - Added UserSettings component to settings page
- `src/views/DashboardView.vue` - Updated greeting to include user's name

## What Was Done

Added a user name setting that allows users to set their display name, which is then used in personalized greetings throughout the application, particularly in the dashboard greeting.

## How It Was Implemented

### 1. Settings Store Updates (`src/stores/settingsStore.ts`)

**Added userName property to AppSettings interface:**
```typescript
export interface AppSettings {
  // ... existing properties
  userName: string
  // ... rest of properties
}
```

**Updated default settings:**
```typescript
const settings = ref<AppSettings>({
  // ... existing defaults
  userName: '',
  // ... rest of defaults
})
```

**Updated resetToDefaults function:**
```typescript
function resetToDefaults() {
  settings.value = {
    // ... existing defaults
    userName: '',
    // ... rest of defaults
  }
}
```

### 2. New UserSettings Component (`src/components/settings/UserSettings.vue`)

**Features:**
- Text input field for user name (max 50 characters)
- Real-time preview of how the greeting will appear
- Follows the same design pattern as ThemeSettings and DiscordSettings
- Responsive design with mobile-friendly layout
- Automatic saving to settings store on input change

**Key functionality:**
- `currentUserName` computed property gets value from settings store
- `previewGreeting` computed property shows time-appropriate greeting with user's name
- `handleNameChange` function updates the settings store in real-time

**Styling:**
- Consistent with existing settings components
- Uses CSS variables for theming
- Responsive design for mobile devices
- Focus states and transitions for better UX

### 3. Settings View Integration (`src/views/SettingsView.vue`)

**Changes:**
- Imported UserSettings component
- Added UserSettings as the first component in the settings page
- Positioned above ThemeSettings for logical flow

### 4. Dashboard Greeting Enhancement (`src/views/DashboardView.vue`)

**Updated greeting logic:**
```typescript
const greeting = computed(() => {
  const hour = new Date().getHours()
  const userName = settingsStore.settings.userName.trim()
  
  let timeGreeting = ''
  if (hour < 12) timeGreeting = 'Good morning'
  else if (hour < 17) timeGreeting = 'Good afternoon'
  else timeGreeting = 'Good evening'
  
  return userName ? `${timeGreeting}, ${userName}!` : `${timeGreeting}!`
})
```

**Behavior:**
- If user name is set: "Good morning, John!" 
- If no user name: "Good morning!" (fallback to original behavior)
- Time-appropriate greetings (morning/afternoon/evening)

## User Experience

1. **Settings Configuration:**
   - User navigates to Settings
   - UserSettings component appears at the top
   - User enters their name in the text input
   - Preview shows how the greeting will appear
   - Changes are saved automatically

2. **Dashboard Experience:**
   - User returns to dashboard
   - Greeting now includes their name: "Good afternoon, Sarah!"
   - If no name is set, shows standard greeting: "Good afternoon!"

## Technical Details

- **Data Persistence:** User name is stored in localStorage via the settings store
- **Validation:** Input is limited to 50 characters maximum
- **Fallback Behavior:** Gracefully falls back to standard greeting if no name is set
- **Real-time Updates:** Changes in settings immediately reflect in the dashboard
- **Responsive Design:** Works on all screen sizes with appropriate layout adjustments

## Future Enhancements

- Could extend to other personalized messages throughout the app
- Could add validation for inappropriate content
- Could support multiple greeting styles or languages
- Could integrate with user profiles if authentication is added
