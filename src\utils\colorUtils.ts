/**
 * Utility functions for color manipulation and CSS filter generation
 */

/**
 * Convert hex color to CSS filter for SVG colorization
 * This uses a more accurate approach for better color representation
 */
export function hexToFilter(hex: string | null): string {
  if (!hex) return '';

  // Remove # if present
  const cleanHex = hex.replace('#', '');

  // Validate hex format
  if (!/^[0-9A-F]{6}$/i.test(cleanHex)) {
    console.warn('Invalid hex color format:', hex);
    return '';
  }

  // Convert to RGB
  const r = parseInt(cleanHex.substr(0, 2), 16);
  const g = parseInt(cleanHex.substr(2, 2), 16);
  const b = parseInt(cleanHex.substr(4, 2), 16);

  // For better color accuracy, use a more sophisticated approach
  // Convert RGB to HSL for better filter calculation
  const rNorm = r / 255;
  const gNorm = g / 255;
  const bNorm = b / 255;

  const max = Math.max(rNorm, gNorm, bNorm);
  const min = Math.min(rNorm, gNorm, bNorm);
  const diff = max - min;

  // Calculate hue
  let hue = 0;
  if (diff !== 0) {
    if (max === rNorm) {
      hue = ((gNorm - bNorm) / diff) % 6;
    } else if (max === gNorm) {
      hue = (bNorm - rNorm) / diff + 2;
    } else {
      hue = (rNorm - gNorm) / diff + 4;
    }
  }
  hue = Math.round(hue * 60);
  if (hue < 0) hue += 360;

  // Calculate lightness and saturation
  const lightness = (max + min) / 2;
  const saturation = diff === 0 ? 0 : diff / (1 - Math.abs(2 * lightness - 1));

  // Create filter with better color mapping
  // Adjust brightness to maintain visibility
  const brightness = Math.max(0.3, Math.min(1.5, lightness + 0.2));
  // Enhance saturation for better color visibility
  const saturate = Math.max(1, saturation * 1.5);

  return `hue-rotate(${hue}deg) saturate(${saturate}) brightness(${brightness})`;
}

/**
 * Get folder icon style with color
 * Uses a direct color replacement approach
 */
export function getFolderIconStyle(color: string | null): Record<string, string> {
  if (!color) {
    return {};
  }



  // For filled SVGs, we can use a much simpler approach
  // First convert to grayscale, then apply the color
  const filter = `brightness(0) saturate(100%) invert(1) sepia(1) saturate(10000%) hue-rotate(${getHueFromHex(color)}deg) brightness(0.8) contrast(1.2)`;
  console.log(`🎨 getFolderIconStyle: filter=${filter}`);

  return {
    filter: filter
  };
}

/**
 * Simple function to get hue from hex color
 */
function getHueFromHex(hex: string): number {
  // Remove # if present
  const cleanHex = hex.replace('#', '');

  // Convert to RGB
  const r = parseInt(cleanHex.substr(0, 2), 16) / 255;
  const g = parseInt(cleanHex.substr(2, 2), 16) / 255;
  const b = parseInt(cleanHex.substr(4, 2), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;

  let hue = 0;
  if (diff !== 0) {
    if (max === r) {
      hue = ((g - b) / diff) % 6;
    } else if (max === g) {
      hue = (b - r) / diff + 2;
    } else {
      hue = (r - g) / diff + 4;
    }
  }
  hue = Math.round(hue * 60);
  if (hue < 0) hue += 360;

  return hue;
}

/**
 * Get the appropriate folder icon source (filled version for coloring)
 */
export function getFolderIconSrc(color: string | null, isOpen: boolean = false): string {
  // If we have a color, use the filled version for better filter results
  if (color) {
    return isOpen ? '/icons/folder-filled-icon.svg' : '/icons/folder-filled-icon.svg';
  }

  // Default icons
  return isOpen ? '/icons/folder-open-icon.svg' : '/icons/folder-icon.svg';
}



/**
 * Create a CSS filter to change stroke color of SVG
 * This is more aggressive than the previous approach
 */
function createStrokeColorFilter(hex: string): string {
  // Remove # if present
  const cleanHex = hex.replace('#', '');

  // Convert to RGB
  const r = parseInt(cleanHex.substr(0, 2), 16);
  const g = parseInt(cleanHex.substr(2, 2), 16);
  const b = parseInt(cleanHex.substr(4, 2), 16);

  // Convert RGB to 0-1 range
  const rNorm = r / 255;
  const gNorm = g / 255;
  const bNorm = b / 255;

  // Calculate hue, saturation, and lightness
  const max = Math.max(rNorm, gNorm, bNorm);
  const min = Math.min(rNorm, gNorm, bNorm);
  const diff = max - min;

  // Calculate hue
  let hue = 0;
  if (diff !== 0) {
    if (max === rNorm) {
      hue = ((gNorm - bNorm) / diff) % 6;
    } else if (max === gNorm) {
      hue = (bNorm - rNorm) / diff + 2;
    } else {
      hue = (rNorm - gNorm) / diff + 4;
    }
  }
  hue = Math.round(hue * 60);
  if (hue < 0) hue += 360;

  // Calculate lightness and saturation
  const lightness = (max + min) / 2;
  const saturation = diff === 0 ? 0 : diff / (1 - Math.abs(2 * lightness - 1));

  // Create a more aggressive filter for stroke-based SVGs
  // First, make the icon brighter, then apply color transformation
  const brightness = Math.max(0.8, lightness * 2);
  const saturate = Math.max(2, saturation * 3);
  const contrast = 1.5;

  return `brightness(0) saturate(100%) invert(${lightness > 0.5 ? 0 : 1}) sepia(1) saturate(${saturate * 100}%) hue-rotate(${hue}deg) brightness(${brightness}) contrast(${contrast})`;
}

/**
 * Check if a color is light or dark (for contrast purposes)
 */
export function isLightColor(hex: string): boolean {
  const cleanHex = hex.replace('#', '');
  const r = parseInt(cleanHex.substr(0, 2), 16);
  const g = parseInt(cleanHex.substr(2, 2), 16);
  const b = parseInt(cleanHex.substr(4, 2), 16);
  
  // Calculate relative luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5;
}
