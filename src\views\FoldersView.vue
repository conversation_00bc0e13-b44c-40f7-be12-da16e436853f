<template>
  <div class="folders-view">
    <div class="folders-sidebar">
      <div class="overlap-wrapper">
        <!-- Sidebar Header with Search and New Folder button -->
        <div class="overlap">
          <div class="sidebar-header">
            <!-- Search Bar from NotesView -->
            <div class="search-bar" @click="focusSearch">
              <img src="/icons/search-icon.svg" class="search-icon" alt="Search" />
              <input ref="searchInputRef" type="text" placeholder="Search folders..." v-model="searchQuery"
                @input="filterFolders" />
              <button class="clear-search" v-if="searchQuery" @click.stop="clearSearch">
                <img src="/icons/close-icon.svg" class="close-icon" alt="Clear" />
              </button>
            </div>
            <!-- Show Delete Button when folders are selected -->
            <button v-if="navigatorSelectedItems.length > 0" class="delete-folder-button"
              :disabled="isProtectedFolderSelected"
              @click="handleDeleteSelectedFolders(navigatorSelectedItems)">
              <img src="/icons/trash-icon.svg"
                   :class="['trash-icon', { 'trash-icon-disabled': isProtectedFolderSelected }]"
                   alt="Delete" />
              <span class="button-text">Delete Selected ({{ navigatorSelectedItems.length }})</span>
            </button>
            <!-- Show New Folder Button when no folders are selected -->
            <button v-else class="new-folder-button" @click="createNewFolderInCurrent">
              <img src="/icons/plus-icon.svg" class="plus-icon" alt="+" />
              <span class="button-text">New Folder</span>
            </button>
          </div>

          <div class="rectangle-divider-2"></div>
          <!-- Replace the overlap-group section in FoldersView.vue with this: -->

          <!-- Folder List Section -->
          <div class="overlap-group">
            <!-- Breadcrumb Section - OUTSIDE the scrolling wrapper -->
            <div v-if="isHierarchyAvailable" class="breadcrumb-section">
              <!-- Breadcrumb navigation -->
              <div class="breadcrumb-container-external" style="position: relative; z-index: 100;">
                <!-- Only show breadcrumb when no folders are selected -->
                <template v-if="navigatorSelectedItems.length === 0">
                  <span class="breadcrumb-item" @click="navigateToRootFromBreadcrumb">
                    All Folders
                  </span>
                  <template v-if="selectedFolder && getFolderPath(selectedFolder).length > 2">
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item truncated" @click="togglePathDropdown($event)"
                      title="Click to see full path">
                      <!-- FIXED: Use the same calculation as FolderNavigator - subtract 2 -->
                      ...{{ getFolderPath(selectedFolder).length - 1 }} more...
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item current"
                      @click="navigateToFolderFromBreadcrumb({ id: selectedFolder.id, name: selectedFolder.name })">
                      {{ selectedFolder.name }}
                    </span>
                  </template>
                  <template v-else-if="selectedFolder">
                    <template v-for="(pathFolder, index) in getFolderPath(selectedFolder)" :key="pathFolder.id">
                      <span class="breadcrumb-separator">/</span>
                      <span class="breadcrumb-item"
                        :class="{ 'breadcrumb-current': index === getFolderPath(selectedFolder).length - 1 }"
                        @click="navigateToFolderFromBreadcrumb({ id: pathFolder.id, name: pathFolder.name })"
                        :title="pathFolder.name">
                        {{ pathFolder.name }}
                      </span>
                    </template>
                  </template>
                </template>
              </div>
            </div>

            <!-- Scrollable Folder List Section -->
            <div class="group-wrapper">
              <div class="group" v-if="!loading">
                <!-- View toggle option -->
                <div class="folder-list">
                  <div v-if="isHierarchyAvailable">
                    <!-- FolderNavigator WITHOUT breadcrumb -->
                    <FolderNavigator ref="folderNavigatorRef" :folderHierarchy="filteredHierarchy"
                      :selectedItemId="selectedItemId" :currentSelectedFolder="selectedFolder"
                      :isModalOpen="isAnyModalOpen" :showBreadcrumb="false"
                      :isProtectedFolderSelected="isProtectedFolderSelected" @selectItem="handleSelectItem"
                      @toggleFolder="selectFolder" @renameFolderRequest="renameFolder"
                      @deleteItems="handleDeleteSelectedFolders" @selectionChange="handleNavigatorSelectionChange"
                      @createFolder="createNewFolderInCurrent" />
                  </div>
                  <div v-else class="empty-folders">
                    <p>No folders found. Root folder is still available.</p>
                  </div>
                </div>
              </div>
              <div v-else class="loading-folders">
                <p>Loading folders...</p>
              </div>
            </div>
          </div>

          <!-- Bottom Statistics Bar -->
          <div class="footer-stats">
            <div class="stats-content">
              <div class="text-wrapper-2">{{ folderStats.folderCount }} folders, {{ folderStats.fileCount }} files</div>
              <div class="text-wrapper-2">{{ folderStats.storageUsed }} used</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="folders-content">
      <!-- When viewing a specific folder -->
      <div v-if="selectedFolder !== null" class="folder-content-area">
        <div class="folder-header">
          <div class="header-content">
            <div class="folder-path"> <!-- Only show "All Folders" as root item if we're in a subfolder --> <template
                v-if="getFolderPath(selectedFolder).length > 0"> <span class="folder-path-item"
                  @click="navigateToRootFromBreadcrumb"
                  :class="{ 'folder-path-unclickable': selectedFolder === null }">All
                  Folders</span>
                <template v-if="getPathForDisplay(selectedFolder).truncated">
                  <span class="folder-path-separator">/</span>
                  <span v-if="getPathForDisplay(selectedFolder).firstFolder" class="folder-path-item"
                    @click="navigateToFolderFromBreadcrumb({ id: getPathForDisplay(selectedFolder).firstFolder!.id, name: getPathForDisplay(selectedFolder).firstFolder!.name })">
                    {{ getPathForDisplay(selectedFolder).firstFolder!.name }}
                  </span>
                  <span class="folder-path-separator" v-if="getPathForDisplay(selectedFolder).firstFolder">/</span>
                  <span class="folder-path-item folder-path-truncated" @click="togglePathDropdown($event)"
                    title="Click to see full path">
                    ...{{ getPathForDisplay(selectedFolder).truncatedCount }} more...
                  </span>
                  <span class="folder-path-separator">/</span>
                  <span class="folder-path-item folder-path-current"
                    @click="navigateToFolderFromBreadcrumb({ id: getPathForDisplay(selectedFolder).lastFolder!.id, name: getPathForDisplay(selectedFolder).lastFolder!.name })">
                    {{ getPathForDisplay(selectedFolder).lastFolder!.name }}
                  </span>
                </template>
                <template v-else>
                  <template v-for="(pathFolder, index) in getFolderPath(selectedFolder)" :key="pathFolder.id">
                    <span class="folder-path-separator">/</span>
                    <span class="folder-path-item"
                      :class="{ 'folder-path-current': index === getFolderPath(selectedFolder).length - 1 }"
                      @click="navigateToFolderFromBreadcrumb({ id: pathFolder.id, name: pathFolder.name })">
                      {{ pathFolder.name }}
                    </span>
                  </template>
                </template>
              </template> <!-- When at root level, just show "All Folders" as current -->
              <template v-else>
                <span class="folder-path-item folder-path-current folder-path-unclickable">All Folders</span>
              </template>
            </div> <!-- End of folder-path -->
          </div>
          <div class="header-divider"></div>
        </div>
        <FolderToolbar :selectedItemId="selectedItemId" :selectedItemType="selectedItemType || undefined"
          :checkedItems="checkedItems" :currentSearchQuery="searchInFolderQuery"
          :isProtectedFolderSelected="isProtectedFolderSelected" @delete="deleteCheckedItems"
          @rename="renameCheckedItem" @color="colorCheckedItems" @move="moveCheckedItems" @export="exportFolder" @import="importToFolder"
          @sort="sortFolder" @search="searchInFolder" @newFolder="createNewFolderInCurrent"
          @newNote="createNewNoteInCurrent" />
        <FolderContent class="folder-content-wrapper" :folderName="selectedFolder.name"
          :items="getFolderItems(selectedFolder)" :selectedItemId="selectedItemId || undefined"
          :selectedItemType="selectedItemType || undefined" :searchQuery="searchInFolderQuery"
          :folderPath="getFolderPath(selectedFolder)" @selectItem="handleSelectItem"
          @checkedItemsChange="handleCheckedItemsChange" @createItem="createNewItem" @deleteFolder="deleteFolder"
          @renameFolder="renameFolder" @moveFolder="moveFolder" @exportFolder="exportFolder"
          @importToFolder="importToFolder" @sortFolder="sortFolder" @searchInFolder="searchInFolder" />
      </div>
      <!-- Root folder view (All Folders) -->
      <div v-else class="folder-content-area">
        <div class="folder-header">
          <div class="header-content">
            <div class="folder-path">
              <span class="folder-path-item folder-path-current folder-path-unclickable">All Folders</span>
            </div>
          </div>
          <div class="header-divider"></div>
        </div>
        <FolderToolbar :selectedItemId="selectedItemId" :selectedItemType="selectedItemType || undefined"
          :checkedItems="checkedItems" :currentSearchQuery="searchInFolderQuery"
          :isProtectedFolderSelected="isProtectedFolderSelected" @delete="deleteCheckedItems"
          @rename="renameCheckedItem" @color="colorCheckedItems" @move="moveCheckedItems" @export="exportFolder" @import="importToFolder"
          @sort="sortFolder" @search="searchInFolder" @newFolder="createNewFolderInCurrent"
          @newNote="createNewNoteInCurrent" />
        <FolderContent class="folder-content-wrapper" folderName="Root" :items="getFolderItems(null)"
          :selectedItemId="selectedItemId || undefined" :selectedItemType="selectedItemType || undefined"
          :searchQuery="searchInFolderQuery" :folderPath="[]" @selectItem="handleSelectItem"
          @checkedItemsChange="handleCheckedItemsChange" @createItem="createNewItem" @deleteFolder="deleteFolder"
          @renameFolder="renameFolder" @moveFolder="moveFolder" @exportFolder="exportFolder"
          @importToFolder="importToFolder" @sortFolder="sortFolder" @searchInFolder="searchInFolder" />
      </div>
    </div>

    <!-- Rename Folder Modal -->
    <Teleport to="body">
      <div v-if="showRenameModal" class="modal-overlay">
        <div class="modal-content">
          <h3>Rename Folder</h3>
          <input v-model="newFolderName" type="text" class="rename-input" @keyup.enter="confirmRename"
            ref="renameInputRef" autofocus />
          <div class="modal-buttons">
            <button @click="cancelRename" class="btn btn-secondary">Cancel</button>
            <button @click="confirmRename" class="btn btn-primary">Rename</button>
          </div>
        </div>
      </div>
    </Teleport> <!-- Export Modal -->
    <Teleport to="body">
      <div v-if="showExportModal && itemsToExport && itemsToExport.length > 0" class="modal-overlay">
        <ExportMultipleItemsModal :items="itemsToExport" @close="showExportModal = false"
          @export-start="handleExportStart"
          @export-complete="handleExportComplete" />
      </div>
    </Teleport>

    <!-- Export Progress Overlay -->
    <Teleport to="body">
      <div v-if="isExportingFile" class="export-progress-overlay">
        <div class="export-progress-modal">
          <div class="export-progress-content">
            <div class="export-spinner"></div>
            <h3 class="export-progress-title">{{ exportProgressTitle }}</h3>
            <p class="export-progress-message">{{ exportProgressMessage }}</p>
            <div class="export-progress-details" v-if="exportProgressDetails">
              <small>{{ exportProgressDetails }}</small>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
    <!-- Move Items Modal -->
    <Teleport to="body">
      <div v-if="showMoveModal && checkedItems.length > 0" class="modal-overlay">
        <MoveFolderModal :folders="folders as any[]" :items="checkedItems as any[]"
          :currentFolderId="selectedFolder?.id || null" @confirm="confirmMove" @cancel="cancelMove" />
      </div>
    </Teleport>

    <!-- Sort Folder Modal -->
    <Teleport to="body">
      <div v-if="showSortModal" class="modal-overlay">
        <SortFolderModal :folderId="selectedFolder?.id" :folderName="selectedFolder?.name || 'Root'"
          :currentSortBy="currentSortBy" :currentSortDirection="currentSortDirection"
          :currentFoldersFirst="currentSortFoldersFirst" @close="showSortModal = false"
          @sort-complete="handleSortComplete" />
      </div>
    </Teleport>

    <!-- Path dropdown for breadcrumb navigation -->
    <div class="folder-path-dropdown" v-if="showPathDropdown" @click.stop>
      <div class="path-dropdown-content" @click.stop>
        <!-- Always show All Folders option in the dropdown -->
        <span class="path-dropdown-item" @click="navigateToRootFromDropdown"
          :class="{ 'path-dropdown-unclickable': selectedFolder === null }">
          All Folders
        </span>
        <!-- Show all folders in the path -->
        <template v-for="(pathFolder, index) in getFolderPath(selectedFolder)" :key="pathFolder.id">
          <span class="path-dropdown-item" :class="{ 'current': index === getFolderPath(selectedFolder).length - 1 }"
            @click="navigateToFolderFromDropdown({ id: pathFolder.id, name: pathFolder.name })">
            {{ pathFolder.name }}
          </span>
        </template>
      </div>
    </div>

    <!-- Delete Folder Modal -->
    <Teleport to="body">
      <div v-if="showDeleteFolderModal" class="modal-overlay">
        <DeleteFolderModal :folderName="foldersToDelete[0]?.name || ''" :folderNames="foldersToDelete.map(f => f.name)"
          :folderCount="foldersToDelete.length"
          :totalNotesCount="foldersToDelete.reduce((total, folder) => total + (folder.notesCount || 0), 0)"
          :availableTargetFolders="folders.filter(f => !foldersToDelete.some(fd => fd.id === f.id))"
          @close="cancelDeleteModals" @cancel="cancelDeleteModals" @delete="confirmFolderDelete" />
      </div>
    </Teleport>

    <!-- Delete Note Modal -->
    <Teleport to="body">
      <div v-if="showDeleteNoteModal" class="modal-overlay">
        <DeleteNoteModal :noteTitle="noteTitle" @close="cancelDeleteModals" @cancel="cancelDeleteModals"
          @delete="confirmNoteDelete" />
      </div>
    </Teleport>

    <!-- Delete Notes Modal -->
    <Teleport to="body">
      <div v-if="showDeleteNotesModal" class="modal-overlay">
        <DeleteNotesModal :noteTitles="noteTitles" :notesCount="notesToDelete.length" @close="cancelDeleteModals"
          @cancel="cancelDeleteModals" @delete="confirmNoteDelete" />
      </div>
    </Teleport>

    <!-- Delete Mixed Items Modal -->
    <Teleport to="body">
      <div v-if="showDeleteMixedItemsModal" class="modal-overlay">
        <DeleteMixedItemsModal :folderCount="foldersToDelete.length" :noteCount="notesToDelete.length"
          :items="mixedItemsToDelete"
          :totalNotesCount="foldersToDelete.reduce((total, folder) => total + (folder.notesCount || 0), 0)"
          :availableTargetFolders="folders.filter(f => !foldersToDelete.some(fd => fd.id === f.id))"
          @close="cancelDeleteModals" @cancel="cancelDeleteModals" @delete="confirmMixedItemsDelete" />
      </div>
    </Teleport>

    <!-- Rename Note Modal -->
    <Teleport to="body">
      <div v-if="showRenameNoteModal" class="modal-overlay">
        <div class="modal-content">
          <h3>Rename Note</h3>
          <input v-model="newNoteName" type="text" class="rename-input" @keyup.enter="confirmNoteRename"
            ref="renameInputRef" autofocus />
          <div class="modal-buttons">
            <button @click="cancelNoteRename" class="btn btn-secondary">Cancel</button>
            <button @click="confirmNoteRename" class="btn btn-primary">Rename</button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>

  <!-- Name New Folder Modal -->
  <Teleport to="body">
    <div v-if="showNameFolderModal" class="modal-overlay">
      <NameFolderModal :initialName="folderToNameInitial?.name || 'New Folder'" @confirm="handleConfirmNameFolder"
        @cancel="handleCancelNameFolder" />
    </div>
  </Teleport>

  <!-- Folder Color Selection Modal -->
  <Teleport to="body">
    <div v-if="showColorModal" class="modal-overlay">
      <FolderColorSelectionModal
        :currentColor="folderToColor?.color || undefined"
        :folderName="folderToColor?.name || 'Folder'"
        @apply-color="handleFolderColorChange"
        @close="handleColorModalClose" />
    </div>
  </Teleport>

  <!-- Hidden file input for import -->
  <input type="file" ref="fileInput" @change="handleFileImport" style="display: none;" accept=".md,.pdf,.noti,.txt" />
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, onBeforeUnmount, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useFoldersKeybinds } from '../composables/useFoldersKeybinds';
import { usePageLoadMonitoring } from '../composables/usePageLoadMonitoring';
import DOMPurify from 'dompurify';
import SingleFolder from '../components/folders/SingleFolder.vue';
import FolderNavigator from '../components/folders/FolderNavigator.vue';
import FolderToolbar from '../components/folders/FolderToolbar.vue';
import FolderContent from '../components/folders/FolderContent.vue';
import ExportMultipleItemsModal from '../components/modals/ExportMultipleItemsModal.vue';
import SortFolderModal from '../components/modals/SortFolderModal.vue';
import DeleteNoteModal from '../components/modals/DeleteNoteModal.vue';
import DeleteNotesModal from '../components/modals/DeleteNotesModal.vue';
import DeleteFolderModal from '../components/modals/DeleteFolderModal.vue';
import DeleteMixedItemsModal from '../components/modals/DeleteMixedItemsModal.vue';
import NameFolderModal from '../components/modals/NameFolderModal.vue';
import FolderColorSelectionModal from '../components/modals/FolderColorSelectionModal.vue';
import MoveFolderModal from '../components/modals/MoveFolderModal.vue';
import { useElectronAPI } from '../useElectronAPI';
import { Folder, FolderWithMeta, Note } from '../types/electron-api';

export default defineComponent({
  name: 'FoldersView', components: {
    SingleFolder,
    FolderNavigator,
    FolderToolbar,
    FolderContent,
    ExportMultipleItemsModal,
    SortFolderModal,
    DeleteNoteModal,
    DeleteNotesModal,
    DeleteFolderModal,
    DeleteMixedItemsModal,
    NameFolderModal,
    FolderColorSelectionModal,
    MoveFolderModal,
  },
  setup() {
    const db = useElectronAPI();
    const router = useRouter();
    const route = useRoute();

    // Setup keybinds
    const { setupFolderFunctions, activate: activateKeybinds, deactivate: deactivateKeybinds } = useFoldersKeybinds();

    // Page load monitoring
    const { autoRecordPageMounted } = usePageLoadMonitoring();
    autoRecordPageMounted('Folders');
    const folders = ref<FolderWithMeta[]>([]);
    const folderHierarchy = ref<FolderWithMeta[]>([]);
    const folderNotes = ref<Note[]>([]);
    const notes = ref<Note[]>([]);
    const loading = ref(true);
    const searchQuery = ref('');
    const selectedFolder = ref<FolderWithMeta | null>(null);
    const selectedItemId = ref<number | null>(null);
    const selectedItemType = ref<string | null>(null);
    const openFolders = ref<number[]>([]); // Track which folders are open
    const searchInputRef = ref<HTMLInputElement | null>(null);
    const renameInputRef = ref<HTMLInputElement | null>(null);
    const folderNavigatorRef = ref<any>(null);
    const fileInput = ref<HTMLInputElement | null>(null);
    const folderStats = ref({
      folderCount: 0,
      fileCount: 0,
      storageUsed: "0 MB"
    });    // Track checked items in the current folder
    const checkedItems = ref<{ type: string, id: number | string }[]>([]);
    // Track selected items in the navigator
    const navigatorSelectedItems = ref<(number | string)[]>([]);

    // Cache for folder items to avoid redundant calculations
    const folderItemsCache = ref<Record<string, Array<any>>>({});

    // Modals state
    const showDeleteModal = ref(false);
    const folderToDelete = ref<FolderWithMeta | null>(null);
    const showRenameModal = ref(false);
    const showRenameNoteModal = ref(false);
    const folderToRename = ref<FolderWithMeta | null>(null);
    const noteToRename = ref<Note | null>(null);
    const newFolderName = ref('');
    const newNoteName = ref('');
    const targetFolderId = ref<number | null>(null);

    // New modal states for NameFolderModal
    const showNameFolderModal = ref(false);
    const folderToNameInitial = ref<FolderWithMeta | null>(null);

    // Color modal states
    const showColorModal = ref(false);
    const folderToColor = ref<FolderWithMeta | null>(null);    // New modal states for other modals (Export, Sort)
    const showExportModal = ref(false);
    const showSortModal = ref(false);
    const showMoveModal = ref(false);
    const itemsToExport = ref<{ id: number; type: 'folder' | 'note'; name: string }[]>([]);
    const searchInFolderQuery = ref('');

    // Export progress states
    const isExportingFile = ref(false);
    const exportProgressTitle = ref('');
    const exportProgressMessage = ref('');
    const exportProgressDetails = ref('');

    // Sort preferences
    const currentSortBy = ref<string>('name'); // Default sort by name
    const currentSortDirection = ref<string>('ascending'); // Default ascending
    const currentSortFoldersFirst = ref<boolean>(true); // Default folders first

    // Delete modals state
    const showDeleteFolderModal = ref(false);
    const showDeleteNoteModal = ref(false);
    const showDeleteNotesModal = ref(false);
    const showDeleteMixedItemsModal = ref(false);
    const foldersToDelete = ref<FolderWithMeta[]>([]);
    const notesToDelete = ref<Note[]>([]);
    const noteTitle = ref('');
    const noteTitles = ref<string[]>([]);

    // Use number | undefined for id to handle potentially undefined note IDs
    const mixedItemsToDelete = ref<{ type: string; name: string; id: number | undefined }[]>([]);

    // Add a guard for loadFolders
    const isLoadFoldersRunning = ref(false);

    // Check if we can use hierarchy view
    const isHierarchyAvailable = computed(() => {
      try {
        return folderHierarchy.value &&
          Array.isArray(folderHierarchy.value) &&
          folderHierarchy.value.length > 0 &&
          folderHierarchy.value.every(item => item !== undefined);
      } catch (error) {
        console.warn('Error checking hierarchy availability', error);
        return false;
      }
    });

    // Calculate the count of notes at the root level
    const rootNotesCount = computed(() => {
      try {
        // If notes aren't loaded yet, return 0
        if (!notes.value) return 0;
        // Count notes that don't have a folder_id (root level notes)
        return notes.value.filter(note => note.folder_id === null).length;
      } catch (error) {
        console.warn('Error calculating root notes count', error);
        return 0;
      }
    });

    // Filter folders based on search query
    const filteredFolders = computed(() => {
      if (!searchQuery.value.trim()) {
        return folders.value;
      }

      const query = searchQuery.value.toLowerCase();
      return folders.value.filter(folder =>
        folder.name.toLowerCase().includes(query)
      );
    });

    // Filter hierarchy based on search query
    const filteredHierarchy = computed(() => {
      if (!searchQuery.value.trim() || !Array.isArray(folderHierarchy.value)) {
        return folderHierarchy.value;
      }

      const query = searchQuery.value.toLowerCase();

      // Helper function to filter hierarchy recursively
      const filterHierarchy = (folders: FolderWithMeta[]): FolderWithMeta[] => {
        if (!Array.isArray(folders)) return [];

        return folders.filter(folder => {
          if (!folder || typeof folder.name !== 'string') {
            return false;
          }

          const nameMatch = folder.name.toLowerCase().includes(query);
          let childrenMatch: FolderWithMeta[] = [];

          if (folder.children && Array.isArray(folder.children) && folder.children.length > 0) {
            childrenMatch = filterHierarchy(folder.children);
            // If any children match, include this folder and its matching children
            if (childrenMatch.length > 0) {
              folder.children = childrenMatch;
              return true;
            }
          }

          return nameMatch;
        });
      };

      try {
        // Create a deep copy to avoid modifying the original
        const clonedHierarchy = JSON.parse(JSON.stringify(folderHierarchy.value)) as FolderWithMeta[];
        return filterHierarchy(clonedHierarchy);
      } catch (error) {
        console.error('Error filtering folder hierarchy:', error);
        return [];
      }
    });

    // Focus the search input
    const focusSearch = () => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    };

    // Clear search query
    const clearSearch = () => {
      searchQuery.value = '';
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    };

    // Filter folders when search input changes
    const filterFolders = () => {
      // Filtering is handled by the computed property
    };
    // Select a folder
    const selectFolder = async (folder: FolderWithMeta) => {
      if (!folder) {
        console.error('Invalid folder object received in selectFolder', folder);
        return;
      }      // Special case for Root folder (id = null)
      if (folder.id === null) {
        // If we're already at All Folders (root), don't do anything
        if (selectedFolder.value === null) {
          console.log('Already at All Folders, no navigation needed');
          return;
        }

        console.log('Navigating to All Folders (root level)');
        selectedFolder.value = null;
        selectedItemId.value = null;
        selectedItemType.value = 'folder';
        searchInFolderQuery.value = '';

        // Clear any open folder dropdown
        showPathDropdown.value = false;

        // Clear checked items when changing folders
        checkedItems.value = [];

        // Clear navigator selected items when changing folders
        navigatorSelectedItems.value = [];

        // Load root level notes - use the improved method to get root notes
        console.log('Loading root notes...');
        await loadRootNotes();

        // Force refresh folder items cache for root to ensure fresh content is shown
        const cacheKey = `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
        delete folderItemsCache.value[cacheKey];

        console.log('Root navigation complete');
        return;
      }

      if (typeof folder.id !== 'number') {
        console.error('Invalid folder ID received in selectFolder', folder);
        return;
      }

      console.log('Selecting folder:', folder.id, folder.name);
      console.log('Full folder object:', folder);

      // If it's the same folder, toggle its open state
      if (selectedFolder.value && selectedFolder.value.id === folder.id) {
        toggleFolderOpen(folder.id);
      } else {      // New folder selection
        selectedFolder.value = folder;
        selectedItemId.value = folder.id;
        selectedItemType.value = 'folder';

        console.log('selectedFolder.value after assignment:', selectedFolder.value);
        console.log('selectedFolder.value.book_id:', selectedFolder.value.book_id);

        // Clear search query when changing folders
        searchInFolderQuery.value = '';

        // Clear checked items when changing folders
        checkedItems.value = [];

        // Clear navigator selected items when changing folders
        navigatorSelectedItems.value = [];

        // Clear any open folder dropdown
        showPathDropdown.value = false;        // Sync folder path in the navigator
        syncFolderPath(folder);
        console.log(`Folder selected: ${folder.name} (ID: ${folder.id})`);

        // Load notes for this folder
        loadNotesForFolder(folder.id);
      }
    };

    // Toggle folder open/closed state
    const toggleFolderOpen = (folderOrId: FolderWithMeta | number) => {
      // Handle either a folder object or just an ID
      const folderId = typeof folderOrId === 'number' ? folderOrId : folderOrId.id;

      if (!folderId) return;

      const index = openFolders.value.indexOf(folderId);
      if (index === -1) {
        // Open the folder
        const folderToOpen = typeof folderOrId === 'number'
          ? folders.value.find(f => f.id === folderOrId)
          : folderOrId;

        // If folder has children, find all other open folders at the same level or higher
        // and close them to maintain horizontal space
        if (folderToOpen && folderToOpen.parent_id !== null && folderToOpen.parent_id !== undefined) {
          // Keep track of the path to the root to keep open
          const pathToRoot: number[] = [];
          let currentFolder: FolderWithMeta | undefined = folderToOpen;

          // Build the path from this folder up to the root
          while (currentFolder && currentFolder.parent_id !== null && currentFolder.parent_id !== undefined) {
            pathToRoot.push(currentFolder.parent_id);
            const foundFolder = folders.value.find(f => f.id === currentFolder!.parent_id);
            currentFolder = foundFolder || undefined;
          }

          // Filter open folders to keep only those in the current path
          openFolders.value = openFolders.value.filter(openFolderId => {
            // Keep folders that are in the path to root
            return pathToRoot.includes(openFolderId);
          });
        }
        // Then open this folder
        openFolders.value.push(folderId);
      } else {
        // Close the folder
        openFolders.value.splice(index, 1);
      }
    };

    // Sync folder path to ensure the navigator shows the correct path to the selected folder
    const syncFolderPath = (folder: FolderWithMeta | null) => {
      if (!folder || folder.id === null) {
        // For root folder, just clear the openFolders
        openFolders.value = [];
        return;
      }

      // Build the path from this folder up to the root
      const pathToRoot: number[] = [];
      if (folder.parent_id !== null && folder.parent_id !== undefined) {
        let currentParentId: number | null = folder.parent_id;
        const maxDepth = 100; // Safety limit for deeper nesting
        let depth = 0;

        // Record all parent folders in the path
        while (currentParentId !== null && depth < maxDepth) {
          pathToRoot.push(currentParentId);

          // Find the parent folder and continue up the chain
          const parentFolder = folders.value.find(f => f.id === currentParentId);
          if (!parentFolder) {
            console.warn(`Parent folder with ID ${currentParentId} not found while syncing path.`);
            break;
          }

          currentParentId = parentFolder.parent_id !== undefined ? parentFolder.parent_id : null;
          depth++;
        }
      }

      // Update open folders to include the current folder and all its parents
      openFolders.value = [...pathToRoot, folder.id];
      console.log('Synced folder path in navigator:', openFolders.value);
    };

    // Create a new folder
    const createNewFolder = async (parentId: number | null) => {
      try {
        // Ensure parentId is a number or null
        const normalizedParentId = parentId === undefined ? null :
          typeof parentId === 'number' ? parentId :
            parentId && !isNaN(Number(parentId)) ? Number(parentId) : null;

        const newFolder = {
          name: 'New Folder', // Default name, will be changed by modal
          parent_id: normalizedParentId,
          skipBackupEvent: true // Skip initial backup event since we'll rename immediately
        };

        const createdFolder = await db.folders.create(newFolder);
        folders.value.push(createdFolder);
        folderStats.value.folderCount = folders.value.length;

        // Update hierarchy
        await updateFolderHierarchy();

        // If this is a subfolder, ensure the parent folder is open and
        // use the same logic to close other branches when opening this folder
        if (parentId !== null) {
          // Build the path from this parent folder up to the root
          const pathToRoot: number[] = [];
          let currentParentId: number | null | undefined = parentId;
          const maxDepth = 10; // Safety limit
          let depth = 0;

          // Record all parent folders in the path
          while (currentParentId !== null && currentParentId !== undefined && depth < maxDepth) {
            if (currentParentId !== parentId) { // Don't include the immediate parent in pathToRoot
              pathToRoot.push(currentParentId);
            }

            // Find the parent's parent and continue up the chain
            const parentFolder = folders.value.find(f => f.id === currentParentId);
            if (!parentFolder) break;

            currentParentId = parentFolder.parent_id;
            depth++;
          }

          // Filter open folders to keep only those in the current path
          openFolders.value = openFolders.value.filter(openFolderId => {
            // Keep the immediate parent and folders that are in the path to root
            return openFolderId === parentId || pathToRoot.includes(openFolderId);
          });

          // Make sure the parent folder is open
          if (!openFolders.value.includes(parentId)) {
            openFolders.value.push(parentId);
          }
        }

        folderToRename.value = null; // Clear any inline rename

        // Show the new name folder modal instead of inline rename
        folderToNameInitial.value = createdFolder;
        showNameFolderModal.value = true;

        // We will select the folder after it's named.
      } catch (error) {
        console.error("Error creating new folder:", error);
      }
    };

    // Create a subfolder within an existing folder
    const createSubfolder = (parentFolder: FolderWithMeta | number | null) => {
      if (!parentFolder) {
        console.error('Invalid parent folder for subfolder creation');
        return;
      }

      // Ensure we have a valid parent ID (must be a number)
      const parentId = typeof parentFolder === 'object' && parentFolder !== null
        ? (typeof parentFolder.id === 'number' ? parentFolder.id : null)
        : (typeof parentFolder === 'number' ? parentFolder : null);

      if (parentId === null) {
        console.error('Invalid parent folder ID for subfolder creation');
        return;
      }

      console.log('Creating subfolder in parent:', parentId,
        typeof parentFolder === 'object' ? parentFolder.name : 'Unknown');

      createNewFolder(parentId);
    };

    // Delete folder functionality
    const deleteFolder = async (folder: FolderWithMeta) => {
      if (!folder) return;

      // Set up for deletion with modal
      foldersToDelete.value = [folder];

      // If the folder has notes, try to load their count
      if (!folder.notesCount) {
        try {
          const notes = await db.notes.getByFolderId(folder.id);
          folder.notesCount = notes.length;
        } catch (error) {
          console.warn(`Could not load notes count for folder ${folder.id}`, error);
          folder.notesCount = 0;
        }
      }

      showDeleteFolderModal.value = true;
    };

    // Get available target folders for moving notes (excluding the folders being deleted)
    const availableTargetFolders = computed(() => {
      return folders.value.filter(f => !foldersToDelete.value.some(fd => fd.id === f.id));
    });

    // Rename folder functionality
    const renameFolder = (folder: FolderWithMeta) => {
      folderToRename.value = folder;
      newFolderName.value = folder.name;
      showRenameModal.value = true;

      // Focus the input after rendering
      setTimeout(() => {
        if (renameInputRef.value) {
          renameInputRef.value.focus();
          renameInputRef.value.select();
        }
      }, 50);
    };

    // Track if any modal is currently open
    const isAnyModalOpen = computed(() => {
      return showDeleteFolderModal.value ||
        showDeleteNoteModal.value ||
        showDeleteNotesModal.value ||
        showDeleteMixedItemsModal.value ||
        showRenameModal.value ||
        showRenameNoteModal.value ||
        showExportModal.value ||
        showSortModal.value ||
        showMoveModal.value ||
        showNameFolderModal.value ||
        showColorModal.value;
    });

    // Helper function to check if a folder is protected (Books folder)
    const isProtectedFolder = (folder: FolderWithMeta | null): boolean => {
      if (!folder) return false;
      // Check if this is the "Books" root folder
      return folder.name === 'Books' && folder.parent_id === null;
    };

    // Check if the currently selected folder or any checked folder is protected
    const isProtectedFolderSelected = computed(() => {
      // Check if the currently selected folder is protected
      if (selectedFolder.value && isProtectedFolder(selectedFolder.value)) {
        return true;
      }

      // Check if any checked items include a protected folder
      const hasProtectedInCheckedItems = checkedItems.value.some(item => {
        if (item.type === 'folder') {
          const folder = folders.value.find(f => f.id === item.id);
          return folder && isProtectedFolder(folder);
        }
        return false;
      });

      // Check if any navigator selected items include a protected folder
      const hasProtectedInNavigatorItems = navigatorSelectedItems.value.some(itemId => {
        const folder = folders.value.find(f => f.id === itemId);
        return folder && isProtectedFolder(folder);
      });

      return hasProtectedInCheckedItems || hasProtectedInNavigatorItems;
    });

    const cancelRename = () => {
      folderToRename.value = null;
      showRenameModal.value = false;
    };

    const confirmRename = async () => {
      if (!folderToRename.value || !newFolderName.value.trim()) return;

      try {
        const updatedFolder = await db.folders.update(
          folderToRename.value.id,
          { name: newFolderName.value.trim() }
        );

        // Update the folder in our list
        const index = folderToRename.value ? folders.value.findIndex(f => f.id === folderToRename.value!.id) : -1;
        if (index !== -1) {
          folders.value[index] = updatedFolder;

          // If this was the selected folder, update the selection
          if (selectedFolder.value && selectedFolder.value.id === folderToRename.value!.id) {
            selectedFolder.value = updatedFolder;
          }
        }

        // Update the folder hierarchy to reflect the changes
        await updateFolderHierarchy();

        showRenameModal.value = false;
        folderToRename.value = null;
      } catch (error) {
        console.error("Error renaming folder:", error);
      }
    };

    // Inline rename folder functionality
    const inlineRenameFolder = (folder: FolderWithMeta) => {
      // First clear the value to ensure the watcher triggers even if it's the same folder
      folderToRename.value = null;

      // Set a small delay to ensure the clearing is processed
      setTimeout(() => {
        folderToRename.value = folder;
      }, 10);
      // No need to show modal, we'll edit inline
    };

    const saveInlineFolderName = async (folder: FolderWithMeta, newName: string) => {
      if (!folder || !newName.trim()) return;

      try {
        const updatedFolder = await db.folders.update(
          folder.id,
          { name: newName.trim() }
        );

        // Update the folder in our list
        const index = folders.value.findIndex(f => f.id === folder.id);
        if (index !== -1) {
          folders.value[index] = updatedFolder;

          // If this was the selected folder, update the selection
          if (selectedFolder.value && selectedFolder.value.id === folder.id) {
            selectedFolder.value = updatedFolder;
          }
        }

        // Update the folder hierarchy to reflect the changes
        await updateFolderHierarchy();

        // Clear the folder being renamed
        folderToRename.value = null;
      } catch (error) {
        console.error("Error renaming folder:", error);
      }
    };

    // Note renaming functions
    const cancelNoteRename = () => {
      noteToRename.value = null;
      showRenameNoteModal.value = false;
    };

    const confirmNoteRename = async () => {
      if (!noteToRename.value || !newNoteName.value.trim()) return;

      try {
        const updatedNote = await db.notes.update(
          noteToRename.value.id as number,
          { title: newNoteName.value.trim() }
        );

        // If this is a note in the current folder, update it in the folderNotes list
        if (selectedFolder.value && noteToRename.value.folder_id === selectedFolder.value.id) {
          const index = folderNotes.value.findIndex(n => n.id === noteToRename.value?.id);
          if (index !== -1) {
            folderNotes.value[index] = updatedNote;
          }
        }

        showRenameNoteModal.value = false;
        noteToRename.value = null;

        // Refresh notes list
        if (selectedFolder.value) {
          await loadNotesForFolder(selectedFolder.value.id);
        }
      } catch (error) {
        console.error("Error renaming note:", error);
      }
    };

    // Get folder path
    const getFolderPath = (folder: any): { id: number; name: string }[] => {
      if (!folder) {
        console.log('getFolderPath called with null folder, returning empty path');
        return [];
      }

      // Defensive check for proper folder object
      if (typeof folder !== 'object' || !('id' in folder) || !('name' in folder)) {
        console.warn('Invalid folder object passed to getFolderPath', folder);
        return [];
      }

      // Handle invalid folder ID 0
      if (folder.id === 0) {
        console.warn('Folder ID 0 is invalid, returning empty path');
        return [];
      }

      try {
        // Build an array of folder objects with id and name
        const pathFolders: { id: number; name: string }[] = [{ id: folder.id, name: folder.name }];

        // If parent_id is null or undefined, it's a root folder
        if (folder.parent_id === null || folder.parent_id === undefined) {
          console.log(`Path for folder "${folder.name}" (ID: ${folder.id}): Root folder, no parent`);
          return pathFolders;
        }

        // Otherwise, build the path recursively
        let currentParentId = folder.parent_id;
        let maxDepth = 100; // Prevent infinite loops, increased from 10 to allow deeper paths
        let depth = 0;

        console.log(`Building path for folder "${folder.name}" (ID: ${folder.id}), starting with parent_id: ${currentParentId}`);

        while (currentParentId !== null && currentParentId !== undefined && maxDepth > 0) {
          const parentFolder = folders.value.find(f => f.id === currentParentId);

          if (!parentFolder) {
            console.warn(`Parent folder with ID ${currentParentId} not found while building path for folder ${folder.id} (${folder.name})`);
            break;
          }

          // Add parent folder to the beginning of our path array
          pathFolders.unshift({ id: parentFolder.id, name: parentFolder.name });

          console.log(`Added parent folder "${parentFolder.name}" (ID: ${parentFolder.id}) to path`);

          // Move up to the next parent
          currentParentId = parentFolder.parent_id;
          maxDepth--;
          depth++;
        }

        console.log(`Complete path for folder "${folder.name}" (${pathFolders.length} levels deep):`,
          pathFolders.map(f => f.name).join(' > '));

        // Ensure we're returning a consistent format for all path calculations
        return pathFolders;
      } catch (error) {
        console.error(`Error building folder path for folder ${folder?.id} (${folder?.name}):`, error);
        // Return at least the current folder if available
        if (folder && folder.id !== undefined && folder.name) {
          return [{ id: folder.id, name: folder.name }];
        }
        return [];
      }
    };

    // Folder path dropdown state
    const showPathDropdown = ref(false);
    // Document click handler reference to prevent memory leaks
    let documentClickHandler: ((evt: MouseEvent) => void) | null = null;
    // Toggle path dropdown
    const togglePathDropdown = (event: MouseEvent) => {
      // Prevent event propagation to avoid immediate closing
      event.stopPropagation();
      event.preventDefault();

      // First, clean up any existing handler
      if (documentClickHandler) {
        document.removeEventListener('click', documentClickHandler, true);
        documentClickHandler = null;
      } showPathDropdown.value = !showPathDropdown.value;

      // If opening dropdown, position it correctly
      if (showPathDropdown.value && event) {
        const element = event.currentTarget as HTMLElement;
        if (element) {
          const rect = element.getBoundingClientRect();

          // Use requestAnimationFrame for better timing with DOM updates
          requestAnimationFrame(() => {
            const dropdownElement = document.querySelector('.folder-path-dropdown') as HTMLElement;
            if (dropdownElement) {
              // Calculate optimal position with offset
              const offset = 8; // Slight offset for visual appeal
              dropdownElement.style.position = 'fixed';
              dropdownElement.style.top = `${rect.bottom + offset}px`;
              dropdownElement.style.left = `${rect.left - 5}px`; // Slight left adjustment

              // Make sure it's visible by increasing its z-index
              dropdownElement.style.zIndex = '10000';

              // Check if dropdown would go off-screen and adjust if needed
              const viewportWidth = window.innerWidth;
              const viewportHeight = window.innerHeight;
              const dropdownRect = dropdownElement.getBoundingClientRect();

              // Right edge check
              if (dropdownRect.right > viewportWidth - 10) {
                // Adjust to stay within viewport with a 10px margin
                dropdownElement.style.left = `${viewportWidth - dropdownRect.width - 10}px`;
              }

              // Bottom edge check
              if (dropdownRect.bottom > viewportHeight) {
                // If it would go off the bottom, place it above the element instead
                const heightWithOffset = dropdownRect.height + offset * 2;
                if (rect.top > heightWithOffset) {
                  // Enough space above, so place it above
                  dropdownElement.style.top = `${rect.top - dropdownRect.height - offset}px`;
                } else {
                  // Not enough space above, reduce the height to fit
                  const availableHeight = viewportHeight - rect.bottom - offset * 2;
                  const content = dropdownElement.querySelector('.path-dropdown-content') as HTMLElement;
                  if (content) {
                    content.style.maxHeight = `${availableHeight - 20}px`; // 20px for padding
                  }
                }
              }
            }            // Create a new click handler function with capture phase to ensure it runs before other handlers
            documentClickHandler = (evt: MouseEvent) => {
              // Get the clicked element
              const target = evt.target as HTMLElement;

              // Check if click is inside the dropdown or on the truncated path element
              const isClickInsideDropdown = target.closest('.folder-path-dropdown') !== null;
              const isClickOnTruncated = target.closest('.folder-path-truncated') !== null;
              const isPathItem = target.closest('.path-dropdown-item') !== null;

              // If clicking on a path item in the dropdown, let the selection happen
              // The selection handler will close the dropdown
              if (isPathItem) {
                return;
              }

              // If clicking outside both elements, close the dropdown
              if (!isClickInsideDropdown && !isClickOnTruncated) {
                // Close the dropdown
                showPathDropdown.value = false;

                // Clean up the event listener
                if (documentClickHandler) {
                  document.removeEventListener('click', documentClickHandler, true);
                  documentClickHandler = null;
                }
              }
            };

            // Use setTimeout to avoid handling the same click event that opened the dropdown
            // Use the capture phase to ensure our handler runs before other click handlers
            setTimeout(() => {
              if (documentClickHandler) {
                document.addEventListener('click', documentClickHandler, true);
              }
            }, 10);
          });
        }
      }
    };

    // Calculate truncated path count consistently across the application
    const calculateTruncatedCount = (path: any[]) => {
      // Standard calculation: Don't count first (root) and last (current) folders
      return path.length - 2;
    };

    // Process folder path for display with truncation when needed
    const getPathForDisplay = (folder: any) => {
      if (!folder) {
        console.log('getPathForDisplay called with null folder, returning empty path');
        return {
          truncated: false,
          path: []
        };
      }

      try {
        // Get the full path
        const path = getFolderPath(folder);

        console.log(`Formatting path for display for folder "${folder.name}" (ID: ${folder.id}), path length: ${path.length}`);

        // If path has 0 items (shouldn't happen) or just 1 item (current folder only),
        // no need to truncate
        if (path.length <= 2) {
          console.log(`Path for folder "${folder.name}" is short (${path.length} items), no truncation needed`);
          return {
            truncated: false,
            path
          };
        }

        // Otherwise, truncate the middle parts
        const truncatedCount = calculateTruncatedCount(path);

        console.log(`Path for folder "${folder.name}" is being truncated, hiding ${truncatedCount} middle items`);
        console.log(`First folder: ${path[0].name} (ID: ${path[0].id})`);
        console.log(`Last folder: ${path[path.length - 1].name} (ID: ${path[path.length - 1].id})`);

        return {
          truncated: true,
          truncatedCount,
          lastFolder: path[path.length - 1],
          firstFolder: path[0],
          fullPath: path // Include full path for reference
        };
      } catch (error) {
        console.error(`Error formatting path for display for folder ${folder?.id} (${folder?.name}):`, error);
        // Return a safe default value
        return {
          truncated: false,
          path: folder ? [{ id: folder.id, name: folder.name }] : []
        };
      }
    };

    // Get folder notes
    const getFolderNotes = (folder: any): any[] => {
      if (!folder) return [];
      return folderNotes.value.filter(note => note.folder_id === folder.id) || [];
    };

    // Format date for display
    const formatDate = (dateString?: string): string => {
      if (!dateString) return '';

      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    };

    // Load notes for the selected folder
    const loadNotesForFolder = async (folderId: number) => {
      if (folderId === undefined || folderId === null) {
        console.warn('Invalid folder ID passed to loadNotesForFolder, use loadRootNotes for root level');
        return;
      }

      try {
        console.log(`Loading notes for folder ID: ${folderId}`);
        const notes = await db.notes.getByFolderId(folderId);
        folderNotes.value = notes;
        console.log(`Loaded ${notes.length} notes for folder ${folderId}`);
      } catch (error) {
        console.error(`Error loading notes for folder ${folderId}:`, error);
        folderNotes.value = [];
      }
    };

    // Load root level notes
    const loadRootNotes = async () => {
      try {
        // Use the getAll method first to get all notes
        const allNotes = await db.notes.getAll();

        // Filter for only those with null folder_id to get root notes
        folderNotes.value = allNotes.filter(note => note.folder_id === null) || [];

        // Also populate notes.value to keep master list in sync and prevent stale data
        notes.value = allNotes;

        console.log(`Loaded ${folderNotes.value.length} root notes`);
      } catch (error) {
        console.error('Error loading root notes:', error);
        folderNotes.value = [];
      }
    };    // Get both folders and notes for the selected folder
    const getFolderItems = (folder: FolderWithMeta | null): Array<any> => {
      try {
        // Use a simple cache to prevent redundant calculations
        const cacheKey = folder ? `folder_${folder.id}_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`
          : `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;

        // Always log for root folder to help with debugging
        if (!folder) {
          console.log('Getting ROOT folder items with sorting:', {
            by: currentSortBy.value,
            direction: currentSortDirection.value,
            foldersFirst: currentSortFoldersFirst.value
          });
        }
        // For non-root folders, only log if not cached yet
        else if (!folderItemsCache.value[cacheKey]) {
          console.log('Getting folder items with sorting:', {
            by: currentSortBy.value,
            direction: currentSortDirection.value,
            foldersFirst: currentSortFoldersFirst.value,
            folder: `${folder.id} (${folder.name})`
          });
        }

        // Return cached result if available
        if (folderItemsCache.value[cacheKey]) {
          return folderItemsCache.value[cacheKey];
        }

        const items: Array<any> = [];

        if (!folder) {
          // We're at the root level, get all root folders and notes
          console.log('Preparing ROOT folder items display');
          const rootFolders = folders.value.filter(f => f.parent_id === null);
          console.log(`Found ${rootFolders.length} root folders to display`);

          rootFolders.forEach(rootFolder => {
            items.push({
              ...rootFolder,
              type: 'folder',
              hasChildren: folders.value.some(f => f.parent_id === rootFolder.id)
            });
          });

          // Add root notes (notes without a folder_id)
          // Use folderNotes.value for root notes since loadRootNotes() populates it with fresh data
          // notes.value may be stale after navigation from deep folders
          const rootNotes = folderNotes.value || [];
          console.log(`Found ${rootNotes.length} root notes to display`);

          rootNotes.forEach(note => {
            items.push({
              ...note,
              type: 'note',
              folderPath: 'All Folders'
            });
          });
        } else {
          // Add subfolders with the proper type
          const subfolders = folders.value.filter(f => f.parent_id === folder.id);
          subfolders.forEach(subfolder => {
            items.push({
              ...subfolder,
              type: 'folder',
              hasChildren: folders.value.some(f => f.parent_id === subfolder.id)
            });
          });

          // Add notes with the proper type
          const folderNoteItems = getFolderNotes(folder);
          folderNoteItems.forEach(note => {
            items.push({
              ...note,
              type: 'note',
              folderPath: folder.name
            });
          });
        }

        // Apply current sorting to the items
        const isAscending = currentSortDirection.value !== 'descending';
        const foldersFirst = currentSortFoldersFirst.value;

        // If folders should be displayed first, separate folders and files, sort each, then combine
        if (foldersFirst) {
          const folderItems = items.filter(item => item.type === 'folder');
          const noteItems = items.filter(item => item.type === 'note' || item.type === 'file');

          // Sort folders
          folderItems.sort((a, b) => {
            let comparison = 0;
            switch (currentSortBy.value) {
              case 'name':
                comparison = a.name.localeCompare(b.name);
                break;
              case 'date': // Sort by created date
                comparison = new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
                break;
              case 'modified': // Sort by updated date
                comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
                break;
              case 'type':
                // Folders are all same type, so fall back to name
                comparison = a.name.localeCompare(b.name);
                break;
              case 'size':
                // For folders, we could use notesCount as a rough size indicator
                comparison = (a.notesCount || 0) - (b.notesCount || 0);
                break;
              default:
                comparison = a.name.localeCompare(b.name);
            }
            return isAscending ? comparison : -comparison;
          });

          // Sort notes
          noteItems.sort((a, b) => {
            let comparison = 0;
            switch (currentSortBy.value) {
              case 'name':
                comparison = (a.title || '').localeCompare(b.title || '');
                break;
              case 'date': // Sort by created date
                comparison = new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
                break;
              case 'modified': // Sort by updated date
                comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
                break;
              case 'size':
                // Approximate size based on content length
                comparison = (a.content?.length || 0) - (b.content?.length || 0);
                break;
              case 'type':
                // For notes, we could sort by file extension or kind
                comparison = (a.file_extension || '').localeCompare(b.file_extension || '');
                break;
              default:
                comparison = (a.title || '').localeCompare(b.title || '');
            }
            return isAscending ? comparison : -comparison;
          });

          // Return folders first, then notes
          return [...folderItems, ...noteItems];
        } else {
          // Sort all items together
          return items.sort((a, b) => {
            let comparison = 0;

            // Sort by type first (if that's the selected criteria)
            if (currentSortBy.value === 'type') {
              // Compare types first (folder vs note)
              comparison = a.type.localeCompare(b.type);
              if (comparison !== 0) return isAscending ? comparison : -comparison;

              // If same type, then sort by name
              if (a.type === 'folder') {
                return isAscending ?
                  a.name.localeCompare(b.name) :
                  b.name.localeCompare(a.name);
              } else {
                return isAscending ?
                  (a.title || '').localeCompare(b.title || '') :
                  (b.title || '').localeCompare(a.title || '');
              }
            }

            // For other criteria, handle differently based on item type
            if (a.type === 'folder' && b.type === 'folder') {
              // Both are folders
              switch (currentSortBy.value) {
                case 'name':
                  comparison = a.name.localeCompare(b.name);
                  break;
                case 'date':
                  comparison = new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
                  break;
                case 'modified':
                  comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
                  break;
                case 'size':
                  comparison = (a.notesCount || 0) - (b.notesCount || 0);
                  break;
                default:
                  comparison = a.name.localeCompare(b.name);
              }
            } else if (a.type !== 'folder' && b.type !== 'folder') {
              // Both are notes/files
              switch (currentSortBy.value) {
                case 'name':
                  comparison = (a.title || '').localeCompare(b.title || '');
                  break;
                case 'date':
                  comparison = new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
                  break;
                case 'modified':
                  comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
                  break;
                case 'size':
                  comparison = (a.content?.length || 0) - (b.content?.length || 0);
                  break;
                default:
                  comparison = (a.title || '').localeCompare(b.title || '');
              }
            } else {
              // One folder, one note - just compare by the selected criteria
              switch (currentSortBy.value) {
                case 'name':
                  comparison = (a.name || a.title || '').localeCompare(b.name || b.title || '');
                  break;
                case 'date':
                  comparison = new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
                  break;
                case 'modified':
                  comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
                  break;
                case 'size':
                  // Not a great comparison between folders and notes, fall back to type
                  comparison = a.type.localeCompare(b.type);
                  break;
                default:
                  comparison = (a.name || a.title || '').localeCompare(b.name || b.title || '');
              }
            } return isAscending ? comparison : -comparison;
          });
        }
        // Cache the sorted items
        folderItemsCache.value[cacheKey] = items;
        return items;
      } catch (error) {
        console.error('Error getting folder items:', error);
        // Return empty array on error
        return [];
      }
    };  // Handle selection of items from the drill-down navigator or folder content
    const handleSelectItem = (item: { id?: number, name?: string, type?: string, parent_id?: number | null, source?: string } | null) => {
      if (!item) {
        console.log('handleSelectItem called with null item, ignoring');
        return;
      }

      console.log('handleSelectItem called with:', {
        itemId: item.id,
        itemType: item.type,
        itemName: item.name,
        source: item.source
      });

      // Clear the current checked items when selecting a new item
      checkedItems.value = [];

      if (item.type === 'folder') {
        // DON'T add folder to checkedItems when navigating - only when explicitly selecting for actions
        // Remove this line: checkedItems.value = [{ type: 'folder', id: item.id }];

        // For folders, use existing selectFolder function
        // Find the complete folder object to ensure we have all metadata
        const fullFolder = folders.value.find(f => f.id === item.id);

        if (fullFolder) {
          // Update selected state before selecting folder
          selectedItemId.value = item.id ?? null;
          selectedItemType.value = 'folder';

          // DON'T update checked items for navigation - keep them clear
          // checkedItems.value = [{ type: 'folder', id: item.id as number }]; // <-- REMOVE THIS LINE

          // Use the full folder object with all metadata
          selectFolder(fullFolder).catch(err => console.error('Error selecting folder:', err));
        } else {
          // Create a minimal FolderWithMeta if full folder not found
          // Only create if we have a valid ID
          if (item.id === null || item.id === undefined) {
            // Handle root folder case
            console.log('Navigating to root folder via handleSelectItem');
            selectedFolder.value = null;
            selectedItemId.value = null;
            selectedItemType.value = 'folder';
            searchInFolderQuery.value = '';

            // Clear checked items when changing folders - already done above
            // checkedItems.value = []; // Already cleared above

            // Clear navigator selected items when changing folders
            navigatorSelectedItems.value = [];

            // Load root notes and clear cache to ensure fresh content
            loadRootNotes().then(() => {
              const cacheKey = `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
              delete folderItemsCache.value[cacheKey];
              console.log('Root folder content cache refreshed');
            }).catch(err => console.error('Error loading root notes:', err));
          } else {
            // Fetch the complete folder data from the database to ensure
            // we have all the information needed for proper breadcrumb path display
            console.log(`Fetching complete folder data for ID ${item.id} to ensure breadcrumb sync`);

            db.folders.getById(item.id)
              .then(folderData => {
                if (folderData) {
                  console.log('Successfully fetched folder data for breadcrumb sync:', folderData);
                  // Create a proper FolderWithMeta with the full data
                  const folderWithMeta: FolderWithMeta = {
                    ...folderData,
                    children: [],
                    notesCount: 0,
                    childFoldersCount: 0
                  };
                  // Use the selectFolder method with the complete folder data
                  return selectFolder(folderWithMeta);
                } else {
                  console.warn(`Folder with ID ${item.id} not found in database, using minimal data`);
                  // Fallback to minimal data if we can't get the full folder
                  const minimalFolder: FolderWithMeta = {
                    id: item.id as number,
                    name: item.name || '',
                    parent_id: item.parent_id ?? null,
                    children: [],
                    notesCount: 0,
                    childFoldersCount: 0,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };

                  // Update selected state before selecting folder
                  selectedItemId.value = item.id ?? null;
                  selectedItemType.value = 'folder';

                  return selectFolder(minimalFolder);
                }
              })
              .catch(err => {
                console.error(`Error fetching folder data for ID ${item.id}:`, err);
                // Fallback to minimal data on error
                const minimalFolder: FolderWithMeta = {
                  id: item.id as number,
                  name: item.name || '',
                  parent_id: item.parent_id ?? null,
                  children: [],
                  notesCount: 0,
                  childFoldersCount: 0,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                };

                // Update selected state before selecting folder
                selectedItemId.value = item.id ?? null;
                selectedItemType.value = 'folder';

                selectFolder(minimalFolder).catch(e => console.error('Error selecting folder with minimal data:', e));
              });
          }
        }
      } else if (item.type === 'file' || item.type === 'note') {
        // For notes/files, set the selection state
        selectedItemType.value = 'note';
        selectedItemId.value = item.id ?? null;

        // Keep folder context active but update note selection
        console.log('Selected note:', item.id, item.name);

        // When selecting a note, also check it in the checkedItems
        const existingCheckIndex = checkedItems.value.findIndex(
          checked => checked.type === 'note' && checked.id === item.id
        );

        // Clear existing selection and add this note
        checkedItems.value = [{ type: 'note', id: item.id as number | string }];

        // Open the note in NotesView
        openNoteInNotesView(item.id as number);
      }
    };    // Handle checked items changes from FolderContent
    const handleCheckedItemsChange = (items: { type: string, id: number | string }[]) => {
      console.log('FoldersView received checkedItemsChange:', items);

      if (items.length === 0 && checkedItems.value.length === 0) {
        console.log('Both arrays empty, skipping update');
        return;
      }

      // More robust comparison to prevent unnecessary updates
      if (items.length === checkedItems.value.length) {
        // Check if arrays contain the same items (regardless of order)
        const currentIds = new Set(checkedItems.value.map(item => `${item.type}-${item.id}`));
        const allMatch = items.every(item => currentIds.has(`${item.type}-${item.id}`));

        if (allMatch) {
          console.log('Items unchanged (same content), skipping update');
          return;
        }
      }

      // Create a new array to avoid reference issues
      checkedItems.value = [...items];
      console.log('Checked items updated in FoldersView:', checkedItems.value);
    };// Handle selection changes from the FolderNavigator
    const handleNavigatorSelectionChange = (selectedIds: (number | string)[]) => {
      // Update the navigatorSelectedItems state
      navigatorSelectedItems.value = selectedIds;

      // Convert the selected IDs to checked items format
      // const folderItems = selectedIds.map(id => ({ type: 'folder', id }));
      // handleCheckedItemsChange(folderItems);
    };

    // Handle deleting selected folders from the FolderNavigator
    const handleDeleteSelectedFolders = (selectedIds: (number | string)[]) => {
      // Convert the selected IDs to folder objects
      const foldersToDeleteList = selectedIds
        .map(id => folders.value.find(f => f.id === id))
        .filter(Boolean) as FolderWithMeta[];

      if (foldersToDeleteList.length > 0) {
        // Set the folders to delete
        foldersToDelete.value = foldersToDeleteList;

        // Show the appropriate delete modal
        if (foldersToDeleteList.length === 1) {
          console.log('Showing single folder delete modal', foldersToDeleteList[0]);
          showDeleteFolderModal.value = true;
        } else {
          console.log('Showing multiple folders delete modal', foldersToDeleteList);
          showDeleteFolderModal.value = true;
        }
      }
    };

    // Delete checked items
    const deleteCheckedItems = async () => {
      try {
        // Reset previous state
        foldersToDelete.value = [];
        notesToDelete.value = [];
        mixedItemsToDelete.value = [];

        // Group items by type for batch operations
        const folderIds = checkedItems.value
          .filter(item => item.type === 'folder')
          .map(item => item.id);

        const noteIds = checkedItems.value
          .filter(item => item.type === 'note')
          .map(item => item.id);

        // Collect folder data
        if (folderIds.length > 0) {
          const foundFolders = folderIds
            .map(id => folders.value.find(f => f.id === id))
            .filter(Boolean) as FolderWithMeta[];

          foldersToDelete.value = foundFolders;
        }

        // Collect note data
        if (noteIds.length > 0) {
          const foundNotes = await Promise.all(
            noteIds.map(async id => {
              try {
                // Make sure we're using getById method as defined in the API
                return await db.notes.getById(Number(id));
              } catch (err) {
                console.error(`Error fetching note with ID ${id}:`, err);
                return null;
              }
            })
          );

          notesToDelete.value = foundNotes.filter(Boolean) as Note[];
          noteTitles.value = notesToDelete.value.map(note => note.title || 'Untitled Note');

          if (notesToDelete.value.length === 1) {
            noteTitle.value = notesToDelete.value[0].title || 'Untitled Note';
          }
        }

        // Determine which modal to show
        if (folderIds.length > 0 && noteIds.length > 0) {
          // Mixed items case
          mixedItemsToDelete.value = [
            ...foldersToDelete.value.map(f => ({ type: 'folder', name: f.name, id: f.id })),
            ...notesToDelete.value.map(n => ({ type: 'note', name: n.title || 'Untitled Note', id: n.id }))
          ];
          console.log('Showing mixed items delete modal', mixedItemsToDelete.value);
          showDeleteMixedItemsModal.value = true;
        } else if (folderIds.length === 1) {
          // Single folder case
          console.log('Showing single folder delete modal', foldersToDelete.value[0]);
          showDeleteFolderModal.value = true;
        } else if (folderIds.length > 1) {
          // Multiple folders case
          console.log('Showing multiple folders delete modal', foldersToDelete.value);
          showDeleteFolderModal.value = true;
        } else if (noteIds.length === 1) {
          // Single note case
          console.log('Showing single note delete modal', noteTitle.value);
          showDeleteNoteModal.value = true;
        } else if (noteIds.length > 1) {
          // Multiple notes case
          console.log('Showing multiple notes delete modal', noteTitles.value);
          showDeleteNotesModal.value = true;
        }
      } catch (error) {
        console.error('Error processing items for deletion:', error);
      }
    };

    // Rename checked item - only if one item is selected
    const renameCheckedItem = () => {
      try {
        if (checkedItems.value.length !== 1) {
          console.log('Can only rename one item at a time');
          return;
        }

        const item = checkedItems.value[0];
        if (item.type === 'folder') {
          const folder = folders.value.find(f => f.id === item.id);
          if (folder) {
            renameFolder(folder);
          }
        } else if (item.type === 'note') {
          console.log('Rename note:', item.id);
          // Get the note from the appropriate source based on current context
          let foundNote = null;

          if (selectedFolder.value) {
            // In a folder: look in folderNotes.value
            foundNote = folderNotes.value.find(note => note.id === item.id);
          } else {
            // At root level: look in notes.value (which contains all notes)
            foundNote = notes.value.find(note => note.id === item.id);
          }

          if (foundNote) {
            noteToRename.value = foundNote;
            newNoteName.value = foundNote.title || 'Untitled Note';
            showRenameNoteModal.value = true;

            // Focus the input after rendering
            setTimeout(() => {
              if (renameInputRef.value) {
                renameInputRef.value.focus();
                renameInputRef.value.select();
              }
            }, 50);
          } else {
            console.warn('Note not found in current context:', item.id);
            console.log('Available folderNotes:', folderNotes.value.map(n => n.id));
            console.log('Available notes:', notes.value.map(n => n.id));
          }
        }
      } catch (error) {
        console.error('Error renaming item:', error);
      }
    };

    // Color checked items - only works for folders
    const colorCheckedItems = () => {
      try {
        // Filter to only include folders
        const folderItems = checkedItems.value.filter(item => item.type === 'folder');

        if (folderItems.length === 0) {
          console.log('No folders selected to color');
          return;
        }

        // Find the actual folder objects
        const actualFolders = folderItems.map(item =>
          folders.value.find(f => f.id === item.id)
        ).filter((folder): folder is FolderWithMeta => folder !== undefined); // Type-safe filter

        if (actualFolders.length === 0) {
          console.error('Could not find folder objects for selected items');
          return;
        }

        if (actualFolders.length === 1) {
          // Single folder - show color modal for that folder
          folderToColor.value = actualFolders[0];
          showColorModal.value = true;
        } else {
          // Multiple folders - use the first one as reference for current color
          // Set folderToColor to the first folder for the modal display
          folderToColor.value = actualFolders[0];
          showColorModal.value = true;
        }
      } catch (error) {
        console.error('Error preparing folders for coloring:', error);
      }
    };

    // Move checked items
    const moveCheckedItems = () => {
      try {
        if (checkedItems.value.length === 0) {
          console.log('No items selected to move');
          return;
        }

        // Group by type
        const folderIds = checkedItems.value
          .filter(item => item.type === 'folder')
          .map(item => item.id);

        const noteIds = checkedItems.value
          .filter(item => item.type === 'note')
          .map(item => item.id);

        console.log('Move folders:', folderIds);
        console.log('Move notes:', noteIds);

        // Show the move modal
        showMoveModal.value = true;
      } catch (error) {
        console.error('Error preparing items for move:', error);
      }
    };    // Move items to a new destination folder
    const confirmMove = async (targetFolderId: number | null) => {
      try {
        // Group by type
        const folderIds = checkedItems.value
          .filter(item => item.type === 'folder')
          .map(item => item.id);

        const noteIds = checkedItems.value
          .filter(item => item.type === 'note')
          .map(item => item.id);

        console.log(`Moving items to folder ID: ${targetFolderId}`);
        console.log('Folders to move:', folderIds);
        console.log('Notes to move:', noteIds);

        // Count items for success message
        const folderCount = folderIds.length;
        const noteCount = noteIds.length;
        const totalItems = folderCount + noteCount;

        // Get destination folder name for the success message
        let destinationName = "Root";
        if (targetFolderId !== null) {
          const targetFolder = folders.value.find(f => f.id === targetFolderId);
          if (targetFolder) {
            destinationName = targetFolder.name;
          }
        }

        // Move folders first
        for (const folderId of folderIds) {
          if (typeof folderId === 'number') {
            await db.folders.update(folderId, { parent_id: targetFolderId });
          }
        }

        // Get target folder details to inherit book_id if applicable
        let targetBookId: number | null = null;
        if (targetFolderId !== null) {
          try {
            const targetFolder = await db.folders.getById(targetFolderId);
            targetBookId = targetFolder.book_id || null;
          } catch (error) {
            console.warn(`Could not fetch folder details for ID ${targetFolderId}:`, error);
            // Continue with null book_id if folder fetch fails
          }
        }

        // Then move notes
        for (const noteId of noteIds) {
          if (typeof noteId === 'number' || (typeof noteId === 'string' && !isNaN(Number(noteId)))) {
            const id = typeof noteId === 'number' ? noteId : Number(noteId);
            await db.notes.update(id, {
              folder_id: targetFolderId,
              book_id: targetBookId // Inherit book_id from target folder
            });
          }
        }

        // Refresh folders to update the UI
        await loadFolders();

        // If we're in a folder, refresh its contents
        if (selectedFolder.value) {
          await loadNotesForFolder(selectedFolder.value.id);
        } else {
          // If at root, reload root notes
          await loadRootNotes();
        }

        // Clear checked items
        checkedItems.value = [];

        // Close the modal
        showMoveModal.value = false;

        // Show success message
        let successMessage = '';
        if (folderCount > 0 && noteCount > 0) {
          successMessage = `Moved ${folderCount} folder(s) and ${noteCount} note(s) to ${destinationName}`;
        } else if (folderCount > 0) {
          successMessage = `Moved ${folderCount} folder(s) to ${destinationName}`;
        } else {
          successMessage = `Moved ${noteCount} note(s) to ${destinationName}`;
        }

        // Use db.showStatusMessage if it exists (this depends on the app's notification system)
        if ((db as any).showStatusMessage) {
          (db as any).showStatusMessage(successMessage);
        } else {
          // Use console as a fallback
          console.log('Move operation completed successfully:', successMessage);
          if (noteCount > 0 && targetBookId !== null) {
            console.log(`Notes inherited book_id: ${targetBookId} from target folder`);
          }
        }
      } catch (error) {
        console.error('Error moving items:', error);
        // Show error message
        if ((db as any).showErrorMessage) {
          (db as any).showErrorMessage('Failed to move items. Please try again.');
        }
      }
    };

    // Cancel the move operation
    const cancelMove = () => {
      showMoveModal.value = false;
    };

    // Create a new item (folder or note) in the current folder
    const createNewItem = () => {
      // This could show a modal to choose between creating a note or a subfolder
      if (selectedFolder.value) {
        void createNewFolder(selectedFolder.value.id); // For now, just create a subfolder
      }
    };

    // Helper method to create a new folder in the current folder
    const createNewFolderInCurrent = () => {
      if (selectedFolder.value && selectedFolder.value.id) {
        void createNewFolder(selectedFolder.value.id);
      } else {
        // Create at root level if no folder is selected
        void createNewFolder(null);
      }
    };

    // Format bytes into human-readable format
    const formatBytes = (bytes: number, decimals = 2) => {
      if (bytes === 0) return '0 Bytes';

      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];

      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    };

    // Calculate storage usage based on notes content
    const calculateStorageUsage = async () => {
      try {
        // Get all notes from the database
        const allNotes = await db.notes.getAll();

        // Calculate total size by summing content lengths
        // Note: This is an approximation as it only counts characters
        // For more accuracy, you'd need to calculate actual storage on disk
        let totalSize = 0;

        allNotes.forEach(note => {
          // Add size of content (2 bytes per character for UTF-16)
          if (note.content) {
            totalSize += note.content.length * 2;
          }

          // Add size of HTML content if present
          if (note.html_content) {
            totalSize += note.html_content.length * 2;
          }

          // Add size of title
          if (note.title) {
            totalSize += note.title.length * 2;
          }

          // Add fixed overhead for metadata (estimation)
          totalSize += 100; // 100 bytes for id, dates, folder_id, etc.
        });

        // Format the size for display
        return formatBytes(totalSize);
      } catch (error) {
        console.error('Error calculating storage usage:', error);
        return '0 MB'; // Fallback value
      }
    };

    // Update storage stats when needed
    const updateStorageStats = async () => {
      folderStats.value.storageUsed = await calculateStorageUsage();
    };

    // Create a new note in the current folder
    const createNewNoteInCurrent = async () => {
      try {
        console.log('=== Creating new note in current folder ===');
        console.log('selectedFolder.value:', selectedFolder.value);

        // Get current folder ID or null for root
        const folderId = selectedFolder.value && selectedFolder.value.id
          ? selectedFolder.value.id
          : null;

        console.log('Extracted folderId:', folderId);

        // Get book_id from current folder or parent hierarchy if applicable
        let bookId: number | null = null;
        if (selectedFolder.value && selectedFolder.value.book_id) {
          bookId = selectedFolder.value.book_id;
          console.log('Found book_id in selectedFolder:', bookId);
        } else if (selectedFolder.value && selectedFolder.value.id) {
          // Try to get inherited book_id from parent hierarchy
          try {
            const folderDetails = await db.folders.getById(selectedFolder.value.id);
            if (folderDetails.book_id) {
              bookId = folderDetails.book_id;
              console.log('Found inherited book_id from folder hierarchy:', bookId);
            } else {
              console.log('No book_id found in folder hierarchy');
            }
          } catch (error) {
            console.warn('Could not fetch folder details for book_id inheritance:', error);
          }
        } else {
          console.log('No folder selected or no folder ID available');
        }

        let createdNote;

        // If we're in a book's folder, use the createForBook API to get auto-generated title
        if (bookId) {
          console.log('Creating note for book using createForBook API with folderId:', folderId);
          createdNote = await db.notes.createForBook(bookId, undefined, folderId);
        } else {
          // Create a regular note with default title
          const newNote = {
            title: 'Untitled Note',
            content: '',
            html_content: '<p></p>',
            folder_id: folderId,
            book_id: null
          };

          console.log('Creating regular note with data:', newNote);
          createdNote = await db.notes.create(newNote);
        }

        console.log('Created note result:', createdNote);

        // Add to notes list if we have it loaded
        if (notes.value) {
          notes.value.unshift(createdNote);
        }

        // Update storage stats after creating a note
        await updateStorageStats();

        // Navigate to Notes view with the newly created note selected
        router.push({
          name: 'Notes',
          query: { noteId: createdNote.id?.toString() }
        });

      } catch (error) {
        console.error('Error creating new note:', error);
      }
    };

    // Move folder to another location
    const moveFolder = (folder: FolderWithMeta) => {
      // This would show a modal to select a new parent folder
      console.log('Moving folder:', folder.name);
      // Implementation for folder move would go here
    };    // Export folder contents
    const exportFolder = () => {
      try {
        // Reset the items to export array
        const exportItems: { id: number; type: 'folder' | 'note'; name: string }[] = [];

        // If there are checked items, export those
        if (checkedItems.value.length > 0) {
          console.log('Exporting checked items:', checkedItems.value);

          checkedItems.value.forEach(item => {
            // Make sure we can parse the id as a number
            let id: number;

            if (typeof item.id === 'number') {
              id = item.id;
            } else if (typeof item.id === 'string') {
              id = parseInt(item.id, 10);
              // Skip if the id is NaN
              if (isNaN(id)) {
                console.warn(`Skipping item with invalid ID: ${item.id}`);
                return;
              }
            } else {
              console.warn(`Skipping item with invalid ID type: ${typeof item.id}`);
              return;
            }

            // Find the name based on the item type
            let name: string;
            if (item.type === 'folder') {
              const folder = folders.value.find(f => f.id === id);
              name = folder?.name || 'Untitled Folder';
            } else {
              const note = folderNotes.value.find(n => n.id === id);
              name = note?.title || 'Untitled Note';
            }

            exportItems.push({
              id,
              type: item.type as 'folder' | 'note',
              name
            });
          });
        }
        // Otherwise export the selected folder
        else if (selectedFolder.value) {
          console.log('Exporting selected folder:', selectedFolder.value.name);
          exportItems.push({
            id: selectedFolder.value.id,
            type: 'folder',
            name: selectedFolder.value.name
          });
        }
        // Or if we're at root with no selection, export all root items
        else {
          console.log('Exporting all root items');
          // First add all root folders
          const rootFolders = folders.value.filter(folder => folder.parent_id === null);
          rootFolders.forEach(folder => {
            if (folder.id !== undefined) {
              exportItems.push({
                id: folder.id,
                type: 'folder' as 'folder',
                name: folder.name
              });
            }
          });

          // Then add all root notes
          const rootNotes = folderNotes.value.filter(note => note.folder_id === null);
          rootNotes.forEach(note => {
            if (note.id !== undefined) {
              exportItems.push({
                id: note.id as number,
                type: 'note' as 'note',
                name: note.title || 'Untitled Note'
              });
            }
          });
        }          // Only show the modal if we have items to export
        if (exportItems.length > 0) {
          console.log(`Ready to export ${exportItems.length} items`);

          // Ensure we're only using serializable data
          const serializableItems = exportItems.map(item => ({
            id: Number(item.id),
            type: item.type,
            name: String(item.name || '')
          }));

          itemsToExport.value = serializableItems;
          // Set the modal visibility only after itemsToExport is updated
          nextTick(() => {
            showExportModal.value = true;
          });
        } else {
          console.log('No items to export');
          alert('No items to export');
        }
      } catch (error) {
        console.error('Error preparing items for export:', error);
        alert('Error preparing items for export. Please try again.');
      }
    };

    // Import content to current folder
    const importToFolder = () => {
      // Use the new direct import functionality
      importNote();
    };

    // Sort folder contents (by name, date, etc)
    const sortFolder = (sortBy = '') => {
      console.log(`Sorting by: ${sortBy || 'advanced options'}`);

      if (sortBy) {
        // Direct sorting based on dropdown selection
        currentSortBy.value = sortBy;
        currentSortDirection.value = 'ascending'; // Default direction for dropdown sorting
        currentSortFoldersFirst.value = true; // Default setting for dropdown sorting

        // Trigger reactivity by calling handleSortComplete
        handleSortComplete({
          success: true,
          folderId: selectedFolder.value?.id, // This can be null for the root folder
          sortBy: sortBy,
          sortDirection: 'ascending',
          foldersFirst: true
        });
      } else {
        // Open modal for advanced sorting options
        showSortModal.value = true;
      }
    };

    // Search within the current folder
    const searchInFolder = (query: string) => {
      // Allow searching regardless of whether a folder is selected or not
      searchInFolderQuery.value = query;
      console.log('Searching in folder for:', query);
      // Implementation of searching in folder contents is handled by FolderContent component
      // through the searchInFolderQuery reactive reference
    };

    // Handle export start event for progress overlay
    const handleExportStart = (data: { format: string; itemCount: number; itemType: string }) => {
      isExportingFile.value = true;
      exportProgressTitle.value = `Exporting ${data.itemType}`;
      exportProgressMessage.value = `Preparing ${data.format.toUpperCase()} export...`;
      exportProgressDetails.value = `${data.itemCount} item${data.itemCount > 1 ? 's' : ''} selected`;
    };

    // Handle export completion
    const handleExportComplete = (result: { success: boolean; error?: string; message?: string }) => {
      // Hide progress overlay
      isExportingFile.value = false;
      exportProgressTitle.value = '';
      exportProgressMessage.value = '';
      exportProgressDetails.value = '';

      showExportModal.value = false;
      if (result.success) {
        // You can add a notification here if needed
        console.log('Export completed successfully:', result.message);
        // Success notification removed - export completes silently
      } else {
        console.error('Export failed:', result.error);
        // Show error alert (in a real app, you'd use a toast notification)
        alert(`Export failed: ${result.error || 'Unknown error'}`);
      }
    };

    // Direct import functionality
    const importNote = () => {
      // Trigger the file input to open the file manager
      if (fileInput.value) {
        fileInput.value.click();
      }
    };

    const handleFileImport = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      const file = target.files[0];

      // Validate file type
      const allowedExtensions = ['.md', '.pdf', '.noti', '.txt'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      if (!allowedExtensions.includes(fileExtension)) {
        alert(`Invalid file type. Only ${allowedExtensions.join(', ')} files are supported.`);
        // Reset file input
        if (fileInput.value) {
          fileInput.value.value = '';
        }
        return;
      }

      // Handle different file types
      if (fileExtension === '.pdf') {
        // For PDF files, we'll need special handling
        alert('PDF import is not yet implemented. Please use .md or .noti files.');
        // Reset file input
        if (fileInput.value) {
          fileInput.value.value = '';
        }
        return;
      }

      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string;
          const rawTitle = file.name.replace(/\.[^/.]+$/, ""); // Use filename without extension as title
          // Sanitize the title to prevent XSS attacks
          const title = DOMPurify.sanitize(rawTitle, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }); // Strip all HTML

          let noteContent = content;
          let htmlContent = `<p>${DOMPurify.sanitize(content)}</p>`; // Sanitized conversion

          // Handle different file formats
          if (fileExtension === '.noti') {
            try {
              // Parse .noti file as JSON
              const notiData = JSON.parse(content);
              if (notiData.content) {
                noteContent = notiData.content;
                htmlContent = notiData.html_content
                  ? DOMPurify.sanitize(notiData.html_content)
                  : `<p>${DOMPurify.sanitize(notiData.content)}</p>`;
              }
            } catch (parseError) {
              console.warn('Failed to parse .noti file as JSON, treating as plain text');
            }
          }

          // Use the backend import API for proper processing
          const createdNote = await db.notes.import(content, fileExtension.substring(1), title);

          // Update the created note with folder and book relationships if needed
          if (createdNote.id && (selectedFolder.value?.id || selectedFolder.value?.book_id)) {
            let bookId: number | null = null;
            if (selectedFolder.value && selectedFolder.value.book_id) {
              bookId = selectedFolder.value.book_id;
            } else if (selectedFolder.value && selectedFolder.value.id) {
              // Try to get inherited book_id from parent hierarchy
              try {
                const folderDetails = await db.folders.getById(selectedFolder.value.id);
                if (folderDetails.book_id) {
                  bookId = folderDetails.book_id;
                  console.log('Found inherited book_id for import:', bookId);
                }
              } catch (error) {
                console.warn('Could not fetch folder details for book_id inheritance during import:', error);
              }
            }

            // Update the note with folder and book relationships
            const updateData: Partial<Note> = {};
            if (selectedFolder.value?.id) {
              updateData.folder_id = selectedFolder.value.id;
            }
            if (bookId) {
              updateData.book_id = bookId;
            }

            if (Object.keys(updateData).length > 0) {
              await db.notes.update(createdNote.id, updateData);
              // Update the local note object for consistency
              Object.assign(createdNote, updateData);
            }
          }

          // Refresh notes for this folder if needed
          if (selectedFolder.value) {
            await loadNotesForFolder(selectedFolder.value.id);
          }

          console.log('Note imported successfully:', createdNote.title);

          // Navigate to NotesView with the newly created note selected
          if (createdNote.id) {
            router.push({
              name: 'Notes',
              query: { noteId: createdNote.id.toString() }
            });
          }

        } catch (error: any) {
          console.error('Error importing note:', error);
          alert('Error importing note. Check console for details.');
        } finally {
          // Reset file input for future imports
          if (fileInput.value) {
            fileInput.value.value = '';
          }
        }
      };

      // Read the file as text
      reader.readAsText(file);
    };

    // Handle sort completion
    const handleSortComplete = (result: {
      success: boolean;
      error?: string;
      sortBy?: string;
      sortDirection?: string;
      folderId?: number;
      foldersFirst?: boolean;
    }) => {
      showSortModal.value = false;
      if (result.success && result.sortBy) {
        // Store the sorting preferences
        currentSortBy.value = result.sortBy;
        currentSortDirection.value = result.sortDirection || 'ascending';
        currentSortFoldersFirst.value = result.foldersFirst !== false;

        console.log('Sorting updated:', {
          sortBy: currentSortBy.value,
          direction: currentSortDirection.value,
          foldersFirst: currentSortFoldersFirst.value
        });

        // Trigger a reactive update to force re-render of FolderContent
        // This is needed because changing the sort preferences doesn't automatically
        // cause Vue to detect that the getFolderItems output would change

        // For root view, we need to ensure notes are loaded
        if (!notes.value || notes.value.length === 0) {
          db.notes.getAll().then(allNotes => {
            notes.value = allNotes;
            console.log('Loaded notes for sorting root view:', allNotes.length);
          });
        }

        // For currently selected folder, reload its notes to ensure fresh sort
        if (selectedFolder.value) {
          loadNotesForFolder(selectedFolder.value.id);
          console.log('Loaded notes for sorting folder:', selectedFolder.value.name);
        }
      } else if (!result.success) {
        console.error('Sorting failed:', result.error);
      }
    };

    // Load folders from database with hierarchical structure
    const loadFolders = async () => {
      if (isLoadFoldersRunning.value) {
        console.warn('loadFolders is already running, skipping call');
        return;
      }
      isLoadFoldersRunning.value = true;
      loading.value = true;
      try {
        // Get flat list of all folders
        folders.value = await db.folders.getAll() || [];
        folderStats.value.folderCount = folders.value.length;

        // Get hierarchical structure for display
        try {
          const hierarchy = await db.folders.getHierarchy();
          if (hierarchy && Array.isArray(hierarchy)) {
            folderHierarchy.value = hierarchy;

            // Update our flat folders list with note counts from hierarchy
            updateFolderCountsFromHierarchy(hierarchy, folders.value);
          } else {
            console.warn("Invalid hierarchy data structure returned");
            throw new Error("Invalid hierarchy data structure");
          }
        } catch (error) {
          console.warn("Folder hierarchy not available, using flat folder list", error);

          // Build a simple hierarchy manually using parent_id relationships
          console.log("Building hierarchy manually from", folders.value.length, "folders");
          const rootFolders = folders.value.filter(f => f.parent_id === null);
          console.log("Found", rootFolders.length, "root folders");
          const folderMap = new Map<number, FolderWithMeta>();

          // First pass: create entries for each folder and try to get notesCount
          // This needs to be async if we fetch note counts here.
          // For simplicity, we'll assume the main notes count loading later will handle it
          // OR, we make this part async too.
          // Let's try to add it here for the fallback scenario.
          for (const f of folders.value) {
            if (f.id !== undefined) {
              let count = 0;
              try {
                const notesInFolder = await db.notes.getByFolderId(f.id);
                count = notesInFolder.length;

                // Special handling for Books folder in fallback scenario
                if (f.name === 'Books' && f.parent_id === null) {
                  console.log(`Fallback: Books folder found, will apply special logic later (note count would be ${count})`);
                }
              } catch (e) {
                console.warn(`Fallback: Could not get notes count for folder ${f.id}`, e);
              }
              folderMap.set(f.id, { ...f, children: [], notesCount: count });
            }
          }

          // Second pass: build the hierarchy
          folders.value.forEach(f => {
            if (f.id !== undefined && f.parent_id !== null && f.parent_id !== undefined) {
              const parent = folderMap.get(f.parent_id);
              if (parent && parent.children) {
                const folderWithMeta = folderMap.get(f.id);
                if (folderWithMeta) {
                  parent.children.push(folderWithMeta);
                }
              }
            }
          });

          const hierarchyResult = rootFolders
            .filter(f => f.id !== undefined)
            .map(f => folderMap.get(f.id))
            .filter((f): f is FolderWithMeta => f !== undefined);

          // Apply the same Books folder counting logic as in folders-api.ts
          // This ensures the Books folder shows the count of direct child folders instead of notes
          const applyBooksFolderLogic = (folders: FolderWithMeta[]) => {
            folders.forEach(folder => {
              if (folder.name === 'Books' && folder.parent_id === null) {
                // For the Books root folder, show count of direct child folders instead of notes
                folder.notesCount = folder.children ? folder.children.length : 0;
              }
              // Apply recursively to children
              if (folder.children && folder.children.length > 0) {
                applyBooksFolderLogic(folder.children);
              }
            });
          };

          applyBooksFolderLogic(hierarchyResult);

          console.log("Built manual hierarchy with", hierarchyResult.length, "root items");
          folderHierarchy.value = hierarchyResult;
        }

        // Try to load notes for all folders to get counts
        try {
          console.log('loadFolders: Starting manual note count loading for folders...');
          await Promise.all(folders.value.map(async (folder) => {
            const notes = await db.notes.getByFolderId(folder.id);
            const noteCount = notes.length;

            // Special handling for Books folder - don't overwrite with note count
            if (folder.name === 'Books' && folder.parent_id === null) {
              console.log(`loadFolders: Skipping note count override for Books folder (would be ${noteCount}, keeping existing count: ${(folder as FolderWithMeta).notesCount})`);
              // Don't overwrite the Books folder count - it should remain as child folder count
            } else {
              (folder as FolderWithMeta).notesCount = noteCount;
            }
            return notes;
          }));

          // Apply Books folder logic after manual count loading to ensure it's correct
          const applyBooksFolderLogic = (folders: FolderWithMeta[]) => {
            folders.forEach(folder => {
              if (folder.name === 'Books' && folder.parent_id === null) {
                // Count direct child folders for Books folder
                const childFolders = folders.filter(f => f.parent_id === folder.id);
                const oldCount = folder.notesCount;
                folder.notesCount = childFolders.length;
                console.log(`loadFolders: Applied Books folder logic - count changed from ${oldCount} to ${folder.notesCount} (child folders)`);
              }
            });
          };

          applyBooksFolderLogic(folders.value as FolderWithMeta[]);

          // Calculate total note count for stats (excluding Books folder special count)
          const totalNoteCount = folders.value.reduce((sum, folder) => {
            const folderWithMeta = folder as FolderWithMeta;
            // For Books folder, don't include its special count in total note stats
            if (folder.name === 'Books' && folder.parent_id === null) {
              return sum; // Skip Books folder from note count stats
            }
            return sum + (folderWithMeta.notesCount || 0);
          }, 0);
          folderStats.value.fileCount = totalNoteCount;

          // Load root notes as well to ensure rootNotesCount works correctly
          // Load root notes as well to ensure rootNotesCount works correctly
          try {
            // Get notes with null folder_id (root level notes)
            notes.value = await db.notes.getAll();

            // FIXED: Also populate folderNotes.value if we're starting at root level
            // This ensures getFolderItems() can find root notes on initial load
            if (selectedFolder.value === null) {
              console.log('Initial load: populating folderNotes with root notes for display');
              folderNotes.value = notes.value.filter(note => note.folder_id === null) || [];
              console.log(`Initial load: found ${folderNotes.value.length} root notes for display`);
            }
          } catch (noteError) {
            console.warn("Could not load notes for root folder count", noteError);
            notes.value = [];
            folderNotes.value = []; // Also clear folderNotes on error
          }
        } catch (error) {
          console.warn("Could not load note counts", error);
          folderStats.value.fileCount = 128; // Fallback
        }

        // Calculate and update storage usage
        folderStats.value.storageUsed = await calculateStorageUsage();
      } catch (error) {
        console.error("Error loading folders:", error);
      } finally {
        loading.value = false;
        isLoadFoldersRunning.value = false;
      }
    };

    // Update folder hierarchy
    const updateFolderHierarchy = async () => {
      try {
        console.log('Updating folder hierarchy...');
        // Get the latest folder hierarchy from the database
        folderHierarchy.value = await db.folders.getHierarchy();

        // If we got a valid hierarchy, update the flat folder list note counts
        if (folderHierarchy.value && Array.isArray(folderHierarchy.value) && folderHierarchy.value.length > 0) {
          updateFolderCountsFromHierarchy(folderHierarchy.value, folders.value);
        }

        // If hierarchy failed, build it manually from the folders list
        if (!folderHierarchy.value || !Array.isArray(folderHierarchy.value) || folderHierarchy.value.length === 0) {
          console.log('Hierarchy update failed, rebuilding manually');
          // Get fresh folder list
          folders.value = await db.folders.getAll() || [];

          const rootFolders = folders.value.filter(f => f.parent_id === null);
          const folderMap = new Map<number, FolderWithMeta>();

          // First pass: create entries for each folder
          folders.value.forEach(f => {
            if (f.id !== undefined) {
              folderMap.set(f.id, { ...f, children: [] });
            }
          });

          // Second pass: build the hierarchy
          folders.value.forEach(f => {
            if (f.id !== undefined && f.parent_id !== null && f.parent_id !== undefined) {
              const parent = folderMap.get(f.parent_id);
              if (parent && parent.children) {
                const folderWithMeta = folderMap.get(f.id);
                if (folderWithMeta) {
                  parent.children.push(folderWithMeta);
                }
              }
            }
          });

          const hierarchyResult = rootFolders
            .filter(f => f.id !== undefined)
            .map(f => folderMap.get(f.id))
            .filter((f): f is FolderWithMeta => f !== undefined);

          // Apply the same Books folder counting logic as in folders-api.ts
          // This ensures the Books folder shows the count of direct child folders instead of notes
          const applyBooksFolderLogic = (folders: FolderWithMeta[]) => {
            folders.forEach(folder => {
              if (folder.name === 'Books' && folder.parent_id === null) {
                // For the Books root folder, show count of direct child folders instead of notes
                folder.notesCount = folder.children ? folder.children.length : 0;
              }
              // Apply recursively to children
              if (folder.children && folder.children.length > 0) {
                applyBooksFolderLogic(folder.children);
              }
            });
          };

          applyBooksFolderLogic(hierarchyResult);

          folderHierarchy.value = hierarchyResult;
        }
      } catch (error) {
        console.error('Error updating folder hierarchy:', error);
      }
    };

    // Helper function to update folder counts from hierarchy
    const updateFolderCountsFromHierarchy = (hierarchyFolders: FolderWithMeta[], flatFolders: FolderWithMeta[]) => {
      console.log('updateFolderCountsFromHierarchy: Starting count synchronization...');

      // Create a map for quick lookups of flat folders by ID
      const folderMap = new Map<number, FolderWithMeta>();
      if (flatFolders && Array.isArray(flatFolders)) {
        flatFolders.forEach(f => {
          if (f && f.id !== undefined) {
            folderMap.set(f.id, f);
          }
        });
      }

      // Recursive function to process each folder in the hierarchy
      const processFolders = (folders: FolderWithMeta[]) => {
        if (!folders || !Array.isArray(folders)) return;

        folders.forEach(folder => {
          if (folder && folder.id !== undefined) {
            // Update the note count in the flat folder list if it exists
            const flatFolder = folderMap.get(folder.id);
            if (flatFolder && folder.notesCount !== undefined) {
              const oldNotesCount = flatFolder.notesCount;
              flatFolder.notesCount = folder.notesCount;
              flatFolder.childFoldersCount = folder.childFoldersCount;
              flatFolder.children = folder.children;

              // Log Books folder count updates for debugging
              if (flatFolder.name === 'Books' && flatFolder.parent_id === null) {
                console.log(`updateFolderCountsFromHierarchy: Books folder count updated from ${oldNotesCount} to ${flatFolder.notesCount} (should be child folder count)`);
              }
            }

            // Process children recursively
            if (folder.children && Array.isArray(folder.children)) {
              processFolders(folder.children);
            }
          }
        });
      };

      // Start processing from the root folders
      processFolders(hierarchyFolders);
      console.log('updateFolderCountsFromHierarchy: Count synchronization completed');
    };

    // Update folder counts for specific folders after note operations
    const updateFolderCounts = async (folderIds: number[]) => {
      console.log('updateFolderCounts: Updating counts for folders:', folderIds);

      try {
        for (const folderId of folderIds) {
          // Get the current note count for this folder
          const notes = await db.notes.getByFolderId(folderId);
          const noteCount = notes.length;

          // Update in the flat folders array
          const flatFolder = folders.value.find(f => f.id === folderId);
          if (flatFolder) {
            flatFolder.notesCount = noteCount;
            console.log(`updateFolderCounts: Updated folder "${flatFolder.name}" count to ${noteCount}`);
          }

          // Update in the hierarchy
          const updateInHierarchy = (folders: FolderWithMeta[]): boolean => {
            for (const folder of folders) {
              if (folder.id === folderId) {
                // Special handling for Books folder - don't overwrite with note count
                if (folder.name === 'Books' && folder.parent_id === null) {
                  console.log(`updateFolderCounts: Skipping note count update for Books folder`);
                } else {
                  folder.notesCount = noteCount;
                  console.log(`updateFolderCounts: Updated hierarchy folder "${folder.name}" count to ${noteCount}`);
                }
                return true;
              }

              if (folder.children && folder.children.length > 0) {
                if (updateInHierarchy(folder.children)) {
                  return true;
                }
              }
            }
            return false;
          };

          updateInHierarchy(folderHierarchy.value);
        }

        console.log('updateFolderCounts: Folder count updates completed');
      } catch (error) {
        console.error('updateFolderCounts: Error updating folder counts:', error);
      }
    };

    // Handle folder deletion confirmation
    const confirmFolderDelete = async (result: { targetFolderId?: string | number | null }) => {
      try {
        if (foldersToDelete.value.length === 0) return;

        const targetFolder = result?.targetFolderId ? Number(result.targetFolderId) : null;

        for (const folder of foldersToDelete.value) {
          if (folder.id !== undefined) {
            await db.folders.delete(folder.id, targetFolder);
          }
        }

        // Refresh folders
        await loadFolders();

        // Clear selection if deleted folder was selected
        if (selectedFolder.value && foldersToDelete.value.some(f => f.id === selectedFolder.value?.id)) {
          selectedFolder.value = null;
        }

        // Clear checked items and navigator selected items
        checkedItems.value = [];
        navigatorSelectedItems.value = [];

        // Update storage stats after deleting folders
        await updateStorageStats();
        showDeleteFolderModal.value = false;
      } catch (error) {
        console.error('Error deleting folder(s):', error);
      }
    };

    // Handle note deletion confirmation
    const confirmNoteDelete = async () => {
      try {
        // Track which folders need their counts updated
        const foldersToUpdate = new Set<number>();

        for (const note of notesToDelete.value) {
          if (note.id !== undefined) {
            // Track the folder that contained this note
            if (note.folder_id) {
              foldersToUpdate.add(note.folder_id);
            }

            await db.notes.delete(note.id);

            // IMPORTANT: Remove the note from folderNotes array
            const index = folderNotes.value.findIndex(n => n.id === note.id);
            if (index !== -1) {
              folderNotes.value.splice(index, 1);
            }

            // Also remove from main notes array if it exists
            if (notes.value) {
              const mainIndex = notes.value.findIndex(n => n.id === note.id);
              if (mainIndex !== -1) {
                notes.value.splice(mainIndex, 1);
              }
            }
          }
        }

        // Update folder counts for affected folders
        await updateFolderCounts(Array.from(foldersToUpdate));

        // Update storage stats after deleting notes
        await updateStorageStats();

        // Clear checked items
        checkedItems.value = [];

        // Close modals
        showDeleteNoteModal.value = false;
        showDeleteNotesModal.value = false;

        // Force refresh of getFolderItems cache
        const cacheKey = selectedFolder.value
          ? `folder_${selectedFolder.value.id}_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`
          : `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
        delete folderItemsCache.value[cacheKey];

      } catch (error) {
        console.error('Error deleting note(s):', error);
      }
    };    // Handle mixed items deletion confirmation
    const confirmMixedItemsDelete = async (result: { targetFolderId?: string | number | null }) => {
      try {
        const targetFolder = result?.targetFolderId ? Number(result.targetFolderId) : null;

        // Track which folders need their counts updated
        const foldersToUpdate = new Set<number>();

        // Delete folders first
        for (const folder of foldersToDelete.value) {
          if (folder.id !== undefined) {
            await db.folders.delete(folder.id, targetFolder);
          }
        }

        // Then delete notes
        for (const note of notesToDelete.value) {
          if (note.id !== undefined) {
            // Track the folder that contained this note
            if (note.folder_id) {
              foldersToUpdate.add(note.folder_id);
            }

            await db.notes.delete(note.id);

            // IMPORTANT: Remove the note from folderNotes array
            const index = folderNotes.value.findIndex(n => n.id === note.id);
            if (index !== -1) {
              folderNotes.value.splice(index, 1);
            }

            // Also remove from main notes array if it exists
            if (notes.value) {
              const mainIndex = notes.value.findIndex(n => n.id === note.id);
              if (mainIndex !== -1) {
                notes.value.splice(mainIndex, 1);
              }
            }
          }
        }

        // Refresh folders
        await loadFolders();

        // Update folder counts for affected folders (after loadFolders to ensure fresh data)
        if (foldersToUpdate.size > 0) {
          await updateFolderCounts(Array.from(foldersToUpdate));
        }

        // Clear selection if deleted folder was selected
        if (selectedFolder.value && foldersToDelete.value.some(f => f.id === selectedFolder.value?.id)) {
          selectedFolder.value = null;
        }

        // Update storage stats after mixed deletion
        await updateStorageStats();

        // Clear checked items and navigator selected items
        checkedItems.value = [];
        navigatorSelectedItems.value = [];

        showDeleteMixedItemsModal.value = false;

        // Force refresh of getFolderItems cache
        const cacheKey = selectedFolder.value
          ? `folder_${selectedFolder.value.id}_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`
          : `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
        delete folderItemsCache.value[cacheKey];

      } catch (error) {
        console.error('Error deleting mixed items:', error);
      }
    };    // Close all delete modals
    const cancelDeleteModals = () => {
      // Close all modal windows
      showDeleteFolderModal.value = false;
      showDeleteNoteModal.value = false;
      showDeleteNotesModal.value = false;
      showDeleteMixedItemsModal.value = false;

      // Clear delete-related state to prevent stale data
      foldersToDelete.value = [];
      notesToDelete.value = [];
      noteTitle.value = '';
      noteTitles.value = [];
      mixedItemsToDelete.value = [];

      // Clear all selections when canceling delete operation
      checkedItems.value = [];
      navigatorSelectedItems.value = [];

      // Clear selection in the FolderNavigator component
      if (folderNavigatorRef.value && typeof folderNavigatorRef.value.clearSelection === 'function') {
        folderNavigatorRef.value.clearSelection();
      }

      console.log('Delete operation cancelled - all selections cleared');
    };

    // Handle confirmation from NameFolderModal
    const handleConfirmNameFolder = async (data: string | { name: string; color: string | null }) => {
      if (!folderToNameInitial.value) return;

      try {
        // Handle both old string format and new object format
        const updateData = typeof data === 'string'
          ? { name: data.trim() }
          : { name: data.name.trim(), color: data.color };

        const updatedFolder = await db.folders.update(
          folderToNameInitial.value.id,
          updateData
        );

        const index = folders.value.findIndex(f => f.id === folderToNameInitial.value!.id);
        if (index !== -1) {
          folders.value[index] = updatedFolder;
        }

        await updateFolderHierarchy();
        await selectFolder(updatedFolder); // Select the folder after naming it

      } catch (error) {
        console.error("Error naming new folder:", error);
        // If renaming fails, we might want to delete the folder or allow another attempt
        // For now, just log error and close modal
      } finally {
        showNameFolderModal.value = false;
        folderToNameInitial.value = null;
      }
    };

    // Handle cancellation from NameFolderModal
    const handleCancelNameFolder = async () => {
      // If the user cancels naming, we should probably delete the "New Folder"
      // or leave it and allow rename later. For now, let's delete it.
      if (folderToNameInitial.value && folderToNameInitial.value.id !== undefined) {
        try {
          await db.folders.delete(folderToNameInitial.value.id, null); // No target folder for notes
          // Remove from local list
          folders.value = folders.value.filter(f => f.id !== folderToNameInitial.value!.id);
          folderStats.value.folderCount = folders.value.length;
          await updateFolderHierarchy();
          // If the parent was selected, keep it selected, otherwise, no specific selection.
          if (folderToNameInitial.value.parent_id) {
            const parent = folders.value.find(f => f.id === folderToNameInitial.value!.parent_id);
            if (parent) await selectFolder(parent);
            else selectedFolder.value = null; // Go to root if parent not found
          } else {
            selectedFolder.value = null; // Go to root
          }

        } catch (error) {
          console.error("Error deleting folder after cancelling name modal:", error);
        }
      }
      showNameFolderModal.value = false;
      folderToNameInitial.value = null;
    };

    // Handle color selection from FolderColorSelectionModal
    const handleFolderColorChange = async (color: string | null) => {
      try {


        // Get folder items to update
        const folderItems = checkedItems.value.filter(item => item.type === 'folder');

        if (folderItems.length === 0) {
          console.log('No folders selected to color');
          return;
        }

        if (folderItems.length > 1) {
          // Update multiple folders
          for (const item of folderItems) {
            const folder = folders.value.find(f => f.id === item.id);
            if (folder) {
              const updatedFolder = await db.folders.update(folder.id, { color });
              const index = folders.value.findIndex(f => f.id === folder.id);
              if (index !== -1) {
                folders.value[index] = updatedFolder;
              }
            }
          }
          console.log(`Updated ${folderItems.length} folders color to: ${color || 'default'}`);
        } else {
          // Update single folder - use the checked item or folderToColor as fallback
          const targetFolder = folderToColor.value || folders.value.find(f => f.id === folderItems[0].id);

          if (targetFolder) {
            const updatedFolder = await db.folders.update(targetFolder.id, { color });
            const index = folders.value.findIndex(f => f.id === targetFolder.id);
            if (index !== -1) {
              folders.value[index] = updatedFolder;
            }
            console.log(`Updated folder "${targetFolder.name}" color to: ${color || 'default'}`);
          } else {
            console.error('Could not find target folder to update');
            return;
          }
        }

        // Update hierarchy to reflect color changes
        await updateFolderHierarchy();

      } catch (error) {
        console.error('Error updating folder color:', error);
      } finally {
        showColorModal.value = false;
        folderToColor.value = null;
      }
    };

    // Handle color modal cancellation
    const handleColorModalClose = () => {
      showColorModal.value = false;
      folderToColor.value = null;
    };

    // Helper function to navigate to root folder from breadcrumb
    const navigateToRootFromBreadcrumb = async () => {
      // Skip if already at root folder
      if (selectedFolder.value === null) {
        console.log('Already at root folder, no navigation needed');
        return;
      }

      try {
        console.log('Navigating to All Folders (root) from breadcrumb');

        // Simply set selectedFolder to null to navigate to root
        selectedFolder.value = null;
        selectedItemId.value = null;
        selectedItemType.value = 'folder';
        searchInFolderQuery.value = '';

        // Clear any open folder dropdown
        showPathDropdown.value = false;

        // Clear checked items when changing folders
        checkedItems.value = [];

        // Clear navigator selected items when changing folders
        navigatorSelectedItems.value = [];

        // Load root level notes
        console.log('Loading root notes...');
        await loadRootNotes();

        // Force refresh folder items cache for root to ensure fresh content is shown
        const cacheKey = `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
        delete folderItemsCache.value[cacheKey];

        console.log('Successfully navigated to root folder');
      } catch (err) {
        console.error('Error navigating to root from breadcrumb:', err);
      }
    };// Helper function to navigate to a folder from breadcrumb
    const navigateToFolderFromBreadcrumb = async (folder: { id: number, name: string }) => {
      try {
        console.log(`Navigating to folder from breadcrumb: ${folder.name} (ID: ${folder.id})`);

        // Handle special case for ID 0 - likely an invalid ID
        if (folder.id === 0) {
          console.warn('Folder ID 0 is invalid, navigating to root folder instead');
          return await navigateToRootFromBreadcrumb();
        }

        // Find the full folder object from our folder list
        const fullFolder = folders.value.find(f => f.id === folder.id);

        if (fullFolder) {
          console.log('Found full folder data, using it for navigation');
          // Use the selectFolder method with the full folder object
          await selectFolder(fullFolder);

          // Close the path dropdown after navigation
          showPathDropdown.value = false;

          console.log('Successfully navigated to folder');
        } else {
          console.warn(`Folder ${folder.id} not found in folders list, fetching from database`);

          // If folder not in our list, try to get it from the database
          try {
            const fetchedFolder = await db.folders.getById(folder.id);
            if (fetchedFolder) {
              console.log('Successfully fetched folder from database');
              // Add any missing properties needed for FolderWithMeta
              const folderWithMeta: FolderWithMeta = {
                ...fetchedFolder,
                children: [],
                notesCount: 0, // We don't know the count yet
                childFoldersCount: 0 // We don't know the count yet
              };

              // Navigate to the folder
              await selectFolder(folderWithMeta);
              console.log('Successfully navigated to fetched folder');
            } else {
              // If we still can't find the folder, navigate to root instead
              console.warn(`Could not fetch folder ${folder.id}, navigating to root folder instead`);
              await navigateToRootFromBreadcrumb();
            }
          } catch (dbError) {
            console.error(`Database error fetching folder ${folder.id}:`, dbError);
            // Navigate to root instead of using invalid folder data
            console.warn('Due to database error, navigating to root folder');
            await navigateToRootFromBreadcrumb();
          }
        }
      } catch (err) {
        console.error('Error navigating to folder from breadcrumb:', err);
      }
    };
    // Helper function to navigate to root from dropdown and close the dropdown
    const navigateToRootFromDropdown = async () => {
      // Skip if already at root folder
      if (selectedFolder.value === null) {
        console.log('Already at root folder, no navigation needed');
        showPathDropdown.value = false;
        return;
      }

      try {
        console.log('Navigating to All Folders (root) from dropdown');

        // Simply set selectedFolder to null to navigate to root
        selectedFolder.value = null;
        selectedItemId.value = null;
        selectedItemType.value = 'folder';
        searchInFolderQuery.value = '';

        // Clear checked items when changing folders
        checkedItems.value = [];

        // Clear navigator selected items when changing folders
        navigatorSelectedItems.value = [];

        // Load root level notes
        console.log('Loading root notes...');
        await loadRootNotes();

        // Force refresh folder items cache for root to ensure fresh content is shown
        const cacheKey = `root_${currentSortBy.value}_${currentSortDirection.value}_${currentSortFoldersFirst.value}`;
        delete folderItemsCache.value[cacheKey];

        console.log('Successfully navigated to root folder from dropdown');
      } catch (err) {
        console.error('Error navigating to root from dropdown:', err);
      }

      // Close the dropdown after navigation
      showPathDropdown.value = false;
    };
    // Helper function to navigate to a folder from dropdown and close the dropdown
    const navigateToFolderFromDropdown = async (folder: { id: number, name: string }) => {
      try {
        console.log(`Navigating to folder from dropdown: ${folder.name} (ID: ${folder.id})`);

        // Handle special case for ID 0 - likely an invalid ID
        if (folder.id === 0) {
          console.warn('Folder ID 0 is invalid, navigating to root folder instead');
          return await navigateToRootFromDropdown();
        }

        // Find the full folder object from our folder list
        const fullFolder = folders.value.find(f => f.id === folder.id);

        if (fullFolder) {
          console.log('Found full folder data, using it for navigation from dropdown');
          // Use the selectFolder method with the full folder object
          await selectFolder(fullFolder);
          console.log('Successfully navigated to folder from dropdown');
        } else {
          console.warn(`Folder ${folder.id} not found in folders list, fetching from database`);

          // If folder not in our list, try to get it from the database
          try {
            const fetchedFolder = await db.folders.getById(folder.id);
            if (fetchedFolder) {
              console.log('Successfully fetched folder from database for dropdown navigation');
              // Add any missing properties needed for FolderWithMeta
              const folderWithMeta: FolderWithMeta = {
                ...fetchedFolder,
                children: [],
                notesCount: 0, // We don't know the count yet
                childFoldersCount: 0 // We don't know the count yet
              };

              // Navigate to the folder
              await selectFolder(folderWithMeta);
              console.log('Successfully navigated to fetched folder from dropdown');
            } else {
              // If we still can't find the folder, navigate to root instead
              console.warn(`Could not fetch folder ${folder.id}, navigating to root folder instead`);
              await navigateToRootFromDropdown();
            }
          } catch (dbError) {
            console.error(`Database error fetching folder ${folder.id} for dropdown navigation:`, dbError);
            // Navigate to root instead of using invalid folder data
            console.warn('Due to database error, navigating to root folder');
            await navigateToRootFromDropdown();
          }
        }
      } catch (err) {
        console.error('Error navigating to folder from dropdown:', err);
      }
      // Close the dropdown after navigation
      showPathDropdown.value = false;
    };

    // Navigate to NotesView and open a specific note
    const openNoteInNotesView = (noteId: number) => {
      if (!noteId) {
        console.error('Invalid note ID for opening in NotesView');
        return;
      }

      console.log(`Opening note ${noteId} in NotesView`);

      // Add note to recent items before navigating
      db.recentItems.addNote(noteId).catch(err => {
        console.error('Error adding note to recent items:', err);
      });

      // Navigate to NotesView with the note ID as a query parameter
      // This will allow NotesView to open this specific note
      window.location.href = `/#/notes?noteId=${noteId}`;
    };

    // Configure keybind functions
    setupFolderFunctions({
      createNewFolder: createNewFolderInCurrent,
      renameSelectedFolder: () => {
        // Find first selected folder in navigator and rename it
        if (navigatorSelectedItems.value.length > 0) {
          const folderId = navigatorSelectedItems.value[0];
          const folder = folders.value.find(f => f.id === folderId);
          if (folder) {
            renameFolder(folder);
          }
        }
      },
      deleteSelectedFolders: () => {
        if (navigatorSelectedItems.value.length > 0) {
          handleDeleteSelectedFolders(navigatorSelectedItems.value);
        }
      },
      enterSelectedFolder: () => {
        // Enter the first selected folder
        if (navigatorSelectedItems.value.length > 0) {
          const folderId = navigatorSelectedItems.value[0];
          const folder = folders.value.find(f => f.id === folderId);
          if (folder) {
            selectFolder(folder);
          }
        }
      },
      navigateUp: () => {
        // Navigate to parent folder or root
        if (selectedFolder.value && selectedFolder.value.parent_id !== null) {
          const parentFolder = folders.value.find(f => f.id === selectedFolder.value?.parent_id);
          if (parentFolder) {
            selectFolder(parentFolder);
          }
        } else if (selectedFolder.value) {
          // Navigate to root
          navigateToRootFromBreadcrumb();
        }
      },
      goToRoot: () => {
        navigateToRootFromBreadcrumb();
      },
      focusFolderSearch: () => {
        focusSearch();
      }
    });

    // Handle action from dashboard
    const handleCreateAction = async () => {
      try {
        // Clean URL first
        await router.replace({ query: { ...route.query, action: undefined } });
        
        // Wait for data loading if needed
        if (loading.value) {
          await new Promise<void>(resolve => {
            const unwatch = watch(() => loading.value, (isLoading) => {
              if (!isLoading) {
                unwatch();
                resolve();
              }
            });
          });
        }
        
        // Create folder at root level
        await createNewFolder(null);
      } catch (error) {
        console.error('Failed to create folder from dashboard action:', error);
      }
    };

    onMounted(() => {
      loadFolders().then(() => {
        // Handle action parameter
        const actionParam = route.query.action;
        if (actionParam === 'create') {
          handleCreateAction();
        }
      });
      // Activate keybinds for this view
      activateKeybinds();
    });    // Cleanup handlers when component is unmounted
    onBeforeUnmount(() => {
      // Deactivate keybinds
      deactivateKeybinds();
      
      // Clean up event listeners when component is unmounted
      if (documentClickHandler) {
        document.removeEventListener('click', documentClickHandler, true);
        documentClickHandler = null;
        console.log('Cleaned up document click handler on component unmount');
      }

      // Close any open dropdowns
      showPathDropdown.value = false;
    });
    
    // Watch for action parameter changes
    watch(() => route.query.action, async (action) => {
      if (action === 'create' && !loading.value) {
        await handleCreateAction();
      }
    });

    return {
      folders,
      folderHierarchy,
      filteredFolders,
      filteredHierarchy,
      rootNotesCount,
      searchQuery,
      loading,
      selectedFolder,
      selectedItemId,
      selectedItemType,
      openFolders,
      searchInputRef,
      renameInputRef,
      fileInput,
      checkedItems,
      navigatorSelectedItems,
      handleCheckedItemsChange,
      deleteCheckedItems,
      renameCheckedItem,
      moveCheckedItems,
      exportFolder,
      importToFolder,
      importNote,
      handleFileImport,
      sortFolder,
      searchInFolder,
      folderStats,
      folderToRename,
      folderNavigatorRef,
      noteToRename,
      showRenameModal,
      showRenameNoteModal,
      newFolderName,
      newNoteName,
      availableTargetFolders,
      isHierarchyAvailable, folderNotes,
      showExportModal,
      showSortModal,
      searchInFolderQuery,
      itemsToExport,
      showDeleteFolderModal,
      showDeleteNoteModal,
      showDeleteNotesModal,
      showDeleteMixedItemsModal,
      isAnyModalOpen,
      foldersToDelete,
      notesToDelete,
      noteTitle,
      noteTitles,
      mixedItemsToDelete,
      handleExportStart,
      handleExportComplete,
      handleSortComplete,
      currentSortBy,
      currentSortDirection,
      currentSortFoldersFirst,
      isExportingFile,
      exportProgressTitle,
      exportProgressMessage,
      exportProgressDetails,
      focusSearch,
      clearSearch,
      filterFolders,
      selectFolder,
      toggleFolderOpen,
      syncFolderPath,
      createNewFolder,
      navigateToRootFromBreadcrumb,
      navigateToFolderFromBreadcrumb,
      navigateToRootFromDropdown,
      navigateToFolderFromDropdown,
      updateFolderHierarchy,
      deleteFolder,
      renameFolder,
      cancelRename,
      confirmRename,
      inlineRenameFolder,
      saveInlineFolderName,
      cancelNoteRename,
      confirmNoteRename,
      getFolderPath,
      calculateTruncatedCount,
      getPathForDisplay,
      togglePathDropdown,
      showPathDropdown,
      getFolderNotes,
      loadNotesForFolder,
      loadRootNotes,
      formatDate,
      getFolderItems,
      handleSelectItem,
      createNewItem,
      createNewFolderInCurrent,
      createNewNoteInCurrent,
      moveFolder,
      confirmFolderDelete,
      confirmNoteDelete,
      confirmMixedItemsDelete, cancelDeleteModals, formatBytes,
      calculateStorageUsage,
      updateStorageStats,
      showNameFolderModal,
      folderToNameInitial,
      handleConfirmNameFolder,
      handleCancelNameFolder,
      showColorModal,
      folderToColor,
      handleFolderColorChange,
      handleColorModalClose,
      colorCheckedItems,
      openNoteInNotesView,
      handleNavigatorSelectionChange,
      handleDeleteSelectedFolders,
      showMoveModal,
      confirmMove,
      cancelMove,
      isProtectedFolderSelected
    };
  }
});
</script>

<style scoped>
.folders-view {
  display: flex;
  height: 100%;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  font-family: 'Montserrat', sans-serif;
}

.folders-sidebar {
  width: 350px;
  border-right: 1px solid var(--color-border-primary);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-bg);
  overflow: hidden;
  position: relative;
}

.overlap-wrapper {
  height: 100%;
  width: 100%;
}

.overlap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Sidebar Header */
.sidebar-header {
  padding: 24px;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Search Bar */
.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-input-bg);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid var(--color-input-border);
  cursor: text;
}

.search-bar:focus-within {
  background-color: var(--color-input-bg);
  border: 1px solid var(--color-input-focus);
}

.search-bar input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-input-text);
  padding: 0 8px;
  user-select: text;
  -webkit-user-select: text;
}

.search-bar input::placeholder {
  color: var(--color-input-placeholder);
}

.search-icon {
  width: 16px;
  height: 16px;
  color: var(--color-primary);
  margin-right: 2px;
}

.clear-search {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 14px;
  height: 14px;
  color: var(--color-primary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.plus-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* Folders List Section */
/* Breadcrumb section - fixed outside scrolling area */
.breadcrumb-section {
  flex-shrink: 0;
  width: 100%;
}

.breadcrumb-container-external {
  padding: 8px 12px;
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-secondary);
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
  z-index: 100;
}

/* Breadcrumb item styles */
.breadcrumb-item {
  color: var(--color-breadcrumb-text);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.breadcrumb-item:not(.breadcrumb-current):not(.truncated):hover {
  background-color: var(--color-breadcrumb-hover);
}

.breadcrumb-item.breadcrumb-current {
  font-weight: 600;
  color: var(--color-breadcrumb-current);
}

.breadcrumb-item.truncated {
  color: var(--color-breadcrumb-truncated);
  cursor: pointer;
  text-decoration: underline dotted;
  background-color: var(--color-breadcrumb-truncated-bg);
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin: 0 4px;
}

.breadcrumb-item.truncated:hover {
  background-color: var(--color-breadcrumb-truncated-hover);
}

.breadcrumb-separator {
  margin: 0 4px;
  color: var(--color-breadcrumb-separator);
}

/* Update overlap-group to use flexbox */
.overlap-group {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Make group-wrapper take remaining space */
.group-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  padding: 0;
  margin: 0;
}

.group-wrapper::-webkit-scrollbar {
  width: 8px;
}

.group-wrapper::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.group-wrapper::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.group-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Improve folder container styles */
.group {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Scroll fade effect removed per user request */
/* .group-wrapper::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, var(--color-bg-primary), transparent);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group-wrapper:not(:hover)::after {
  opacity: 1;
} */

.group {
  width: 100%;
  margin: 0;
  padding: 0;
}

.folder-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  /* Prevent horizontal scrolling */
  /* Add a max-height to ensure scrolling for many folders */
  max-height: 100%;
}

/* Scrollbar styles for .folder-list */
.folder-list::-webkit-scrollbar {
  width: 8px;
}

.folder-list::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.folder-list::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.folder-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.empty-folders,
.loading-folders {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: var(--color-loading-text);
  font-size: 14px;
  margin-top: 10px;
  font-style: italic;
}

.new-folder-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: var(--color-btn-secondary-bg);
  border: none;
  border-radius: 8px;
  color: var(--color-btn-secondary-text);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 16px;
  transition: background-color 0.2s;
}

.new-folder-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.delete-folder-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: var(--color-delete-btn-bg);
  border: none;
  border-radius: 8px;
  color: var(--color-delete-btn-text);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 16px;
  transition: background-color 0.2s;
}

.delete-folder-button:hover:not(:disabled) {
  background-color: var(--color-delete-btn-hover);
}

.delete-folder-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-delete-btn-disabled-bg);
  color: var(--color-delete-btn-disabled-text);
}

.trash-icon-disabled {
  filter: sepia(100%) saturate(200%) hue-rotate(0deg) brightness(0.8) !important;
}

.trash-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  filter: invert(21%) sepia(68%) saturate(3598%) hue-rotate(350deg) brightness(89%) contrast(86%);
}

.button-text {
  margin-left: 8px;
}

.rectangle-divider-2 {
  display: none;
  /* Not needed as .sidebar-header has border-bottom */
}

/* Footer Stats Section */
.footer-stats {
  background-color: var(--color-card-bg);
  height: 50px;
  width: 100%;
  border-top: 1px solid var(--color-border-primary);
  margin-top: auto;
}

.stats-content {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 0 24px;
  height: 100%;
}

.text-wrapper-2 {
  color: var(--color-text-secondary);
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  position: relative;
}

.folders-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-bg-secondary);
  overflow: auto;
}

/* Custom scrollbar styling for folders content */
.folders-content::-webkit-scrollbar {
  width: 8px;
}

.folders-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.folders-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.folders-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.folder-toolbar-instance {
  width: 100%;
  position: relative;
  margin-bottom: 10px;
}

/* Export Progress Overlay Styles */
.export-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-export-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20000;
  backdrop-filter: blur(2px);
}

.export-progress-modal {
  background-color: var(--color-export-modal-bg);
  border-radius: 16px;
  padding: 40px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px var(--color-modal-content-shadow);
  text-align: center;
  font-family: 'Montserrat', sans-serif;
}

.export-progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.export-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-export-spinner-track);
  border-top: 4px solid var(--color-export-spinner-active);
  border-radius: 50%;
  animation: export-spin 1s linear infinite;
}

@keyframes export-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.export-progress-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-export-title);
}

.export-progress-message {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-export-message);
}

.export-progress-details {
  margin: 0;
  font-size: 14px;
  color: var(--color-export-details);
  font-style: italic;
}

.export-progress-details small {
  display: block;
  max-width: 300px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  z-index: 5;
}

.folder-content-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.root-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  flex: 1;
  text-align: center;
  color: var(--color-text-secondary);
}

.folder-large-icon {
  width: 80px;
  height: 80px;
  opacity: 0.6;
  margin-bottom: 20px;
}

/* Folder empty state */
.folder-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--color-text-secondary);
}

.note-empty-icon {
  width: 48px;
  height: 48px;
  opacity: 0.5;
  margin-bottom: 16px;
}

.folder-notes-list {
  padding: 24px;
}

.note-count {
  color: var(--color-text-secondary);
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
}

.folder-large-icon,
.note-large-icon {
  width: 64px;
  height: 64px;
  opacity: 0.5;
  margin-bottom: 16px;
}

.folder-content-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.folder-header {
  background-color: var(--color-bg-primary);
  display: flex;
  width: 100%;
  padding-top: 22px;
  flex-direction: column;
  align-items: stretch;
  color: var(--color-text-primary);
  position: relative;
  z-index: 950;
  /* Increased z-index to be higher than toolbar (900) */
}

.header-content {
  align-self: center;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.folder-path {
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
}

.folder-path-item {
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
}

.folder-path-item:hover {
  background-color: var(--color-nav-item-hover);
}

.folder-path-unclickable {
  cursor: default;
}

.folder-path-unclickable:hover {
  background-color: transparent;
}

.folder-path-current {
  font-weight: 600;
  color: var(--color-text-primary);
}

.folder-path-separator {
  margin: 0 4px;
  color: var(--color-text-tertiary);
}

.folder-path-truncated {
  color: var(--color-breadcrumb-truncated);
  cursor: pointer;
  text-decoration: underline dotted;
}

.folder-path-dropdown {
  position: fixed;
  background-color: var(--color-path-dropdown-bg);
  border: 1px solid var(--color-path-dropdown-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--color-path-dropdown-shadow);
  z-index: 10000;
  /* Use highest z-index to ensure it appears above everything */
  padding: 8px;
  display: flex;
  flex-direction: column;
  width: 250px;
  overflow: hidden;
  animation: dropdown-appear 0.2s ease-out forwards;
  pointer-events: auto;
  /* Ensure click events work */
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.path-dropdown-content {
  display: flex;
  flex-direction: column;
  max-height: 350px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  padding-right: 4px;
}

.path-dropdown-content::-webkit-scrollbar {
  width: 8px;
}

.path-dropdown-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.path-dropdown-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.path-dropdown-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.path-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  margin: 2px 0;
  transition: all 0.2s ease;
  color: var(--color-path-dropdown-item);
  flex-shrink: 0;
  min-height: 36px;
}

.path-dropdown-item:hover {
  background-color: var(--color-path-dropdown-hover);
  color: var(--color-path-dropdown-hover-text);
  transform: translateX(2px);
}

.path-dropdown-unclickable {
  cursor: default;
  opacity: 0.7;
}

path-dropdown-unclickable:hover {
  background-color: transparent;
  transform: none;
}

.path-dropdown-item.current {
  font-weight: 600;
  color: var(--color-path-dropdown-current);
  background-color: var(--color-path-dropdown-current-bg);
}

.path-dropdown-item::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  min-width: 16px;
  background: url('/icons/folder-icon.svg') no-repeat center;
  background-size: contain;
  margin-right: 8px;
  opacity: 0.7;
  flex-shrink: 0;
}

.path-dropdown-item:first-child::before {
  background-image: url('/icons/folder-open-icon.svg');
}

.header-divider {
  background-color: var(--color-border-primary);
  height: 1px;
  margin-top: 22px;
  width: 100%;
}

.folder-actions-header {
  display: flex;
  gap: 8px;
}

.folder-actions-header .folder-action-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--color-folder-action-bg);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.folder-actions-header .folder-action-btn span {
  margin-left: 6px;
}

.folder-actions-header .folder-action-btn:hover {
  background-color: var(--color-folder-action-hover);
}

.folder-actions-header .delete-btn {
  color: var(--color-folder-action-delete);
}

.folder-actions-header .delete-btn:hover {
  background-color: var(--color-folder-action-delete-hover);
}

.folder-notes-container {
  margin-top: 16px;
}

.folder-notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.folder-notes-header h2 {
  margin: 0;
  font-size: 20px;
  color: var(--color-folder-notes-title);
}

.folder-notes-header p {
  color: var(--color-folder-notes-desc);
  font-size: 14px;
}

.new-note-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
}

.new-note-button:hover {
  background-color: var(--color-btn-primary-hover);
}

.folder-notes {
  flex: 1;
  overflow-y: auto;
}

/* Custom scrollbar styling for folder notes */
.folder-notes::-webkit-scrollbar {
  width: 8px;
}

.folder-notes::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.folder-notes::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.folder-notes::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.note-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.note-preview {
  border: 1px solid var(--color-card-border);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--color-card-bg);
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
}

.note-preview:hover {
  box-shadow: 0 3px 8px var(--color-card-hover-shadow);
  transform: translateY(-2px);
}

.note-preview h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: var(--color-text-primary);
}

.note-date {
  margin: 0;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.notes-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--color-text-secondary);
}

.create-note-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.create-note-btn .btn-plus-icon {
  width: 14px;
  height: 14px;
}

.create-note-btn:hover {
  background-color: var(--color-btn-primary-hover);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay-alt);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  /* Updated to match sidebar z-index for proper overlay coverage */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--color-modal-content-bg);
  border-radius: 12px;
  padding: 28px;
  width: 420px;
  max-width: 90%;
  box-shadow: 0 10px 25px var(--color-modal-content-shadow);
  animation: modal-appear 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 18px;
  color: var(--color-modal-title-alt);
  font-size: 22px;
  font-weight: 700;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: var(--color-btn-ripple);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.btn:active::after {
  opacity: 1;
  width: 100%;
  height: 100%;
  border-radius: 0;
  transform: scale(0, 0) translate(-50%, -50%);
  transition: transform 0.3s, opacity 0.3s;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  box-shadow: 0 2px 4px var(--color-card-shadow);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  box-shadow: 0 4px 8px var(--color-card-hover-shadow);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  box-shadow: 0 2px 4px var(--color-card-shadow);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
  box-shadow: 0 4px 8px var(--color-card-hover-shadow);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-btn-primary-text);
  box-shadow: 0 2px 4px var(--color-card-shadow);
}

.btn-danger:hover {
  background-color: var(--color-error);
  box-shadow: 0 4px 8px var(--color-card-hover-shadow);
  transform: translateY(-1px);
  filter: brightness(0.9);
}

.rename-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-form-input-border);
  border-radius: 4px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: var(--color-form-input-text);
  background-color: var(--color-input-bg);
}

.folder-notes-warning {
  margin: 20px 0;
  padding: 12px;
  background-color: var(--color-warning-bg);
  border-radius: 4px;
  border-left: 4px solid var(--color-warning-border);
}

.notes-target-selector {
  margin-top: 12px;
}

.notes-target-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.notes-target-selector select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--color-form-input-border);
  border-radius: 4px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: var(--color-form-input-text);
  background-color: var(--color-input-bg);
}
</style>