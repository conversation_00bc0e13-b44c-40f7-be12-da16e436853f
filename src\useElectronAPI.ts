// Composable to access Electron API in a type-safe way
import type { NotesAP<PERSON>, FoldersAPI, RecentItemsAPI, BooksAPI, TimerAPI } from './types/electron-api';
import { dbApi } from './types/mock-api';

// Discord API interface
export interface DiscordAPI {
  initialize: () => Promise<boolean>;
  setEnabled: (enabled: boolean) => Promise<boolean>;
  setActivity: (activityData: any) => Promise<boolean>;
  setActiveState: () => Promise<boolean>;
  setIdle: () => Promise<boolean>;
  updateSettings: (settings: any) => Promise<boolean>;
  clearActivity: () => Promise<boolean>;
  getStatus: () => Promise<{ connected: boolean; enabled: boolean; settings: any }>;
  destroy: () => Promise<boolean>;
  testConnection: () => Promise<boolean>;
}

// Settings API interface
export interface SettingsAPI {
  get: (key: string) => Promise<any>;
  getByCategory: (category: string) => Promise<any[]>;
  getAll: () => Promise<any[]>;
  set: (key: string, value: any, category?: string) => Promise<any>;
  delete: (key: string) => Promise<any>;
  getActiveTheme: () => Promise<any>;
  getAllThemes: () => Promise<any[]>;
  createTheme: (themeName: string) => Promise<any>;
  setActiveTheme: (themeId: number) => Promise<any>;
  deleteTheme: (themeId: number) => Promise<any>;
}


// Structure to match the exposed API from preload/index.ts
export interface ElectronAPI {
  notes: NotesAPI;
  folders: FoldersAPI;
  recentItems: RecentItemsAPI;
  books: BooksAPI;
  timer: TimerAPI;
  discord: DiscordAPI;
  settings: SettingsAPI;
}

/**
 * Composable to access the Electron API bridge
 * This function provides access to the database API, either through
 * the real Electron IPC bridge or through a mock implementation for development
 */
export function useElectronAPI(): ElectronAPI {
  // In real Electron environment, window.db and window.electronAPI are available
  // In development or test environment, we use the mock
  const isElectron = typeof window !== 'undefined' && 
                     typeof (window as any).db !== 'undefined' && 
                     (window as any).db !== null;
  
  if (isElectron) {
    // Combine APIs from both window.db and window.electronAPI
    const dbAPI = (window as any).db;
    const electronAPI = (window as any).electronAPI;
    
    return {
      ...dbAPI,
      settings: electronAPI?.settings || dbAPI?.settings
    };
  } else {
    // Use the mock API for development
    return dbApi as ElectronAPI;
  }
}
