# Page Load Monitoring System

## Files Modified
- `src/utils/pageLoadMonitor.ts` - Core monitoring utility
- `src/composables/usePageLoadMonitoring.ts` - Vue composable wrapper
- `src/router/index.ts` - Router integration
- `src/main.ts` - Global utilities setup
- `src/views/DashboardView.vue` - Added monitoring
- `src/views/NotesView.vue` - Added monitoring
- `src/views/BooksView.vue` - Added monitoring
- `src/views/FoldersView.vue` - Added monitoring
- `src/views/TimerView.vue` - Added monitoring
- `src/views/SettingsView.vue` - Added monitoring
- `scripts/add-monitoring-to-views.js` - Automation script

## What Was Done
Implemented a comprehensive page load monitoring system that tracks navigation timing data when clicking sidebar navigation items. The system provides clean data collection, analysis, and export functionality.

## How It Works

### Core Components

1. **PageLoadMonitor Class** (`src/utils/pageLoadMonitor.ts`)
   - Hooks into Vue Router navigation lifecycle
   - Tracks component load times and mount times
   - Stores data in localStorage for persistence
   - Provides data analysis and export capabilities

2. **Vue Composable** (`src/composables/usePageLoadMonitoring.ts`)
   - Provides easy integration with Vue components
   - Auto-records page mount events
   - Includes utility functions for reporting and data management

3. **Router Integration** (`src/router/index.ts`)
   - Automatically tracks navigation start/end events
   - Measures component loading time

### Metrics Collected

For each navigation, the system tracks:
- **Navigation Start Time**: When user clicks sidebar item
- **Component Load Time**: Time to load Vue component
- **Mount Time**: Time for component to fully mount
- **Total Load Time**: Complete navigation duration
- **First Visit Flag**: Whether this is the first visit to the route
- **Memory Usage**: JavaScript heap size (if available)
- **Route Information**: From/to routes and route names

### Usage Instructions

#### Browser Console Commands
The system exposes global utilities at `window.pagePerf`:

```javascript
// View performance report in console
pagePerf.report()

// Download CSV file with all metrics
pagePerf.download()

// Clear all collected data
pagePerf.clear()

// Enable/disable monitoring
pagePerf.enable()
pagePerf.disable()

// Get current metrics count
pagePerf.count()

// Get raw data object
pagePerf.raw()

// Get CSV string
pagePerf.csv()
```

#### Programmatic Usage
```typescript
import { usePageLoadMonitoring } from '@/composables/usePageLoadMonitoring'

const { 
  generateReport, 
  downloadCSV, 
  printReport, 
  clearData 
} = usePageLoadMonitoring()

// Generate detailed report
const report = generateReport()
console.log('Average load time:', report.averageLoadTime)

// Download data as CSV
downloadCSV('my-metrics.csv')
```

### Data Analysis Features

#### Performance Report Includes:
- **Total Navigations**: Count of all tracked navigations
- **Average Load Time**: Mean load time across all navigations
- **Fastest/Slowest Loads**: Best and worst performing navigations
- **Route Averages**: Performance breakdown by route
- **First vs Subsequent Visits**: Performance comparison

#### CSV Export Contains:
- Timestamp of navigation
- From/to route paths
- Route name
- Component load time (ms)
- Total load time (ms)
- First visit flag
- Memory usage (MB)

### Implementation Details

#### Automatic Integration
All view components now include monitoring via:
```typescript
// Page load monitoring
const { autoRecordPageMounted } = usePageLoadMonitoring()
autoRecordPageMounted('RouteName')
```

#### Data Persistence
- Metrics stored in localStorage as `pageLoadMetrics`
- Survives browser refreshes and app restarts
- Automatic cleanup of invalid entries

#### Performance Optimizations
- Minimal overhead during navigation
- Efficient data structures for large datasets
- Automatic memory management

### Testing the System

1. **Start Monitoring**: Navigate between sidebar items (Dashboard, Notes, Books, etc.)
2. **View Results**: Open browser console and run `pagePerf.report()`
3. **Export Data**: Run `pagePerf.download()` to get CSV file
4. **Analyze Patterns**: Look for differences between first visits vs subsequent visits

### Expected Results

- **First Visits**: Typically slower due to component loading and data fetching
- **Subsequent Visits**: Faster due to caching and pre-loaded components
- **Route Differences**: Some routes may be consistently slower due to data complexity

### Automation Script

The included script (`scripts/add-monitoring-to-views.js`) can automatically add monitoring to new view components:

```bash
node scripts/add-monitoring-to-views.js
```

### Memory Considerations

- Each metric entry is ~200 bytes
- 1000 navigations ≈ 200KB storage
- Automatic cleanup prevents unbounded growth
- Can be disabled if not needed: `pagePerf.disable()`

## Benefits

1. **Data-Driven Optimization**: Identify slow-loading pages
2. **User Experience Insights**: Understand navigation patterns
3. **Performance Regression Detection**: Track changes over time
4. **Clean Data Collection**: No manual timing required
5. **Export Capabilities**: Share data with team or tools

## Future Enhancements

- Integration with performance monitoring services
- Automatic performance alerts
- Component-level timing breakdown
- Network timing analysis
- User interaction tracking
