#!/usr/bin/env ts-node

/**
 * Test script for .noti file format export and import functionality
 * 
 * This script tests:
 * 1. Exporting notes to .noti format with embedded media
 * 2. Importing .noti files with media restoration
 * 3. Integrity verification
 * 4. Error handling and validation
 * 5. Performance with large files
 */

import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { app } from 'electron';

// Import the notes API
import notesApi from '../electron/main/api/notes-api';
import mediaApi from '../electron/main/api/media-api';
import { Note } from '../electron/main/database/database-api';
import { initializeDatabase, closeDatabase } from '../electron/main/database/database';

// Test data constants
const TEST_OUTPUT_DIR = path.join(process.cwd(), 'test-output');
const TEST_MEDIA_DIR = path.join(process.cwd(), 'test-media');

// Color codes for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// Helper functions
const log = {
    info: (msg: string) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
    success: (msg: string) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    error: (msg: string) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
    warning: (msg: string) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
    test: (name: string) => console.log(`\n${colors.cyan}${colors.bright}[TEST]${colors.reset} ${name}`),
    result: (passed: boolean, msg: string) => {
        if (passed) {
            console.log(`  ${colors.green}✓${colors.reset} ${msg}`);
        } else {
            console.log(`  ${colors.red}✗${colors.reset} ${msg}`);
        }
    }
};

// Setup test directories
const setupTestEnvironment = async () => {
    // Create test directories
    if (!fs.existsSync(TEST_OUTPUT_DIR)) {
        fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
    }
    if (!fs.existsSync(TEST_MEDIA_DIR)) {
        fs.mkdirSync(TEST_MEDIA_DIR, { recursive: true });
    }
    
    // Initialize database
    await initializeDatabase();
    
    log.info('Test environment setup complete');
};

// Cleanup test directories
const cleanupTestEnvironment = async () => {
    // Remove test directories
    if (fs.existsSync(TEST_OUTPUT_DIR)) {
        fs.rmSync(TEST_OUTPUT_DIR, { recursive: true, force: true });
    }
    if (fs.existsSync(TEST_MEDIA_DIR)) {
        fs.rmSync(TEST_MEDIA_DIR, { recursive: true, force: true });
    }
    
    // Close database
    await closeDatabase();
    
    log.info('Test environment cleanup complete');
};

// Create a test image
const createTestImage = (filename: string): Buffer => {
    // Create a simple 1x1 pixel PNG
    const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
        0x89, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02,
        0x00, 0x01, 0xE5, 0x27, 0xDE, 0xFC, 0x00, 0x00, // IEND chunk
        0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42,
        0x60, 0x82
    ]);
    
    const filepath = path.join(TEST_MEDIA_DIR, filename);
    fs.writeFileSync(filepath, pngData);
    
    return pngData;
};

// Test 1: Basic Export
const testBasicExport = async (): Promise<boolean> => {
    log.test('Basic Export Test');
    
    try {
        // Create a test note
        const testNote: Note = {
            title: 'Test Note for Export',
            content: '# Test Note\n\nThis is a test note with **markdown** content.',
            html_content: '<h1>Test Note</h1><p>This is a test note with <strong>markdown</strong> content.</p>',
            type: 'text',
            color: '#ff0000'
        };
        
        const createdNote = await notesApi.createNote(testNote);
        log.result(true, 'Created test note');
        
        // Export the note
        const exportPath = path.join(TEST_OUTPUT_DIR, `${createdNote.title}.noti`);
        await notesApi.exportNoteToNotiToPath(createdNote, exportPath);
        log.result(true, 'Exported note to .noti format');
        
        // Verify file exists
        const fileExists = fs.existsSync(exportPath);
        log.result(fileExists, 'Export file exists');
        
        // Parse and validate the exported file
        const exportedContent = fs.readFileSync(exportPath, 'utf8');
        const exportedData = JSON.parse(exportedContent);
        
        // Validate structure
        const hasCorrectStructure = 
            exportedData.version === "1.0" &&
            exportedData.type === "noti-note" &&
            exportedData.schema === "https://noti.app/schemas/note/v1.0" &&
            exportedData.metadata && 
            exportedData.content &&
            exportedData.integrity;
            
        log.result(hasCorrectStructure, 'Exported file has correct structure');
        
        // Validate content
        const contentMatches = 
            exportedData.metadata.title === testNote.title &&
            exportedData.content.markdown === testNote.content &&
            exportedData.content.html === testNote.html_content;
            
        log.result(contentMatches, 'Content matches original note');
        
        // Validate integrity
        const contentToHash = JSON.stringify({
            version: exportedData.version,
            type: exportedData.type,
            schema: exportedData.schema,
            metadata: exportedData.metadata,
            content: exportedData.content,
            media: exportedData.media
        });
        const calculatedHash = crypto.createHash('sha256').update(contentToHash).digest('hex');
        const integrityValid = calculatedHash === exportedData.integrity.content_hash;
        
        log.result(integrityValid, 'Integrity hash is valid');
        
        // Cleanup
        await notesApi.deleteNote(createdNote.id!);
        
        return fileExists && hasCorrectStructure && contentMatches && integrityValid;
    } catch (error) {
        log.error(`Test failed: ${error}`);
        return false;
    }
};

// Test 2: Export with Media
const testExportWithMedia = async (): Promise<boolean> => {
    log.test('Export with Media Test');
    
    try {
        // Create a test note
        const testNote: Note = {
            title: 'Test Note with Images',
            content: 'This note has embedded images',
            html_content: '<p>This note has embedded images</p><img src="noti-media://test1.png">',
            type: 'text'
        };
        
        const createdNote = await notesApi.createNote(testNote);
        log.result(true, 'Created test note');
        
        // Add test image to note
        const imageBuffer = createTestImage('test1.png');
        await mediaApi.saveMediaFile(
            imageBuffer,
            'test1.png',
            'image/png',
            createdNote.id!,
            null,
            false
        );
        log.result(true, 'Added test image to note');
        
        // Export the note
        const exportPath = path.join(TEST_OUTPUT_DIR, `${createdNote.title}.noti`);
        await notesApi.exportNoteToNotiToPath(createdNote, exportPath);
        log.result(true, 'Exported note with media');
        
        // Parse and validate the exported file
        const exportedContent = fs.readFileSync(exportPath, 'utf8');
        const exportedData = JSON.parse(exportedContent);
        
        // Check media array
        const hasMedia = exportedData.media && exportedData.media.length > 0;
        log.result(hasMedia, 'Exported file contains media');
        
        if (hasMedia) {
            const media = exportedData.media[0];
            const hasEmbeddedData = media.embedded && media.data && media.file_name === 'test1.png';
            log.result(hasEmbeddedData, 'Media is properly embedded');
        }
        
        // Cleanup
        await notesApi.deleteNote(createdNote.id!);
        
        return hasMedia;
    } catch (error) {
        log.error(`Test failed: ${error}`);
        return false;
    }
};

// Test 3: Basic Import
const testBasicImport = async (): Promise<boolean> => {
    log.test('Basic Import Test');
    
    try {
        // Create a .noti file manually
        const testNotiData = {
            version: "1.0",
            type: "noti-note",
            schema: "https://noti.app/schemas/note/v1.0",
            metadata: {
                title: "Imported Test Note",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                type: "text",
                color: "#00ff00",
                export: {
                    version: "1.0.0",
                    app_version: "1.0.0",
                    exported_at: new Date().toISOString()
                }
            },
            content: {
                html: "<h1>Imported Note</h1><p>This is an imported note.</p>",
                markdown: "# Imported Note\n\nThis is an imported note.",
                plain_text: "Imported Note\n\nThis is an imported note.",
                statistics: {
                    word_count: 5,
                    character_count: 30,
                    reading_time: 1
                }
            },
            media: []
        };
        
        // Add integrity
        const contentToHash = JSON.stringify({
            version: testNotiData.version,
            type: testNotiData.type,
            schema: testNotiData.schema,
            metadata: testNotiData.metadata,
            content: testNotiData.content,
            media: testNotiData.media
        });
        const contentHash = crypto.createHash('sha256').update(contentToHash).digest('hex');
        (testNotiData as any).integrity = {
            algorithm: "sha256",
            content_hash: contentHash
        };
        
        const notiContent = JSON.stringify(testNotiData);
        log.result(true, 'Created test .noti data');
        
        // Import the note
        const importedNote = await notesApi.importNote(notiContent, 'noti', 'Imported Note');
        log.result(true, 'Imported .noti file');
        
        // Verify imported content
        const titleMatches = importedNote.title === testNotiData.metadata.title;
        const contentMatches = importedNote.html_content === testNotiData.content.html;
        const colorMatches = importedNote.color === testNotiData.metadata.color;
        
        log.result(titleMatches, 'Title imported correctly');
        log.result(contentMatches, 'Content imported correctly');
        log.result(colorMatches, 'Metadata imported correctly');
        
        // Cleanup
        await notesApi.deleteNote(importedNote.id!);
        
        return titleMatches && contentMatches && colorMatches;
    } catch (error) {
        log.error(`Test failed: ${error}`);
        return false;
    }
};

// Test 4: Import with Media
const testImportWithMedia = async (): Promise<boolean> => {
    log.test('Import with Media Test');
    
    try {
        // Create test image data
        const imageBuffer = createTestImage('import-test.png');
        const base64Data = imageBuffer.toString('base64');
        
        // Create a .noti file with embedded media
        const testNotiData = {
            version: "1.0",
            type: "noti-note",
            schema: "https://noti.app/schemas/note/v1.0",
            metadata: {
                title: "Note with Embedded Image",
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                type: "text",
                export: {
                    version: "1.0.0",
                    app_version: "1.0.0",
                    exported_at: new Date().toISOString()
                }
            },
            content: {
                html: '<p>Note with image</p><img src="noti-media://import-test.png">',
                markdown: "Note with image",
                plain_text: "Note with image",
                statistics: {
                    word_count: 3,
                    character_count: 15,
                    reading_time: 1
                }
            },
            media: [{
                id: "media_1",
                file_name: "import-test.png",
                file_type: "image/png",
                file_size: imageBuffer.length,
                original_path: "noti-media://import-test.png",
                embedded: true,
                data: base64Data
            }]
        };
        
        // Add integrity
        const contentToHash = JSON.stringify({
            version: testNotiData.version,
            type: testNotiData.type,
            schema: testNotiData.schema,
            metadata: testNotiData.metadata,
            content: testNotiData.content,
            media: testNotiData.media
        });
        const contentHash = crypto.createHash('sha256').update(contentToHash).digest('hex');
        (testNotiData as any).integrity = {
            algorithm: "sha256",
            content_hash: contentHash
        };
        
        const notiContent = JSON.stringify(testNotiData);
        log.result(true, 'Created test .noti data with media');
        
        // Import the note
        const importedNote = await notesApi.importNote(notiContent, 'noti', 'Imported Note');
        log.result(true, 'Imported .noti file with media');
        
        // Check if media was imported
        const mediaFiles = await mediaApi.getMediaFilesByNoteId(importedNote.id!);
        const hasMedia = mediaFiles.length > 0;
        log.result(hasMedia, 'Media files were imported');
        
        if (hasMedia) {
            const mediaExists = fs.existsSync(mediaFiles[0].file_path);
            log.result(mediaExists, 'Media file exists on disk');
        }
        
        // Cleanup
        await notesApi.deleteNote(importedNote.id!);
        
        return hasMedia;
    } catch (error) {
        log.error(`Test failed: ${error}`);
        return false;
    }
};

// Test 5: Error Handling
const testErrorHandling = async (): Promise<boolean> => {
    log.test('Error Handling Test');
    
    let allTestsPassed = true;
    
    // Test invalid JSON
    try {
        await notesApi.importNote('invalid json', 'noti', 'Test');
        log.result(false, 'Invalid JSON should throw error');
        allTestsPassed = false;
    } catch (error) {
        log.result(true, 'Invalid JSON correctly rejected');
    }
    
    // Test missing required fields
    try {
        const invalidData = {
            version: "1.0",
            type: "noti-note"
            // Missing content and metadata
        };
        await notesApi.importNote(JSON.stringify(invalidData), 'noti', 'Test');
        log.result(false, 'Missing fields should throw error');
        allTestsPassed = false;
    } catch (error) {
        log.result(true, 'Missing fields correctly rejected');
    }
    
    // Test invalid version
    try {
        const invalidData = {
            version: "2.0", // Unsupported version
            type: "noti-note",
            metadata: {},
            content: {}
        };
        await notesApi.importNote(JSON.stringify(invalidData), 'noti', 'Test');
        log.result(false, 'Invalid version should throw error');
        allTestsPassed = false;
    } catch (error) {
        log.result(true, 'Invalid version correctly rejected');
    }
    
    // Test file size limit
    try {
        const largeContent = 'x'.repeat(51 * 1024 * 1024); // 51MB
        await notesApi.importNote(largeContent, 'noti', 'Test');
        log.result(false, 'Large file should throw error');
        allTestsPassed = false;
    } catch (error) {
        log.result(true, 'Large file correctly rejected');
    }
    
    return allTestsPassed;
};

// Test 6: Round-trip Test
const testRoundTrip = async (): Promise<boolean> => {
    log.test('Round-trip Export/Import Test');
    
    try {
        // Create a complex note
        const testNote: Note = {
            title: 'Complex Test Note',
            content: '# Complex Note\n\n- List item 1\n- List item 2\n\n**Bold** and *italic* text.',
            html_content: '<h1>Complex Note</h1><ul><li>List item 1</li><li>List item 2</li></ul><p><strong>Bold</strong> and <em>italic</em> text.</p>',
            type: 'text',
            color: '#0000ff'
        };
        
        const originalNote = await notesApi.createNote(testNote);
        log.result(true, 'Created original note');
        
        // Add image
        const imageBuffer = createTestImage('roundtrip.png');
        await mediaApi.saveMediaFile(
            imageBuffer,
            'roundtrip.png',
            'image/png',
            originalNote.id!,
            null,
            false
        );
        
        // Export
        const exportPath = path.join(TEST_OUTPUT_DIR, 'roundtrip.noti');
        await notesApi.exportNoteToNotiToPath(originalNote, exportPath);
        log.result(true, 'Exported note');
        
        // Delete original
        await notesApi.deleteNote(originalNote.id!);
        
        // Import
        const exportedContent = fs.readFileSync(exportPath, 'utf8');
        const importedNote = await notesApi.importNote(exportedContent, 'noti', 'Imported');
        log.result(true, 'Re-imported note');
        
        // Compare
        const titleMatches = importedNote.title === originalNote.title;
        const contentMatches = importedNote.content === originalNote.content;
        const htmlMatches = importedNote.html_content === originalNote.html_content;
        const colorMatches = importedNote.color === originalNote.color;
        
        log.result(titleMatches && contentMatches && htmlMatches && colorMatches, 'Round-trip data preserved');
        
        // Check media
        const mediaFiles = await mediaApi.getMediaFilesByNoteId(importedNote.id!);
        log.result(mediaFiles.length > 0, 'Media preserved in round-trip');
        
        // Cleanup
        await notesApi.deleteNote(importedNote.id!);
        
        return titleMatches && contentMatches && htmlMatches && colorMatches && mediaFiles.length > 0;
    } catch (error) {
        log.error(`Test failed: ${error}`);
        return false;
    }
};

// Main test runner
const runTests = async () => {
    console.log(`\n${colors.bright}${colors.magenta}=== Noti Format Test Suite ===${colors.reset}\n`);
    
    await setupTestEnvironment();
    
    const tests = [
        { name: 'Basic Export', fn: testBasicExport },
        { name: 'Export with Media', fn: testExportWithMedia },
        { name: 'Basic Import', fn: testBasicImport },
        { name: 'Import with Media', fn: testImportWithMedia },
        { name: 'Error Handling', fn: testErrorHandling },
        { name: 'Round-trip Export/Import', fn: testRoundTrip }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) {
                passed++;
                log.success(`${test.name} PASSED`);
            } else {
                failed++;
                log.error(`${test.name} FAILED`);
            }
        } catch (error) {
            failed++;
            log.error(`${test.name} FAILED with error: ${error}`);
        }
    }
    
    console.log(`\n${colors.bright}=== Test Results ===${colors.reset}`);
    console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
    console.log(`${colors.red}Failed: ${failed}${colors.reset}`);
    console.log(`Total: ${tests.length}\n`);
    
    await cleanupTestEnvironment();
    
    process.exit(failed > 0 ? 1 : 0);
};

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(error => {
        log.error(`Test suite failed: ${error}`);
        process.exit(1);
    });
}

export { runTests };