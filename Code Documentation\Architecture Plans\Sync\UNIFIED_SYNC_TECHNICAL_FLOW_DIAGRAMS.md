# Unified Sync System: Technical Flow Diagrams

## Core Sync Loop

```
┌─────────────────────────────────────────────────────────────────┐
│                     UNIFIED SYNC ENGINE                          │
│                                                                  │
│  Every X seconds (auto) or on-demand (manual):                  │
│                                                                  │
│  1. READ MANIFEST from backup directory                         │
│     ↓                                                           │
│  2. COMPARE with local database                                │
│     ↓                                                           │
│  3. DETERMI<PERSON> what changed where                               │
│     ↓                                                           │
│  4. APPLY CHANGES bidirectionally                              │
│     ↓                                                           │
│  5. WRITE UPDATED MANIFEST                                     │
│                                                                  │
│  That's the entire system.                                      │
└─────────────────────────────────────────────────────────────────┘
```

## Detailed Sync Flow

```
START SYNC
    │
    ▼
┌─────────────────────────┐
│ Check if directory      │
│ exists and is accessible│
└───────────┬─────────────┘
            │
            ▼
┌─────────────────────────┐     NO      ┌─────────────────────┐
│ manifest.json exists?   │─────────────▶│ First sync!         │
└───────────┬─────────────┘              │ Create manifest     │
            │ YES                        │ from database       │
            ▼                            └─────────────────────┘
┌─────────────────────────┐
│ Load manifest into      │
│ memory                  │
└───────────┬─────────────┘
            │
            ▼
┌─────────────────────────┐
│ Query database for      │
│ all notes, folders,     │
│ books                   │
└───────────┬─────────────┘
            │
            ▼
┌─────────────────────────────────────────────────────┐
│                 COMPARISON LOGIC                     │
│                                                      │
│  For each item in manifest:                         │
│    - Not in DB? → Import to DB                     │
│    - In DB but different? → Resolve conflict       │
│                                                      │
│  For each item in DB:                              │
│    - Not in manifest? → Export to backup           │
│    - In manifest but different? → Already handled  │
│                                                      │
│  For each deletion in manifest:                    │
│    - Still exists in DB? → Delete from DB          │
└───────────┬─────────────────────────────────────────┘
            │
            ▼
┌─────────────────────────┐
│ Apply all changes       │
│ (import/export/delete)  │
└───────────┬─────────────┘
            │
            ▼
┌─────────────────────────┐
│ Update manifest with    │
│ current state           │
└───────────┬─────────────┘
            │
            ▼
         END SYNC
```

## Change Detection Algorithm

```
MANIFEST STATE                    DATABASE STATE
┌──────────────┐                 ┌──────────────┐
│ note-123     │                 │ note-123     │
│ hash: abc... │ ◀─── COMPARE ──▶│ hash: abc... │ → NO CHANGE
│ modified: 1pm│                 │ modified: 1pm│
└──────────────┘                 └──────────────┘

┌──────────────┐                 ┌──────────────┐
│ note-456     │                 │ note-456     │
│ hash: def... │ ◀─── COMPARE ──▶│ hash: xyz... │ → CONFLICT!
│ modified: 2pm│                 │ modified: 3pm│   (DB is newer)
└──────────────┘                 └──────────────┘

┌──────────────┐                 ┌──────────────┐
│ note-789     │                 │     NOT      │
│ hash: ghi... │ ◀─── COMPARE ──▶│    FOUND     │ → IMPORT TO DB
│ modified: 4pm│                 │              │
└──────────────┘                 └──────────────┘

┌──────────────┐                 ┌──────────────┐
│     NOT      │                 │ note-999     │
│    FOUND     │ ◀─── COMPARE ──▶│ hash: jkl... │ → EXPORT TO BACKUP
│              │                 │ modified: 5pm│
└──────────────┘                 └──────────────┘
```

## Conflict Resolution Flow

```
                    CONFLICT DETECTED
                           │
                           ▼
                ┌──────────────────────┐
                │ Compare timestamps   │
                └──────────┬───────────┘
                           │
        ┌──────────────────┴──────────────────┐
        │                                     │
        ▼                                     ▼
┌─────────────────┐                  ┌─────────────────┐
│ Local is newer  │                  │ Remote is newer │
└────────┬────────┘                  └────────┬────────┘
         │                                     │
         ▼                                     ▼
┌─────────────────┐                  ┌─────────────────┐
│ Export local    │                  │ Import remote   │
│ to backup       │                  │ to database     │
└─────────────────┘                  └─────────────────┘
         │                                     │
         └──────────────┬──────────────────────┘
                        ▼
                ┌─────────────────┐
                │ Update manifest │
                │ with resolution │
                └─────────────────┘
```

## Multi-Device Sync Scenario

```
DAY 1 - PC Setup:
┌─────────────────────────────────────────────────────────┐
│ PC: C:\Users\<USER>\OneDrive\Noti\                       │
│                                                         │
│ 1. User creates notes                                   │
│ 2. Sync engine writes:                                 │
│    - manifest.json                                      │
│    - Books/MyBook/chapter1.md                          │
│    - Books/MyBook/chapter2.md                          │
│ 3. OneDrive syncs to cloud                             │
└─────────────────────────────────────────────────────────┘

DAY 2 - Laptop First Sync:
┌─────────────────────────────────────────────────────────┐
│ Laptop: C:\Users\<USER>\OneDrive\Noti\                   │
│                                                         │
│ 1. Manifest exists! (from PC)                          │
│ 2. Sync engine reads manifest                          │
│ 3. Imports all items to database                       │
│ 4. User edits chapter1.md                              │
│ 5. Sync engine updates:                                │
│    - manifest.json (new timestamp, device ID)          │
│    - Books/MyBook/chapter1.md (new content)            │
│ 6. OneDrive syncs to cloud                             │
└─────────────────────────────────────────────────────────┘

DAY 2 - PC Sync:
┌─────────────────────────────────────────────────────────┐
│ PC: C:\Users\<USER>\OneDrive\Noti\                       │
│                                                         │
│ 1. OneDrive has pulled changes                         │
│ 2. Sync engine detects manifest changed                │
│ 3. Compares manifest vs database:                      │
│    - chapter1.md is different (laptop version newer)   │
│ 4. Imports laptop's chapter1.md                        │
│ 5. Database now matches backup                         │
└─────────────────────────────────────────────────────────┘
```

## File Structure Example

```
C:\Users\<USER>\OneDrive\Noti\              (or any directory)
├── manifest.json                         ← Single source of truth
├── Books/
│   ├── "Clean Code"/
│   │   ├── notes.md
│   │   └── highlights.noti.json
│   └── "Design Patterns"/
│       └── chapter1.md
├── Personal/
│   ├── journal.md
│   └── ideas.md
└── Work/
    └── meeting-notes.md

manifest.json contains:
{
  "version": "2.0",
  "lastSyncTime": "2024-01-15T14:30:00Z",
  "deviceId": "laptop-work",
  "lastDeviceId": "pc-home",
  "items": {
    "note-123": {
      "type": "note",
      "path": "Books/Clean Code/notes.md",
      "hash": "sha256:abc123...",
      "modified": "2024-01-15T14:00:00Z"
    },
    // ... all other items
  },
  "deletions": []
}
```

## Database Tables Interaction

```
┌─────────────────────┐         ┌─────────────────────┐
│    SYNC ENGINE      │         │   BACKUP DIRECTORY  │
└──────────┬──────────┘         └──────────┬──────────┘
           │                               │
           ▼                               ▼
┌─────────────────────┐         ┌─────────────────────┐
│  sync_manifest_cache│         │    manifest.json    │
│  ┌─────────────┐   │         │                     │
│  │ manifest_   │   │◀────────│  Compared on each   │
│  │ hash        │   │         │  sync to detect     │
│  │             │   │         │  changes            │
│  └─────────────┘   │         └─────────────────────┘
└─────────────────────┘
           │
           ▼
┌─────────────────────┐
│  sync_item_state    │
│  ┌─────────────┐   │
│  │ item_id     │   │ ← Tracks sync status of each
│  │ sync_hash   │   │   item to detect local changes
│  │ sync_status │   │
│  └─────────────┘   │
└─────────────────────┘
           │
           ▼
┌─────────────────────────────────────────┐
│         CORE TABLES                      │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐│
│  │  notes   │ │ folders  │ │  books   ││
│  └──────────┘ └──────────┘ └──────────┘│
└─────────────────────────────────────────┘
```

## Auto-Sync Event Flow

```
Database Operation          Event System           Sync Engine
       │                         │                      │
       ▼                         │                      │
┌─────────────┐                  │                      │
│ Note Created│──────emit───────▶│                      │
└─────────────┘                  ▼                      │
                         ┌───────────────┐              │
                         │ Debounce 5sec │              │
                         └───────┬───────┘              │
                                 │                      │
┌─────────────┐                  ▼                      │
│ Note Updated│──────emit───────▶│ (accumulating)      │
└─────────────┘                  │                      │
                                 │                      │
                          5 seconds pass                │
                                 │                      │
                                 ▼                      │
                         ┌───────────────┐              │
                         │ Batch Events  │              │
                         └───────┬───────┘              │
                                 │                      ▼
                                 └──────trigger────▶┌─────────┐
                                                    │  SYNC   │
                                                    └─────────┘
```

## Error Handling Flow

```
┌─────────────────────────────────────────────────────────┐
│                    SYNC ATTEMPT                          │
└───────────────────┬─────────────────────────────────────┘
                    │
                    ▼
         ┌──────────────────────┐
         │ Directory accessible?│──NO──▶ Log error, skip sync
         └──────────┬───────────┘
                    │ YES
                    ▼
         ┌──────────────────────┐
         │ Manifest readable?   │──NO──▶ Treat as first sync
         └──────────┬───────────┘
                    │ YES
                    ▼
         ┌──────────────────────┐
         │ Manifest valid JSON? │──NO──▶ Backup corrupt manifest,
         └──────────┬───────────┘        create new one
                    │ YES
                    ▼
         ┌──────────────────────┐
         │ Can write to dir?    │──NO──▶ Read-only mode,
         └──────────┬───────────┘        import only
                    │ YES
                    ▼
         ┌──────────────────────┐
         │ Perform full sync    │
         └──────────────────────┘
```

## Performance Optimization

```
NAIVE APPROACH (Slow):
- Read manifest: 10ms
- Query ALL items from DB: 500ms  ❌
- Compare everything: 200ms
- Write changes: 100ms
Total: 810ms

OPTIMIZED APPROACH (Fast):
- Read manifest: 10ms
- Check manifest hash: 1ms
  └─ If unchanged: SKIP (11ms total) ✅
- Query only changed items: 50ms
- Compare only changes: 20ms
- Write only changes: 30ms
Total: 111ms (7x faster)

CACHING STRATEGY:
┌────────────────┐
│ Memory Cache   │ ← manifest.json content (if unchanged)
├────────────────┤
│ DB Cache       │ ← sync_item_state (last known hashes)
├────────────────┤
│ File System    │ ← Only read/write what changed
└────────────────┘
```

This simplified architecture removes all the complexity while providing better functionality than the current dual-system approach.