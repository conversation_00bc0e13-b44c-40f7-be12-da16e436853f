import { EventEmitter } from 'events';
import { unifiedSyncEngine } from './unified-sync-engine';
import type { SyncOptions, SyncResult } from './types';

export interface AutoSyncOptions {
  enabled?: boolean;
  debounceTime?: number; // milliseconds
  syncInterval?: number; // milliseconds
  maxRetries?: number;
}

export type AutoSyncState = 'idle' | 'syncing' | 'error' | 'disabled';

export interface AutoSyncStatus {
  state: AutoSyncState;
  lastSyncTime?: Date;
  nextSyncTime?: Date;
  syncInProgress: boolean;
  error?: string;
}

export class AutoSync extends EventEmitter {
  private syncDirectory?: string;
  private options: Required<AutoSyncOptions>;
  private state: AutoSyncState = 'disabled';
  private syncInProgress = false;
  private lastSyncTime?: Date;
  private nextSyncTime?: Date;
  private lastError?: string;
  
  // Timers
  private debounceTimer?: NodeJS.Timeout;
  private intervalTimer?: NodeJS.Timeout;
  
  // Change tracking
  private pendingChanges = false;
  private retryCount = 0;

  constructor() {
    super();
    this.options = {
      enabled: false,
      debounceTime: 5000, // 5 seconds
      syncInterval: 300000, // 5 minutes
      maxRetries: 3
    };
  }

  /**
   * Start auto-sync monitoring
   */
  async start(directory: string, options?: AutoSyncOptions): Promise<void> {
    if (this.state === 'syncing') {
      throw new Error('Cannot start auto-sync while sync is in progress');
    }

    this.syncDirectory = directory;
    this.options = {
      ...this.options,
      ...options
    };

    if (!this.options.enabled) {
      this.state = 'disabled';
      return;
    }

    this.state = 'idle';
    this.emit('started', { directory });

    // Perform initial sync
    await this.performSync();

    // Schedule next sync based on interval
    this.scheduleSync();
  }

  /**
   * Stop auto-sync monitoring
   */
  stop(): void {
    this.options.enabled = false;
    this.state = 'disabled';
    
    // Clear all timers
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = undefined;
    }
    
    if (this.intervalTimer) {
      clearTimeout(this.intervalTimer);
      this.intervalTimer = undefined;
    }

    this.pendingChanges = false;
    this.nextSyncTime = undefined;
    
    this.emit('stopped');
  }

  /**
   * Manually trigger a sync (debounced)
   */
  triggerSync(): void {
    if (!this.options.enabled || this.state === 'disabled') {
      return;
    }

    // Mark that we have pending changes
    this.pendingChanges = true;

    // Clear existing debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set new debounce timer
    this.debounceTimer = setTimeout(() => {
      this.debounceTimer = undefined;
      if (this.pendingChanges && !this.syncInProgress) {
        this.performSync().catch(error => {
          console.error('[AutoSync] Error during triggered sync:', error);
        });
      }
    }, this.options.debounceTime);
  }

  /**
   * Called when database changes are detected
   */
  onDatabaseChange(changeType: string, details?: any): void {
    if (!this.options.enabled || this.state === 'disabled') {
      return;
    }

    this.emit('database-change', { changeType, details });

    // Trigger a debounced sync
    this.triggerSync();
  }

  /**
   * Schedule the next sync based on interval
   */
  private scheduleSync(): void {
    if (!this.options.enabled || this.state === 'disabled') {
      return;
    }

    // Clear existing interval timer
    if (this.intervalTimer) {
      clearTimeout(this.intervalTimer);
    }

    // Calculate next sync time
    this.nextSyncTime = new Date(Date.now() + this.options.syncInterval);

    // Set interval timer
    this.intervalTimer = setTimeout(() => {
      this.intervalTimer = undefined;
      if (!this.syncInProgress) {
        this.performSync().then(() => {
          // Schedule next sync after completion
          this.scheduleSync();
        }).catch(error => {
          console.error('[AutoSync] Error during scheduled sync:', error);
          // Still schedule next sync even on error
          this.scheduleSync();
        });
      } else {
        // If sync is in progress, reschedule for later
        this.scheduleSync();
      }
    }, this.options.syncInterval);
  }

  /**
   * Execute the actual sync
   */
  private async performSync(): Promise<void> {
    if (!this.syncDirectory || !this.options.enabled || this.syncInProgress) {
      return;
    }

    // Mark that we're starting a sync
    this.syncInProgress = true;
    this.pendingChanges = false;
    this.state = 'syncing';
    
    // Just emit the event and let the sync API handle the actual sync
    // The sync API will call our sync complete/error methods
    this.emit('sync-start');
  }

  /**
   * Called when sync completes successfully
   */
  onSyncComplete(result: any): void {
    this.syncInProgress = false;
    this.state = 'idle';
    this.lastSyncTime = new Date();
    this.lastError = undefined;
    this.retryCount = 0;
    
    this.emit('sync-complete', result);
    
    // Schedule next sync if interval is set
    this.scheduleSync();
  }
  
  /**
   * Called when sync fails
   */
  onSyncError(error: Error): void {
    const errorMessage = error.message || 'Unknown error';
    this.lastError = errorMessage;
    this.state = 'error';
    this.syncInProgress = false;
    
    // Handle retries
    if (this.retryCount < this.options.maxRetries) {
      this.retryCount++;
      
      // Schedule retry with exponential backoff
      const retryDelay = Math.min(
        this.options.debounceTime * Math.pow(2, this.retryCount - 1),
        60000 // Max 1 minute
      );
      
      setTimeout(() => {
        if (this.options.enabled) {
          this.performSync().catch(err => {
            console.error('[AutoSync] Retry failed:', err);
          });
        }
      }, retryDelay);
      
      this.emit('sync-error', { 
        error: errorMessage, 
        retry: this.retryCount,
        nextRetryIn: retryDelay 
      });
    } else {
      // Max retries reached
      this.retryCount = 0;
      this.emit('sync-error', { 
        error: errorMessage, 
        maxRetriesReached: true 
      });
      
      // Still schedule next sync even on error
      this.scheduleSync();
    }
  }

  /**
   * Get current auto-sync status
   */
  getStatus(): AutoSyncStatus {
    return {
      state: this.state,
      lastSyncTime: this.lastSyncTime,
      nextSyncTime: this.nextSyncTime,
      syncInProgress: this.syncInProgress,
      error: this.lastError
    };
  }

  /**
   * Update options
   */
  updateOptions(options: Partial<AutoSyncOptions>): void {
    const wasEnabled = this.options.enabled;
    
    this.options = {
      ...this.options,
      ...options
    };

    // Handle enable/disable transitions
    if (wasEnabled && !this.options.enabled) {
      this.stop();
    } else if (!wasEnabled && this.options.enabled && this.syncDirectory) {
      this.start(this.syncDirectory, this.options).catch(error => {
        console.error('[AutoSync] Error restarting after options update:', error);
      });
    } else if (this.options.enabled) {
      // If interval changed, reschedule
      if (options.syncInterval !== undefined) {
        this.scheduleSync();
      }
    }
  }

  /**
   * Force an immediate sync (bypasses debouncing)
   */
  async forceSync(): Promise<SyncResult> {
    if (!this.syncDirectory) {
      throw new Error('No sync directory configured');
    }

    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    // Clear any pending debounced syncs
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = undefined;
    }

    // Perform sync
    await this.performSync();

    // Return the result
    return {
      success: this.state === 'idle',
      imported: { books: 0, folders: 0, notes: 0 },
      exported: { books: 0, folders: 0, notes: 0 },
      deleted: { books: 0, folders: 0, notes: 0 },
      conflicts: [],
      errors: this.lastError ? [this.lastError] : [],
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Check if auto-sync is enabled
   */
  isEnabled(): boolean {
    return this.options.enabled;
  }

  /**
   * Check if sync is in progress
   */
  isSyncing(): boolean {
    return this.syncInProgress;
  }

  /**
   * Check if auto-sync is running
   */
  isRunning(): boolean {
    return this.state !== 'disabled' && this.options.enabled;
  }
}

// Export singleton instance
export const autoSync = new AutoSync();

// Export default
export default autoSync;