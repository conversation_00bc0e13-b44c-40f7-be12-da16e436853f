<div align="center">
  <img src="./public/logo.svg" alt="Noti Logo" width="120">
  <h1>Noti</h1>
  <p>Smart Note-Taking & Study Companion</p>
  
  [![Build Status](https://img.shields.io/github/actions/workflow/status/noti-app/noti/build.yml?branch=main&style=flat-square)](https://github.com/noti-app/noti/actions)
  [![Version](https://img.shields.io/badge/version-0.8.0-blue.svg?style=flat-square)](https://github.com/noti-app/noti/releases)
  [![Electron](https://img.shields.io/badge/electron-latest-blue.svg?style=flat-square)](https://www.electronjs.org/)
  [![Vue](https://img.shields.io/badge/vue-3-green.svg?style=flat-square)](https://vuejs.org/)
  [![TypeScript](https://img.shields.io/badge/typescript-5-blue.svg?style=flat-square)](https://www.typescriptlang.org/)
</div>

## ✨ Overview

Noti is an all-in-one desktop application for note-taking, book tracking, and study management. Built with Electron, Vue, and TypeScript, it provides a seamless experience for students, researchers, and lifelong learners.

<div align="center">
  <p><i>A modern, intuitive desktop application for note-taking and study management</i></p>
</div>

## 🚀 Features

### Implemented ✅
- **📝 Note Management**: Create, organize, and edit rich-text notes with TipTap editor
- **📁 Folder Organization**: Hierarchical folder structure with drag-and-drop support
- **📚 Book Tracking**: Search, add, and track your reading progress with OpenLibrary integration
- **📖 Book Details & Management**: View, edit, rate, and delete books with comprehensive modal interface
- **⭐ Book Rating & Progress**: Rate books and track reading progress with visual indicators
- **📝 Book-Linked Notes**: Create and view notes linked to specific books with seamless integration
- **⏱️ Study Timer**: Full Pomodoro timer with session tracking and statistics
- **📊 Timer Statistics & Analytics**: Comprehensive statistics with Chart.js visualization
  - Daily focus time charts with 7-day history tracking
  - Weekly progress charts with session completion rates
  - Category-based time distribution analysis
  - Pomodoro completion statistics with visual indicators
- **📈 Session Management**: Create, track, and analyze study sessions with focus areas and categories
- **📤 Export System**: Export notes to PDF, Markdown, and .noti format with progress indicators
- **⚙️ Settings Management**: Comprehensive settings system with persistent storage
  - Theme switching (Light/Dark mode) with instant preview
  - Timer display preferences and title bar customization
  - User preferences and backup configurations
  - Discord Rich Presence settings and privacy controls
- **🎮 Discord Rich Presence**: Show your study activity on Discord
  - Real-time study session display with timer information
  - Activity types: study sessions, note-taking, book reading
  - Privacy controls for customizing shared information
  - Custom messages and professional Discord presence
- **📋 Dashboard**: Overview of notes, folders, books, and study time statistics

### Coming Soon 🚧
- **🔍 Advanced Search**: Search across notes, books, and folders
- **📱 Mobile Companion**: Sync with mobile devices
- **🔄 Cloud Sync**: Backup and sync across devices
- **📊 Dashboard Enhancement**: Comprehensive overview with aggregated data

## 🔧 Tech Stack

- **[Electron](https://www.electronjs.org/)**: Cross-platform desktop framework
- **[Vue 3](https://vuejs.org/)**: Frontend framework
- **[TypeScript](https://www.typescriptlang.org/)**: Type-safe JavaScript
- **[Vite](https://vitejs.dev/)**: Frontend tooling
- **[electron-builder](https://www.electron.build/)**: Application packaging
- **[SQLite](https://www.sqlite.org/)**: Local database storage
- **[Tiptap](https://tiptap.dev/)**: Text editor framework
- **[Puppeteer](https://pptr.dev/)**: PDF document generation
- **[Markdown-it](https://github.com/markdown-it/markdown-it)**: HTML to Markdown conversion
- **[Archiver](https://github.com/archiverjs/node-archiver)**: ZIP file creation for note exports
- **[Chart.js](https://www.chartjs.org/)**: Data visualization for timer statistics
- **[Vue Chart.js](https://vue-chartjs.org/)**: Vue 3 wrapper for Chart.js
- **[Pinia](https://pinia.vuejs.org/)**: State management for Vue 3
- **[OpenLibrary API](https://openlibrary.org/developers/api)**: Book search and metadata
- **[Axios](https://axios-http.com/)**: HTTP client for API requests

## 🏗️ Project Structure

```
/electron          # Electron main process code (TypeScript)
  /main            # Main process entry point (TypeScript)
  /preload         # Preload scripts for IPC (TypeScript)
  /database        # Database connection & utilities (TypeScript)
  /api             # API modules (notes-api, folders-api, etc., all TypeScript)
  /models          # TypeScript interfaces for data models
/src               # Vue application (renderer process, TypeScript)
  /components      # Vue components (TypeScript)
  /views           # Application views (TypeScript)
  /router          # Vue router configuration (TypeScript)
  /assets          # Static assets
/public            # Public static files
  /icons           # Application icons
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16+)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/noti.git
cd noti

# Install dependencies
npm install
```

### Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

## 🧪 Testing the Application

### Running the Application

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Populate test data (optional):
```bash
npm run populate-test-data
```

### Testing Core Features

#### Note Management
- Navigate to the Notes view in the sidebar
- Click "New Note" to create a note with rich-text editing
- Use the TipTap editor for formatting (bold, italic, lists, links, etc.)
- Notes auto-save after 1-second delay
- Export notes to PDF, Markdown, or .noti format

#### Folder Organization
- Navigate to the Folders view to create hierarchical folder structures
- Drag and drop notes between folders
- Use context menus for folder operations (rename, delete)
- Filter notes by selecting folders in the sidebar

#### Book Management
- Navigate to the Books view to search and add books
- Search using OpenLibrary API integration
- Rate books and track reading progress
- Create book-specific notes linked to your reading
- View book details with comprehensive modal interface

#### Timer & Productivity
- Navigate to the Timer view for Pomodoro sessions
- Start focused study sessions with customizable durations
- Track sessions by category and focus area
- View detailed statistics with charts and analytics
- Configure timer settings for optimal productivity

#### Export & Data Management
- Export individual notes or entire folders
- Choose from multiple formats (PDF, Markdown, .noti)
- Batch export with progress indicators
- Automatic ZIP creation for multiple items

## 📄 Current Status

The application is in active development (v0.8.0). We've completed:

### Core Infrastructure ✅
- Complete application structure with Electron + Vue 3 + TypeScript
- SQLite database with comprehensive schema for all features
- Type-safe IPC communication layer between main and renderer processes
- Custom window management with frameless design

### Note Management System ✅
- Rich-text editor with TipTap integration
- Full CRUD operations for notes with auto-save functionality
- Hierarchical folder organization with drag-and-drop support
- Note-to-book linking system for research and study notes

### Book Management System ✅
- OpenLibrary API integration for book search and metadata
- Local book database with cover image storage
- Book rating and progress tracking
- Comprehensive book details modal with editing capabilities

### Timer & Productivity System ✅
- Full Pomodoro timer implementation with customizable durations
- Session tracking with focus areas and categories
- Advanced timer statistics and analytics with Chart.js visualization
- Real-time chart updates with daily, weekly, and category-based analysis
- Comprehensive settings management with theme switching (Light/Dark mode)
- Timer settings persistence and configuration management
- Discord Rich Presence integration showing real-time study activities

### Export & Data Management ✅
- Multi-format export system (PDF, Markdown, .noti)
- Batch export capabilities with progress indicators
- ZIP archive creation for multiple items
- File system integration with save dialogs

### User Interface ✅
- Modern, responsive design with custom components
- Dashboard with statistics overview
- Comprehensive settings management interface with organized sections
- Theme switching system with Light/Dark mode support
- Interactive charts and data visualization components
- Modal system for complex operations
- Consistent visual design language with theme-aware styling

## 👥 Contributing

This project is being developed as part of a sprint-based development process. Contributors include:
- Hélder Ribeiro Dos Santos Barbosa
- Enzo Freni

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need assistance, please open an issue on GitHub.

---

<div align="center">
  Made with ❤️ by the Noti Team
</div>