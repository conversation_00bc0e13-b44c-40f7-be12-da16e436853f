# Mark For Deletion Implementation

## Bug Analysis

### Current Issue
The `markForDeletion` method in `change-detector.ts` (lines 352-356) only logs a message but doesn't implement actual deletion logic. This causes sync deletions to not propagate to the local database.

### Expected Behavior
When PC1 deletes an item and syncs, PC2 should:
1. Detect the deletion in the manifest during sync
2. Delete the corresponding item from its local database
3. Handle cascading deletes properly (e.g., notes in deleted folders)

## Investigation Findings

### 1. Current markForDeletion Method
```typescript
private markForDeletion(id: string): void {
  // This will be handled by the sync engine
  // For now, just log it
  console.log(`Item ${id} marked for deletion`);
}
```

### 2. Deletion Workflow
- The `processDeletions` method in `change-detector.ts` identifies items that need deletion
- It calls `markForDeletion` for each item that exists locally but is marked as deleted in the manifest
- However, `markForDeletion` doesn't actually delete anything

### 3. Available Deletion APIs
The database already has deletion methods:
- `deleteBook(id: number)` - Deletes a book and handles media files
- `deleteFolder(id: number)` - Deletes a folder (cascades to child folders, nullifies notes)
- `deleteNote(id: number)` - Deletes a note

### 4. Sync Requirements
Based on the sync design:
- Deletions should be automatic (no user confirmation during sync)
- Cascading deletes should be handled by the database (ON DELETE CASCADE)
- The sync system should track which items were deleted

## Implementation Approach

### Option 1: Direct Deletion in ChangeDetector (Not Recommended)
- Modify `markForDeletion` to directly call deletion APIs
- Problem: ChangeDetector shouldn't have database dependencies
- Problem: No way to track deletion results

### Option 2: Return Deletion List (Recommended)
- Have `markForDeletion` collect items to delete
- Return deletion list from `compareStates`
- Let UnifiedSyncEngine handle actual deletions
- Benefits:
  - Maintains separation of concerns
  - Allows proper error handling
  - Can track deletion results in SyncResult

### Option 3: Queue-Based Deletion
- Create a deletion queue in the sync state
- Process queue after imports/exports
- Benefits: Could handle retries on failure
- Drawback: More complex implementation

## Recommended Implementation

### 1. Modify ChangeDetector to collect deletions:
```typescript
export class ChangeDetector {
  private pendingDeletions: Array<{id: string, type: 'book' | 'folder' | 'note'}> = [];

  async compareStates(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Reset pending deletions
    this.pendingDeletions = [];
    
    // ... existing code ...
    
    // Process deletions
    this.processDeletions(manifest.deletions, dbByType);
    
    return {
      toImport,
      toExport,
      conflicts,
      toDelete: this.pendingDeletions // Add this to Changes interface
    };
  }

  private markForDeletion(id: string, type: 'book' | 'folder' | 'note'): void {
    this.pendingDeletions.push({ id, type });
    console.log(`Item ${id} (${type}) marked for deletion`);
  }
}
```

### 2. Update Changes interface in types.ts:
```typescript
export interface Changes {
  // ... existing fields ...
  /** Items that need to be deleted from local database */
  toDelete: Array<{
    id: string;
    type: 'book' | 'folder' | 'note';
  }>;
}
```

### 3. Handle deletions in UnifiedSyncEngine:
```typescript
// After processing imports/exports but before updating manifest
if (changes.toDelete && changes.toDelete.length > 0) {
  this.emitProgress({
    phase: 'deleting',
    total: changes.toDelete.length,
    processed: 0,
    percentage: 80,
    progress: 80,
    message: 'Processing deletions...'
  });

  for (const item of changes.toDelete) {
    try {
      const numericId = parseInt(item.id);
      switch (item.type) {
        case 'book':
          await deleteBook(numericId);
          console.log(`Deleted book ${item.id} from local database`);
          break;
        case 'folder':
          await deleteFolder(numericId);
          console.log(`Deleted folder ${item.id} from local database`);
          break;
        case 'note':
          await deleteNote(numericId);
          console.log(`Deleted note ${item.id} from local database`);
          break;
      }
    } catch (error) {
      console.error(`Failed to delete ${item.type} ${item.id}:`, error);
      result.errors.push(`Failed to delete ${item.type} ${item.id}: ${error.message}`);
    }
  }
}
```

## Implementation Steps

1. **Update types.ts** - Add toDelete array to Changes interface
2. **Modify change-detector.ts** - Collect deletions instead of just logging
3. **Update unified-sync-engine.ts** - Process deletions after imports/exports
4. **Add deletion tracking** - Update SyncResult to include deletion counts
5. **Test thoroughly** - Ensure cascading deletes work correctly

## Testing Scenarios

1. **Book Deletion**
   - Delete book on PC1
   - Sync both PCs
   - Verify book and its folder are deleted on PC2
   - Verify notes in book folder are handled correctly

2. **Folder Deletion**
   - Delete folder on PC1
   - Sync both PCs
   - Verify folder is deleted on PC2
   - Verify child folders are cascaded
   - Verify notes have folder_id set to null

3. **Note Deletion**
   - Delete note on PC1
   - Sync both PCs
   - Verify note is deleted on PC2

4. **Conflict Scenarios**
   - Modify item on PC2 while it's deleted on PC1
   - Verify conflict is detected and resolved properly

## Files Modified
- `electron/main/api/sync-logic/types.ts`
- `electron/main/api/sync-logic/change-detector.ts`
- `electron/main/api/sync-logic/unified-sync-engine.ts`