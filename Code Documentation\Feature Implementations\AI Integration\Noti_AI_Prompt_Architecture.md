# Noti AI Prompt Architecture

## 1. Component Development Prompt
```markdown
**Context:** Noti uses Vue 3 composition API with TypeScript in the renderer process and Electron with SQLite in the main process. The BooksView component manages book data with real-time search.

**Task:** Create a new `ReadingProgressTracker` component that:
- Displays progress bars for reading sessions
- Integrates with the existing timer API
- Uses the same color scheme as BookCard

**Output Format:**
```vue
<template>...</template>
<script lang="ts">...</script>
<style scoped>...</style>
```

**Example Reference:**
```typescript
// Use similar state management as BooksView
const { startTimer, endTimer } = useTimerAPI();
```
```

## 2. Debugging Prompt
```markdown
**Context:** Users report slow loading when opening BooksView with 500+ books. The current implementation processes books synchronously.

**Task:** Diagnose the performance bottleneck in `BooksView.vue` and propose an optimized solution.

**Output Format:**
1. Identified bottleneck with code location
2. Proposed solution with code changes
3. Expected performance improvement

**Example Reference:**
```typescript
// Current bottleneck (BooksView.vue:197-233)
books.value = booksData.map(book => {
  // CPU-intensive processing
});
```
```

## 3. Refactoring Prompt
```markdown
**Context:** The edit book handling in BooksView contains duplicate logic. We want to extract shared functionality into a composable.

**Task:** Refactor the edit book logic into a `useBookEditor` composable that:
- Handles both manual and OpenLibrary edits
- Manages custom cover persistence
- Returns consistent editing interface

**Output Format:**
```typescript
// useBookEditor.ts
export function useBookEditor() {
  // Implementation
}
```

**Integration Example:**
```typescript
// In BooksView.vue
const { editBook, saveBook } = useBookEditor();
```
```

## 4. Security Audit Prompt
```markdown
**Context:** Electron security requires context isolation and disabled nodeIntegration in renderer windows. We've had issues with child window creation.

**Task:** Audit `electron/main/index.ts` for security anti-patterns and suggest hardening measures.

**Output Format:**
1. List of vulnerabilities with code locations
2. Severity assessment (Critical/High/Medium/Low)
3. Specific code fixes

**Example Vulnerability:**
```typescript
// index.ts:195-197 - Disabled context isolation
webPreferences: {
  nodeIntegration: true,
  contextIsolation: false
}
```
```

## 5. Documentation Prompt
```markdown
**Context:** New developers need to understand the IPC communication flow between the renderer and main processes.

**Task:** Create sequence diagrams for the book addition process covering:
1. Renderer process flow
2. IPC message flow
3. Main process handlers
4. Database interactions

**Output Format:**
```mermaid
sequenceDiagram
    participant Renderer
    participant Main
    participant Database
    Renderer->>Main: addBook request
    Main->>Database: Save book
    Database-->>Main: Confirmation
    Main-->>Renderer: Success response
```

**Example IPC Reference:**
```typescript
// books-api.ts
ipcMain.handle('books:addFromOpenLibrary', ...)
```
```

## Usage Guidelines
1. Always include the **Context** section with relevant Noti specifics
2. Keep tasks focused on single responsibilities
3. Reference existing patterns when possible
4. Specify exact output formats for consistency
5. Include code location references for context