// Database utility for SQLite connection & initialization
import sqlite3 from 'sqlite3';
import { app } from 'electron';
import * as path from 'node:path';
import * as fs from 'node:fs';
import { databaseHooks } from './database-hooks';

// Type for Database
type Database = sqlite3.Database;

// Helper function to run database commands as promises
const runAsync = (db: Database, sql: string): Promise<void> => {
    return new Promise((resolve, reject) => {
        db.run(sql, (err) => {
            if (err) reject(err);
            else resolve();
        });
    });
};

// Helper function to get data as promise
const getAsync = (db: Database, sql: string): Promise<any> => {
    return new Promise((resolve, reject) => {
        db.get(sql, (err, row) => {
            if (err) reject(err);
            else resolve(row);
        });
    });
};

// Database will be stored in the user's application data directory
const getUserDataPath = () => {
    return app.getPath('userData');
};

// Ensure data directory exists
const ensureDataDirectory = () => {
    const userDataPath = getUserDataPath();
    if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true });
    }
    return userDataPath;
};

// Get database path
const getDbPath = () => {
    const userDataPath = ensureDataDirectory();
    return path.join(userDataPath, 'noti-database.sqlite');
};

// Setup database configuration (pragmas)
const setupDatabaseConfig = async (db: Database): Promise<void> => {
    try {
        // Set busy timeout first to handle potential locks during initialization
        await runAsync(db, 'PRAGMA busy_timeout = 10000;');
        
        // Enable WAL mode immediately after setting timeout
        await runAsync(db, 'PRAGMA journal_mode = WAL;');
        console.log('WAL mode enabled successfully');
        
        // Enable foreign keys
        await runAsync(db, 'PRAGMA foreign_keys = ON');
        console.log('Foreign key support enabled.');
    } catch (error) {
        if (error instanceof Error) {
            if (error.message.includes('busy_timeout')) {
                console.error('Error setting busy timeout:', error.message);
            } else if (error.message.includes('journal_mode')) {
                console.error('Error enabling WAL mode:', error.message);
            } else {
                console.error('Error enabling foreign keys:', error.message);
            }
        }
        throw error;
    }
};

// Create all database tables
const createAllTables = async (db: Database): Promise<void> => {
    try {
        // Create Notes table - Note that "order" is a reserved keyword, so we escape it with quotes
        await runAsync(db, `CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT,
            html_content TEXT,
            folder_id INTEGER,
            book_id INTEGER,
            type TEXT,
            color TEXT,
            "order" INTEGER,
            last_viewed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
        )`);

        // Create Folders table - Note that "order" is a reserved keyword, so we escape it with quotes
        await runAsync(db, `CREATE TABLE IF NOT EXISTS folders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            parent_id INTEGER,
            book_id INTEGER,
            color TEXT,
            "order" INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
        )`);

        // Create Books table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS books (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            author TEXT,
            isbn TEXT,
            cover_url TEXT,
            publication_date TEXT,
            description TEXT,
            page_count INTEGER,
            current_page INTEGER,
            rating INTEGER,
            language TEXT,
            genres TEXT,
            olid TEXT,
            status TEXT,
            custom_fields TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Recent Items table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS recent_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER,
            book_id INTEGER,
            viewed_at TIMESTAMP NOT NULL,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
        )`);

        // Create Theme Settings table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS theme_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            theme_name TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 0,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Exports table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS exports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER NOT NULL,
            export_path TEXT NOT NULL,
            export_type TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
        )`);

        // Create Search History table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS search_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Settings table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value_json TEXT,
            category TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Media Files table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS media_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER,
            book_id INTEGER,
            file_path TEXT NOT NULL,
            file_name TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            is_cover BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
        )`);

        // Create Timer Sessions table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS timer_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP,
            duration INTEGER,
            session_type TEXT,
            is_completed BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            focus TEXT,
            category TEXT,
            updated_at TIMESTAMP,
            session_name TEXT,
            pomodoro_cycles_completed INTEGER DEFAULT 0,
            is_user_session BOOLEAN DEFAULT 1
        )`);

        // Create Pomodoro Cycles table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS pomodoro_cycles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            cycle_type TEXT NOT NULL CHECK (cycle_type IN ('pomodoro', 'short_break', 'long_break')),
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP,
            duration INTEGER,
            completed BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES timer_sessions(id) ON DELETE CASCADE
        )`);

        // Create Timer Settings table
        await runAsync(db, `CREATE TABLE IF NOT EXISTS timer_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            work_duration INTEGER NOT NULL DEFAULT 1500,
            short_break_duration INTEGER NOT NULL DEFAULT 300,
            long_break_duration INTEGER NOT NULL DEFAULT 900,
            long_break_interval INTEGER NOT NULL DEFAULT 4,
            auto_start_breaks BOOLEAN DEFAULT 1,
            auto_start_work BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create sync_state table for tracking sync status
        await runAsync(db, `CREATE TABLE IF NOT EXISTS sync_state (
            item_type TEXT NOT NULL,
            item_id INTEGER NOT NULL,
            sync_hash TEXT NOT NULL,
            last_synced TIMESTAMP NOT NULL,
            device_id TEXT NOT NULL,
            sync_version INTEGER DEFAULT 1,
            PRIMARY KEY (item_type, item_id)
        )`);

        // Create sync_sessions table for tracking sync operations
        await runAsync(db, `CREATE TABLE IF NOT EXISTS sync_sessions (
            id TEXT PRIMARY KEY,
            device_id TEXT NOT NULL,
            started_at TIMESTAMP NOT NULL,
            completed_at TIMESTAMP,
            status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed')),
            items_synced INTEGER DEFAULT 0,
            error_message TEXT
        )`);

        // Create sync_directory_state table for unified sync engine
        await runAsync(db, `CREATE TABLE IF NOT EXISTS sync_directory_state (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            directory TEXT UNIQUE NOT NULL,
            last_sync_hash TEXT,
            last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // Phase 3: Removed sync_items table - sync state now tracked in manifest only

    } catch (error) {
        console.error('Error creating database tables:', error);
        throw error;
    }
};

// Handle database migrations (add columns that might not exist)
const handleDatabaseMigrations = async (db: Database): Promise<void> => {
    try {
        // Remove cover_data and cover_path columns from existing books table if they exist
        try {
            await runAsync(db, `ALTER TABLE books DROP COLUMN cover_data`);
        } catch (alterErr) {
            // Only log actual errors, ignore "no such column" errors silently
            if (alterErr instanceof Error && !alterErr.message.includes('no such column')) {
                console.error('Warning: Failed to drop cover_data column:', alterErr.message);
            }
        }

        try {
            await runAsync(db, `ALTER TABLE books DROP COLUMN cover_path`);
        } catch (alterErr) {
            // Only log actual errors, ignore "no such column" errors silently
            if (alterErr instanceof Error && !alterErr.message.includes('no such column')) {
                console.error('Warning: Failed to drop cover_path column:', alterErr.message);
            }
        }

        // Migrate existing media_files table to add book_id and is_cover columns if they don't exist
        try {
            await runAsync(db, `ALTER TABLE media_files ADD COLUMN book_id INTEGER`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add book_id column:', alterErr.message);
            }
        }

        try {
            await runAsync(db, `ALTER TABLE media_files ADD COLUMN is_cover BOOLEAN DEFAULT 0`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add is_cover column:', alterErr.message);
            }
        }

        // Migrate existing folders table to add book_id column if it doesn't exist
        try {
            await runAsync(db, `ALTER TABLE folders ADD COLUMN book_id INTEGER`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add book_id column to folders:', alterErr.message);
            }
        }

        // Add created_at column to existing timer_settings table if it doesn't exist
        try {
            await runAsync(db, `ALTER TABLE timer_settings ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`);
        } catch (alterErr) {
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Warning: Could not add created_at column to timer_settings:', alterErr.message);
            } else if (!alterErr) {
                console.log('Column "created_at" added to timer_settings or already exists.');
            }
        }

    } catch (error) {
        console.error('Error handling database migrations:', error);
        throw error;
    }
};

// Create database indexes for performance
const createDatabaseIndexes = async (db: Database): Promise<void> => {
    const indexes = [
        { sql: 'CREATE INDEX IF NOT EXISTS idx_notes_folder_id ON notes (folder_id)', name: 'notes.folder_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders (parent_id)', name: 'folders.parent_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_time ON timer_sessions (start_time DESC)', name: 'timer_sessions_start_time' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_session_type ON timer_sessions (session_type)', name: 'timer_sessions_session_type' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_category ON timer_sessions (category)', name: 'timer_sessions_category' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_is_completed ON timer_sessions (is_completed)', name: 'timer_sessions_is_completed' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_completed ON timer_sessions (start_time DESC, is_completed)', name: 'timer_sessions_start_completed' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_is_user_session ON timer_sessions (is_user_session)', name: 'timer_sessions_is_user_session' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_pomodoro_cycles_session_id ON pomodoro_cycles (session_id)', name: 'pomodoro_cycles_session_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_pomodoro_cycles_cycle_type ON pomodoro_cycles (cycle_type)', name: 'pomodoro_cycles_cycle_type' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at)', name: 'notes_updated_at' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_folders_updated_at ON folders(updated_at)', name: 'folders_updated_at' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_state_last_synced ON sync_state(last_synced)', name: 'sync_state_last_synced' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_sessions_device_id ON sync_sessions(device_id)', name: 'sync_sessions_device_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_sessions_status ON sync_sessions(status)', name: 'sync_sessions_status' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_directory_state_directory ON sync_directory_state(directory)', name: 'sync_directory_state_directory' }
    ];

    for (const index of indexes) {
        try {
            await runAsync(db, index.sql);
        } catch (indexErr) {
            console.error(`Error creating index ${index.name}:`, indexErr);
            // Not rejecting, as this is an optimization, but logging the error
        }
    }
};

// Create default folders and setup
const setupDefaultData = async (db: Database): Promise<void> => {
    try {
        // Create unique constraint for Books root folder to prevent duplicates
        try {
            await runAsync(db, `CREATE UNIQUE INDEX IF NOT EXISTS idx_books_root_folder
                ON folders (name, parent_id)
                WHERE name = 'Books' AND parent_id IS NULL`);
        } catch (constraintErr) {
            console.error('Error creating Books folder unique constraint:', constraintErr);
            // Continue anyway, the INSERT will still work
        }

        // Check if Books folder already exists before creating
        const existingBooksFolder = await getAsync(db, `SELECT id FROM folders WHERE name = 'Books' AND parent_id IS NULL LIMIT 1`);
        
        if (existingBooksFolder) {
            console.log('Books folder already exists with ID:', existingBooksFolder.id);
        } else {
            // Create the default "Books" root folder
            await runAsync(db, `INSERT INTO folders (name, parent_id, color, created_at, updated_at)
                VALUES ('Books', NULL, '#4285F4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`);
            console.log('Default "Books" folder created successfully.');
        }

    } catch (error) {
        console.error('Error setting up default data:', error);
        throw error;
    }
};

// Main async function to handle all database setup after connection
const setupDatabase = async (db: Database): Promise<void> => {
    try {
        await setupDatabaseConfig(db);
        await createAllTables(db);
        await handleDatabaseMigrations(db);
        await createDatabaseIndexes(db);
        await setupDefaultData(db);
        
        console.log('All database tables and indexes created successfully.');
        
        // Initialize database hooks manager
        databaseHooks.initialize();
        console.log('Database hooks manager initialized.');
        
    } catch (error) {
        console.error('Error during database setup:', error);
        throw error;
    }
};

// Initialize the database
export const initDatabase = (): Promise<Database> => {
    return new Promise((resolve, reject) => {
        const dbPath = getDbPath();
        console.log(`Initializing database at: ${dbPath}`);

        // Create a new database connection
        const db: Database = new sqlite3.Database(dbPath, async (err: Error | null) => {
            if (err) {
                console.error('Error opening database:', err.message);
                reject(err);
                return;
            }

            console.log('Connected to the SQLite database.');

            try {
                // Setup database using async/await
                await setupDatabase(db);
                
                // Assign to singleton instance
                dbInstance = db;
                console.log('Database singleton instance assigned.');
                
                resolve(db);
            } catch (setupError) {
                console.error('Error setting up database:', setupError);
                reject(setupError);
            }
        });
    });
};

// Singleton database connection
let dbInstance: Database | null = null;

// Get database connection
export const getDatabase = (): Database => {
    if (dbInstance) {
        return dbInstance;
    }

    const dbPath = getDbPath();
    dbInstance = new sqlite3.Database(dbPath, (err: Error | null) => {
        if (err) {
            console.error('Error connecting to database:', err.message);
            throw err;
        }
    });

    return dbInstance;
};

// Close database connection (call this when app is shutting down)
export const closeDatabase = (): Promise<void> => {
    return new Promise((resolve, reject) => {
        if (dbInstance) {
            // Shutdown database hooks manager first
            databaseHooks.shutdown();
            
            dbInstance.close((err: Error | null) => {
                if (err) {
                    console.error('Error closing database:', err.message);
                    reject(err);
                    return;
                }
                console.log('Database connection closed.');
                dbInstance = null;
                resolve();
            });
        } else {
            resolve();
        }
    });
};
