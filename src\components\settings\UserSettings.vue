<template>
  <div class="user-container">
    <div class="user-header">
      User Settings
    </div>
    <div class="user-divider"></div>
    
    <!-- User Name Setting -->
    <div class="setting-row">
      <div class="setting-info">
        <div class="setting-label">Display Name</div>
        <div class="setting-description">How the application will address you in greetings and messages</div>
      </div>
      <div class="setting-control">
        <input
          type="text"
          class="name-input"
          :value="currentUserName"
          @input="handleNameChange"
          placeholder="Enter your name"
          maxlength="50"
        />
      </div>
    </div>

    <!-- Preview Section -->
    <div class="preview-section" v-if="currentUserName.trim()">
      <div class="user-subtitle">Preview</div>
      <div class="preview-greeting">
        {{ previewGreeting }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useSettingsStore } from '../../stores/settingsStore'

const settingsStore = useSettingsStore()

// Get current user name from store
const currentUserName = computed(() => settingsStore.settings.userName)

// Preview greeting based on current time and user name
const previewGreeting = computed(() => {
  const hour = new Date().getHours()
  const name = currentUserName.value.trim()
  
  let timeGreeting = ''
  if (hour < 12) timeGreeting = 'Good morning'
  else if (hour < 17) timeGreeting = 'Good afternoon'
  else timeGreeting = 'Good evening'
  
  return name ? `${timeGreeting}, ${name}!` : `${timeGreeting}!`
})

// Handle name input changes
function handleNameChange(event: Event) {
  const target = event.target as HTMLInputElement
  const newName = target.value
  settingsStore.updateSetting('userName', newName)
}
</script>

<style scoped>
.user-container {
  border-radius: 16px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-primary);
  display: flex;
  padding: 32px;
  flex-direction: column;
  align-items: start;
  font-family: 'Montserrat', sans-serif;
}

@media (max-width: 991px) {
  .user-container {
    padding: 20px;
  }
}

.user-header {
  color: var(--color-text-primary);
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.user-divider {
  background-color: var(--color-border-primary);
  align-self: stretch;
  display: flex;
  margin-top: 24px;
  flex-shrink: 0;
  height: 1px;
}

@media (max-width: 991px) {
  .user-divider {
    max-width: 100%;
  }
}

.user-subtitle {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
  margin-bottom: 12px;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  width: 100%;
  gap: 20px;
}

.setting-info {
  flex: 1;
}

.setting-label {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.setting-description {
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
}

.name-input {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  min-width: 200px;
  transition: all 0.2s ease;
}

.name-input:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.1);
}

.name-input::placeholder {
  color: var(--color-text-tertiary);
}

.preview-section {
  width: 100%;
  margin-top: 8px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border-secondary);
}

.preview-greeting {
  color: var(--color-text-primary);
  font-size: 18px;
  font-weight: 500;
  padding: 12px 16px;
  background-color: var(--color-bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--color-border-secondary);
}

@media (max-width: 768px) {
  .setting-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .name-input {
    min-width: unset;
    width: 100%;
  }
}
</style>
