# Pomodoro Cycle Creation Fix

## Issue Description
The pomodoro cycle counting system was not working correctly. Only one row was being created in the `pomodoro_cycles` table per session, even when multiple 25-minute pomodoro cycles were completed or skipped within that session.

## Root Cause Analysis

### The Problem
The issue was in the timer transition logic in `PomodoroTimer.vue`. Here's what was happening:

1. **First pomodoro cycle**: Created correctly when starting a pomodoro timer
2. **Pomodoro completion**: The cycle was completed and `currentCycleId.value` was reset to `null`
3. **Break transition**: Timer switched to break mode (short/long break)
4. **Return to pomodoro**: When transitioning back to pomodoro mode, **no new cycle was created**

### Specific Code Issues

#### Issue 1: Missing Cycle Creation After Break
In the timer completion logic (lines 242-259), when transitioning from break back to pomodoro:
```typescript
} else {
  // Coming back from a break, switch to pomodoro
  switchTimerType('pomodoro', true);
  
  // ❌ NO NEW CYCLE CREATION HERE
}
```

#### Issue 2: Conditional Cycle Creation
The original cycle creation logic only ran under very specific conditions:
```typescript
// Start new pomodoro cycle if auto-starting
if (isRunning.value && timerType.value === 'pomodoro') {
  // Only created cycles in this narrow case
}
```

#### Issue 3: Manual Start Issues
In `toggleTimer()`, cycles were always created without checking if one already existed:
```typescript
// ❌ Always created new cycle, even if one existed
const cycle = await db.timer.startPomodoroInSession(session.id, timerType.value);
```

## Solution Implemented

### Fix 1: Ensure Cycle Creation After Break Transitions
**File**: `src/components/timer/PomodoroTimer.vue` (lines 242-259)

Added explicit cycle creation when transitioning from break back to pomodoro:
```typescript
} else {
  // Coming back from a break, switch to pomodoro
  switchTimerType('pomodoro', true);
  
  // ✅ Always create a new pomodoro cycle when transitioning from break to pomodoro
  if (isRunning.value) {
    try {
      const session = await checkAndCreateAutoSession();
      if (session) {
        const cycle = await db.timer.startPomodoroInSession(session.id, 'pomodoro');
        currentCycleId.value = cycle.id;
        currentSessionId.value = session.id;
      }
    } catch (error) {
      console.error('Failed to start new pomodoro cycle after break:', error);
    }
  }
}
```

### Fix 2: Prevent Duplicate Cycle Creation
**File**: `src/components/timer/PomodoroTimer.vue` (lines 291-301)

Modified `toggleTimer()` to only create cycles when none exist:
```typescript
// ✅ Only start a new pomodoro cycle if we don't already have one active
if (!currentCycleId.value) {
  try {
    const cycle = await db.timer.startPomodoroInSession(session.id, timerType.value);
    currentCycleId.value = cycle.id;
    currentSessionId.value = session.id;
  } catch (error) {
    console.error('Failed to start pomodoro in session:', error);
    return;
  }
}
```

### Fix 3: Handle Skip Timer Transitions
**File**: `src/components/timer/PomodoroTimer.vue` (lines 342-354)

Added cycle creation for skip transitions from break to pomodoro:
```typescript
// Create a new pomodoro cycle if we're auto-starting
if (wasRunning && isRunning.value) {
  try {
    const session = await checkAndCreateAutoSession();
    if (session) {
      const cycle = await db.timer.startPomodoroInSession(session.id, 'pomodoro');
      currentCycleId.value = cycle.id;
      currentSessionId.value = session.id;
    }
  } catch (error) {
    console.error('Failed to start new pomodoro cycle after break skip:', error);
  }
}
```

## Files Modified
1. **`src/components/timer/PomodoroTimer.vue`**
   - Fixed timer completion transition logic (lines 242-259)
   - Fixed manual timer start logic (lines 291-301)
   - Fixed skip timer transition logic (lines 342-354)

## Expected Behavior After Fix
1. **Each 25-minute pomodoro period** (completed or skipped) creates a new row in `pomodoro_cycles` table
2. **Session continuity** is maintained across multiple pomodoro cycles
3. **Proper cycle tracking** for both automatic and manual timer transitions
4. **Database consistency** with accurate cycle counts per session

## Testing Recommendations
1. Start a session and complete multiple pomodoro cycles
2. Check the `pomodoro_cycles` table for multiple rows per session
3. Test both automatic and manual timer transitions
4. Verify skip functionality creates proper cycles
5. Confirm session pomodoro counts match database cycle counts

## Database Schema Reference
```sql
CREATE TABLE pomodoro_cycles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id INTEGER NOT NULL,
  cycle_type TEXT NOT NULL CHECK (cycle_type IN ('pomodoro', 'short_break', 'long_break')),
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP,
  duration INTEGER,
  completed BOOLEAN DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES timer_sessions(id) ON DELETE CASCADE
);
```

Each completed or skipped 25-minute pomodoro should now create a separate row in this table with `cycle_type = 'pomodoro'`.
