# 503 Server Error Handling in AddBookModal

## Files Modified
- `src/components/modals/AddBookModal.vue`

## Section of the App
- Book Management - Add Book Modal

## Issue
The AddBookModal component was showing a generic error message for all types of network errors, including when the server returns a 503 (Service Unavailable) status code. Users couldn't distinguish between different types of server errors.

## Solution
Enhanced the error handling in the `searchBooks` function to specifically detect 503 server unavailable errors and display a more appropriate message.

### Changes Made
1. **Enhanced Error Detection**: Added comprehensive checking for 503 errors in multiple formats:
   - Direct status code property (`err.status === 503`)
   - Code property (`err.code === 503`) 
   - String representation containing "503"
   - Message property containing "503"

2. **Specific Error Message**: When a 503 error is detected, the modal now shows:
   ```
   "Server is currently unavailable. Please try again later."
   ```
   
3. **Fallback Handling**: All other errors continue to show the original generic message:
   ```
   "Failed to search for books. Please check your internet connection and try again."
   ```

4. **Type Safety**: Added proper TypeScript type checking to prevent linter errors when accessing error object properties.

## User Experience Improvement
Users will now receive more accurate feedback when the book search service is temporarily unavailable, helping them understand that the issue is server-side and not related to their internet connection. 