/* This file contains additional web fonts for the Noti application */
/* Import local fonts instead of Google Fonts CDN for offline support */
@import url('./fonts-local.css');

/* Make sure fonts are loaded before editor initializes */
:root {
  --montserrat: 'Montserrat', sans-serif;
  --roboto: 'Roboto', sans-serif;
  --open-sans: 'Open Sans', sans-serif;
  --lato: 'Lato', sans-serif;
  --arial: Arial, sans-serif;
  --times: 'Times New Roman', serif;
  --georgia: Georgia, serif;
  --courier: 'Courier New', monospace;
  --verdana: Verdana, sans-serif;
  --helvetica: Helvetica, sans-serif;
}

/* Custom classes for applying fonts */
.font-montserrat {
  font-family: 'Montserrat', sans-serif !important;
}

.font-roboto {
  font-family: 'Roboto', sans-serif !important;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif !important;
}

.font-lato {
  font-family: 'Lato', sans-serif !important;
}

.font-arial {
  font-family: Arial, sans-serif !important;
}

.font-times-new-roman {
  font-family: 'Times New Roman', serif !important;
}

.font-georgia {
  font-family: Georgia, serif !important;
}

.font-courier-new {
  font-family: 'Courier New', monospace !important;
}

.font-verdana {
  font-family: Verdana, sans-serif !important;
}

.font-helvetica {
  font-family: Helvetica, sans-serif !important;
}

/* Direct attribute selectors for TipTap font styles */
[style*="font-family: Montserrat"] {
  font-family: 'Montserrat', sans-serif !important;
}

[style*="font-family: Arial"] {
  font-family: Arial, sans-serif !important;
}

[style*="font-family: Times New Roman"] {
  font-family: 'Times New Roman', serif !important;
}

[style*="font-family: Georgia"] {
  font-family: Georgia, serif !important;
}

[style*="font-family: Courier New"] {
  font-family: 'Courier New', monospace !important;
}

[style*="font-family: Verdana"] {
  font-family: Verdana, sans-serif !important;
}

[style*="font-family: Helvetica"] {
  font-family: Helvetica, sans-serif !important;
}

[style*="font-family: Roboto"] {
  font-family: 'Roboto', sans-serif !important;
}

[style*="font-family: Open Sans"] {
  font-family: 'Open Sans', sans-serif !important;
}

[style*="font-family: Lato"] {
  font-family: 'Lato', sans-serif !important;
}
