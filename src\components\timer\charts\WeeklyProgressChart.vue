<template>
  <div class="chart-container">
    <h3 class="chart-title">Weekly Progress</h3>
    <div class="chart-wrapper">
      <Line
        v-if="chartData"
        :data="chartData"
        :options="chartOptions"
        :height="200"
      />
      <div v-else class="chart-loading">Loading chart data...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'
import { useElectronAPI } from '../../../useElectronAPI'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

interface Session {
  id: number
  start_time: string
  duration?: number | null
  category?: string | null
  session_name?: string | null
  is_completed: number
  pomodoro_cycles_completed?: number | null
}

const db = useElectronAPI()
const sessions = ref<Session[]>([])

const chartData = computed(() => {
  if (!sessions.value.length) return null

  // Get last 14 days for better trend visualization
  const days = []
  const sessionCounts = []
  const focusTimes = []
  const pomodoroTotals = []
  const today = new Date()
  
  for (let i = 13; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    
    // Filter sessions for this day
    const daySessions = sessions.value.filter(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      return sessionDate === dateStr && session.is_completed === 1
    })
    
    // Calculate metrics for this day
    const sessionCount = daySessions.length
    const dayFocusTime = daySessions.reduce((total, session) => total + (session.duration || 0), 0)
    const pomodoroCount = daySessions.reduce((total, session) => total + (session.pomodoro_cycles_completed || 0), 0)
    
    days.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
    sessionCounts.push(sessionCount)
    focusTimes.push(Math.round(dayFocusTime / 60)) // Convert to minutes
    pomodoroTotals.push(pomodoroCount)
  }

  return {
    labels: days,
    datasets: [
      {
        label: 'Sessions',
        data: sessionCounts,
        borderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-primary-border-fallback').trim() || 'rgba(74, 74, 74, 1)',
        backgroundColor: (getComputedStyle(document.documentElement).getPropertyValue('--color-primary').trim() + '1A') || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-primary-fallback').trim() || 'rgba(74, 74, 74, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--color-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-primary-border-fallback').trim() || 'rgba(74, 74, 74, 1)',
        pointBorderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-text-fallback').trim() || '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.3,
        yAxisID: 'y'
      },
      {
        label: 'Focus Time (min)',
        data: focusTimes,
        borderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-secondary-border-fallback').trim() || 'rgba(136, 136, 136, 1)',
        backgroundColor: (getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary').trim() + '1A') || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-secondary-fallback').trim() || 'rgba(136, 136, 136, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-secondary-border-fallback').trim() || 'rgba(136, 136, 136, 1)',
        pointBorderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-text-fallback').trim() || '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.3,
        yAxisID: 'y1'
      }
    ]
  }
})

const chartOptions = computed((): ChartOptions<'line'> => {
  // Get theme-aware colors
  const textColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-text-fallback').trim() || '#4a4a4a'
  const tertiaryTextColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-tertiary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tertiary-text-fallback').trim() || '#888'
  const tooltipBg = getComputedStyle(document.documentElement).getPropertyValue('--color-bg-elevated').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-bg-fallback').trim() || 'rgba(0, 0, 0, 0.8)'
  const tooltipText = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-text-fallback').trim() || '#fff'

  return {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: textColor,
          font: {
            family: 'Montserrat',
            size: 12,
            weight: 500
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle'
        }
      },
      tooltip: {
        backgroundColor: tooltipBg,
        titleColor: tooltipText,
        bodyColor: tooltipText,
        borderColor: textColor,
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || ''
            const value = context.parsed.y

            if (label === 'Focus Time (min)') {
              const hours = Math.floor(value / 60)
              const minutes = value % 60
              if (hours > 0) {
                return `${label}: ${hours}h ${minutes}m`
              }
              return `${label}: ${minutes}m`
            }

            return `${label}: ${value}`
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: tertiaryTextColor,
          font: {
            family: 'Montserrat',
            size: 10
          },
          maxRotation: 45
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        beginAtZero: true,
        grid: {
          color: getComputedStyle(document.documentElement).getPropertyValue('--color-border-secondary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-border-fallback').trim() || 'rgba(200, 200, 200, 0.3)',
          lineWidth: 1
        },
        ticks: {
          color: tertiaryTextColor,
          font: {
            family: 'Montserrat',
            size: 11
          },
          stepSize: 1
        },
        title: {
          display: true,
          text: 'Sessions',
          color: textColor,
          font: {
            family: 'Montserrat',
            size: 12,
            weight: 600
          }
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: tertiaryTextColor,
          font: {
            family: 'Montserrat',
            size: 11
          },
          callback: function(value) {
            const minutes = value as number
            const hours = Math.floor(minutes / 60)
            const remainingMinutes = minutes % 60
            if (hours > 0) {
              return `${hours}h ${remainingMinutes}m`
            }
            return `${minutes}m`
          }
        },
        title: {
          display: true,
          text: 'Focus Time',
          color: textColor,
          font: {
            family: 'Montserrat',
            size: 12,
            weight: 600
          }
        }
      }
    }
  }
})

const loadChartData = async () => {
  try {
    const fourteenDaysAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const today = new Date().toISOString().split('T')[0]
    const sessionData = await db.timer.getSessionsByDateRange(fourteenDaysAgo, today)
    sessions.value = sessionData
  } catch (error) {
    console.error('Failed to load chart data:', error)
  }
}

onMounted(() => {
  loadChartData()
})

// Expose method to refresh data
defineExpose({
  refreshData: loadChartData
})
</script>

<style scoped>
.chart-container {
  background: var(--color-card-bg);
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  padding: 20px;
  margin-bottom: 15px;
}

.chart-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 15px;
  font-family: 'Montserrat', sans-serif;
}

.chart-wrapper {
  height: 200px;
  position: relative;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-tertiary);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
  }
  
  .chart-wrapper {
    height: 180px;
  }
}
</style>
