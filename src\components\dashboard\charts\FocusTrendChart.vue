<template>
  <div class="focus-trend-chart">
    <h3 class="chart-title">Focus Time Trend</h3>
    <div class="chart-container">
      <svg :width="chartWidth" :height="chartHeight" class="chart-svg">
        <!-- Grid lines -->
        <g class="grid">
          <line 
            v-for="i in 4" 
            :key="`grid-${i}`"
            :x1="padding" 
            :y1="padding + (i - 1) * (chartHeight - 2 * padding) / 3"
            :x2="chartWidth - padding" 
            :y2="padding + (i - 1) * (chartHeight - 2 * padding) / 3"
            stroke="var(--color-dashboard-chart-grid)"
            stroke-width="1"
            opacity="0.3"
          />
        </g>
        
        <!-- Area fill -->
        <path
          v-if="pathData"
          :d="areaPath"
          :fill="areaColor"
          opacity="0.2"
        />
        
        <!-- Line -->
        <path
          v-if="pathData"
          :d="pathData"
          :stroke="lineColor"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        
        <!-- Data points -->
        <g class="data-points">
          <circle
            v-for="(day, index) in weekData"
            :key="`point-${index}`"
            :cx="padding + index * pointSpacing"
            :cy="chartHeight - padding - (day.focusMinutes / maxFocusTime) * (chartHeight - 2 * padding)"
            r="3"
            :fill="lineColor"
          />
        </g>
        
        <!-- Day labels -->
        <g class="day-labels">
          <text
            v-for="(day, index) in weekData"
            :key="`label-${index}`"
            :x="padding + index * pointSpacing"
            :y="chartHeight - 5"
            text-anchor="middle"
            class="day-label"
          >
            {{ day.dayLabel }}
          </text>
        </g>
      </svg>
      
      <!-- Stats -->
      <div class="chart-stats">
        <div class="stat-item">
          <span class="stat-value">{{ totalHours }}h</span>
          <span class="stat-label">This Week</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ avgMinutes }}m</span>
          <span class="stat-label">Daily Avg</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useElectronAPI } from '../../../useElectronAPI'

interface DayData {
  date: string
  dayLabel: string
  focusMinutes: number
}

const db = useElectronAPI()

const weekData = ref<DayData[]>([])
const chartWidth = 280
const chartHeight = 120
const padding = 20

const pointSpacing = computed(() => (chartWidth - 2 * padding) / 6)
const maxFocusTime = computed(() => {
  const maxMinutes = Math.max(...weekData.value.map(day => day.focusMinutes), 60)
  return Math.ceil(maxMinutes / 60) * 60 // Round up to nearest hour
})

const totalHours = computed(() => {
  const totalMinutes = weekData.value.reduce((sum, day) => sum + day.focusMinutes, 0)
  return Math.round(totalMinutes / 60 * 10) / 10 // Round to 1 decimal
})

const avgMinutes = computed(() => {
  const totalMinutes = weekData.value.reduce((sum, day) => sum + day.focusMinutes, 0)
  return Math.round(totalMinutes / 7)
})

// Theme-aware colors
const lineColor = computed(() => {
  if (typeof window === 'undefined') return '#4A4A4A'
  return getComputedStyle(document.documentElement)
    .getPropertyValue('--color-dashboard-chart-primary')
    .trim() || '#4A4A4A'
})

const areaColor = computed(() => {
  if (typeof window === 'undefined') return '#4A4A4A'
  return getComputedStyle(document.documentElement)
    .getPropertyValue('--color-dashboard-chart-primary')
    .trim() || '#4A4A4A'
})

const pathData = computed(() => {
  if (weekData.value.length === 0) return ''
  
  const points = weekData.value.map((day, index) => {
    const x = padding + index * pointSpacing.value
    const y = chartHeight - padding - (day.focusMinutes / maxFocusTime.value) * (chartHeight - 2 * padding)
    return `${x},${y}`
  })
  
  return `M ${points.join(' L ')}`
})

const areaPath = computed(() => {
  if (weekData.value.length === 0) return ''
  
  const points = weekData.value.map((day, index) => {
    const x = padding + index * pointSpacing.value
    const y = chartHeight - padding - (day.focusMinutes / maxFocusTime.value) * (chartHeight - 2 * padding)
    return `${x},${y}`
  })
  
  const firstX = padding
  const lastX = padding + (weekData.value.length - 1) * pointSpacing.value
  const bottomY = chartHeight - padding
  
  return `M ${firstX},${bottomY} L ${points.join(' L ')} L ${lastX},${bottomY} Z`
})

const loadFocusData = async () => {
  try {
    const today = new Date()
    const weekDays: DayData[] = []
    
    // Generate last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      weekDays.push({
        date: dateStr,
        dayLabel: date.toLocaleDateString('en-US', { weekday: 'short' }),
        focusMinutes: 0
      })
    }
    
    // Load sessions data
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const sessions = await db.timer.getSessionsByDateRange(
      sevenDaysAgo.toISOString().split('T')[0],
      today.toISOString().split('T')[0]
    )
    
    sessions.filter(s => s.is_completed === 1).forEach(session => {
      const sessionDate = new Date(session.start_time).toISOString().split('T')[0]
      const dayIndex = weekDays.findIndex(day => day.date === sessionDate)
      if (dayIndex !== -1) {
        weekDays[dayIndex].focusMinutes += Math.round((session.duration || 0) / 60)
      }
    })
    
    weekData.value = weekDays
  } catch (error) {
    console.error('Failed to load focus trend data:', error)
  }
}

onMounted(() => {
  loadFocusData()
})

defineExpose({
  refresh: loadFocusData
})
</script>

<style scoped>
.focus-trend-chart {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 12px;
  padding: 20px;
  height: 200px;
  transition: all 0.2s ease;
}

.focus-trend-chart:hover {
  border-color: var(--color-border-hover);
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
  text-align: center;
  letter-spacing: -0.025em;
}

.chart-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
}

.chart-svg {
  flex: 1;
}

.day-label {
  font-size: 11px;
  fill: var(--color-text-secondary);
  font-family: system-ui, -apple-system, sans-serif;
  font-weight: 500;
}

.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--color-border-secondary);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  font-weight: 500;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .focus-trend-chart {
    padding: 16px;
    height: 180px;
    border-radius: 10px;
  }

  .chart-title {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .chart-container {
    height: calc(100% - 32px);
  }

  .chart-stats {
    margin-top: 10px;
    padding-top: 10px;
  }

  .stat-value {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .focus-trend-chart {
    padding: 12px;
    height: 160px;
  }

  .chart-title {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 0.9rem;
  }
}
</style>
