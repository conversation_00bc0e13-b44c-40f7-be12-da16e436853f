# Session Duration Calculation Fix

## Issue Description
The SessionDetailsModal was displaying incorrect duration data. Instead of showing the total time the session existed (from creation to completion), it was always using the current time as the end time, making the duration calculation meaningless.

## Root Cause Analysis
The issue was in `TimerView.vue` in the `handleViewSessionDetails` function:
```typescript
const handleViewSessionDetails = (sessionData: any) => {
    selectedSession.value = {
        ...sessionData,
        isActive: sessionData.isActive ?? false,
        endTime: sessionData.isActive ? null : new Date()  // ← Problem: always current time!
    }
    showSessionDetailsModal.value = true
}
```

Additionally, the session data mapping in `loadSessions` was not including the actual `end_time` from the database.

## Files Modified
- `src/views/TimerView.vue`

## What Was Done
1. **Fixed Session Data Mapping**: Added `endTime` property to session objects when loading from database, using the actual `end_time` value.

2. **Fixed Session Details Handler**: Removed the incorrect `endTime` override that was always setting it to current time.

3. **Updated Session Interface**: Added `endTime` property to the base `Session` interface.

## How It Was Fixed
### Before (Incorrect):
```typescript
// In loadSessions - missing endTime
.map(session => ({
    id: session.id,
    sessionName: session.session_name || session.focus || 'Unnamed Session',
    // ... other properties
    isActive: false
    // endTime was missing!
}))

// In handleViewSessionDetails - wrong endTime
selectedSession.value = {
    ...sessionData,
    isActive: sessionData.isActive ?? false,
    endTime: sessionData.isActive ? null : new Date()  // Always current time!
}
```

### After (Correct):
```typescript
// In loadSessions - include actual endTime
.map(session => ({
    id: session.id,
    sessionName: session.session_name || session.focus || 'Unnamed Session',
    // ... other properties
    endTime: session.end_time ? new Date(session.end_time) : null,  // Actual end time!
    isActive: false
}))

// In handleViewSessionDetails - use existing endTime
selectedSession.value = {
    ...sessionData,
    isActive: sessionData.isActive ?? false
    // endTime is already included in sessionData from loadSessions
}
```

## Technical Details
- **Data Source**: Now uses actual `end_time` from database instead of current time
- **Database Field**: Maps `session.end_time` to `endTime` property in session objects
- **Duration Calculation**: SessionDetailsModal now correctly calculates duration as `endTime - startTime`

## Benefits
1. **Accurate Duration Display**: Shows actual total session time from creation to completion
2. **Correct Historical Data**: Past sessions show their actual duration, not time since viewing
3. **Proper Database Integration**: Uses the `end_time` field that's already stored in the database
4. **Consistent Data Flow**: Session data includes all necessary information from the start

## Testing Recommendations
1. Create and complete a session
2. Wait some time, then view the session details
3. Verify that the duration shows the actual session time, not the time since viewing
4. Check that the duration matches the difference between start and end times displayed
