# Sync System .noti Format Implementation

## Overview

This document describes the successful implementation of the .noti format migration for the Noti sync system. The sync system has been updated from using Markdown (.md) files to the enhanced .noti format with embedded media support.

## Files Modified

### Core Implementation Files

1. **`electron/main/api/sync-logic/types.ts`**
   - Added `NotiFileData`, `NotiMetadata`, `NotiContent`, and `EmbeddedMedia` interfaces
   - Defined the simplified .noti format structure for sync operations

2. **`electron/main/api/sync-logic/media-utils.ts`** (NEW FILE)
   - `embedMediaFiles()` - Converts note's media files to base64 embedded format
   - `restoreEmbeddedMedia()` - Extracts embedded media and restores to database
   - `createMarkdownPreview()` - Creates first 50 words preview for notes list
   - `extractPlainText()` - Removes HTML tags for search functionality
   - `replaceMediaUrlsWithEmbedded()` - Converts media URLs to embedded references
   - Utility functions for validation and media handling

3. **`electron/main/api/sync-logic/file-operations.ts`**
   - Updated `readNote()` method to parse .noti JSON files
   - Updated `writeNote()` method to write .noti JSON files with validation
   - Added legacy methods for backward compatibility during migration
   - Enhanced error handling for JSON parsing and validation

4. **`electron/main/api/sync-logic/unified-sync-engine.ts`**
   - Updated `exportNote()` method to create .noti files with embedded media
   - Updated `importNote()` method to parse .noti files and restore media
   - Changed file paths from `.md` to `.noti` throughout
   - Added media processing workflow for export/import operations
   - Updated file type detection to recognize .noti files

5. **`electron/main/api/sync-logic/manifest-manager.ts`**
   - Changed file extension generation from `.md` to `.noti`
   - Updated hash calculation to include `html_content` for better change detection
   - Maintained existing manifest structure while supporting new format

## What Was Done

### 1. Type Definitions Added
- **NotiFileData**: Main structure for .noti files with version, metadata, content, and media
- **NotiMetadata**: Note properties from database (id, title, timestamps, relationships)
- **NotiContent**: Multiple content formats (HTML, markdown preview, plain text)
- **EmbeddedMedia**: Base64-encoded media files with metadata for portability

### 2. Media Utilities Created
- **Media Embedding**: Reads media files from disk, converts to base64, creates embedded references
- **Media Restoration**: Decodes base64 data, saves to media storage, updates database
- **URL Processing**: Replaces `noti-media://` URLs with embedded references and vice versa
- **Content Processing**: Creates markdown previews and plain text for search

### 3. File Operations Updated
- **Read Operations**: Parse .noti JSON files with validation and error handling
- **Write Operations**: Create .noti JSON files with proper formatting and validation
- **Legacy Support**: Maintain backward compatibility with .md files during transition
- **Error Handling**: Comprehensive error handling for JSON operations

### 4. Sync Engine Enhanced
- **Export Process**: 
  - Get note from database with HTML content
  - Embed associated media files as base64
  - Replace media URLs with embedded references
  - Create markdown preview and plain text
  - Save as .noti JSON file
- **Import Process**:
  - Parse .noti JSON file
  - Create/update note with preview text
  - Restore embedded media to database and file system
  - Update HTML content with new media URLs
  - Maintain folder/book relationships

### 5. Manifest Manager Updated
- **Path Generation**: Changed from `.md` to `.noti` file extensions
- **Hash Calculation**: Include `html_content` in hash for better change detection
- **File Type Detection**: Recognize .noti files in directory listings

## How It Was Implemented

### .noti File Structure (Simplified for Sync)
```json
{
  "version": "1.0",
  "type": "noti-note",
  "schema": "https://noti.app/schemas/note/v1.0",
  "metadata": {
    "id": 123,
    "title": "Note Title",
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T14:30:00.000Z",
    "last_viewed_at": "2024-01-15T14:30:00.000Z",
    "type": "text",
    "color": "#ff0000",
    "folder_id": 456,
    "book_id": 789
  },
  "content": {
    "html": "<p>Full rich HTML content</p><img src=\"noti-media://embedded_media_1\">",
    "markdown": "First 50 words for preview...",
    "plain_text": "Plain text for search"
  },
  "media": [
    {
      "id": "embedded_media_1",
      "file_name": "image.png",
      "file_type": "image/png",
      "file_size": 102400,
      "original_path": "noti-media://original/path",
      "embedded": true,
      "data": "base64_encoded_image_data"
    }
  ]
}
```

### Media Workflow
1. **Export**: Read media files → Convert to base64 → Replace URLs → Embed in .noti
2. **Import**: Parse .noti → Decode base64 → Save to storage → Update database → Restore URLs

### Key Design Decisions
- **HTML Priority**: Store full HTML content, markdown is just preview
- **Media Embedding**: All media files embedded as base64 for portability
- **No Migration**: App hasn't been released, so no backward compatibility needed
- **Manifest Unchanged**: Keep existing manifest structure, just change file paths

## Benefits Achieved

1. **Complete Portability**: .noti files contain all media embedded as base64
2. **Rich Content Preservation**: Full HTML content maintained during sync
3. **Fast Preview**: Markdown preview for quick notes list display
4. **Search Support**: Plain text extraction for search functionality
5. **Media Integrity**: All media files preserved and restored correctly
6. **Existing Compatibility**: Sync system functionality unchanged for users

## Testing Requirements

The implementation includes comprehensive error handling and validation:
- .noti file structure validation
- Media embedding/extraction error handling
- JSON parsing with fallback mechanisms
- Database transaction safety
- File system operation error handling

## Future Considerations

- Monitor file sizes with embedded media for performance
- Consider compression for large media files
- Implement media deduplication if needed
- Add progress reporting for large media operations

## Conclusion

The sync system has been successfully migrated to use the .noti format with embedded media support. This provides complete portability while maintaining all existing sync functionality and improving content preservation during sync operations.
