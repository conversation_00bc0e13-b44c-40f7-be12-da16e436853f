// Database API with CRUD operations
import { getDatabase } from './database';
import sqlite3 from 'sqlite3';
import * as fs from 'fs';

// Types
type Database = sqlite3.Database;
type RunResult = sqlite3.RunResult;

// Define interfaces for data structures
export interface Note {
  id?: number;
  title: string;
  content?: string;
  html_content?: string | null;
  folder_id?: number | null;
  book_id?: number | null;
  type?: string;
  color?: string | null;
  order?: number | null;
  last_viewed_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Folder {
  id?: number;
  name: string;
  parent_id?: number | null;
  book_id?: number | null;
  color?: string | null;
  order?: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface FolderWithNoteCount extends Folder {
  notesCount: number;
}

export interface DbResult {
  id?: number;
  changes?: number;
}

export interface Book {
  id?: number;
  title: string;
  author?: string | null;
  isbn?: string | null;
  cover_url?: string | null;
  publication_date?: string | null;
  description?: string | null;
  page_count?: number | null;
  current_page?: number | null;
  rating?: number | null;
  language?: string | null;
  genres?: string | null;
  olid?: string | null;
  status?: string | null;
  custom_fields?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface BookWithNoteCount extends Book {
  notesCount: number;
  recentNote?: {
    id: number;
    title: string;
    last_viewed_at: string;
  };
  addedDate?: Date;
  cover_media_url?: string | null;
}

// Helper function to run queries that return a single result
export const dbGet = <T>(query: string, params: any[] = []): Promise<T> => {
  return new Promise((resolve, reject) => {
    const db: Database = getDatabase();
    db.get(query, params, (err: Error | null, row: T) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

// Helper function to run queries that return multiple results
export const dbAll = <T>(query: string, params: any[] = []): Promise<T[]> => {
  return new Promise((resolve, reject) => {
    const db: Database = getDatabase();
    db.all(query, params, (err: Error | null, rows: T[]) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// Helper function to run queries that don't return a result (INSERT, UPDATE, DELETE)
export const dbRun = (query: string, params: any[] = []): Promise<RunResult> => {
  return new Promise((resolve, reject) => {
    const db: Database = getDatabase();
    // Use RunResult which includes lastID and changes
    db.run(query, params, function (this: RunResult, err: Error | null) {
      if (err) {
        reject(err);
      } else {
        // 'this' is implicitly RunResult here
        resolve(this);
      }
    });
  });
};

// NOTES CRUD OPERATIONS

// Create a new note
export const createNote = async (note: Note): Promise<Note> => {
  // Input validation
  if (!note.title || typeof note.title !== 'string' || note.title.trim() === '') {
    throw new Error('Note title is required and must be a non-empty string');
  }

  if (note.title.length > 255) {
    throw new Error('Note title must not exceed 255 characters');
  }

  if (note.content && typeof note.content !== 'string') {
    throw new Error('Note content must be a string');
  }

  if (note.html_content && typeof note.html_content !== 'string') {
    throw new Error('Note HTML content must be a string');
  }

  if (note.folder_id !== null && note.folder_id !== undefined && (!Number.isInteger(note.folder_id) || note.folder_id <= 0)) {
    throw new Error('Folder ID must be a positive integer');
  }

  if (note.book_id !== null && note.book_id !== undefined && (!Number.isInteger(note.book_id) || note.book_id <= 0)) {
    throw new Error('Book ID must be a positive integer');
  }

  const {
    title,
    content,
    html_content = null,
    folder_id = null,
    book_id = null,
    type = 'text',
    color = null,
    order = null
  } = note;

  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO notes (
      title, content, html_content, folder_id, book_id,
      type, color, "order", last_viewed_at, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  try {
    const result: RunResult = await dbRun(query, [
      title.trim(), content, html_content, folder_id, book_id,
      type, color, order, now, now, now
    ]);

    // Return the created note using lastID
    if (result.lastID) {
      return getNoteById(result.lastID);
    }
    throw new Error('Failed to create note, no ID returned');
  } catch (error) {
    console.error('Error creating note:', error);
    throw error;
  }
};

// Get all notes
export const getAllNotes = async (): Promise<Note[]> => {
  const query = 'SELECT * FROM notes ORDER BY updated_at DESC';

  try {
    return await dbAll<Note>(query);
  } catch (error) {
    console.error('Error getting all notes:', error);
    throw error;
  }
};

// Get a note by ID
export const getNoteById = async (id: number): Promise<Note> => {
  const query = 'SELECT * FROM notes WHERE id = ?';

  try {
    return await dbGet<Note>(query, [id]);
  } catch (error) {
    console.error(`Error getting note with ID ${id}:`, error);
    throw error;
  }
};

// Get notes by folder ID
export const getNotesByFolderId = async (folderId: number): Promise<Note[]> => {
  const query = 'SELECT * FROM notes WHERE folder_id = ? ORDER BY updated_at DESC';

  try {
    return await dbAll<Note>(query, [folderId]);
  } catch (error) {
    console.error(`Error getting notes for folder ID ${folderId}:`, error);
    throw error;
  }
};

// Get notes by book ID
export const getNotesByBookId = async (bookId: number): Promise<Note[]> => {
  const query = 'SELECT * FROM notes WHERE book_id = ? ORDER BY updated_at DESC';

  try {
    return await dbAll<Note>(query, [bookId]);
  } catch (error) {
    console.error(`Error getting notes for book ID ${bookId}:`, error);
    throw error;
  }
};

// Update a note
export const updateNote = async (id: number, noteUpdates: Partial<Note>): Promise<Note> => {
  const {
    title,
    content,
    html_content,
    folder_id,
    book_id,
    type,
    color,
    order,
    last_viewed_at
  } = noteUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE notes SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (title !== undefined) {
    query += ', title = ?';
    params.push(title);
  }

  if (content !== undefined) {
    query += ', content = ?';
    params.push(content);
  }

  if (html_content !== undefined) {
    query += ', html_content = ?';
    params.push(html_content);
  }

  if (folder_id !== undefined) {
    query += ', folder_id = ?';
    params.push(folder_id);
  }

  if (book_id !== undefined) {
    query += ', book_id = ?';
    params.push(book_id);
  }

  if (type !== undefined) {
    query += ', type = ?';
    params.push(type);
  }

  if (color !== undefined) {
    query += ', color = ?';
    params.push(color);
  }

  if (order !== undefined) {
    query += ', "order" = ?';
    params.push(order);
  }

  if (last_viewed_at !== undefined) {
    query += ', last_viewed_at = ?';
    params.push(last_viewed_at);
  } else {
    // Update the last_viewed_at when a note is updated
    query += ', last_viewed_at = CURRENT_TIMESTAMP';
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = await dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getNoteById(id);
    }
    throw new Error(`Note with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating note with ID ${id}:`, error);
    throw error;
  }
};

// Delete a note
export const deleteNote = async (id: number): Promise<{ success: boolean; id: number }> => {
  const query = 'DELETE FROM notes WHERE id = ?';

  try {
    const result: RunResult = await dbRun(query, [id]);
    return { success: result.changes ? result.changes > 0 : false, id };
  } catch (error) {
    console.error(`Error deleting note with ID ${id}:`, error);
    throw error;
  }
};

// FOLDERS CRUD OPERATIONS

// Create a new folder
export const createFolder = async (folder: Folder): Promise<Folder> => {
  const { name, parent_id = null, book_id = null, color = null, order = null } = folder;
  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO folders (name, parent_id, book_id, color, "order", created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  try {
    const result: RunResult = await dbRun(query, [name, parent_id, book_id, color, order, now, now]);
    // Return the created folder using lastID
    if (result.lastID) {
      return getFolderById(result.lastID);
    }
    throw new Error('Failed to create folder, no ID returned');
  } catch (error) {
    console.error('Error creating folder:', error);
    throw error;
  }
};

// Get all folders
export const getAllFolders = async (): Promise<Folder[]> => {
  const query = 'SELECT * FROM folders ORDER BY name';

  try {
    return await dbAll<Folder>(query);
  } catch (error) {
    console.error('Error getting all folders:', error);
    throw error;
  }
};

// Get all folders with their note counts
export const getAllFoldersWithNoteCounts = async (): Promise<FolderWithNoteCount[]> => {
  const query = `
    SELECT
        f.*,
        COUNT(n.id) as notesCount
    FROM
        folders f
    LEFT JOIN
        notes n ON f.id = n.folder_id
    GROUP BY
        f.id
    ORDER BY
        f.name;
  `;
  try {
    // The result from SQL will have notesCount, so we cast to FolderWithNoteCount
    return await dbAll<FolderWithNoteCount>(query);
  } catch (error) {
    console.error('Error getting all folders with note counts:', error);
    throw error;
  }
};

// Get a folder by ID
export const getFolderById = async (id: number): Promise<Folder> => {
  // Validate folder ID
  if (id === 0 || !Number.isInteger(id) || id < 0) {
    console.error(`Invalid folder ID: ${id}`);
    throw new Error(`Folder with ID ${id} not found`);
  }

  const query = 'SELECT * FROM folders WHERE id = ?';

  try {
    return await dbGet<Folder>(query, [id]);
  } catch (error) {
    console.error(`Error getting folder with ID ${id}:`, error);
    throw error;
  }
};

// Get child folders
export const getChildFolders = async (parentId: number | null): Promise<Folder[]> => {
  let query: string;
  let params: any[] = [];

  if (parentId === null) {
    // Get root folders (with no parent)
    query = 'SELECT * FROM folders WHERE parent_id IS NULL ORDER BY name';
  } else {
    // Get child folders of a specific parent
    query = 'SELECT * FROM folders WHERE parent_id = ? ORDER BY name';
    params = [parentId];
  }

  try {
    return await dbAll<Folder>(query, params);
  } catch (error) {
    console.error('Error getting child folders:', error);
    throw error;
  }
};

// Get folder by book_id
export const getFolderByBookId = async (bookId: number): Promise<Folder | null> => {
  const query = 'SELECT * FROM folders WHERE book_id = ?';

  try {
    const result = await dbGet<Folder>(query, [bookId]);
    return result || null;
  } catch (error) {
    console.error(`Error getting folder for book ID ${bookId}:`, error);
    throw error;
  }
};

// Get inherited book_id by traversing up the folder hierarchy
export const getInheritedBookId = async (parentId: number | null): Promise<number | null> => {
  if (parentId === null || parentId === undefined) {
    return null;
  }

  try {
    let currentFolderId: number | null = parentId;
    const maxDepth = 50; // Prevent infinite loops
    let depth = 0;

    while (currentFolderId !== null && depth < maxDepth) {
      const folder = await getFolderById(currentFolderId);

      // If this folder has a book_id, return it
      if (folder.book_id !== null && folder.book_id !== undefined) {
        console.log(`Found inherited book_id ${folder.book_id} from folder "${folder.name}" (ID: ${folder.id})`);
        return folder.book_id;
      }

      // Move up to the parent folder
      currentFolderId = folder.parent_id;
      depth++;
    }

    // No book_id found in the hierarchy
    return null;
  } catch (error) {
    console.error(`Error getting inherited book_id for parent ID ${parentId}:`, error);
    return null;
  }
};

// Update a folder
export const updateFolder = async (id: number, folderUpdates: Partial<Folder>): Promise<Folder> => {
  const { name, parent_id, book_id, color, order } = folderUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE folders SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (name !== undefined) {
    query += ', name = ?';
    params.push(name);
  }

  if (parent_id !== undefined) {
    query += ', parent_id = ?';
    params.push(parent_id);
  }

  if (book_id !== undefined) {
    query += ', book_id = ?';
    params.push(book_id);
  }

  if (color !== undefined) {
    query += ', color = ?';
    params.push(color);
  }

  if (order !== undefined) {
    query += ', "order" = ?';
    params.push(order);
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = await dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getFolderById(id);
    }
    throw new Error(`Folder with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating folder with ID ${id}:`, error);
    throw error;
  }
};

// Delete a folder and its notes
export const deleteFolder = async (id: number): Promise<{ success: boolean; id: number }> => {
  // Note: We're relying on ON DELETE CASCADE for child folders and
  // ON DELETE SET NULL for notes in this folder
  const query = 'DELETE FROM folders WHERE id = ?';

  try {
    const result: RunResult = await dbRun(query, [id]);
    return { success: result.changes ? result.changes > 0 : false, id };
  } catch (error) {
    console.error(`Error deleting folder with ID ${id}:`, error);
    throw error;
  }
};

// BOOKS CRUD OPERATIONS

// Create a new book
export const createBook = async (book: Book): Promise<Book> => {
  const {
    title,
    author = null,
    isbn = null,
    cover_url = null,
    publication_date = null,
    description = null,
    page_count = null,
    current_page = null,
    rating = null,
    language = null,
    genres = null,
    olid = null,
    status = 'unread',
    custom_fields = null
  } = book;

  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO books (
      title, author, isbn, cover_url, publication_date,
      description, page_count, current_page, rating, language, genres,
      olid, status, custom_fields, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const params = [
    title, author, isbn, cover_url, publication_date,
    description, page_count, current_page, rating, language, genres,
    olid, status, custom_fields, now, now
  ];

  try {
    const result: RunResult = await dbRun(query, params);

    // Return the created book using lastID
    if (result.lastID) {
      return getBookById(result.lastID);
    }
    throw new Error('Failed to create book, no ID returned');
  } catch (error) {
    console.error('Error creating book:', error);
    throw error;
  }
};

// Get all books
export const getAllBooks = async (): Promise<Book[]> => {
  const query = 'SELECT * FROM books ORDER BY created_at DESC';

  try {
    return await dbAll<Book>(query);
  } catch (error) {
    console.error('Error getting all books:', error);
    throw error;
  }
};

// Get all books with their note counts and recent note info
export const getAllBooksWithNoteCounts = async (): Promise<BookWithNoteCount[]> => {
  const query = `
    SELECT
      b.*,
      COUNT(n.id) as notesCount,
      rn.id as recent_note_id,
      rn.title as recent_note_title,
      rn.last_viewed_at as recent_note_last_viewed_at
    FROM
      books b
    LEFT JOIN
      notes n ON b.id = n.book_id
    LEFT JOIN (
      SELECT
        book_id,
        id,
        title,
        last_viewed_at,
        ROW_NUMBER() OVER (PARTITION BY book_id ORDER BY last_viewed_at DESC) as rn
      FROM notes
      WHERE book_id IS NOT NULL
    ) rn ON b.id = rn.book_id AND rn.rn = 1
    GROUP BY
      b.id
    ORDER BY
      b.created_at DESC;
  `;

  try {
    const results = await dbAll<any>(query);

    // Transform the flat result into the proper structure
    return results.map(row => {
      const book: BookWithNoteCount = {
        id: row.id,
        title: row.title,
        author: row.author,
        isbn: row.isbn,
        cover_url: row.cover_url,
        publication_date: row.publication_date,
        description: row.description,
        page_count: row.page_count,
        current_page: row.current_page,
        rating: row.rating,
        language: row.language,
        genres: row.genres,
        olid: row.olid,
        status: row.status,
        custom_fields: row.custom_fields,
        created_at: row.created_at,
        updated_at: row.updated_at,
        notesCount: row.notesCount
      };

      // Add recent note if it exists
      if (row.recent_note_id) {
        book.recentNote = {
          id: row.recent_note_id,
          title: row.recent_note_title,
          last_viewed_at: row.recent_note_last_viewed_at
        };
      }

      return book;
    });
  } catch (error) {
    console.error('Error getting all books with note counts:', error);
    throw error;
  }
};

// Get a book by ID
export const getBookById = async (id: number): Promise<Book> => {
  const query = 'SELECT * FROM books WHERE id = ?';

  try {
    const result = await dbGet<Book>(query, [id]);
    if (!result) {
      throw new Error(`Book with ID ${id} not found`);
    }
    return result;
  } catch (error) {
    console.error(`Error getting book with ID ${id}:`, error);
    throw error;
  }
};

// Get book by ISBN
export const getBookByIsbn = async (isbn: string): Promise<Book | null> => {
  const query = 'SELECT * FROM books WHERE isbn = ?';

  try {
    const result = await dbGet<Book>(query, [isbn]);
    return result || null;
  } catch (error) {
    console.error(`Error getting book with ISBN ${isbn}:`, error);
    throw error;
  }
};

// Get book by OpenLibrary ID
export const getBookByOlid = async (olid: string): Promise<Book | null> => {
  const query = 'SELECT * FROM books WHERE olid = ?';

  try {
    const result = await dbGet<Book>(query, [olid]);
    return result || null;
  } catch (error) {
    console.error(`Error getting book with OLID ${olid}:`, error);
    throw error;
  }
};

// Search books by title, author, or ISBN
export const searchBooks = async (searchTerm: string): Promise<Book[]> => {
  const query = `
    SELECT * FROM books
    WHERE
      title LIKE ? OR
      author LIKE ? OR
      isbn LIKE ? OR
      description LIKE ?
    ORDER BY
      CASE
        WHEN title LIKE ? THEN 1
        WHEN author LIKE ? THEN 2
        WHEN isbn LIKE ? THEN 3
        ELSE 4
      END,
      created_at DESC
  `;

  const searchPattern = `%${searchTerm}%`;
  const exactSearchPattern = `${searchTerm}%`;

  try {
    return await dbAll<Book>(query, [
      searchPattern, searchPattern, searchPattern, searchPattern,
      exactSearchPattern, exactSearchPattern, exactSearchPattern
    ]);
  } catch (error) {
    console.error(`Error searching books with term "${searchTerm}":`, error);
    throw error;
  }
};

// Get recent books (added in the last N days)
export const getRecentBooks = async (days: number = 14): Promise<Book[]> => {
  const query = `
    SELECT * FROM books
    WHERE created_at >= ?
    ORDER BY created_at DESC
  `;

  // Calculate the cutoff date
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  const cutoffDateString = cutoffDate.toISOString();

  try {
    return await dbAll<Book>(query, [cutoffDateString]);
  } catch (error) {
    console.error(`Error getting recent books from last ${days} days:`, error);
    throw error;
  }
};

// Update a book
export const updateBook = async (id: number, bookUpdates: Partial<Book>): Promise<Book> => {
  const {
    title,
    author,
    isbn,
    cover_url,
    publication_date,
    description,
    page_count,
    current_page,
    rating,
    language,
    genres,
    olid,
    status,
    custom_fields
  } = bookUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE books SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (title !== undefined) {
    query += ', title = ?';
    params.push(title);
  }

  if (author !== undefined) {
    query += ', author = ?';
    params.push(author);
  }

  if (isbn !== undefined) {
    query += ', isbn = ?';
    params.push(isbn);
  }

  // DEFENSIVE FIX: Only update cover_url if explicitly provided and not null/empty
  // This prevents accidental deletion of existing covers during regular book updates
  if (cover_url !== undefined && cover_url !== null && cover_url !== '') {
    query += ', cover_url = ?';
    params.push(cover_url);
  } else if (cover_url === null || cover_url === '') {
    // Only explicitly set to null if that's the intention (e.g., removing a cover)
    console.log(`Warning: Attempting to clear cover_url for book ID ${id}. This should be intentional.`);
    query += ', cover_url = ?';
    params.push(cover_url);
  }
  // If cover_url is undefined, we don't update it at all (preserve existing value)

  if (publication_date !== undefined) {
    query += ', publication_date = ?';
    params.push(publication_date);
  }

  if (description !== undefined) {
    query += ', description = ?';
    params.push(description);
  }

  if (page_count !== undefined) {
    query += ', page_count = ?';
    params.push(page_count);
  }

  if (current_page !== undefined) {
    query += ', current_page = ?';
    params.push(current_page);
  }

  if (rating !== undefined) {
    query += ', rating = ?';
    params.push(rating);
  }

  if (language !== undefined) {
    query += ', language = ?';
    params.push(language);
  }

  if (genres !== undefined) {
    query += ', genres = ?';
    params.push(genres);
  }

  if (olid !== undefined) {
    query += ', olid = ?';
    params.push(olid);
  }

  if (status !== undefined) {
    query += ', status = ?';
    params.push(status);
  }

  if (custom_fields !== undefined) {
    query += ', custom_fields = ?';
    params.push(custom_fields);
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = await dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getBookById(id);
    }
    throw new Error(`Book with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating book with ID ${id}:`, error);
    throw error;
  }
};

// Delete a book
// This function now also needs to handle deleting associated media files
// and unlinking notes (or deleting them if they are exclusively for this book and not in a folder)
export const deleteBook = async (id: number): Promise<{ success: boolean; id: number }> => {
  const db: Database = getDatabase();

  return new Promise((resolve, reject) => {
    db.serialize(async () => {
      try {
        await dbRun('BEGIN TRANSACTION');

        // 1. Delete associated media files (cover image and any other media linked to this book)
        // Query all media files linked to this book
        const mediaFilesQuery = 'SELECT * FROM media_files WHERE book_id = ?';
        const mediaFiles = await dbAll<{id: number; file_path: string; file_name: string}>(mediaFilesQuery, [id]);

        // Delete each media file (both database record and physical file)
        for (const mediaFile of mediaFiles) {
          try {
            // Delete from database first
            const deleteMediaQuery = 'DELETE FROM media_files WHERE id = ?';
            await dbRun(deleteMediaQuery, [mediaFile.id]);

            // Then delete physical file if it exists
            if (fs.existsSync(mediaFile.file_path)) {
              fs.unlinkSync(mediaFile.file_path);
              console.log(`Deleted media file: ${mediaFile.file_name} (${mediaFile.file_path})`);
            }
          } catch (mediaError) {
            console.error(`Error deleting media file ${mediaFile.id} for book ${id}:`, mediaError);
            // Continue with other files even if one fails
          }
        }

        console.log(`Deleted ${mediaFiles.length} media file(s) for book ${id}`);

        // 2. Handle notes associated with the book but NOT in a specific book folder
        // Notes directly linked to the book (book_id = id) and not in any folder (folder_id IS NULL)
        // or in a folder that is NOT the book's dedicated folder should be considered for deletion or unlinking.
        // The new logic in books-api.ts (deleteBookAndHandleFolder) moves the book's folder to root
        // and unlinks notes within that folder from the book (sets book_id to NULL).
        // So, notes that are *only* linked via book_id and are *not* in that book's folder might be orphaned.
        // The current schema for notes has ON DELETE SET NULL for book_id, so they will be unlinked automatically if not handled otherwise.
        // Let's ensure notes that were *only* tied to this book and not in its folder are deleted.
        // However, the requirement is that notes in the book's folder are preserved and the folder moved.
        // The `deleteBookAndHandleFolder` in `books-api.ts` already handles notes within the book's specific folder.
        // So, the database's ON DELETE SET NULL for `notes.book_id` should suffice for other notes.
        console.log(`Notes linked to book ${id} will have their book_id set to NULL due to database constraints (ON DELETE SET NULL).`);

        // 3. Delete the book itself
        const bookDeleteQuery = 'DELETE FROM books WHERE id = ?';
        const result: RunResult = await dbRun(bookDeleteQuery, [id]);

        await dbRun('COMMIT');
        resolve({ success: result.changes ? result.changes > 0 : false, id });

      } catch (error) {
        console.error(`Error deleting book with ID ${id} in transaction:`, error);
        try {
          await dbRun('ROLLBACK');
        } catch (rollbackError) {
          console.error('Error rolling back transaction:', rollbackError);
        }
        reject(error);
      }
    });
  });
};

// TRANSACTION HELPER FUNCTIONS

/**
 * Execute a function within a database transaction
 * Provides automatic commit/rollback handling
 */
export const withTransaction = async <T>(
  operation: () => Promise<T>
): Promise<T> => {
  const db: Database = getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(async () => {
      try {
        await dbRun('BEGIN TRANSACTION');
        const result = await operation();
        await dbRun('COMMIT');
        resolve(result);
      } catch (error) {
        try {
          await dbRun('ROLLBACK');
        } catch (rollbackError) {
          console.error('Error rolling back transaction:', rollbackError);
        }
        reject(error);
      }
    });
  });
};

/**
 * Execute multiple database queries within a single transaction
 * Ensures all queries see the same consistent database state
 */
export const withReadTransaction = async <T>(
  operation: () => Promise<T>
): Promise<T> => {
  const db: Database = getDatabase();
  
  return new Promise((resolve, reject) => {
    db.serialize(async () => {
      try {
        // Begin a read transaction for consistent snapshot
        await dbRun('BEGIN IMMEDIATE TRANSACTION');
        const result = await operation();
        await dbRun('COMMIT');
        resolve(result);
      } catch (error) {
        try {
          await dbRun('ROLLBACK');
        } catch (rollbackError) {
          console.error('Error rolling back read transaction:', rollbackError);
        }
        reject(error);
      }
    });
  });
};