<template>
  <Teleport to="body">
    <div class="modal-overlay" @click="handleOverlayClick">
      <div class="backup-history-modal" @click.stop>
        <div class="close-icon" @click="$emit('close')">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>

        <div class="modal-header">
          <h1 class="modal-title">Sync History</h1>
          <p class="modal-subtitle">View all sync operations including manual and auto syncs</p>
        </div>

        <div class="divider"></div>

        <div class="modal-content">
          <!-- Loading state -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <span class="loading-text">Loading sync history...</span>
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">Failed to Load History</h3>
            <p class="error-message">{{ error }}</p>
            <button class="retry-button" @click="loadBackupHistory">
              Retry
            </button>
          </div>

          <!-- Empty state -->
          <div v-else-if="backupHistory.length === 0" class="empty-state">
            <div class="empty-icon">📋</div>
            <h3 class="empty-title">No Sync History</h3>
            <p class="empty-message">You haven't performed any syncs yet. Unified sync system pending implementation.</p>
          </div>

          <!-- History list -->
          <div v-else class="history-list">
            <div 
              v-for="backup in backupHistory" 
              :key="backup.id" 
              class="history-item"
              :class="{ 'failed': backup.status === 'failed' }"
            >
              <div class="history-header">
                <div class="backup-type">
                  <span class="type-badge" :class="backup.type">{{ backup.type }}</span>
                  <span class="status-badge" :class="backup.status">{{ backup.status }}</span>
                </div>
                <div class="backup-time">{{ formatTime(backup.timestamp) }}</div>
              </div>
              
              <div class="history-details">
                <div class="backup-path">{{ backup.location || 'Unknown location' }}</div>
                <div class="backup-stats">
                  <span v-if="backup.itemsProcessed">{{ backup.itemsProcessed }} items</span>
                  <span v-if="backup.errors && backup.errors > 0" class="error-count">{{ backup.errors }} errors</span>
                  <span v-if="backup.duration">{{ formatDuration(backup.duration) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="footer-left">
            <span class="history-count">{{ backupHistory.length }} backup{{ backupHistory.length !== 1 ? 's' : '' }} shown</span>
          </div>
          <div class="footer-right">
            <button 
              class="btn btn-secondary" 
              @click="clearHistory"
              :disabled="backupHistory.length === 0 || isClearing"
            >
              <div v-if="isClearing" class="loading-spinner small"></div>
              <span v-else>Clear History</span>
            </button>
            <button class="btn btn-primary" @click="$emit('close')">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Define a basic backup metadata interface since it's not in the main types
interface BackupMetadata {
  id: string
  type: 'manual' | 'auto'
  status: 'completed' | 'failed' | 'in-progress'
  timestamp: string
  location?: string
  itemsProcessed?: number
  errors?: number
  duration?: number
}

// Component state
const backupHistory = ref<BackupMetadata[]>([])
const isLoading = ref(true)
const isClearing = ref(false)
const error = ref<string | null>(null)

// Emits
const emit = defineEmits<{
  close: []
}>()

// TODO: Load sync history from unified sync API
async function loadBackupHistory() {
  try {
    isLoading.value = true
    error.value = null
    
    // TODO: Replace with unified sync history API
    // const history = await window.electronAPI.unifiedSync.getHistory(100)
    
    // Placeholder - show empty history for now
    backupHistory.value = []
  } catch (err: any) {
    console.error('Failed to load sync history:', err)
    error.value = 'Unified sync system pending implementation'
  } finally {
    isLoading.value = false
  }
}

// TODO: Clear sync history via unified sync API
async function clearHistory() {
  try {
    isClearing.value = true
    
    // TODO: Replace with unified sync clear history API
    // await window.electronAPI.unifiedSync.clearHistory()
    
    // Placeholder - clear local history
    backupHistory.value = []
  } catch (err: any) {
    console.error('Failed to clear sync history:', err)
    error.value = 'Failed to clear history'
  } finally {
    isClearing.value = false
  }
}

// Handle overlay click to close modal
function handleOverlayClick() {
  emit('close')
}

// Format timestamp for display
function formatTime(timestamp: string): string {
  try {
    const date = new Date(timestamp)
    return date.toLocaleString()
  } catch {
    return 'Unknown time'
  }
}

// Format duration in milliseconds to human readable
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

// Load history on mount
onMounted(() => {
  loadBackupHistory()
})
</script>

<style scoped>
/* Apply Montserrat font to all elements */
.backup-history-modal,
.backup-history-modal * {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.backup-history-modal {
  background-color: var(--color-card-bg);
  border-radius: 16px;
  border: 1px solid var(--color-card-border);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.close-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-icon:hover {
  background-color: var(--color-btn-secondary-hover);
}

.close-icon img {
  width: 16px;
  height: 16px;
  filter: var(--icon-filter);
}

.modal-header {
  padding: 32px 32px 16px 32px;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 14px;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-secondary);
  margin: 0 32px;
}

.modal-content {
  flex: 1;
  padding: 24px 32px;
  overflow-y: auto;
  min-height: 200px;
}

/* Scrollbar styling to match notes-list */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

/* Error state */
.error-state {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  color: var(--color-text-primary);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.error-message {
  color: var(--color-text-secondary);
  font-size: 14px;
  margin: 0 0 24px 0;
}

.retry-button {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: var(--color-primary-hover);
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  color: var(--color-text-primary);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.empty-message {
  color: var(--color-text-secondary);
  font-size: 14px;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px 32px 32px;
  border-top: 1px solid var(--color-border-secondary);
}

.footer-left {
  color: var(--color-text-secondary);
  font-size: 12px;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
}

/* History list */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background-color: var(--color-input-bg);
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 16px;
  transition: border-color 0.2s ease;
}

.history-item:hover {
  border-color: var(--color-primary);
}

.history-item.failed {
  border-color: var(--color-error);
  background-color: var(--color-error-bg);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.backup-type {
  display: flex;
  gap: 8px;
  align-items: center;
}

.type-badge,
.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.manual {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.type-badge.auto {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
}

.status-badge.completed {
  background-color: var(--color-success);
  color: var(--color-text-inverse);
}

.status-badge.failed {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
}

.status-badge.in-progress {
  background-color: var(--color-warning);
  color: var(--color-text-inverse);
}

.backup-time {
  color: var(--color-text-secondary);
  font-size: 12px;
}

.history-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.backup-path {
  color: var(--color-text-primary);
  font-size: 13px;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.backup-stats {
  display: flex;
  gap: 12px;
  color: var(--color-text-secondary);
  font-size: 12px;
}

.error-count {
  color: var(--color-error);
  font-weight: 500;
}
</style>
