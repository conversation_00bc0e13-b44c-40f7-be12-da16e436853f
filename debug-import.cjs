// Debug script to test .noti import
const fs = require('fs');
const path = require('path');

console.log('🔍 Debug: Testing .noti file content...');

try {
    const notiPath = path.join(__dirname, 'test-output/test-note.noti');
    const content = fs.readFileSync(notiPath, 'utf8');
    const notiData = JSON.parse(content);
    
    console.log('📄 File contents:');
    console.log('  Version:', notiData.version);
    console.log('  Type:', notiData.type);
    console.log('  Title:', notiData.metadata.title);
    console.log('  Content structure:');
    console.log('    HTML:', typeof notiData.content.html, '- Length:', notiData.content.html?.length || 0);
    console.log('    Markdown:', typeof notiData.content.markdown, '- Length:', notiData.content.markdown?.length || 0);
    console.log('    Plain text:', typeof notiData.content.plain_text, '- Length:', notiData.content.plain_text?.length || 0);
    
    // Simulate the extraction logic
    let noteContent, htmlContent;
    
    if (notiData.content.html) {
        htmlContent = notiData.content.html;
        noteContent = notiData.content.markdown || notiData.content.plain_text || '';
    } else if (notiData.content.markdown) {
        noteContent = notiData.content.markdown;
        // htmlContent = md.render(noteContent); // Would convert here
    } else if (notiData.content.plain_text) {
        noteContent = notiData.content.plain_text;
        // htmlContent = `<p>${escapeHtml(noteContent)}</p>`;
    }
    
    console.log('\n✅ Extracted content:');
    console.log('  noteContent type:', typeof noteContent);
    console.log('  noteContent length:', noteContent?.length || 0);
    console.log('  noteContent value:', JSON.stringify(noteContent));
    console.log('  htmlContent type:', typeof htmlContent);
    console.log('  htmlContent length:', htmlContent?.length || 0);
    
    // Test note object structure
    const testNote = {
        title: notiData.metadata.title.trim(),
        content: noteContent || '',
        html_content: htmlContent,
        type: notiData.metadata.type || 'text',
        color: notiData.metadata.color || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };
    
    console.log('\n🧪 Test note object:');
    console.log('  Title type:', typeof testNote.title);
    console.log('  Content type:', typeof testNote.content);
    console.log('  HTML content type:', typeof testNote.html_content);
    
    // Validate like the app would
    console.log('\n🔍 Validation checks:');
    console.log('  Title valid:', typeof testNote.title === 'string' && testNote.title.trim() !== '');
    console.log('  Content valid:', typeof testNote.content === 'string');
    console.log('  Type valid:', typeof testNote.type === 'string');
    
    console.log('\n✅ Debug completed - check output above');
    
} catch (error) {
    console.error('❌ Debug failed:', error);
}