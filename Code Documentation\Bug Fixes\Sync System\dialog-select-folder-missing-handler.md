# Dialog Select Folder Missing IPC Handler Fix

## Issue Description
The frontend is calling `window.electronAPI.selectFolder()` but there's no corresponding IPC handler for the 'dialog:selectFolder' channel, causing the sync folder selection to fail.

## Files Modified
- `electron/main/ipc-handlers.ts` - Added missing dialog:selectFolder handler
- `electron/preload/api-bridge.ts` - Updated to use existing sync:browseDirectory handler

## Root Cause
The preload script exposes a `selectFolder` method that invokes 'dialog:selectFolder', but no handler was registered for this IPC channel in the main process. However, there's already a `sync:browseDirectory` handler that provides the exact same functionality.

## Solution Implemented

### Option 1: Add Missing Handler (Recommended)
Add a new handler for 'dialog:selectFolder' in `ipc-handlers.ts`:

```typescript
// In initializeIpcHandlers() function, add:
registerDialogHandlers();

// Add new function to register dialog handlers
const registerDialogHandlers = (): void => {
    // Handle folder selection dialog
    ipcMain.handle('dialog:selectFolder', async () => {
        try {
            const result = await dialog.showOpenDialog({
                properties: ['openDirectory', 'createDirectory'],
                title: 'Select Folder',
                buttonLabel: 'Select'
            });

            if (result.canceled || result.filePaths.length === 0) {
                return null;
            }

            return result.filePaths[0];
        } catch (error) {
            console.error('IPC dialog:selectFolder error:', error);
            throw error;
        }
    });
};
```

### Option 2: Update Preload Script (Alternative)
Update the preload script to use the existing sync:browseDirectory handler:

```typescript
// In api-bridge.ts, change:
selectFolder: () => ipcRenderer.invoke('dialog:selectFolder')

// To:
selectFolder: () => ipcRenderer.invoke('sync:browseDirectory')
```

## Implementation Details

The dialog configuration ensures:
1. Only directories can be selected (`openDirectory`)
2. Users can create new directories if needed (`createDirectory`)
3. Returns null if user cancels
4. Returns the selected directory path as a string

## Testing
1. Open Settings > Backup Settings
2. Click "Add Location" or "Change" button
3. Verify folder selection dialog opens
4. Select a folder and verify it's displayed in the UI
5. Cancel selection and verify no errors occur

## Related Issues
- The sync system is in transition to a unified sync implementation
- Multiple TODO comments indicate pending unified sync system integration
- The existing `sync:browseDirectory` handler provides identical functionality