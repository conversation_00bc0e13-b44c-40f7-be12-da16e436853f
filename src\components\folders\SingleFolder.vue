<template>  <div 
    class="single-folder" 
    :class="{ 
      'active': isActive, 
      'subfolder': isSubfolder,
      'has-children': hasChildren,
      'is-open': isOpen
    }" 
    :style="{ 
      '--folder-level': nestingLevel,
      '--folder-indent': nestingLevel > 0 ? `${4 + Math.min(nestingLevel, 10) * 2}px` : '0px',
      '--folder-opacity': nestingLevel > 0 ? Math.min(0.1 + nestingLevel * 0.05, 0.5) : 0
    }"
    :data-level="nestingLevel"
    @click="$emit('select')"
  >
    <!-- Main folder content -->
    <div class="folder-content">
      <!-- Folder path breadcrumb for top-level folders -->
      <div v-if="nestingLevel === 0 && folderPath && folderPath.length > 0" class="folder-breadcrumb">
        <span 
          v-for="(pathItem, index) in displayedBreadcrumbs" 
          :key="index"
          class="breadcrumb-item"
          :class="{ 
            'current': index === folderPath.length - 1,
            'truncated': pathItem.truncated
          }"          @click.stop="pathItem.truncated ? $emit('togglePathDropdown', $event) : $emit('selectPath', pathItem.index)"
          :title="pathItem.truncated ? 'Click to see full path' : pathItem.name"
        >
          {{ pathItem.truncated ? `...${pathItem.truncatedCount} more...` : pathItem.name }}
          <span v-if="!pathItem.truncated && index < folderPath.length - 1" class="breadcrumb-separator">/</span>
        </span>
      </div>
      
      <!-- Toggle arrow with animation -->
      <div 
        class="dropdown-arrow" 
        :class="{ 'placeholder': !hasChildren }" 
        @click.stop="hasChildren && $emit('toggle')"
      >
        <transition name="arrow-rotate">
          <img 
            src="/icons/dropdown-arrow-icon.svg" 
            class="arrow-icon" 
            :class="{ 
              'rotated': isOpen, 
              'disabled': !hasChildren,
              'hidden': !hasChildren
            }" 
            alt="Toggle" 
          />
        </transition>
      </div>
      
      <!-- Folder icon -->
      <div class="folder-icon">
        <FolderIcon
          :color="folder.color || undefined"
          :size="24"
          :isOpen="isOpen && hasChildren"
        />
      </div>
      
      <!-- Folder name with optional inline renaming -->
      <div class="folder-name-wrapper">
        <!-- Regular display mode -->
        <div 
          v-if="!isRenaming" 
          class="folder-name"
          @dblclick="$emit('rename')"
        >
          {{ folder.name }}
        </div>
        
        <!-- Rename input mode -->
        <div v-else class="folder-rename">
          <input 
            ref="renameInputRef"
            type="text" 
            v-model="newFolderName" 
            class="rename-input"
            @keyup.enter="$emit('confirmRename', newFolderName)"
            @keyup.esc="$emit('cancelRename')"
            @blur="$emit('cancelRename')"
            @click.stop
            spellcheck="false"
          />
        </div>
      </div>
      
      <!-- Note count badge -->
      <div class="folder-count" v-if="notesCount !== undefined && !isRenaming">
        {{ notesCount || 0 }}
      </div>
    </div>
    
    <!-- Subfolders container with slide transition -->
    <div 
      class="subfolders" 
      v-if="hasChildren"
      :class="{ 'open': isOpen }"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType, watch } from 'vue';
import { FolderWithMeta } from '../../types/electron-api';
import { getFolderIconStyle, getFolderIconSrc } from '../../utils/colorUtils';
import FolderIcon from '../icons/FolderIcon.vue';

export default defineComponent({
  name: 'SingleFolder',
  components: {
    FolderIcon
  },
  props: {
    folder: {
      type: Object as PropType<FolderWithMeta>,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    isOpen: {
      type: Boolean,
      default: false
    },
    nestingLevel: {
      type: Number,
      default: 0
    },
    hasChildren: {
      type: Boolean,
      default: false
    },
    isRenaming: {
      type: Boolean,
      default: false
    },
    notesCount: {
      type: Number,
      default: undefined
    },
    folderPath: {
      type: Array as PropType<{ id: number, name: string }[]>,
      default: () => []
    }
  },
  
  emits: [
    'select',
    'toggle',
    'rename',
    'confirmRename',
    'cancelRename',
    'togglePathDropdown',
    'selectPath'
  ],

  setup(props) {
    const renameInputRef = ref<HTMLInputElement | null>(null);
    const newFolderName = ref(props.folder.name);
    
    // Update name when folder prop changes
    watch(() => props.folder.name, (newName) => {
      newFolderName.value = newName;
    });
    
    // Focus input when renaming starts
    watch(() => props.isRenaming, (isRenaming) => {
      if (isRenaming) {
        setTimeout(() => {
          if (renameInputRef.value) {
            renameInputRef.value.focus();
            renameInputRef.value.select();
          }
        }, 10);
      }
    });
    
    // Computed to determine if this is a subfolder
    const isSubfolder = computed(() => props.nestingLevel > 0);
    
    // Computed for folder icon path
    const folderIconPath = computed(() => {
      const isOpen = props.isOpen && props.hasChildren;
      const color = props.folder.color || null;
      return getFolderIconSrc(color, isOpen);
    });

    // Computed for folder icon style (color)
    const folderIconStyle = computed(() => {
      const color = props.folder.color || null;
      return getFolderIconStyle(color);
    });
    
    // Computed for breadcrumb display
    const displayedBreadcrumbs = computed(() => {
      const path = props.folderPath;
      
      // If no path or empty path, return empty array
      if (!path || path.length === 0) {
        return [];
      }
      
      // If path is just 1 or 2 items, show all items without truncation
      if (path.length <= 2) {
        return path.map((item, index) => ({
          name: item.name,
          index,
          truncated: false
        }));
      }      // For longer paths, show first, truncated indicator, and last item
      return [        // First item (Root)
        { name: path[0].name, index: 0, truncated: false },
        // Truncated indicator - show number of hidden items
        { 
          name: '', 
          index: -1, 
          truncated: true, 
          // Standard calculation used across all components: path.length - 2
          // This matches the calculation in FoldersView.calculateTruncatedCount()
          truncatedCount: path.length - 2 // Don't count first and last items
        },
        // Last item (current folder)
        { name: path[path.length - 1].name, index: path.length - 1, truncated: false }
      ];
    });
    
    return {
      renameInputRef,
      newFolderName,
      isSubfolder,
      folderIconPath,
      folderIconStyle,
      displayedBreadcrumbs
    };
  }
});
</script>

<style scoped>
.single-folder {
  position: relative;
  margin-bottom: 4px;
  border-radius: 6px;
  transition: background-color 0.2s;
  cursor: pointer;
  overflow: hidden; /* Hide overflowing content from subfolders */
}

.folder-content {
  display: flex;
  align-items: center;
  padding: 8px 8px 8px var(--folder-indent, 8px);
  transition: padding 0.2s ease;
}

.active > .folder-content {
  background-color: var(--color-folder-active-bg);
  border-radius: 6px;
}

.single-folder:not(.active):hover > .folder-content {
  background-color: var(--color-folder-hover-bg);
  border-radius: 6px;
}

/* Folder breadcrumb */
.folder-breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  margin-right: 8px;
  font-size: 12px;
  background-color: var(--color-folder-breadcrumb-bg);
  border-radius: 4px;
  padding: 2px 6px;
  max-width: 200px;
  flex-shrink: 1;
  min-width: 0;
}

.breadcrumb-item {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2px 4px;
  border-radius: 4px;
  color: var(--color-folder-breadcrumb-text);
  cursor: pointer;
  max-width: 80px;
  min-width: 0;
}

.breadcrumb-item:hover:not(.current) {
  background-color: var(--color-folder-breadcrumb-hover);
}

.breadcrumb-item.current {
  font-weight: 500;
  color: var(--color-folder-breadcrumb-current);
}

.breadcrumb-item.truncated {
  color: var(--color-folder-breadcrumb-truncated);
  background-color: var(--color-folder-breadcrumb-hover);
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.75rem;
  cursor: pointer;
  margin: 0 2px;
  max-width: none;
}

.breadcrumb-item.truncated:hover {
  background-color: var(--color-folder-breadcrumb-hover-alt);
}

.breadcrumb-separator {
  margin: 0 2px;
  color: var(--color-folder-breadcrumb-separator);
}

/* Dropdown arrow */
.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 10px;
  transition: transform 0.2s ease;
  cursor: pointer;
}

.dropdown-arrow.placeholder {
  visibility: hidden;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  opacity: 0.6;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

.arrow-icon.disabled {
  opacity: 0.2;
  cursor: default;
}

.arrow-icon.hidden {
  visibility: hidden;
}

/* Folder icon */
.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  width: 18px;
  height: 18px;
}

.folder-icon img {
  width: 18px;
  height: 18px;
  transition: opacity 0.2s ease;
}

/* Folder name */
.folder-name-wrapper {
  flex-grow: 1;
  min-width: 0; /* Needed for text-overflow to work */
  display: flex;
  font-size: 14px;
  line-height: 1.2;
}

.folder-name {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 4px 0;
}

.folder-rename {
  flex-grow: 1;
}

.rename-input {
  width: 100%;
  padding: 3px 5px;
  border: 1px solid var(--color-folder-rename-border);
  border-radius: 3px;
  font-size: 14px;
  outline: none;
  background-color: var(--color-folder-rename-bg);
  color: var(--color-input-text);
}

/* Note count badge */
.folder-count {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  background-color: var(--color-folder-count-bg);
  color: var(--color-folder-count-text);
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 11px;
  flex-shrink: 0;
}

/* Subfolders container */
.subfolders {
  height: 0;
  overflow: hidden;
  transition: height 0.2s ease-out;
  opacity: 0;
}

.subfolders.open {
  height: auto;
  opacity: 1;
}

/* Transition for arrow rotation */
.arrow-rotate-enter-active,
.arrow-rotate-leave-active {
  transition: transform 0.2s ease;
}

.arrow-rotate-enter-from,
.arrow-rotate-leave-to {
  transform: rotate(-90deg);
}
</style>
