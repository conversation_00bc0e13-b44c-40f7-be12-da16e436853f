import { DefineComponent } from 'vue';
import { FolderWithMeta, Note } from './electron-api';

export interface FolderNavigatorProps {
  folderHierarchy: FolderWithMeta[] | null;
  selectedItemId?: number | string | null;
  rootFolderName?: string;
}

export interface FolderItem {
  id: number | null;
  name: string;
  type: 'folder';
  children?: FolderItem[];
  notesCount?: number;
  parent_id?: number | null;
  book_id?: number | null;
  color?: string | null;
}

export interface NoteItem {
  id: number;
  title: string;
  type: 'note' | 'file';
  folder_id?: number | null;
}

export type NavigatorItem = FolderItem | NoteItem;

export interface BreadcrumbItem {
  id: number;
  name: string;
  parent_id?: number | null;
}

// Future component interfaces can be added here
