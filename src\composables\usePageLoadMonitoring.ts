/**
 * Vue Composable for Page Load Monitoring
 * 
 * Provides easy integration with Vue components for tracking page load performance
 */

import { onMounted, getCurrentInstance } from 'vue'
import { pageLoadMonitor, type PerformanceReport } from '../utils/pageLoadMonitor'

export function usePageLoadMonitoring() {
  /**
   * Record that the current page has mounted
   * Call this in your component's onMounted hook
   */
  const recordPageMounted = (routeName?: string) => {
    const instance = getCurrentInstance()
    const componentName = routeName || instance?.type.name || 'Unknown'
    pageLoadMonitor.recordPageMounted(componentName)
  }

  /**
   * Auto-record page mount (convenience method)
   * Automatically calls recordPageMounted when the component mounts
   */
  const autoRecordPageMounted = (routeName?: string) => {
    onMounted(() => {
      recordPageMounted(routeName)
    })
  }

  /**
   * Generate performance report
   */
  const generateReport = (): PerformanceReport => {
    return pageLoadMonitor.generateReport()
  }

  /**
   * Export data as CSV
   */
  const exportToCSV = (): string => {
    return pageLoadMonitor.exportToCSV()
  }

  /**
   * Download CSV file
   */
  const downloadCSV = (filename = 'page-load-metrics.csv') => {
    const csv = exportToCSV()
    if (!csv) {
      console.warn('No metrics data to export')
      return
    }

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    console.log(`📁 Downloaded metrics as ${filename}`)
  }

  /**
   * Print report to console
   */
  const printReport = () => {
    const report = generateReport()
    
    console.group('📊 Page Load Performance Report')
    console.log(`Total Navigations: ${report.totalNavigations}`)
    console.log(`Average Load Time: ${report.averageLoadTime.toFixed(2)}ms`)
    
    if (report.fastestLoad.routeName) {
      console.log(`Fastest Load: ${report.fastestLoad.routeName} (${report.fastestLoad.totalLoadTime.toFixed(2)}ms)`)
    }
    
    if (report.slowestLoad.routeName) {
      console.log(`Slowest Load: ${report.slowestLoad.routeName} (${report.slowestLoad.totalLoadTime.toFixed(2)}ms)`)
    }

    console.group('📈 Route Averages')
    Object.entries(report.routeAverages).forEach(([route, stats]) => {
      console.log(`${route}:`, {
        average: `${stats.averageTime.toFixed(2)}ms`,
        visits: stats.visitCount,
        firstVisit: stats.firstVisitAverage > 0 ? `${stats.firstVisitAverage.toFixed(2)}ms` : 'N/A',
        subsequent: stats.subsequentVisitAverage > 0 ? `${stats.subsequentVisitAverage.toFixed(2)}ms` : 'N/A'
      })
    })
    console.groupEnd()
    
    console.groupEnd()
  }

  /**
   * Clear all collected data
   */
  const clearData = () => {
    pageLoadMonitor.clearData()
  }

  /**
   * Enable/disable monitoring
   */
  const setEnabled = (enabled: boolean) => {
    pageLoadMonitor.setEnabled(enabled)
  }

  /**
   * Get current metrics count
   */
  const getMetricsCount = (): number => {
    return pageLoadMonitor.getMetricsCount()
  }

  return {
    // Core functions
    recordPageMounted,
    autoRecordPageMounted,
    
    // Reporting functions
    generateReport,
    exportToCSV,
    downloadCSV,
    printReport,
    
    // Management functions
    clearData,
    setEnabled,
    getMetricsCount
  }
}

/**
 * Global performance monitoring utilities
 * Available in browser console as window.pagePerf
 */
export function setupGlobalPerformanceUtils() {
  if (typeof window !== 'undefined') {
    const { generateReport, exportToCSV, downloadCSV, printReport, clearData, setEnabled, getMetricsCount } = usePageLoadMonitoring()
    
    ;(window as any).pagePerf = {
      report: printReport,
      download: downloadCSV,
      clear: clearData,
      enable: () => setEnabled(true),
      disable: () => setEnabled(false),
      count: getMetricsCount,
      raw: generateReport,
      csv: exportToCSV,
      // Test preloading effectiveness
      testPreloading: () => {
        const report = generateReport()
        console.group('🧪 Preloading Effectiveness Test')

        const routeStats = report.routeAverages
        Object.entries(routeStats).forEach(([route, stats]) => {
          console.log(`${route}:`)
          console.log(`  First visit: ${stats.firstVisitAverage.toFixed(2)}ms`)
          console.log(`  Subsequent: ${stats.subsequentVisitAverage.toFixed(2)}ms`)
          const improvement = stats.firstVisitAverage > 0 && stats.subsequentVisitAverage > 0
            ? ((stats.firstVisitAverage - stats.subsequentVisitAverage) / stats.firstVisitAverage * 100)
            : 0
          console.log(`  Improvement: ${improvement.toFixed(1)}%`)
        })

        console.groupEnd()
      }
    }
    
    console.log('🔧 Page performance utilities available at window.pagePerf')
    console.log('Commands: pagePerf.report(), pagePerf.download(), pagePerf.clear(), pagePerf.enable(), pagePerf.disable()')
  }
}
