# Sync System .noti Format Implementation Plan

## Overview

This document provides a detailed step-by-step implementation plan for migrating the Noti sync system from Markdown (.md) files to the enhanced .noti format. The plan is organized into phases with specific tasks, file modifications, and testing requirements.

## Implementation Phases

### Phase 1: Core File Operations Infrastructure

#### 1.1 Update FileOperations Class (`file-operations.ts`)

**Files to Modify:**
- `electron/main/api/sync-logic/file-operations.ts`

**Changes Required:**

```typescript
// Current readNote method reads .md files
async readNote(notePath: string): Promise<{ content: string; metadata: any }>

// New readNote method should read .noti files
async readNote(notePath: string): Promise<NotiFileData>

// Current writeNote method writes .md files
async writeNote(notePath: string, content: string, metadata: any): Promise<void>

// New writeNote method should write .noti files
async writeNote(notePath: string, notiData: NotiFileData): Promise<void>
```

**Simplified Methods (No Migration Needed):**
```typescript
// Read and parse .noti JSON file
async readNote(notePath: string): Promise<NotiFileData> {
  const content = await this.readFileAtomic(notePath);
  return JSON.parse(content);
}

// Write .noti JSON file
async writeNote(notePath: string, notiData: NotiFileData): Promise<void> {
  const validatedPath = this.syncDirectory
    ? this.validatePath(notePath, this.syncDirectory)
    : path.normalize(notePath);

  await this.ensurePath(path.dirname(validatedPath));
  await this.writeFileAtomic(validatedPath, JSON.stringify(notiData, null, 2));
}
```

#### 1.2 Add Simplified .noti Format Types (`types.ts`)

**Files to Modify:**
- `electron/main/api/sync-logic/types.ts`

**New Types to Add:**
```typescript
export interface NotiFileData {
  version: string;
  type: string;
  schema: string;
  metadata: NotiMetadata;
  content: NotiContent;
  media: EmbeddedMedia[];
}

export interface NotiMetadata {
  id: number;
  title: string;
  created_at: string;
  updated_at: string;
  last_viewed_at?: string;
  type: string;
  color?: string;
  folder_id?: number;
  book_id?: number;
}

export interface NotiContent {
  html: string;           // Full rich HTML content
  markdown: string;       // First ~50 words for preview
  plain_text: string;     // Simple text for search
}

export interface EmbeddedMedia {
  id: string;             // e.g., "embedded_media_1"
  file_name: string;      // Original filename
  file_type: string;      // MIME type
  file_size: number;      // File size in bytes
  original_path: string;  // Original noti-media:// path
  embedded: boolean;      // Always true for sync
  data: string;           // base64 encoded file data
}
```

#### 1.3 Create Media Utilities

**New File to Create:**
- `electron/main/api/sync-logic/media-utils.ts`

**Functions to Implement:**
```typescript
// Convert note's media files to base64 embedded format for sync
export async function embedMediaFiles(noteId: number): Promise<EmbeddedMedia[]> {
  const mediaFiles = await mediaApi.getMediaFilesByNoteId(noteId);
  const embeddedMedia: EmbeddedMedia[] = [];

  for (const mediaFile of mediaFiles) {
    const fileBuffer = await fs.readFile(mediaFile.file_path);
    const base64Data = fileBuffer.toString('base64');

    embeddedMedia.push({
      id: `embedded_media_${mediaFile.id}`,
      file_name: mediaFile.file_name,
      file_type: mediaFile.file_type,
      file_size: mediaFile.file_size,
      original_path: filePathToMediaUrl(mediaFile.file_path),
      embedded: true,
      data: base64Data
    });
  }

  return embeddedMedia;
}

// Extract embedded media and restore to media_files table
export async function restoreEmbeddedMedia(
  htmlContent: string,
  media: EmbeddedMedia[],
  noteId: number
): Promise<string> {
  let restoredHtml = htmlContent;

  for (const mediaItem of media) {
    const fileBuffer = Buffer.from(mediaItem.data, 'base64');

    // Save to media storage and database
    const savedMedia = await saveMediaFile(
      noteId, fileBuffer, mediaItem.file_name, mediaItem.file_type
    );

    // Replace embedded reference with actual media URL
    const newMediaUrl = filePathToMediaUrl(savedMedia.file_path);
    restoredHtml = restoredHtml.replace(
      new RegExp(`noti-media://${mediaItem.id}`, 'g'),
      newMediaUrl
    );
  }

  return restoredHtml;
}

// Create markdown preview (first 50 words)
export function createMarkdownPreview(content: string): string {
  const words = content.split(/\s+/).slice(0, 50);
  return words.join(' ') + (words.length >= 50 ? '...' : '');
}

// Extract plain text from HTML for search
export function extractPlainText(htmlContent: string): string {
  return htmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
}

// Replace media URLs in HTML with embedded references
export function replaceMediaUrlsWithEmbedded(
  htmlContent: string,
  mediaFiles: any[],
  embeddedMedia: EmbeddedMedia[]
): string {
  let processedHtml = htmlContent;

  for (let i = 0; i < mediaFiles.length; i++) {
    const mediaFile = mediaFiles[i];
    const embeddedRef = embeddedMedia[i];

    const originalUrl = filePathToMediaUrl(mediaFile.file_path);
    const embeddedUrl = `noti-media://${embeddedRef.id}`;

    processedHtml = processedHtml.replace(
      new RegExp(escapeRegExp(originalUrl), 'g'),
      embeddedUrl
    );
  }

  return processedHtml;
}
```

### Phase 2: Sync Engine Integration

#### 2.1 Update Unified Sync Engine (`unified-sync-engine.ts`)

**Files to Modify:**
- `electron/main/api/sync-logic/unified-sync-engine.ts`

**Methods to Update:**

```typescript
// Current exportNote method
private async exportNote(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void>

// Updated to create .noti files with embedded media
private async exportNote(item: LocalItem, directory: string, manifest: SyncManifest): Promise<void> {
  const note = await getNoteById(item.id);
  
  // Build .noti file path (change from .md to .noti)
  const notiPath = this.buildNotiPath(note, directory, manifest);
  
  // Get embedded media for the note
  const embeddedMedia = await embedMediaFiles(note.id);
  
  // Create .noti file content
  const notiContent = this.createNotiContent(note, embeddedMedia);
  
  // Write .noti file
  await fileOperations.writeNote(
    notiPath, 
    note.content || '', 
    note.html_content || '',
    this.extractNoteMetadata(note),
    embeddedMedia
  );
  
  // Update manifest
  const relativePath = path.relative(directory, notiPath);
  manifestManager.updateManifestWithExport(manifest, {
    ...note,
    type: 'note',
    name: note.title
  }, relativePath);
}

// Current importNote method  
private async importNote(item: ManifestItem, directory: string): Promise<void>

// Updated to parse .noti files and restore media
private async importNote(item: ManifestItem, directory: string): Promise<void> {
  const notiPath = path.join(directory, item.path);
  
  // Read .noti file
  const notiData = await fileOperations.readNote(notiPath);
  
  // Extract metadata from .noti file
  const metadata = notiData.metadata || {};
  
  // Determine relationships
  let folderId: number | null = null;
  let bookId: number | null = null;
  
  if (item.relationships) {
    if (item.relationships.bookId) {
      const localBookId = this.importIdMapping.get(item.relationships.bookId);
      if (localBookId) bookId = localBookId;
    }
    if (item.relationships.folderId) {
      const localFolderId = this.importIdMapping.get(item.relationships.folderId);
      if (localFolderId) folderId = localFolderId;
    }
  }

  await withTransaction(async () => {
    const noteTitle = item.name || path.basename(notiPath, '.noti');
    
    // Check if note already exists
    let existingNote: Note | null = null;
    const mappedId = this.importIdMapping.get(item.id);
    if (mappedId) {
      existingNote = await this.noteExistsById(mappedId);
    }
    
    if (!existingNote) {
      existingNote = await this.noteExists(noteTitle, folderId, bookId);
    }
    
    let note: Note;
    if (existingNote) {
      // Update existing note
      await updateNote(existingNote.id!, {
        title: noteTitle,
        content: notiData.content,
        html_content: notiData.htmlContent,
        folder_id: folderId,
        book_id: bookId,
        type: metadata.type || existingNote.type,
        color: metadata.color || existingNote.color,
        last_viewed_at: metadata.last_viewed_at || existingNote.last_viewed_at
      });
      note = existingNote;
    } else {
      // Create new note
      note = await createNote({
        title: noteTitle,
        content: notiData.content,
        html_content: notiData.htmlContent,
        folder_id: folderId,
        book_id: bookId,
        type: metadata.type || 'text',
        color: metadata.color || null,
        last_viewed_at: metadata.last_viewed_at
      });
    }

    // Extract and restore embedded media
    if (notiData.media && notiData.media.length > 0) {
      await extractEmbeddedMedia(notiData.media, note.id!);
    }

    // Track mapping
    this.importIdMapping.set(item.id, note.id!);
  });
}
```

**New Helper Methods:**
```typescript
private buildNotiPath(note: Note, directory: string, manifest: SyncManifest): string
private createNotiContent(note: Note, media: EmbeddedMedia[]): string
private extractNoteMetadata(note: Note): NotiMetadata
```

### Phase 3: Manifest and Path Updates

#### 3.1 Update Manifest Manager (`manifest-manager.ts`)

**Files to Modify:**
- `electron/main/api/sync-logic/manifest-manager.ts`

**Changes Required:**

```typescript
// Update generateManifestFromDatabase method
// Change file extension from .md to .noti in path generation
// Line ~402: notePath = `${folderPath}${noteTitle}.md`;
// Change to: notePath = `${folderPath}${noteTitle}.noti`;

// Line ~413: notePath = `${noteTitle}.md`;  
// Change to: notePath = `${noteTitle}.noti`;

// Update hash calculation to include HTML content and media
private calculateItemHash(data: any): string {
  // Include html_content in hash calculation
  // Include media references in hash calculation
  return fileOperations.calculateHash(JSON.stringify(data));
}
```

#### 3.2 Update Change Detector (`change-detector.ts`)

**Files to Modify:**
- `electron/main/api/sync-logic/change-detector.ts`

**Changes Required:**

```typescript
// Update getDbNotes method to include html_content in hash calculation
private async getDbNotes(): Promise<SyncItem[]> {
  const query = `
    SELECT id, title, content, html_content, book_id, folder_id, color, created_at, updated_at
    FROM notes
    ORDER BY created_at DESC
  `;
  
  const notes = await dbAll<any>(query);
  
  return notes.map(note => ({
    id: `note_${note.id}`,
    type: 'note' as const,
    hash: this.generateHash({
      ...note,
      // Include html_content in hash for better change detection
      html_content: note.html_content
    }),
    lastModified: new Date(note.updated_at || note.created_at).toISOString(),
    metadata: {
      title: note.title,
      bookId: note.book_id,
      folderId: note.folder_id,
      color: note.color
    }
  }));
}
```

### Phase 4: Migration Utilities

#### 4.1 Create Migration Utility

**New File to Create:**
- `electron/main/api/sync-logic/migration-utils.ts`

**Functions to Implement:**
```typescript
// Detect sync directory format
export async function detectSyncFormat(directory: string): Promise<'md' | 'noti' | 'mixed'>

// Migrate .md files to .noti format
export async function migrateMdToNoti(directory: string): Promise<MigrationResult>

// Convert single .md file to .noti format
export async function convertMdFileToNoti(
  mdPath: string, 
  metadata: any
): Promise<string>

// Update manifest after migration
export async function updateManifestAfterMigration(
  directory: string,
  migrationMap: Map<string, string>
): Promise<void>

// Validate migration results
export async function validateMigration(directory: string): Promise<ValidationResult>
```

#### 4.2 Integration with Sync Engine

**Files to Modify:**
- `electron/main/api/sync-logic/unified-sync-engine.ts`

**Add Migration Check:**
```typescript
async sync(directory: string): Promise<SyncResult> {
  // ... existing code ...
  
  // Check if migration is needed
  const syncFormat = await detectSyncFormat(directory);
  if (syncFormat === 'md') {
    console.log('[UnifiedSyncEngine] Detected .md format, migrating to .noti');
    await migrateMdToNoti(directory);
  } else if (syncFormat === 'mixed') {
    console.log('[UnifiedSyncEngine] Detected mixed format, completing migration');
    await migrateMdToNoti(directory);
  }
  
  // ... continue with normal sync ...
}
```

### Phase 5: Testing and Validation

#### 5.1 Unit Tests

**New Test Files to Create:**
- `tests/sync-logic/file-operations-noti.test.ts`
- `tests/sync-logic/media-utils.test.ts`
- `tests/sync-logic/migration-utils.test.ts`

**Test Cases:**
- .noti file reading/writing
- Media embedding/extraction
- Migration from .md to .noti
- Hash calculation with new format
- Error handling and edge cases

#### 5.2 Integration Tests

**Test Scenarios:**
- Full sync cycle with .noti format
- Migration of existing .md sync directory
- Media handling in sync operations
- Performance with large embedded media
- Rollback scenarios

#### 5.3 Performance Testing

**Metrics to Measure:**
- File size comparison (.md vs .noti)
- Sync time with embedded media
- Memory usage during sync
- Hash calculation performance

## Implementation Timeline

- **Week 1**: Phase 1 - Core file operations
- **Week 2**: Phase 2 - Sync engine integration  
- **Week 3**: Phase 3 - Manifest and path updates
- **Week 4**: Phase 4 - Migration utilities
- **Week 5**: Phase 5 - Testing and validation
- **Week 6**: Performance optimization and bug fixes

## Risk Mitigation

1. **Backup Strategy**: Always backup sync directories before migration
2. **Rollback Plan**: Implement reverse migration from .noti to .md
3. **Gradual Rollout**: Test with small sync directories first
4. **Validation**: Comprehensive validation after each migration
5. **User Communication**: Clear documentation and warnings about breaking changes

## Success Criteria

- [ ] All .md files successfully converted to .noti format
- [ ] Media files properly embedded and extractable
- [ ] Sync operations work correctly with .noti format
- [ ] Performance acceptable with embedded media
- [ ] Migration utility handles edge cases
- [ ] Comprehensive test coverage
- [ ] Documentation updated

This implementation plan provides a structured approach to migrating the sync system while minimizing risks and ensuring data integrity.
