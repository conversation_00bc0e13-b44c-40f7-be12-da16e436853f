# Changelog

## [0.8.0] - 2025-06-08

### Added
- 📊 **Advanced Timer Statistics & Analytics**
  - Comprehensive timer statistics with daily, weekly, and all-time metrics
  - Real-time calculation of focus time, session counts, and completion rates
  - Category-based time distribution analysis and tracking
  - Pomodoro completion statistics with visual progress indicators
- 📈 **Chart.js Integration & Data Visualization**
  - Complete Chart.js library integration for rich data visualization
  - Daily Focus Time charts showing 7-day focus time history
  - Weekly Progress charts with session completion tracking
  - Category Distribution pie charts for time allocation analysis
  - Pomodoro Completion bar charts with cycle tracking
  - Interactive chart refresh functionality with loading states
- ⚙️ **Comprehensive Settings Management System**
  - Persistent application settings with localStorage integration
  - Theme switching system with Light/Dark mode support
  - Timer display preferences and title bar customization
  - User preferences for auto-save intervals and backup settings
  - Discord Rich Presence configuration options
- 🎨 **Enhanced Theme System**
  - Complete Light/Dark theme implementation with CSS custom properties
  - Theme-aware chart colors and visual consistency
  - Instant theme switching with real-time preview
  - Theme persistence across application restarts
- 🔧 **Settings API & Database Integration**
  - Settings persistence using database storage
  - IPC communication for settings management
  - Settings validation and error handling
  - Migration support for settings schema updates
- 🎮 **Discord Rich Presence Integration**
  - Complete Discord RPC implementation with activity tracking
  - Real-time study session display on Discord status
  - Privacy controls for customizing shared information
  - Activity types: study sessions, note-taking, book reading, idle states
  - Custom messages and professional Discord presence
  - Automatic reconnection with error handling

### Enhanced
- 🔄 **Timer System Integration**
  - Enhanced timer statistics calculation with database queries
  - Real-time chart updates when sessions are completed
  - Improved session tracking with category and focus area support
  - Better integration between timer components and analytics
- 📱 **UI/UX Improvements**
  - Responsive chart layouts for different screen sizes
  - Improved settings interface with organized sections
  - Enhanced visual feedback for theme switching
  - Better loading states and error handling throughout the application
- 🗄️ **Database Schema Enhancements**
  - Extended timer sessions table with additional metadata
  - Settings table implementation for persistent configuration
  - Improved query performance for statistics calculations

### Fixed
- 🐞 **Chart Rendering Issues**
  - Fixed chart responsiveness and theme integration
  - Resolved data synchronization between charts and timer
  - Improved chart refresh performance and reliability
- 🔧 **Settings Persistence**
  - Fixed theme switching persistence across app restarts
  - Resolved settings validation and error handling
  - Improved settings migration and default value handling
- 📊 **Statistics Calculation**
  - Fixed timer statistics accuracy and real-time updates
  - Resolved category-based filtering and data aggregation
  - Improved performance for large datasets

## [0.7.0] - 2025-06-05

### Added
- ⏱️ **Complete Timer System**
  - Full Pomodoro timer implementation with customizable durations
  - Session tracking with focus areas and categories
  - Timer settings persistence with database storage
  - Comprehensive timer statistics and analytics
- 📊 **Advanced Statistics & Visualization**
  - Chart.js integration for data visualization
  - Daily focus time charts with 7-day history
  - Weekly progress tracking with session completion rates
  - Category-based time distribution analysis
  - Pomodoro completion statistics with visual indicators
- 📤 **Comprehensive Export System**
  - Multi-format export (PDF, Markdown, .noti format)
  - Batch export capabilities for multiple notes/folders
  - Export progress overlays with real-time feedback
  - ZIP archive creation for bulk exports
  - File system integration with native save dialogs
- ⚙️ **Settings Management**
  - Persistent application settings with database storage
  - Timer configuration options (Pomodoro/break durations)
  - UI preferences and display settings
  - Settings store with Pinia state management
- 📋 **Enhanced Dashboard**
  - Statistics overview with real-time data
  - Recent items tracking and display
  - Quick access to notes, books, and timer functions
  - Visual indicators for productivity metrics

### Changed
- 🔄 Upgraded timer system from mock to full database integration
- 🛠️ Enhanced IPC communication with comprehensive timer APIs
- 📱 Improved UI responsiveness and visual feedback
- 🗄️ Expanded database schema for timer sessions and settings

### Fixed
- 🐞 Timer state persistence across application restarts
- 🔧 Session management and pomodoro cycle tracking
- 📊 Chart rendering and data synchronization issues
- 💾 Export system reliability and error handling

## [0.6.0] - 2025-05-30

### Added
- 📖 Book Details & Management
  - Added BookDetailsModal.vue for viewing, editing, rating, and deleting books
  - BookCard.vue now displays book rating and note count
  - Implemented ability to rate books and track reading progress
  - Linked notes to books via book_id; notes can be created/viewed directly from book details
  - Book-specific notes are displayed in the Book Details modal
- 📝 Notes Integration
  - Added support for creating notes linked to specific books
  - Notes for a book are searchable and accessible from the book details view
- 🎨 UI/UX Improvements
  - Enhanced BooksView and BookCard UI to show ratings, note counts, and improved interactions
  - Improved modal transitions and error handling for book operations

### Changed
- 🔄 Refactored notes and books database schema to support book_id relationship
- 🛠️ Improved IPC and API logic for book editing, deletion, and note linkage

### Fixed
- 🐞 Fixed issues with book cover updates, rating persistence, and note linkage

## [0.5.0] - 2025-05-24

### Added
- 📚 Book Tracking
  - Added books table to the database schema for storing book information
  - Implemented books-api.ts with all book-related CRUD and OpenLibrary integration
  - Book search, addition, and OpenLibrary API integration are handled in books-api.ts (no separate openlibrary-api file)
  - Book cover download and local storage implemented via media-api.ts and media_files table
  - Created BooksView.vue page and Book search UI based on Figma design
  - Book cover management and storage handled through media-api.ts and media_files table
- 🎨 UI/UX Improvements
  - Designed and implemented BooksView and Book search UI in Figma
  - Improved book addition flow and cover image handling
  - Added feedback and error handling for book search and addition

### Changed
- 🛠️ Refactored database and API layers to support book-related operations
- 📦 Improved file storage for book covers using media-api.ts and media_files table
- 🔄 Enhanced IPC communication for book operations

### Fixed
- 🐞 Various bug fixes and UI polish for book addition and cover download flows

## [0.4.0] - 2025-05-18

### Added
- 📁 Folder Organization  - Implemented Folder Create, Read, Update, Delete logic in folders-api.ts
  - Created logic to link Notes to Folders with folder_id relationship
  - Developed FoldersView.vue and FolderNavigator.vue components based on Figma designs
  - Added FolderContent.vue to display notes within selected folders
  - Implemented SingleFolder.vue component for individual folder display and management
  - Created FolderToolbar.vue for folder operation controls (create, rename, delete)
- 🔄 Note-Folder Integration
  - Implemented UI to assign notes to folders through an intuitive interface
  - Added ability to filter Notes list based on selected folder
  - Created drag-and-drop functionality for moving notes between folders
- 🎨 UI/UX Improvements
  - Enhanced folder tree visualization with proper indentation and hierarchy
  - Added folder icons with open/closed states
  - Implemented smooth transitions for folder operations

### Changed
- 🔄 Refactored note listing to work with folder filtering
- 📱 Improved sidebar navigation with better folder visibility
- 🛠️ Enhanced IPC communication for folder operations
- 📊 Updated database schema to better support folder relationships

## [0.3.0] - 2025-05-10

### Added
- 📝 Note Creation and Management  - Implemented Note Create, Read, Update, Delete logic in notes-api.ts
  - Created NotesView.vue page based on Figma designs
  - Integrated basic Note list display with data fetching via IPC
  - Implemented Tiptap rich text editor (NoteEditor.vue component)
  - Connected Note Editor to create/update IPC calls for seamless editing
- 🧪 Testing and Quality Assurance
  - Added unit tests for Note API CRUD functions
  - Implemented backend integration testing for notes functionality
- 🎨 UI/UX Improvements
  - Finalized NotesView and NoteEditor components based on Figma designs
  - Added smooth transitions between notes list and editor
  - Improved responsive layout for different screen sizes

### Changed
- 🔄 Enhanced IPC communication for note operations
- 📱 Improved note list refreshing after operations
- 🛠️ Refactored database API for better error handling

## [0.2.0] - 2025-05-05

### Added
- 🗄️ Database infrastructure with SQLite setup  - Implemented database.ts utility with SQLite connection & initialization
  - Defined and created initial database schema (Notes, Folders tables)
  - Implemented database-api.ts with basic CRUD functions for Notes & Folders
- 📝 API layer for core functionality  - Set up notes-api.ts & folders-api.ts skeletons with full CRUD operations
  - Created robust IPC handlers in main.ts for all Note/Folder operations
- 🧪 Database testing infrastructure
  - Added comprehensive tests for database connection and schema creation
- 🪟 Window styling improvements
  - Implemented custom title bar with window controls
  - Added route name & icon display in title bar
- 🧭 Enhanced sidebar navigation
  - Improved visual consistency
  - Added better hover and selection states

### Changed
- 🏗️ Moved to frameless window architecture for cleaner UI
- 🔄 Refactored IPC communication pattern for better type safety
- 📱 Improved responsive layout handling

## [0.1.0] - 2025-04-30

### Added
- 🚀 Project initialization with Electron, Vue 3, and TypeScript
- 🛠️ Configured build system using electron-builder and Vite
- 🪟 Setup basic window management in Electron main process
- 🧩 Implemented core application components:
  - Main layout structure with Vue components
  - SidebarNavigation component
  - Icons for all main sections (dashboard, notes, folders, books, timer, settings)
- 📋 Created placeholder views for all main application sections:
  - DashboardView
  - NotesView
  - FoldersView
  - BooksView
  - TimerView
  - SettingsView
- 🔍 Configured VS Code integration for debugging and development
- 🔄 Setup GitHub workflows for CI/CD
- 🧭 Implemented basic router configuration for navigation between views

### Infrastructure
- 📝 TypeScript configuration for both main and renderer processes
- ⚡ Vite build pipeline setup
- 📦 electron-builder configuration for packaging
- 👷 GitHub Actions for automated builds

### In Progress
- 📡 Main process to renderer process communication (IPC)
- 📝 Note creation and editing functionality
- 🗃️ Database integration