# Noti Scripts

This directory contains utility scripts for the Noti application.

## Test Data Population Script

The `populateTestData.ts` script allows you to populate either a test database or your actual Noti application database with sample folders and notes.

### Usage

```bash
# Basic usage (creates a test database with 3 top folders, 9 sub-folders, and 27 notes)
npm run populate-test-data -- 3

# Add test data to your actual Noti application database
npm run populate-real-db -- 3

# Reset test database and add test data
npm run populate-test-data -- 3 --reset

# Reset the actual Noti database and add test data (EXTREME CAUTION!)
npm run populate-real-db -- 3 --reset
```

### Command Line Options

- `<N>`: Number of items to create (creates N top folders, N sub-folders per top folder, and N notes per sub-folder)
- `--reset`: Reset the database before adding test data (deletes all existing data!)

### Scripts

- `npm run populate-test-data`: Creates test data in a separate test database
- `npm run populate-real-db`: Creates test data in your actual Noti application database

### Output

- When using `populate-real-db`, the script will write to your actual Noti application database (at the standard Electron app data location)
- With `populate-test-data`, a test database will be created at `./test-data/noti-test-data.sqlite`
- The script creates a log file at `./scripts/test_data_output.log`

### Safety Features

- The script includes warnings and delays when using `--reset` with the real database
- A 5-second delay is added before resetting the actual database to allow aborting with Ctrl+C

### Viewing Test Data

- Test database: Open `./test-data/noti-test-data.sqlite` with DB Browser for SQLite
- Actual database: Launch the Noti application after running the script

### Examples

1. **For development and testing:**
   ```bash
   npm run populate-test-data -- 5
   ```
   This creates a test database with 5 top folders, 25 sub-folders, and 125 notes.

2. **For adding sample data to your Noti application:**
   ```bash
   npm run populate-real-db -- 2
   ```
   This adds 2 top folders, 4 sub-folders, and 8 notes to your actual Noti database.

## Database Contents Dump Script

The database dump scripts allow you to output the entire contents of specific database tables for debugging, analysis, or backup purposes.

### Usage

```bash
# Basic usage - output all table contents to console
npm run dump-db

# Save output to a timestamped file
npm run dump-db -- --save

# Save output to a specific file
npm run dump-db -- --save --output=my-dump.txt

# Short form
npm run dump-db -- -s -o=my-dump.txt

# Use the simple CommonJS version (no advanced options)
npm run dump-db-simple

# Show help
npm run dump-db -- --help
```

### What Gets Dumped

The script outputs the complete contents of these tables:

**Sync Tables:**
- `sync_state` - Tracks sync status for individual items
- `sync_sessions` - Records of sync operations and their status
- `sync_directory_state` - Unified sync engine state tracking

**Main Tables:**
- `folders` - Complete folder hierarchy and structure
- `books` - All book information including metadata
- `notes` - All notes content and associated data

### Output Format

- Each table is clearly labeled with record counts
- Column names are displayed for easy reference
- Long text fields are truncated for readability (first 100 characters)
- NULL values are clearly marked
- Records are ordered by ID when possible

### Files

- [`dumpDatabaseContents.ts`](dumpDatabaseContents.ts) - Full-featured TypeScript version with file output options
- [`dumpDatabaseContents.cjs`](dumpDatabaseContents.cjs) - Simple CommonJS version for compatibility

### Safety

- Both scripts open the database in **read-only mode**
- No data modification is performed
- Scripts will fail gracefully if database doesn't exist
- Compatible with all platforms (Windows, macOS, Linux)