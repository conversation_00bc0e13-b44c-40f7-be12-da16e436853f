# Unsafe File Path Construction Analysis

## Issue Summary
The unified sync engine constructs file paths using unsanitized note titles and folder names, which can cause sync failures when titles contain special characters that are invalid for file systems.

## Files Analyzed
- `electron/main/api/sync-logic/unified-sync-engine.ts`
- `electron/utils/filename-sanitizer.ts`
- `electron/main/api/sync-logic/import-handler.ts`
- `electron/main/api/notes-api.ts`

## Current Situation

### 1. Sanitization Utilities Already Exist
The codebase has a comprehensive filename sanitization utility at `electron/utils/filename-sanitizer.ts` that provides:
- `sanitizeNoteTitle()` - For note filenames
- `sanitizeFolderName()` - For folder names
- `sanitizeBookTitle()` - For book folder names
- Cross-platform compatibility (Windows, macOS, Linux)
- Unicode preservation (diacritics like é, ñ, ü)
- Handling of reserved names and length limits

### 2. The Bug Location
In `unified-sync-engine.ts`, the following lines use unsanitized titles:

**Lines 601-604 (exportNote method):**
```typescript
notePath = path.join(directory, folderSyncItem.path, `${note.title}.md`);
// Should be:
notePath = path.join(directory, folderSyncItem.path, `${sanitizeNoteTitle(note.title)}.md`);
```

**Lines 557-569 (exportFolder method):**
```typescript
folderPath = path.join(directory, parentSyncItem.path, folder.name);
// Should be:
folderPath = path.join(directory, parentSyncItem.path, sanitizeFolderName(folder.name));
```

### 3. Import Already Handles This
The import handler reads file names from the file system, so it inherently works with already-sanitized names. When it extracts note titles:
```typescript
const noteTitle = path.basename(notePath, '.md');
```
This gets the sanitized filename without the extension.

### 4. Existing Import Behavior
The sanitizer is already imported in the file (line 26):
```typescript
import { sanitizeBookTitle, sanitizeFolderName, sanitizeNoteTitle } from '../../../utils/filename-sanitizer';
```
But it's not being used in the critical path construction sections.

## Sync Compatibility Analysis

### Current Sync Behavior
1. **Export**: Creates files with unsanitized names (bug)
2. **Import**: Reads whatever filenames exist on disk
3. **Manifest**: Stores the original, unsanitized titles in metadata

### Impact of Fix
1. **New Syncs**: Will work correctly with special characters
2. **Existing Syncs**: 
   - Files with safe names will continue to work
   - Files with unsafe characters likely already failed to sync
   - The manifest tracks items by ID, not filename
   
### Characters That Need Sanitization
Based on the sanitizer utility:
- Windows forbidden: `< > : " | ? * \ /`
- Control characters: `0x00-0x1F, 0x7F`
- Leading/trailing dots and spaces
- Windows reserved names: CON, PRN, AUX, NUL, COM1-9, LPT1-9

## Recommended Fix

### 1. Update exportNote method (lines 601-610):
```typescript
// Replace all instances of `${note.title}.md` with:
`${sanitizeNoteTitle(note.title)}.md`
```

### 2. Update exportFolder method (lines 557-569):
```typescript
// Replace all instances of folder.name in path construction with:
sanitizeFolderName(folder.name)
```

### 3. Update exportBook method (lines 534-537):
```typescript
// Already uses sanitizeBookTitle correctly!
const bookPath = path.join(directory, 'Books', sanitizeBookTitle(book.title));
```

## Testing Considerations

After implementing the fix, test with:
1. Note titles containing: `My Note: Part 1 | "Special" <test>`
2. Folder names with Windows reserved: `CON`, `AUX`, `PRN`
3. Unicode titles: `Café Notes`, `Año Nuevo`, `北京笔记`
4. Long titles exceeding 255 characters
5. Titles with only invalid characters: `***`, `???`

## Migration Strategy

Since the sync system uses IDs for tracking, not filenames:
1. Existing valid syncs will continue working
2. Previously failed syncs due to invalid characters will now succeed
3. No migration needed - the manifest already stores original titles
4. Import will correctly map sanitized filenames back to original titles

## Conclusion

This is a straightforward fix that involves using the already-imported sanitization functions in the correct places. The infrastructure is already in place, it just needs to be applied consistently throughout the sync engine's export methods.