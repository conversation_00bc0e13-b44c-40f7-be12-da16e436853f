<template>
  <div class="discord-container">
    <div class="discord-header-wrapper">
      <div class="discord-header">
        Discord Rich Presence
      </div>
      <div class="discord-master-toggle">
        <label class="toggle-switch">
          <input
            type="checkbox"
            :checked="settingsStore.settings.discordRichPresenceEnabled"
            @change="handleToggleEnabled"
          />
          <span class="toggle-slider"></span>
        </label>
        <span class="toggle-label">{{ settingsStore.settings.discordRichPresenceEnabled ? 'On' : 'Off' }}</span>
      </div>
    </div>
    <div class="discord-divider"></div>

    <!-- Discord Settings Content (shown when enabled) -->
    <div v-if="settingsStore.settings.discordRichPresenceEnabled" class="discord-content">
      <div class="discord-options">
      <div class="discord-subtitle">Activity Privacy Controls</div>

      <!-- Show Note Taking -->
      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-label">Show Note Taking</div>
          <div class="setting-description">Display "Taking notes" when writing notes</div>
        </div>
        <div class="setting-control">
          <label class="toggle-switch">
            <input
              type="checkbox"
              :checked="discordSettings.showNoteTaking"
              @change="handleToggleNoteTaking"
            />
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Show Book Writing -->
      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-label">Show Book Writing</div>
          <div class="setting-description">Display when writing about books</div>
        </div>
        <div class="setting-control">
          <label class="toggle-switch">
            <input
              type="checkbox"
              :checked="discordSettings.showBookWriting"
              @change="handleToggleBookWriting"
            />
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Show Book Names -->
      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-label">Show Book Names</div>
          <div class="setting-description">Display actual book titles (vs generic "a book")</div>
        </div>
        <div class="setting-control">
          <label class="toggle-switch">
            <input
              type="checkbox"
              :checked="discordSettings.showBookNames"
              @change="handleToggleBookNames"
            />
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Show Timer -->
      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-label">Show Timer Sessions</div>
          <div class="setting-description">Display "In focus session" when using timer</div>
        </div>
        <div class="setting-control">
          <label class="toggle-switch">
            <input
              type="checkbox"
              :checked="discordSettings.showTimer"
              @change="handleToggleTimer"
            />
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <!-- Show Settings -->
      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-label">Show Settings Activity</div>
          <div class="setting-description">Display "Configuring app" when in settings</div>
        </div>
        <div class="setting-control">
          <label class="toggle-switch">
            <input
              type="checkbox"
              :checked="discordSettings.showSettings"
              @change="handleToggleSettings"
            />
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <div class="discord-subtitle">Idle Detection</div>
      <div class="setting-info">
        <div class="setting-description">Automatically shows "Idle" after 3 minutes of inactivity</div>
      </div>

      </div>
    </div> <!-- End discord-content -->

    <!-- Connection Status -->
    <div class="connection-status">
      <div class="status-row">
        <div class="status-label">Connection Status:</div>
        <div class="status-indicator" :class="{ 'connected': discordStatus.connected, 'disconnected': !discordStatus.connected }">
          {{ discordStatus.connected ? 'Connected' : 'Disconnected' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from '../../stores/settingsStore'
import { useElectronAPI } from '../../useElectronAPI'
import { useDiscordActivity } from '../../composables/useDiscordActivity'

const settingsStore = useSettingsStore()
const db = useElectronAPI()
const { updateDiscordSettings } = useDiscordActivity()

// Discord connection status
const discordStatus = ref({ connected: false, enabled: false })

// Discord settings state
const discordSettings = ref({
  showNoteTaking: true,
  showBookWriting: true,
  showBookNames: true,
  showTimer: true,
  showSettings: true
})



// Status check interval
let statusInterval: number | null = null

// Initialize Discord and check status
onMounted(async () => {
  console.log('🎮 [DiscordSettings] Component mounted')
  console.log('🎮 [DiscordSettings] Discord enabled in settings:', settingsStore.settings.discordRichPresenceEnabled)

  await checkDiscordStatus()

  // Check status every 5 seconds
  statusInterval = window.setInterval(checkDiscordStatus, 5000)

  // Discord should already be initialized by the settings store
  // No need to initialize here anymore
})

onUnmounted(() => {
  if (statusInterval) {
    clearInterval(statusInterval)
  }
})

// Check Discord connection status
async function checkDiscordStatus() {
  try {
    const status = await db.discord.getStatus()
    console.log('🎮 [DiscordSettings] Discord status:', status)
    discordStatus.value = { connected: status.connected, enabled: status.enabled }
    if (status.settings) {
      discordSettings.value = { ...status.settings }
      console.log('🎮 [DiscordSettings] Discord settings loaded:', discordSettings.value)
    }
  } catch (error) {
    console.error('Failed to get Discord status:', error)
    discordStatus.value = { connected: false, enabled: false }
  }
}

// Initialize Discord RPC
async function initializeDiscord() {
  try {
    await db.discord.initialize()
    await db.discord.setEnabled(true)
    await checkDiscordStatus()
  } catch (error) {
    console.error('Failed to initialize Discord:', error)
  }
}

// Handle toggle enabled - with instant activation
async function handleToggleEnabled(event: Event) {
  const target = event.target as HTMLInputElement
  const enabled = target.checked

  // Store the previous value for rollback
  const previousEnabled = settingsStore.settings.discordRichPresenceEnabled

  try {
    if (enabled) {
      // Instant activation when enabled
      await initializeDiscord()
    } else {
      await db.discord.setEnabled(false)
      await db.discord.clearActivity()
    }
    await checkDiscordStatus()
    
    // Only update settings after successful async operations
    await settingsStore.updateSetting('discordRichPresenceEnabled', enabled)
  } catch (error) {
    console.error('Failed to toggle Discord:', error)
    
    // Rollback the checkbox state on failure
    target.checked = previousEnabled
    
    // Optionally show an error message to the user
    // You might want to add a notification system here
  }
}

// Handle toggle note taking
async function handleToggleNoteTaking(event: Event) {
  const target = event.target as HTMLInputElement
  discordSettings.value.showNoteTaking = target.checked
  await updateDiscordSettings({ showNoteTaking: target.checked })
}

// Handle toggle book writing
async function handleToggleBookWriting(event: Event) {
  const target = event.target as HTMLInputElement
  discordSettings.value.showBookWriting = target.checked
  await updateDiscordSettings({ showBookWriting: target.checked })
}

// Handle toggle book names
async function handleToggleBookNames(event: Event) {
  const target = event.target as HTMLInputElement
  discordSettings.value.showBookNames = target.checked
  await updateDiscordSettings({ showBookNames: target.checked })
}

// Handle toggle timer
async function handleToggleTimer(event: Event) {
  const target = event.target as HTMLInputElement
  discordSettings.value.showTimer = target.checked
  await updateDiscordSettings({ showTimer: target.checked })
}

// Handle toggle settings
async function handleToggleSettings(event: Event) {
  const target = event.target as HTMLInputElement
  discordSettings.value.showSettings = target.checked
  await updateDiscordSettings({ showSettings: target.checked })
}


</script>

<style scoped>
.discord-container {
  border-radius: 16px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-primary);
  display: flex;
  padding: 32px;
  flex-direction: column;
  align-items: start;
  font-family: 'Montserrat', sans-serif;
}

@media (max-width: 991px) {
  .discord-container {
    padding: 20px;
  }
}

.discord-header-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.discord-header {
  color: var(--color-text-primary);
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.discord-master-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-label {
  color: var(--color-text-primary);
  font-size: 13px;
  font-weight: 500;
  min-width: 22px;
}

.discord-divider {
  background-color: var(--color-border-secondary);
  align-self: stretch;
  display: flex;
  margin-top: 24px;
  flex-shrink: 0;
  height: 1px;
}

@media (max-width: 991px) {
  .discord-divider {
    max-width: 100%;
  }
}

.discord-subtitle {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}

.discord-content {
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.discord-options {
  width: 100%;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--color-border-secondary);
}

.setting-row:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-label {
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.setting-description {
  color: var(--color-text-secondary);
  font-size: 12px;
}

.setting-control {
  margin-left: 16px;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 22px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 22px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-bg-primary);
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Connection Status */
.connection-status {
  width: 100%;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border-secondary);
}

.status-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.status-indicator {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
}

.status-indicator.connected {
  color: var(--color-success-text);
  background-color: var(--color-success-bg);
}

.status-indicator.disconnected {
  color: var(--color-error-text);
  background-color: var(--color-error-bg);
}


</style>
