<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Import Backup</h3>
          <div class="close-button" @click="handleClose">
            <img src="/icons/close-icon.svg" alt="Close" />
          </div>
        </div>

        <!-- Step Indicator -->
        <div class="step-indicator">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="step-item"
            :class="{ 
              'active': currentStep === index + 1, 
              'completed': currentStep > index + 1 
            }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-label">{{ step }}</div>
          </div>
        </div>

        <!-- Step Content -->
        <div class="form-container">
          <!-- Step 1: Select Backup Directory -->
          <div v-if="currentStep === 1" class="step-content">
            <div class="step-title">Select Backup Directory</div>
            <div class="step-description">
              Choose the backup directory you want to import from
            </div>
            
            <div class="directory-selection">
              <div class="directory-input-wrapper">
                <div class="directory-display" v-if="selectedDirectory">
                  <div class="directory-path">{{ selectedDirectory }}</div>
                  <button class="change-directory-button" @click="selectDirectory">
                    Change
                  </button>
                </div>
                <div class="directory-empty" v-else>
                  <div class="empty-message">No directory selected</div>
                  <button class="select-directory-button" @click="selectDirectory">
                    Select Directory
                  </button>
                </div>
              </div>
              
              <div v-if="directoryValidation.error" class="validation-error">
                {{ directoryValidation.error }}
              </div>
              
              <div v-if="directoryAnalysis" class="directory-analysis">
                <div class="analysis-title">Backup Analysis</div>
                <div class="analysis-stats">
                  <div class="stat-item">
                    <span class="stat-label">Books Found:</span>
                    <span class="stat-value">{{ directoryAnalysis.booksCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Notes Found:</span>
                    <span class="stat-value">{{ directoryAnalysis.notesCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Folders Found:</span>
                    <span class="stat-value">{{ directoryAnalysis.foldersCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Configure Import Options -->
          <div v-if="currentStep === 2" class="step-content">
            <div class="step-title">Configure Import Options</div>
            <div class="step-description">
              Choose what to import and how to handle conflicts
            </div>
            
            <div class="import-options">
              <div class="option-group">
                <div class="option-title">Import Content</div>
                <label class="option-checkbox">
                  <input type="checkbox" v-model="importConfig.importBooks" />
                  <span class="checkbox-label">Import Books</span>
                </label>
                <label class="option-checkbox">
                  <input type="checkbox" v-model="importConfig.importNotes" />
                  <span class="checkbox-label">Import Notes</span>
                </label>
                <label class="option-checkbox">
                  <input type="checkbox" v-model="importConfig.importFolders" />
                  <span class="checkbox-label">Import Folder Structure</span>
                </label>
              </div>
              
              <div class="option-group">
                <div class="option-title">Conflict Resolution</div>
                <div class="radio-group">
                  <label class="radio-option">
                    <input type="radio" value="skip" v-model="importConfig.conflictResolution" />
                    <span class="radio-label">Skip conflicting items</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" value="rename" v-model="importConfig.conflictResolution" />
                    <span class="radio-label">Rename conflicting items</span>
                  </label>
                  <label class="radio-option">
                    <input type="radio" value="overwrite" v-model="importConfig.conflictResolution" />
                    <span class="radio-label">Overwrite existing items</span>
                  </label>
                </div>
              </div>
              
              <div class="option-group">
                <div class="option-title">Additional Options</div>
                <label class="option-checkbox">
                  <input type="checkbox" v-model="importConfig.preserveTimestamps" />
                  <span class="checkbox-label">Preserve original timestamps</span>
                </label>
                <label class="option-checkbox">
                  <input type="checkbox" v-model="importConfig.createBackup" />
                  <span class="checkbox-label">Create backup before import</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Step 3: Import Progress -->
          <div v-if="currentStep === 3" class="step-content">
            <div class="step-title">Importing Backup</div>
            <div class="step-description">
              Please wait while your backup is being imported...
            </div>
            
            <div class="import-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: importProgress + '%' }"></div>
              </div>
              <div class="progress-text">{{ importProgressText }}</div>
              <div class="progress-details">{{ importProgressDetails }}</div>
            </div>
          </div>

          <!-- Step 4: Import Results -->
          <div v-if="currentStep === 4" class="step-content">
            <div class="step-title">Import Complete</div>
            <div class="step-description">
              Your backup import has finished
            </div>
            
            <div class="import-results">
              <div class="result-summary" :class="{ 'success': importResults.success, 'error': !importResults.success }">
                <div class="result-icon">
                  <img v-if="importResults.success" src="/icons/check-icon.svg" alt="Success" />
                  <img v-else src="/icons/close-icon.svg" alt="Error" />
                </div>
                <div class="result-message">
                  {{ importResults.success ? 'Import completed successfully!' : 'Import completed with errors' }}
                </div>
              </div>
              
              <div class="result-stats">
                <div class="stat-item">
                  <span class="stat-label">Items Processed:</span>
                  <span class="stat-value">{{ importResults.itemsProcessed }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Items Imported:</span>
                  <span class="stat-value">{{ importResults.itemsImported }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Errors:</span>
                  <span class="stat-value">{{ importResults.errors.length }}</span>
                </div>
              </div>
              
              <div v-if="importResults.errors.length > 0" class="error-list">
                <div class="error-title">Errors encountered:</div>
                <div class="error-items">
                  <div v-for="(error, index) in importResults.errors" :key="index" class="error-item">
                    {{ error }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
          <div class="modal-footer-left">
            <button 
              v-if="currentStep > 1 && currentStep < 4" 
              class="btn btn-secondary" 
              @click="previousStep"
              :disabled="isImporting"
            >
              Back
            </button>
          </div>
          <div class="modal-footer-right">
            <button class="btn btn-secondary" @click="handleClose">
              {{ currentStep === 4 ? 'Close' : 'Cancel' }}
            </button>
            <button 
              v-if="currentStep < 3"
              class="btn btn-primary" 
              @click="nextStep"
              :disabled="!canProceed"
            >
              {{ currentStep === 2 ? 'Start Import' : 'Next' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'

// Define emits
const emit = defineEmits<{
  close: []
  'import-complete': [result: { success: boolean; itemsProcessed: number; errors: string[] }]
}>()

// Component state
const currentStep = ref(1)
const steps = ['Select', 'Configure', 'Import', 'Results']
const selectedDirectory = ref<string>('')
const isImporting = ref(false)

// Directory validation and analysis
const directoryValidation = ref<{ isValid: boolean; error?: string }>({ isValid: false })
const directoryAnalysis = ref<{
  booksCount: number
  notesCount: number
  foldersCount: number
} | null>(null)

// Import configuration
const importConfig = ref({
  importBooks: true,
  importNotes: true,
  importFolders: true,
  conflictResolution: 'rename' as 'skip' | 'rename' | 'overwrite',
  preserveTimestamps: true,
  createBackup: false
})

// Import progress
const importProgress = ref(0)
const importProgressText = ref('Preparing import...')
const importProgressDetails = ref('')

// Import results
const importResults = ref({
  success: false,
  itemsProcessed: 0,
  itemsImported: 0,
  errors: [] as string[]
})

// Computed properties
const canProceed = computed(() => {
  if (currentStep.value === 1) {
    return selectedDirectory.value && directoryValidation.value.isValid
  }
  if (currentStep.value === 2) {
    return importConfig.value.importBooks || importConfig.value.importNotes || importConfig.value.importFolders
  }
  return false
})

// Methods
const handleClose = () => {
  if (currentStep.value === 4) {
    emit('import-complete', {
      success: importResults.value.success,
      itemsProcessed: importResults.value.itemsProcessed,
      errors: importResults.value.errors
    })
  }
  emit('close')
}

const selectDirectory = async () => {
  try {
    console.log('Opening directory selection dialog...')

    // TODO: Replace with unified sync directory selection
    const selectedPath = await window.electronAPI.selectFolder()
    if (!selectedPath) {
      console.log('Directory selection cancelled')
      return
    }

    selectedDirectory.value = selectedPath
    console.log('Selected directory:', selectedPath)

    // TODO: Replace with unified sync validation
    // const validation = await window.electronAPI.unifiedSync.validateLocation(selectedPath)
    // const analysis = await window.electronAPI.unifiedSync.analyzeDirectory(selectedPath)
    
    // Placeholder validation
    directoryValidation.value = {
      isValid: true
    }
    
    // Placeholder analysis
    directoryAnalysis.value = {
      booksCount: 0,
      notesCount: 0,
      foldersCount: 0
    }
    
    console.log('Directory selected (unified sync system pending)')
  } catch (error) {
    console.error('Error selecting directory:', error)
    directoryValidation.value = {
      isValid: false,
      error: 'Failed to select directory. Unified sync system pending.'
    }
    directoryAnalysis.value = null
  }
}

const nextStep = () => {
  if (currentStep.value === 2) {
    // Start import process
    startImport()
  } else {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const startImport = async () => {
  currentStep.value = 3
  isImporting.value = true
  importProgress.value = 0
  importProgressText.value = 'Initializing import...'

  try {
    console.log('Starting import with config:', importConfig.value)

    // TODO: Replace with unified sync import API
    // const result = await window.electronAPI.unifiedSync.importBackup(
    //   selectedDirectory.value,
    //   {
    //     importBooks: importConfig.value.importBooks,
    //     importNotes: importConfig.value.importNotes,
    //     importFolders: importConfig.value.importFolders,
    //     conflictResolution: importConfig.value.conflictResolution,
    //     preserveTimestamps: importConfig.value.preserveTimestamps
    //   }
    // )

    // Placeholder simulation
    for (let i = 0; i <= 100; i += 10) {
      importProgress.value = i
      importProgressText.value = `Processing... ${i}%`
      importProgressDetails.value = 'Unified sync system pending'
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Placeholder results
    importResults.value = {
      success: true,
      itemsProcessed: 0,
      itemsImported: 0,
      errors: ['Unified sync system pending implementation']
    }

    currentStep.value = 4
  } catch (error) {
    console.error('Import failed:', error)
    importResults.value = {
      success: false,
      itemsProcessed: 0,
      itemsImported: 0,
      errors: ['Import failed: Unified sync system pending']
    }
    currentStep.value = 4
  } finally {
    isImporting.value = false
  }
}

// Escape key handler
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && !isImporting.value) {
    handleClose()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleEscapeKey)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleEscapeKey)
  // TODO: Clean up unified sync progress listener when implemented
  // if (window.electronAPI?.unifiedSync?.removeProgressListener) {
  //   window.electronAPI.unifiedSync.removeProgressListener()
  // }
})
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

/* Modal Content */
.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-hover-shadow);
  width: 700px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: 90vh;
}

/* Modal Header */
.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
}

.close-button {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: center;
  padding: 20px 40px;
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: var(--color-border-primary);
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background-color: var(--color-primary);
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  background-color: var(--color-bg-primary);
  border: 2px solid var(--color-border-primary);
  color: var(--color-text-secondary);
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.step-item.completed .step-number {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.step-label {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-secondary);
}

.step-item.active .step-label {
  color: var(--color-text-primary);
}

/* Form Container */
.form-container {
  padding: 20px 40px;
  flex: 1;
  overflow-y: auto;
}

.step-content {
  max-width: 100%;
}

.step-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.step-description {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 24px;
  line-height: 1.5;
}

/* Directory Selection */
.directory-selection {
  margin-bottom: 20px;
}

.directory-input-wrapper {
  margin-bottom: 12px;
}

.directory-display,
.directory-empty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
}

.directory-path {
  color: var(--color-text-primary);
  font-size: 13px;
  font-family: 'Courier New', monospace;
  flex: 1;
  margin-right: 12px;
  word-break: break-all;
}

.empty-message {
  color: var(--color-text-secondary);
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.change-directory-button,
.select-directory-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.change-directory-button:hover,
.select-directory-button:hover {
  background-color: var(--color-primary-hover);
}

.validation-error {
  color: var(--color-error);
  font-size: 12px;
  margin-bottom: 12px;
}

/* Directory Analysis */
.directory-analysis {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-tertiary);
}

.analysis-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 12px;
}

.analysis-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* Import Options */
.import-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
}

.option-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 12px;
}

.option-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

.option-checkbox:last-child {
  margin-bottom: 0;
}

.option-checkbox input[type="checkbox"] {
  margin-right: 10px;
  accent-color: var(--color-primary);
}

.checkbox-label {
  color: var(--color-text-primary);
  font-size: 13px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  margin-right: 10px;
  accent-color: var(--color-primary);
}

.radio-label {
  color: var(--color-text-primary);
  font-size: 13px;
}

/* Import Progress */
.import-progress {
  text-align: center;
  padding: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--color-bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 8px;
}

.progress-details {
  font-size: 14px;
  color: var(--color-text-secondary);
}

/* Import Results */
.import-results {
  text-align: center;
}

.result-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.result-summary.success {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-success);
}

.result-summary.error {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-error);
}

.result-icon {
  margin-bottom: 12px;
}

.result-icon img {
  width: 48px;
  height: 48px;
}

.result-message {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.result-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
  margin-bottom: 20px;
}

.error-list {
  text-align: left;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-error);
  background-color: var(--color-bg-secondary);
}

.error-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-error);
  margin-bottom: 12px;
}

.error-items {
  max-height: 150px;
  overflow-y: auto;
}

.error-item {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--color-bg-tertiary);
}

/* Modal Footer */
.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-modal-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-bg-secondary);
}

.modal-footer-left,
.modal-footer-right {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn:disabled {
  background-color: var(--color-border-primary);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .form-container {
    padding: 16px 20px;
  }

  .modal-header {
    padding: 12px 20px;
  }

  .modal-footer {
    padding: 12px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .modal-footer-left,
  .modal-footer-right {
    width: 100%;
    justify-content: center;
  }

  .analysis-stats {
    flex-direction: column;
    gap: 12px;
  }

  .result-stats {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
