/**
 * Media utilities for .noti format sync operations
 * Handles embedding and extracting media files for sync portability
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { getMediaFilesByNoteId, saveMediaFile, filePathToMediaUrl } from '../media-api';
import { EmbeddedMedia } from './types';

/**
 * Convert note's media files to base64 embedded format for sync
 * Reads all media files associated with a note and embeds them as base64
 */
export async function embedMediaFiles(noteId: number): Promise<EmbeddedMedia[]> {
  try {
    const mediaFiles = await getMediaFilesByNoteId(noteId);
    const embeddedMedia: EmbeddedMedia[] = [];

    for (const mediaFile of mediaFiles) {
      try {
        // Read the file from disk
        const fileBuffer = await fs.readFile(mediaFile.file_path);
        const base64Data = fileBuffer.toString('base64');

        // Create embedded media entry
        embeddedMedia.push({
          id: `embedded_media_${mediaFile.id}`,
          file_name: mediaFile.file_name,
          file_type: mediaFile.file_type,
          file_size: mediaFile.file_size,
          original_path: filePathToMediaUrl(mediaFile.file_path),
          embedded: true,
          data: base64Data
        });

        console.log(`Embedded media file: ${mediaFile.file_name} (${mediaFile.file_size} bytes)`);
      } catch (fileError) {
        console.error(`Failed to embed media file ${mediaFile.id} (${mediaFile.file_name}):`, fileError);
        // Continue with other files - don't fail the entire operation
      }
    }

    return embeddedMedia;
  } catch (error) {
    console.error(`Failed to get media files for note ${noteId}:`, error);
    return []; // Return empty array if we can't get media files
  }
}

/**
 * Extract embedded media and restore to media_files table
 * Decodes base64 data and saves files to media storage
 */
export async function restoreEmbeddedMedia(
  htmlContent: string,
  media: EmbeddedMedia[],
  noteId: number
): Promise<string> {
  let restoredHtmlContent = htmlContent;

  for (const mediaItem of media) {
    try {
      // Convert base64 back to buffer
      const fileBuffer = Buffer.from(mediaItem.data, 'base64');

      // Save media file to media storage and database
      const savedMediaFile = await saveMediaFile(
        noteId,
        fileBuffer,
        mediaItem.file_name,
        mediaItem.file_type,
        null, // book_id
        false // is_cover
      );

      // Create the new noti-media URL for the restored file
      const newMediaUrl = filePathToMediaUrl(savedMediaFile.file_path);

      // Replace embedded reference with actual media URL
      const embeddedReference = `noti-media://${mediaItem.id}`;
      restoredHtmlContent = restoredHtmlContent.replace(
        new RegExp(escapeRegExp(embeddedReference), 'g'),
        newMediaUrl
      );

      console.log(`Restored media: ${mediaItem.file_name} -> ${newMediaUrl}`);

    } catch (error) {
      console.error(`Failed to restore media ${mediaItem.file_name}:`, error);
      // Remove broken media references from HTML
      const embeddedReference = `noti-media://${mediaItem.id}`;
      restoredHtmlContent = restoredHtmlContent.replace(
        new RegExp(`<img[^>]*src="${escapeRegExp(embeddedReference)}"[^>]*>`, 'g'),
        `<span style="color: red;">[Missing media: ${mediaItem.file_name}]</span>`
      );
    }
  }

  return restoredHtmlContent;
}

/**
 * Create markdown preview from content (first 50 words)
 * Used for notes list display in the app
 */
export function createMarkdownPreview(content: string): string {
  if (!content || content.trim() === '') {
    return '';
  }

  // Split by whitespace and take first 50 words
  const words = content.split(/\s+/).filter(word => word.length > 0).slice(0, 50);
  return words.join(' ') + (words.length >= 50 ? '...' : '');
}

/**
 * Extract plain text from HTML for search functionality
 * Removes HTML tags and normalizes whitespace
 */
export function extractPlainText(htmlContent: string): string {
  if (!htmlContent) {
    return '';
  }

  // Remove HTML tags and normalize whitespace
  return htmlContent
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Replace media URLs in HTML with embedded references for export
 * Converts noti-media:// URLs to embedded references
 */
export function replaceMediaUrlsWithEmbedded(
  htmlContent: string,
  mediaFiles: any[],
  embeddedMedia: EmbeddedMedia[]
): string {
  let processedHtml = htmlContent;

  for (let i = 0; i < mediaFiles.length && i < embeddedMedia.length; i++) {
    const mediaFile = mediaFiles[i];
    const embeddedRef = embeddedMedia[i];

    // Get the original media URL
    const originalUrl = filePathToMediaUrl(mediaFile.file_path);
    const embeddedUrl = `noti-media://${embeddedRef.id}`;

    // Replace the original URL with embedded reference
    processedHtml = processedHtml.replace(
      new RegExp(escapeRegExp(originalUrl), 'g'),
      embeddedUrl
    );

    // Also handle various URL formats that might exist
    const patterns = [
      // Full file path
      mediaFile.file_path.replace(/\\/g, '/'),
      // Just filename
      mediaFile.file_name,
      // Encoded paths
      encodeURIComponent(mediaFile.file_path)
    ];

    for (const pattern of patterns) {
      const notiMediaPattern = `noti-media://${pattern}`;
      processedHtml = processedHtml.replace(
        new RegExp(escapeRegExp(notiMediaPattern), 'g'),
        embeddedUrl
      );
    }
  }

  return processedHtml;
}

/**
 * Escape special regex characters in a string
 * Used for safe regex replacement operations
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Validate embedded media data
 * Checks if the embedded media has valid base64 data
 */
export function validateEmbeddedMedia(media: EmbeddedMedia[]): boolean {
  for (const item of media) {
    if (!item.id || !item.file_name || !item.data) {
      console.warn(`Invalid embedded media item: missing required fields`);
      return false;
    }

    // Basic base64 validation
    try {
      Buffer.from(item.data, 'base64');
    } catch (error) {
      console.warn(`Invalid base64 data in embedded media: ${item.file_name}`);
      return false;
    }
  }

  return true;
}

/**
 * Calculate total size of embedded media
 * Useful for performance monitoring and size limits
 */
export function calculateEmbeddedMediaSize(media: EmbeddedMedia[]): number {
  return media.reduce((total, item) => total + item.file_size, 0);
}

/**
 * Get media file extension from MIME type
 * Fallback for when file extension is needed
 */
export function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
    'application/pdf': '.pdf',
    'text/plain': '.txt',
    'application/json': '.json'
  };

  return mimeToExt[mimeType] || '';
}
