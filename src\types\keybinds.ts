// Type definitions for the keybind system

export interface KeybindContext {
  view: 'dashboard' | 'notes' | 'books' | 'folders' | 'timer' | 'settings'
  modalOpen: boolean
  editorFocused: boolean
  selectedItems: any[]
  currentRoute?: string
}

export type KeybindHandler = (context: KeybindContext, event?: KeyboardEvent) => void

export interface KeybindConfig {
  key: string
  handler: KeybindHandler
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  enabled: boolean
  context?: Partial<KeybindContext>
}

export interface KeyCombination {
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
  key: string
}

export interface GlobalKeybindState {
  sidebarCollapsed: boolean
  activeModal: string | null
  currentView: string
}

// Keybind categories for organization
export enum KeybindCategory {
  GLOBAL = 'global',
  NAVIGATION = 'navigation',
  NOTES = 'notes',
  BOOKS = 'books',
  FOLDERS = 'folders',
  TIMER = 'timer',
  EDITOR = 'editor',
  MODAL = 'modal',
  SEARCH = 'search'
}

// Available views in the application
export enum AppView {
  DASHBOARD = 'dashboard',
  NOTES = 'notes',
  BOOKS = 'books',
  FOLDERS = 'folders',
  TIMER = 'timer',
  SETTINGS = 'settings'
}