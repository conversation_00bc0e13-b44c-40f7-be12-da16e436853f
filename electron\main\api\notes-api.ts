// Notes API - Specialized functions for note operations
import {
    createNote,
    getAllNotes,
    getNoteById,
    getNotesByFolderId,
    getNotesByBookId,
    updateNote,
    deleteNote,
    Note, // Import the Note interface
    getFolderById,
    getChildFolders,
    getBookById
} from '../database/database-api';
import { getBookFolder } from './folders-api';
import mediaApi from './media-api';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { app, dialog } from 'electron';
import * as fsExtra from 'fs-extra';
import { notifyNoteChange } from '../database/database-hooks';

import puppeteer from 'puppeteer';
import archiver from 'archiver';
import MarkdownIt from 'markdown-it';
import { sanitizeFilename, sanitizeNoteTitle } from '../../utils/filename-sanitizer';
import crypto from 'crypto';

// ES modules equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// HTML escaping function for security
const escapeHtml = (unsafe: string): string => {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
};

// Initialize markdown parser with enhanced configuration
const md = new MarkdownIt({
    html: true,        // Enable HTML tags in source
    xhtmlOut: true,    // Use '/' to close single tags (<br />)
    breaks: true,      // Convert '\n' in paragraphs into <br>
    linkify: true,     // Autoconvert URL-like text to links
    typographer: true, // Enable some language-neutral replacement + quotes beautification
});

// Define an interface for notes with added metadata
export interface NoteWithMetadata extends Note {
    wordCount: number;
    createdAtFormatted: string;
    updatedAtFormatted: string;
}

// Helper function to validate a note object (accepts partial for updates)
const validateNote = (note: Partial<Note>): string[] => {
    const errors: string[] = [];

    // Title validation (optional but validated if present)
    if (note.title !== undefined) {
        if (typeof note.title !== 'string' || note.title.trim() === '') {
            errors.push('Note title must be a non-empty string');
        }
    }

    // Content validation (optional but validated if present)
    if (note.content !== undefined && typeof note.content !== 'string') {
        errors.push('Note content must be a string');
    }

    // Folder ID validation (optional but validated if present)
    if (note.folder_id !== undefined && note.folder_id !== null && typeof note.folder_id !== 'number') {
        errors.push('Folder ID must be a number or null');
    }

    // Book ID validation (optional but validated if present)
    if (note.book_id !== undefined && note.book_id !== null && typeof note.book_id !== 'number') {
        errors.push('Book ID must be a number or null');
    }

    return errors;
};

// Create a new note with validation
export const createNoteWithValidation = async (note: Note): Promise<Note> => {
    // Ensure title is present for creation
    if (!note.title || typeof note.title !== 'string' || note.title.trim() === '') {
        throw new Error('Note title is required for creation');
    }
    // Validate the full note object
    const validationErrors = validateNote(note);
    if (validationErrors.length > 0) {
        throw new Error(`Invalid note data: ${validationErrors.join(', ')}`);
    }

    try {
        const result = await createNote(note);

        // Notify database hooks about note creation
        if (result.id) {
            notifyNoteChange('create', result.id, {
                title: result.title,
                folder_id: result.folder_id,
                book_id: result.book_id
            });
        }

        return result;
    } catch (error: any) {
        console.error('Error in createNoteWithValidation:', error);
        throw new Error(`Failed to create note: ${error.message}`);
    }
};

// Helper function to generate a note title for a book
const generateNoteTitle = (bookTitle: string, customTitle?: string): string => {
    if (customTitle && customTitle.trim()) {
        return customTitle.trim();
    }

    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    return `${bookTitle} - ${currentDate}`;
};

// Create a new note linked to a specific book
export const createNoteForBook = async (bookId: number, customTitle?: string, folderId?: number | null): Promise<Note> => {
    // Validate bookId
    if (!Number.isInteger(bookId) || bookId <= 0) {
        throw new Error('Book ID must be a positive integer');
    }

    // Validate folderId if provided
    if (folderId !== undefined && folderId !== null && (!Number.isInteger(folderId) || folderId <= 0)) {
        throw new Error('Folder ID must be a positive integer or null');
    }

    try {
        // Get the book to ensure it exists and get its title
        const book = await getBookById(bookId);
        if (!book) {
            throw new Error(`Book with ID ${bookId} not found`);
        }

        // Determine the folder ID to use
        let targetFolderId: number | null = null;

        if (folderId !== undefined) {
            // Use the provided folderId (could be null)
            targetFolderId = folderId;

            // If a specific folder ID is provided, validate it exists and has correct book_id
            if (folderId !== null) {
                const folder = await getFolderById(folderId);
                if (!folder) {
                    throw new Error(`Folder with ID ${folderId} not found`);
                }

                // Check if folder has the correct book_id or inherits it
                if (folder.book_id !== bookId) {
                    // For now, we'll allow this but log a warning
                    // In the future, we could implement inheritance checking
                    console.warn(`Folder ${folderId} does not have book_id ${bookId}, but proceeding anyway`);
                }
            }
        } else {
            // Fallback to the book's main folder (existing behavior)
            const bookFolder = await getBookFolder(bookId);
            targetFolderId = bookFolder?.id || null;
        }

        // Generate the note title
        const noteTitle = generateNoteTitle(book.title, customTitle);

        // Create the note object
        const newNote: Note = {
            title: noteTitle,
            content: '',
            html_content: '<p></p>',
            book_id: bookId,
            folder_id: targetFolderId,
            type: 'text'
        };

        // Create the note using the existing validation function
        const createdNote = await createNoteWithValidation(newNote);

        console.log(`Created note "${noteTitle}" for book "${book.title}" (ID: ${bookId}) in folder ${targetFolderId}`);
        return createdNote;

    } catch (error: any) {
        console.error('Error in createNoteForBook:', error);
        throw new Error(`Failed to create note for book ${bookId}: ${error.message}`);
    }
};

// Update a note with validation
export const updateNoteWithValidation = async (id: number, noteUpdates: Partial<Note>): Promise<Note> => {
    // Ensure id is a number
    if (typeof id !== 'number') {
        throw new Error('Note ID is required and must be a number');
    }

    // Validate only the fields provided in the update
    const validationErrors = validateNote(noteUpdates);
    if (validationErrors.length > 0) {
        throw new Error(`Invalid note update data: ${validationErrors.join(', ')}`);
    }

    try {
        const result = await updateNote(id, noteUpdates);

        // Notify database hooks about note update
        notifyNoteChange('update', id, {
            title: result.title,
            folder_id: result.folder_id,
            book_id: result.book_id,
            updatedFields: Object.keys(noteUpdates)
        });

        return result;
    } catch (error: any) {
        console.error('Error in updateNoteWithValidation:', error);
        throw new Error(`Failed to update note ${id}: ${error.message}`);
    }
};

// Interface for options in getNotesWithMetadata
interface GetNotesOptions {
    folderId?: number;
    bookId?: number;
}

// Get notes with additional metadata
export const getNotesWithMetadata = async (options: GetNotesOptions = {}): Promise<NoteWithMetadata[]> => {
    try {
        let notes: Note[];

        // Filter by folder, book, or get all
        if (options.folderId !== undefined) {
            if (typeof options.folderId !== 'number') throw new Error('folderId must be a number');
            notes = await getNotesByFolderId(options.folderId);
        } else if (options.bookId !== undefined) {
            if (typeof options.bookId !== 'number') throw new Error('bookId must be a number');
            notes = await getNotesByBookId(options.bookId);
        } else {
            notes = await getAllNotes();
        }

        // Process notes to add additional metadata
        const processedNotes: NoteWithMetadata[] = notes.map(note => ({
            ...note,
            wordCount: note.content ? note.content.split(/\s+/).filter(Boolean).length : 0,
            createdAtFormatted: note.created_at ? new Date(note.created_at).toLocaleString() : 'N/A',
            updatedAtFormatted: note.updated_at ? new Date(note.updated_at).toLocaleString() : 'N/A'
        }));

        return processedNotes;
    } catch (error: any) {
        console.error('Error in getNotesWithMetadata:', error);
        throw new Error(`Failed to get notes with metadata: ${error.message}`);
    }
};

// Search notes by title or content (Placeholder)
export const searchNotes = async (searchTerm: string): Promise<Note[]> => {
    if (typeof searchTerm !== 'string') {
        throw new Error('Search term must be a string');
    }
    try {
        // TODO: Implement actual search logic using database queries (e.g., LIKE operator)
        console.log(`Searching notes for: "${searchTerm}" (Implementation pending)`);
        // Example placeholder: Fetch all notes and filter in memory (inefficient for large datasets)
        const allNotes = await getAllNotes();
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        return allNotes.filter(note =>
            (note.title && note.title.toLowerCase().includes(lowerCaseSearchTerm)) ||
            (note.content && note.content.toLowerCase().includes(lowerCaseSearchTerm))
        );
    } catch (error: any) {
        console.error('Error in searchNotes:', error);
        throw new Error(`Failed to search notes: ${error.message}`);
    }
};

// Helper function to get export directory
const getExportDirectory = (): string => {
    const userDataPath = app.getPath('userData');
    const exportDir = path.join(userDataPath, 'exports');

    // Create the directory if it doesn't exist
    if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
    }

    return exportDir;
};

// ‑- removed to avoid shadowing the imported helper

// Helper function to convert note content to HTML
const noteContentToHtml = (note: Note): string => {
    if (note.html_content) {
        return note.html_content;
    } else if (note.content) {
        // Convert markdown to HTML if the note is in markdown format
        return md.render(note.content);
    }
    return '<p>No content</p>';
};

// Helper function to process HTML content and convert noti-media URLs to base64 for PDF generation
const processHtmlForPdf = async (note: Note): Promise<string> => {
    let htmlContent = noteContentToHtml(note);

    if (!note.id) {
        return htmlContent;
    }

    try {
        // Get all media files associated with this note
        const mediaFiles = await mediaApi.getMediaFilesByNoteId(note.id);

        if (mediaFiles.length === 0) {
            return htmlContent;
        }

        // Process each media file and replace noti-media URLs with base64 data
        for (const mediaFile of mediaFiles) {
            try {
                // Check if file exists and is an image
                if (fs.existsSync(mediaFile.file_path) && mediaFile.file_type.startsWith('image/')) {
                    // Read the file and convert to base64
                    const fileBuffer = fs.readFileSync(mediaFile.file_path);
                    const base64Data = fileBuffer.toString('base64');
                    const mimeType = mediaFile.file_type;
                    const dataUrl = `data:${mimeType};base64,${base64Data}`;

                    // Create pattern to match noti-media URLs for this file
                    // The URL might be noti-media://path or noti-media:///path or just the filename
                    const fileNamePattern = mediaFile.file_name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const fullPathPattern = mediaFile.file_path.replace(/\\/g, '/').replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                    // Replace various possible URL formats
                    const patterns = [
                        new RegExp(`noti-media://${fullPathPattern}`, 'g'),
                        new RegExp(`noti-media:///${fullPathPattern}`, 'g'),
                        new RegExp(`noti-media://${fileNamePattern}`, 'g'),
                        new RegExp(`noti-media://.*?${fileNamePattern}`, 'g')
                    ];

                    for (const pattern of patterns) {
                        htmlContent = htmlContent.replace(pattern, dataUrl);
                    }
                }
            } catch (fileError) {
                console.error(`Error processing media file ${mediaFile.id}:`, fileError);
                // Continue with other files even if one fails
            }
        }

        return htmlContent;
    } catch (error) {
        console.error('Error processing HTML for PDF:', error);
        // Return original content if processing fails
        return htmlContent;
    }
};

// Helper function to optimize HTML content for large files
const optimizeHtmlForPdf = (htmlContent: string): string => {
    // Remove unnecessary whitespace and comments
    let optimized = htmlContent
        .replace(/<!--[\s\S]*?-->/g, '') // Remove HTML comments
        .replace(/\s+/g, ' ') // Collapse multiple whitespace
        .replace(/>\s+</g, '><') // Remove whitespace between tags
        .trim();

    return optimized;
};

// Helper function to generate embedded font CSS for PDF export
const generateEmbeddedFontCss = (): string => {
    const fontsDir = path.join(__dirname, '../../../src/assets/fonts');
    let fontCss = '';

    try {
        // Montserrat fonts
        const montserratWeights = [300, 400, 500, 600, 700];
        for (const weight of montserratWeights) {
            const fontPath = path.join(fontsDir, 'montserrat', `montserrat-${weight}.ttf`);
            if (fs.existsSync(fontPath)) {
                const fontData = fs.readFileSync(fontPath);
                const base64Font = fontData.toString('base64');
                fontCss += `
                @font-face {
                    font-family: 'Montserrat';
                    font-style: normal;
                    font-weight: ${weight};
                    font-display: swap;
                    src: url(data:font/truetype;base64,${base64Font}) format('truetype');
                }`;
            }
        }

        // Roboto fonts
        const robotoWeights = [300, 400, 500, 700];
        for (const weight of robotoWeights) {
            const fontPath = path.join(fontsDir, 'roboto', `roboto-${weight}.ttf`);
            if (fs.existsSync(fontPath)) {
                const fontData = fs.readFileSync(fontPath);
                const base64Font = fontData.toString('base64');
                fontCss += `
                @font-face {
                    font-family: 'Roboto';
                    font-style: normal;
                    font-weight: ${weight};
                    font-display: swap;
                    src: url(data:font/truetype;base64,${base64Font}) format('truetype');
                }`;
            }
        }

        // Open Sans fonts
        const openSansWeights = [300, 400, 500, 600, 700];
        for (const weight of openSansWeights) {
            const fontPath = path.join(fontsDir, 'opensans', `opensans-${weight}.ttf`);
            if (fs.existsSync(fontPath)) {
                const fontData = fs.readFileSync(fontPath);
                const base64Font = fontData.toString('base64');
                fontCss += `
                @font-face {
                    font-family: 'Open Sans';
                    font-style: normal;
                    font-weight: ${weight};
                    font-display: swap;
                    src: url(data:font/truetype;base64,${base64Font}) format('truetype');
                }`;
            }
        }

        // Lato fonts
        const latoWeights = [300, 400, 700];
        for (const weight of latoWeights) {
            const fontPath = path.join(fontsDir, 'lato', `lato-${weight}.ttf`);
            if (fs.existsSync(fontPath)) {
                const fontData = fs.readFileSync(fontPath);
                const base64Font = fontData.toString('base64');
                fontCss += `
                @font-face {
                    font-family: 'Lato';
                    font-style: normal;
                    font-weight: ${weight};
                    font-display: swap;
                    src: url(data:font/truetype;base64,${base64Font}) format('truetype');
                }`;
            }
        }
    } catch (error) {
        console.warn('Warning: Could not load local fonts for PDF export, falling back to system fonts:', error);
    }

    return fontCss;
};

// Helper function to generate complete HTML document for PDF export
const generatePdfHtml = async (note: Note): Promise<string> => {
    const processedContent = await processHtmlForPdf(note);

    // Optimize content for large files
    const optimizedContent = optimizeHtmlForPdf(processedContent);

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${note.title}</title>
            <!-- Local fonts embedded for PDF export -->
            <style>
                ${generateEmbeddedFontCss()}
                /* Base styles */
                body {
                    font-family: 'Montserrat', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 20px;
                    font-size: 14px;
                }

                /* Font family definitions */
                .font-montserrat, [style*="font-family: Montserrat"] {
                    font-family: 'Montserrat', sans-serif !important;
                }
                .font-roboto, [style*="font-family: Roboto"] {
                    font-family: 'Roboto', sans-serif !important;
                }
                .font-open-sans, [style*="font-family: Open Sans"] {
                    font-family: 'Open Sans', sans-serif !important;
                }
                .font-lato, [style*="font-family: Lato"] {
                    font-family: 'Lato', sans-serif !important;
                }
                .font-arial, [style*="font-family: Arial"] {
                    font-family: Arial, sans-serif !important;
                }
                .font-times-new-roman, [style*="font-family: Times New Roman"] {
                    font-family: 'Times New Roman', serif !important;
                }
                .font-georgia, [style*="font-family: Georgia"] {
                    font-family: Georgia, serif !important;
                }
                .font-courier-new, [style*="font-family: Courier New"] {
                    font-family: 'Courier New', monospace !important;
                }
                .font-verdana, [style*="font-family: Verdana"] {
                    font-family: Verdana, sans-serif !important;
                }
                .font-helvetica, [style*="font-family: Helvetica"] {
                    font-family: Helvetica, sans-serif !important;
                }

                /* Typography styles */
                h1, h2, h3, h4, h5, h6 {
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                    font-weight: 600;
                    line-height: 1.3;
                }

                h1 {
                    color: #2c3e50;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 10px;
                    font-size: 2em;
                    margin-top: 0;
                }

                h2 { font-size: 1.5em; }
                h3 { font-size: 1.3em; }
                h4 { font-size: 1.1em; }
                h5 { font-size: 1em; }
                h6 { font-size: 0.9em; }

                /* Paragraph and text styles */
                p {
                    margin-bottom: 1em;
                    text-align: left;
                }

                /* Text formatting */
                strong, b {
                    font-weight: 700;
                }

                em, i {
                    font-style: italic;
                }

                u {
                    text-decoration: underline;
                }

                s, strike {
                    text-decoration: line-through;
                }

                /* Text alignment */
                [style*="text-align: left"], .text-left {
                    text-align: left !important;
                }

                [style*="text-align: center"], .text-center {
                    text-align: center !important;
                }

                [style*="text-align: right"], .text-right {
                    text-align: right !important;
                }

                [style*="text-align: justify"], .text-justify {
                    text-align: justify !important;
                }

                /* Lists */
                ul, ol {
                    margin: 1em 0;
                    padding-left: 2em;
                }

                li {
                    margin-bottom: 0.5em;
                }

                /* Task lists */
                ul[data-type="taskList"] {
                    list-style: none;
                    padding-left: 0;
                }

                li[data-type="taskItem"] {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 0.5em;
                }

                li[data-type="taskItem"] > label {
                    margin-right: 0.5em;
                    user-select: none;
                }

                li[data-type="taskItem"] > div {
                    flex: 1;
                }

                /* Links */
                a {
                    color: #1e88e5;
                    text-decoration: underline;
                }

                /* Code */
                code {
                    background-color: #f5f5f5;
                    border-radius: 3px;
                    padding: 2px 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9em;
                }

                pre {
                    background-color: #f5f5f5;
                    border-radius: 5px;
                    padding: 1em;
                    overflow-x: auto;
                    margin: 1em 0;
                }

                pre code {
                    background: none;
                    padding: 0;
                }

                /* Blockquotes */
                blockquote {
                    border-left: 4px solid #ddd;
                    margin: 1em 0;
                    padding-left: 1em;
                    color: #666;
                    font-style: italic;
                }

                /* Tables */
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 1em 0;
                }

                th, td {
                    border: 1px solid #ddd;
                    padding: 8px 12px;
                    text-align: left;
                }

                th {
                    background-color: #f5f5f5;
                    font-weight: 600;
                }

                /* Images */
                img {
                    max-width: 100%;
                    height: auto;
                    display: block;
                    margin: 1em 0;
                }

                /* Highlights */
                mark {
                    background-color: #ffeb3b;
                    padding: 1px 2px;
                    border-radius: 2px;
                }

                /* Subscript and superscript */
                sub {
                    vertical-align: sub;
                    font-size: 0.8em;
                }

                sup {
                    vertical-align: super;
                    font-size: 0.8em;
                }

                /* Note metadata */
                .note-metadata {
                    font-size: 0.8em;
                    color: #7f8c8d;
                    margin-bottom: 20px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }

                .note-content {
                    margin-top: 20px;
                }

                /* Print optimizations */
                @media print {
                    body {
                        margin: 0;
                        padding: 20px;
                    }

                    .note-metadata {
                        break-inside: avoid;
                    }

                    h1, h2, h3, h4, h5, h6 {
                        break-after: avoid;
                    }

                    img {
                        break-inside: avoid;
                    }

                    table {
                        break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <h1>${note.title}</h1>
            <div class="note-metadata">
                <p><strong>Created:</strong> ${note.created_at ? new Date(note.created_at).toLocaleString() : 'N/A'}</p>
                <p><strong>Last Updated:</strong> ${note.updated_at ? new Date(note.updated_at).toLocaleString() : 'N/A'}</p>
            </div>
            <div class="note-content">
                ${optimizedContent}
            </div>
        </body>
        </html>
    `;
};

// Helper function to generate PDF using Puppeteer directly with timeout control
const generatePdfWithPuppeteer = async (htmlContent: string, outputPath: string): Promise<void> => {
    let browser;
    try {
        console.log('🚀 Starting PDF generation...');
        console.log(`📄 Content size: ${(htmlContent.length / 1024 / 1024).toFixed(2)} MB`);

        // Launch browser with optimized args for large content
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--font-render-hinting=none',
                '--disable-font-subpixel-positioning',
                '--max-old-space-size=4096', // Increase memory limit
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        const page = await browser.newPage();

        // Set very long timeouts for large files (10 minutes)
        const LONG_TIMEOUT = 600000; // 10 minutes
        page.setDefaultTimeout(LONG_TIMEOUT);

        console.log('⏳ Setting page content...');

        // Set content with extended timeout and optimized wait conditions
        await page.setContent(htmlContent, {
            waitUntil: 'domcontentloaded', // Changed from 'networkidle0' for better performance with large content
            timeout: 300000 // 5 minutes timeout for content loading
        });

        console.log('✅ Content loaded, generating PDF...');

        // Generate PDF with very extended timeout and optimized settings
        const pdfBuffer = await page.pdf({
            format: 'A4',
            margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' },
            printBackground: true,
            preferCSSPageSize: true,
            displayHeaderFooter: false,
            timeout: LONG_TIMEOUT // 10 minutes timeout for PDF generation
        });

        console.log('📁 Writing PDF to file...');

        // Write the PDF to file
        fs.writeFileSync(outputPath, pdfBuffer);

        console.log('✅ PDF generation completed successfully');

    } catch (error) {
        console.error('❌ PDF generation failed:', error);
        throw error;
    } finally {
        if (browser) {
            console.log('🔄 Closing browser...');
            await browser.close();
        }
    }
};

// Helper function to export note to PDF
const exportNoteToPdf = async (note: Note): Promise<string> => {
    const exportDir = getExportDirectory();
    const sanitizedTitle = sanitizeFilename(note.title);
    const outputPath = path.join(exportDir, `${sanitizedTitle}.pdf`);

    try {
        // Generate HTML content
        const htmlContent = await generatePdfHtml(note);

        // Generate PDF using Puppeteer directly
        await generatePdfWithPuppeteer(htmlContent, outputPath);

        return outputPath;
    } catch (error: any) {
        console.error('Error generating PDF:', error);
        throw new Error(`Failed to export note as PDF: ${error.message}`);
    }
};

// Helper function to export note to PDF to a specific path
const exportNoteToPdfToPath = async (note: Note, outputPath: string): Promise<void> => {
    try {
        // Generate HTML content
        const htmlContent = await generatePdfHtml(note);

        // Generate PDF using Puppeteer directly
        await generatePdfWithPuppeteer(htmlContent, outputPath);

    } catch (error: any) {
        console.error('Error generating PDF:', error);
        throw new Error(`Failed to export note as PDF: ${error.message}`);
    }
};

// Helper function to export note to Markdown
const exportNoteToMarkdown = async (note: Note): Promise<string> => {
    const exportDir = getExportDirectory();
    const sanitizedTitle = sanitizeFilename(note.title);
    const outputPath = path.join(exportDir, `${sanitizedTitle}.md`);

    let markdownContent = `# ${note.title}\n\n`;
    markdownContent += `**Created:** ${note.created_at ? new Date(note.created_at).toLocaleString() : 'N/A'}\n`;
    markdownContent += `**Updated:** ${note.updated_at ? new Date(note.updated_at).toLocaleString() : 'N/A'}\n\n`;

    // Add the note content
    if (note.content) {
        markdownContent += note.content;
    } else if (note.html_content) {
        // Here you would need to convert HTML to Markdown
        // This is a simplified approach, you might want to use a library like turndown
        markdownContent += `\n\n${note.html_content}\n`;
    }

    try {
        fs.writeFileSync(outputPath, markdownContent);
        return outputPath;
    } catch (error: any) {
        console.error('Error exporting to Markdown:', error);
        throw new Error(`Failed to export note as Markdown: ${error.message}`);
    }
};

// Helper function to export note to Markdown to a specific path
const exportNoteToMarkdownToPath = async (note: Note, outputPath: string): Promise<void> => {
    let markdownContent = `# ${note.title}\n\n`;
    markdownContent += `**Created:** ${note.created_at ? new Date(note.created_at).toLocaleString() : 'N/A'}\n`;
    markdownContent += `**Updated:** ${note.updated_at ? new Date(note.updated_at).toLocaleString() : 'N/A'}\n\n`;

    // Add the note content
    if (note.content) {
        markdownContent += note.content;
    } else if (note.html_content) {
        // Here you would need to convert HTML to Markdown
        markdownContent += `\n\n${note.html_content}\n`;
    }

    try {
        fs.writeFileSync(outputPath, markdownContent);
    } catch (error: any) {
        console.error('Error exporting to Markdown:', error);
        throw new Error(`Failed to export note as Markdown: ${error.message}`);
    }
};

// Helper function to export note to Noti format (JSON)
const exportNoteToNoti = async (note: Note): Promise<string> => {
    const exportDir = getExportDirectory();
    const sanitizedTitle = sanitizeFilename(note.title);
    const outputPath = path.join(exportDir, `${sanitizedTitle}.noti`);

    try {
        // Get app version from package.json
        const packageJsonPath = path.join(__dirname, '../../../package.json');
        let appVersion = '1.0.0';
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            appVersion = packageJson.version || '1.0.0';
        } catch (err) {
            console.warn('Could not read package.json for version', err);
        }

        // Calculate content statistics
        const plainText = note.content || '';
        const wordCount = plainText.split(/\s+/).filter(Boolean).length;
        const characterCount = plainText.length;
        const readingTime = Math.ceil(wordCount / 200); // Average reading speed: 200 words/minute

        // Create the enhanced .noti structure
        const notiData: any = {
            version: "1.0",
            type: "noti-note",
            schema: "https://noti.app/schemas/note/v1.0",
            metadata: {
                id: note.id,
                title: note.title,
                created_at: note.created_at,
                updated_at: note.updated_at,
                last_viewed_at: note.last_viewed_at,
                type: note.type || 'text',
                color: note.color,
                folder_id: note.folder_id,
                book_id: note.book_id,
                export: {
                    version: "1.0.0",
                    app_version: appVersion,
                    exported_at: new Date().toISOString()
                }
            },
            content: {
                html: note.html_content || '',
                markdown: note.content || '',
                plain_text: plainText,
                statistics: {
                    word_count: wordCount,
                    character_count: characterCount,
                    reading_time: readingTime
                }
            },
            media: []
        };

        // Embed media files if the note has an ID
        if (note.id) {
            try {
                const mediaFiles = await mediaApi.getMediaFilesByNoteId(note.id);
                
                for (const mediaFile of mediaFiles) {
                    try {
                        if (fs.existsSync(mediaFile.file_path) && mediaFile.file_type.startsWith('image/')) {
                            const fileBuffer = fs.readFileSync(mediaFile.file_path);
                            const base64Data = fileBuffer.toString('base64');
                            
                            notiData.media.push({
                                id: `media_${mediaFile.id}`,
                                file_name: mediaFile.file_name,
                                file_type: mediaFile.file_type,
                                file_size: mediaFile.file_size,
                                original_path: `noti-media://${mediaFile.file_path}`,
                                embedded: true,
                                data: base64Data
                            });
                        }
                    } catch (mediaError) {
                        console.error(`Error embedding media file ${mediaFile.id}:`, mediaError);
                        // Continue with other media files
                    }
                }
            } catch (error) {
                console.error('Error getting media files for note:', error);
                // Continue without media embedding
            }
        }

        // Calculate integrity hash (excluding the integrity field itself)
        const contentToHash = JSON.stringify({
            version: notiData.version,
            type: notiData.type,
            schema: notiData.schema,
            metadata: notiData.metadata,
            content: notiData.content,
            media: notiData.media
        });
        const contentHash = crypto.createHash('sha256').update(contentToHash).digest('hex');

        // Add integrity section
        notiData.integrity = {
            algorithm: "sha256",
            content_hash: contentHash
        };

        // Write the file
        fs.writeFileSync(outputPath, JSON.stringify(notiData, null, 2));
        return outputPath;
    } catch (error: any) {
        console.error('Error exporting to Noti format:', error);
        throw new Error(`Failed to export note as Noti format: ${error.message}`);
    }
};

// Helper function to export note to Noti format (JSON) to a specific path
const exportNoteToNotiToPath = async (note: Note, outputPath: string): Promise<void> => {
    try {
        // Get app version from package.json
        const packageJsonPath = path.join(__dirname, '../../../package.json');
        let appVersion = '1.0.0';
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            appVersion = packageJson.version || '1.0.0';
        } catch (err) {
            console.warn('Could not read package.json for version', err);
        }

        // Calculate content statistics
        const plainText = note.content || '';
        const wordCount = plainText.split(/\s+/).filter(Boolean).length;
        const characterCount = plainText.length;
        const readingTime = Math.ceil(wordCount / 200); // Average reading speed: 200 words/minute

        // Create the enhanced .noti structure
        const notiData: any = {
            version: "1.0",
            type: "noti-note",
            schema: "https://noti.app/schemas/note/v1.0",
            metadata: {
                id: note.id,
                title: note.title,
                created_at: note.created_at,
                updated_at: note.updated_at,
                last_viewed_at: note.last_viewed_at,
                type: note.type || 'text',
                color: note.color,
                folder_id: note.folder_id,
                book_id: note.book_id,
                export: {
                    version: "1.0.0",
                    app_version: appVersion,
                    exported_at: new Date().toISOString()
                }
            },
            content: {
                html: note.html_content || '',
                markdown: note.content || '',
                plain_text: plainText,
                statistics: {
                    word_count: wordCount,
                    character_count: characterCount,
                    reading_time: readingTime
                }
            },
            media: []
        };

        // Embed media files if the note has an ID
        if (note.id) {
            try {
                const mediaFiles = await mediaApi.getMediaFilesByNoteId(note.id);
                
                for (const mediaFile of mediaFiles) {
                    try {
                        if (fs.existsSync(mediaFile.file_path) && mediaFile.file_type.startsWith('image/')) {
                            const fileBuffer = fs.readFileSync(mediaFile.file_path);
                            const base64Data = fileBuffer.toString('base64');
                            
                            notiData.media.push({
                                id: `media_${mediaFile.id}`,
                                file_name: mediaFile.file_name,
                                file_type: mediaFile.file_type,
                                file_size: mediaFile.file_size,
                                original_path: `noti-media://${mediaFile.file_path}`,
                                embedded: true,
                                data: base64Data
                            });
                        }
                    } catch (mediaError) {
                        console.error(`Error embedding media file ${mediaFile.id}:`, mediaError);
                        // Continue with other media files
                    }
                }
            } catch (error) {
                console.error('Error getting media files for note:', error);
                // Continue without media embedding
            }
        }

        // Calculate integrity hash (excluding the integrity field itself)
        const contentToHash = JSON.stringify({
            version: notiData.version,
            type: notiData.type,
            schema: notiData.schema,
            metadata: notiData.metadata,
            content: notiData.content,
            media: notiData.media
        });
        const contentHash = crypto.createHash('sha256').update(contentToHash).digest('hex');

        // Add integrity section
        notiData.integrity = {
            algorithm: "sha256",
            content_hash: contentHash
        };

        // Write the file
        fs.writeFileSync(outputPath, JSON.stringify(notiData, null, 2));
    } catch (error: any) {
        console.error('Error exporting to Noti format:', error);
        throw new Error(`Failed to export note as Noti format: ${error.message}`);
    }
};

// Export a note to a specific format
export const exportNote = async (id: number, format: string): Promise<string> => {
    try {
        if (typeof id !== 'number') {
            throw new Error('Note ID must be a number');
        }

        if (!['pdf', 'md', 'noti'].includes(format)) {
            throw new Error('Unsupported export format. Supported formats are: pdf, md, noti');
        }

        // Get the note to export
        const note = await getNoteById(id);
        if (!note) {
            throw new Error(`Note with ID ${id} not found`);
        }        // Determine the file extension
        const extension = format; // Now 'noti' is just 'noti', not 'noti.json'
        const sanitizedTitle = sanitizeFilename(note.title);
        const defaultPath = path.join(app.getPath('documents'), `${sanitizedTitle}.${extension}`);

        // Show save dialog to let user choose where to save
        const { canceled, filePath } = await dialog.showSaveDialog({
            defaultPath,
            filters: [
                { name: format.toUpperCase(), extensions: [extension] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['createDirectory', 'showOverwriteConfirmation']
        });

        if (canceled || !filePath) {
            return 'Export cancelled by user';
        }

        // Handle each format
        switch (format) {
            case 'pdf':
                await exportNoteToPdfToPath(note, filePath);
                return `Success: Note "${note.title}" exported as PDF to ${filePath}`;

            case 'md':
                await exportNoteToMarkdownToPath(note, filePath);
                return `Success: Note "${note.title}" exported as Markdown to ${filePath}`;

            case 'noti':
                await exportNoteToNotiToPath(note, filePath);
                return `Success: Note "${note.title}" exported as Noti format to ${filePath}`;

            default:
                throw new Error(`Unsupported format: ${format}`);
        }
    } catch (error: any) {
        console.error(`Error exporting note ${id} as ${format}:`, error);
        throw new Error(`Failed to export note: ${error.message}`);
    }
};

// Helper function to recursively get all notes in a folder
const getAllNotesInFolder = async (
    folderId: number,
    includeSubfolders: boolean = true,
    includeNotes: boolean = true,
    folderPath: string = ''
): Promise<Array<Note & { folderPath?: string }>> => {
    let notes: Array<Note & { folderPath?: string }> = [];

    // Get the current folder name
    let currentFolderName = '';
    try {
        const folder = await getFolderById(folderId);
        if (folder) {
            currentFolderName = sanitizeFilename(folder.name);
        }
    } catch (error) {
        console.error(`Error getting folder name for ID ${folderId}:`, error);
    }

    // Update the folder path
    const currentPath = folderPath
        ? `${folderPath}/${currentFolderName}`
        : currentFolderName;

    if (includeNotes) {
        // Get notes directly in this folder
        const folderNotes = await getNotesByFolderId(folderId);
        // Add folder path to each note
        notes = [...notes, ...folderNotes.map(note => ({
            ...note,
            folderPath: currentPath
        }))];
    }

    if (includeSubfolders) {
        // Get subfolders
        const subfolders = await getChildFolders(folderId);

        // Recursively get notes from each subfolder
        for (const subfolder of subfolders) {
            if (subfolder.id) {
                const subfolderNotes = await getAllNotesInFolder(
                    subfolder.id,
                    includeSubfolders,
                    includeNotes,
                    currentPath
                );
                notes = [...notes, ...subfolderNotes];
            }
        }
    }

    return notes;
};

// Helper function to create a zip file with multiple files
const createZipArchive = async (
    files: { path: string; name: string; folderPath?: string }[],
    zipPath: string
): Promise<string> => {
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', {
        zlib: { level: 9 } // Maximum compression
    });

    archive.pipe(output);

    // Add each file to the archive, preserving folder structure if present
    for (const file of files) {
        const zipFilePath = file.folderPath
            ? `${file.folderPath}/${file.name}`
            : file.name;

        archive.file(file.path, { name: zipFilePath });
    }

    await archive.finalize();

    return zipPath;
};

// Export multiple notes or folders
export const exportMultiple = async (
    items: { id: number; type: 'folder' | 'note'; name: string }[],
    format: string,
    options: { includeSubfolders?: boolean; includeNotes?: boolean } = {}
): Promise<string> => {
    try {
        if (!Array.isArray(items)) {
            throw new Error('Items must be an array');
        }

        if (items.length === 0) {
            throw new Error('Items must be a non-empty array');
        }

        // Validate items to ensure they're properly formatted
        const validItems = items.filter(item => {
            if (!item || typeof item !== 'object') return false;
            if (typeof item.id !== 'number') return false;
            if (item.type !== 'folder' && item.type !== 'note') return false;
            if (typeof item.name !== 'string') return false;
            return true;
        });

        if (validItems.length === 0) {
            throw new Error('No valid items to export');
        }

        if (!['pdf', 'md', 'noti'].includes(format)) {
            throw new Error('Unsupported export format. Supported formats are: pdf, md, noti');
        }

        console.log(`Exporting ${validItems.length} items as ${format}`);
        console.log('Export options:', options);        // Set default options
        const includeSubfolders = options.includeSubfolders !== false;
        const includeNotes = options.includeNotes !== false;

        // Collect all notes to export
        let notesToExport: Note[] = [];

        for (const item of validItems) {
            if (item.type === 'note') {
                // Add note directly
                try {
                    const note = await getNoteById(item.id);
                    if (note) {
                        notesToExport.push(note);
                    } else {
                        console.warn(`Note with ID ${item.id} not found, skipping`);
                    }
                } catch (error) {
                    console.error(`Error fetching note with ID ${item.id}:`, error);
                    // Continue with other items
                }
            } else if (item.type === 'folder') {
                // Get all notes in the folder (and subfolders if includeSubfolders is true)
                try {
                    const folderNotes = await getAllNotesInFolder(
                        item.id,
                        includeSubfolders,
                        includeNotes
                    );
                    notesToExport = [...notesToExport, ...folderNotes];
                } catch (error) {
                    console.error(`Error getting notes from folder ${item.id}:`, error);
                    // Continue with other items
                }
            }
        }        // If no notes to export, return early
        if (notesToExport.length === 0) {
            return 'No notes found to export';
        }        // If only one note and not coming from a folder, export it directly
        if (notesToExport.length === 1 && validItems.length === 1 && validItems[0].type === 'note') {
            return await exportNote(notesToExport[0].id as number, format);
        }

        // Otherwise, export all notes and zip them
        const exportedFiles: { path: string; name: string; folderPath?: string }[] = []; for (const note of notesToExport) {
            try {
                let outputPath: string;
                const sanitizedTitle = sanitizeFilename(note.title);

                switch (format) {
                    case 'pdf':
                        outputPath = await exportNoteToPdf(note);
                        exportedFiles.push({
                            path: outputPath,
                            name: `${sanitizedTitle}.pdf`,
                            folderPath: (note as any).folderPath
                        });
                        break;

                    case 'md':
                        outputPath = await exportNoteToMarkdown(note);
                        exportedFiles.push({
                            path: outputPath,
                            name: `${sanitizedTitle}.md`,
                            folderPath: (note as any).folderPath
                        });
                        break;

                    case 'noti':
                        outputPath = await exportNoteToNoti(note);
                        exportedFiles.push({
                            path: outputPath,
                            name: `${sanitizedTitle}.noti`,
                            folderPath: (note as any).folderPath
                        });
                        break;
                }
            } catch (error) {
                console.error(`Error exporting note ${note.id}:`, error);
                // Continue with other notes even if one fails
            }
        }        // Create a zip file with all exported files
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const zipFileName = `noti_export_${timestamp}.zip`;

        // Show save dialog to let user choose where to save the zip
        const { canceled, filePath } = await dialog.showSaveDialog({
            defaultPath: path.join(app.getPath('documents'), zipFileName),
            filters: [
                { name: 'ZIP Archive', extensions: ['zip'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['createDirectory', 'showOverwriteConfirmation']
        });

        if (canceled || !filePath) {
            // Clean up temporary files
            for (const file of exportedFiles) {
                try {
                    fs.unlinkSync(file.path);
                } catch (error) {
                    console.error(`Error deleting temporary file ${file.path}:`, error);
                }
            }
            return 'Export cancelled by user';
        }

        const zipPath = await createZipArchive(exportedFiles, filePath);

        // Clean up individual files after zipping
        for (const file of exportedFiles) {
            try {
                fs.unlinkSync(file.path);
            } catch (error) {
                console.error(`Error deleting temporary file ${file.path}:`, error);
            }
        }

        return `Success: Exported ${notesToExport.length} notes as ${format} to ${zipPath}`;
    } catch (error: any) {
        console.error(`Error exporting multiple items as ${format}:`, error);
        throw new Error(`Failed to export items: ${error.message}`);
    }
};

// Import note content with validation
export const importNote = async (content: string, format: string, title: string): Promise<Note> => {
    try {
        // Validate input parameters
        if (typeof content !== 'string') {
            throw new Error('Content must be a string');
        }

        if (typeof format !== 'string') {
            throw new Error('Format must be a string');
        }

        if (typeof title !== 'string' || title.trim() === '') {
            throw new Error('Title must be a non-empty string');
        }

        // Validate file format
        const allowedFormats = ['md', 'pdf', 'noti', 'txt'];
        if (!allowedFormats.includes(format.toLowerCase())) {
            throw new Error(`Unsupported import format. Supported formats are: ${allowedFormats.join(', ')}`);
        }

        // Handle PDF format (not yet implemented)
        if (format.toLowerCase() === 'pdf') {
            throw new Error('PDF import is not yet implemented. Please use .md or .noti files.');
        }

        let noteContent = content;
        let htmlContent = `<p>${escapeHtml(content)}</p>`; // Default escaped conversion
        let metadata: any = {};
        let mediaToImport: any[] = [];

        // Handle different file formats
        if (format.toLowerCase() === 'noti') {
            try {
                // Parse .noti file as JSON
                const notiData = JSON.parse(content);
                
                // Validate .noti file structure
                if (!notiData.version || notiData.version !== "1.0" || notiData.type !== "noti-note") {
                    throw new Error('Invalid .noti file format: unsupported version or type');
                }
                
                // Validate required fields
                if (!notiData.content || !notiData.metadata) {
                    throw new Error('Invalid .noti file format: missing required fields');
                }
                
                // File size security check (limit to 50MB)
                const maxFileSize = 50 * 1024 * 1024; // 50MB
                if (content.length > maxFileSize) {
                    throw new Error(`File size exceeds maximum allowed size of 50MB`);
                }
                
                // Verify integrity if present
                if (notiData.integrity) {
                    const contentToHash = JSON.stringify({
                        version: notiData.version,
                        type: notiData.type,
                        schema: notiData.schema,
                        metadata: notiData.metadata,
                        content: notiData.content,
                        media: notiData.media
                    });
                    const calculatedHash = crypto.createHash('sha256').update(contentToHash).digest('hex');
                    
                    if (calculatedHash !== notiData.integrity.content_hash) {
                        console.warn('Integrity check failed for .noti file, but continuing with import');
                    }
                }
                
                // Extract content - prefer HTML, fallback to markdown
                if (notiData.content.html) {
                    htmlContent = notiData.content.html;
                    noteContent = notiData.content.markdown || notiData.content.plain_text || '';
                } else if (notiData.content.markdown) {
                    noteContent = notiData.content.markdown;
                    htmlContent = md.render(noteContent);
                } else if (notiData.content.plain_text) {
                    noteContent = notiData.content.plain_text;
                    htmlContent = `<p>${escapeHtml(noteContent)}</p>`;
                }
                
                // Use metadata from file
                metadata = notiData.metadata;
                if (metadata.title && title === 'Imported Note') {
                    title = metadata.title;
                }
                
                // Store media for later processing
                if (notiData.media && Array.isArray(notiData.media)) {
                    mediaToImport = notiData.media;
                }
            } catch (parseError) {
                console.warn('Failed to parse .noti file:', parseError);
                throw new Error('Invalid .noti file format');
            }
        } else if (format.toLowerCase() === 'md') {
            // For markdown files, convert to HTML
            noteContent = content;
            htmlContent = md.render(noteContent);
        } else if (format.toLowerCase() === 'txt') {
            // For text files, keep the content as-is
            noteContent = content;
            htmlContent = `<p>${escapeHtml(content)}</p>`;
        }

        // Create the note object with metadata
        const newNote: Note = {
            title: title.trim(),
            content: noteContent || '',  // Ensure content is always a string
            html_content: htmlContent,
            type: metadata.type || 'text',
            color: metadata.color || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // Create the note using the existing validation function
        const createdNote = await createNoteWithValidation(newNote);

        // Import media files if present
        if (mediaToImport.length > 0 && createdNote.id) {
            for (const media of mediaToImport) {
                try {
                    if (media.embedded && media.data && media.file_type.startsWith('image/')) {
                        // Convert base64 back to buffer
                        const buffer = Buffer.from(media.data, 'base64');
                        
                        // Save media file associated with the new note
                        await mediaApi.saveMediaFile(
                            createdNote.id,
                            buffer,
                            media.file_name,
                            media.file_type,
                            null,
                            false
                        );
                    }
                } catch (mediaError) {
                    console.error('Error importing media file:', mediaError);
                    // Continue with import even if media fails
                }
            }
            
            // Refresh the note to get updated HTML content with proper media URLs
            const updatedNote = await getNoteById(createdNote.id);
            if (updatedNote) {
                return updatedNote;
            }
        }

        return createdNote;

    } catch (error: any) {
        console.error('Error importing note:', error);
        throw new Error(`Failed to import note: ${error.message}`);
    }
};

// Function to link a note to a book (useful when moving notes to book folders)
export const linkNoteToBook = async (noteId: number, bookId: number | null): Promise<Note> => {
    // Validate noteId
    if (!Number.isInteger(noteId) || noteId <= 0) {
        throw new Error('Note ID must be a positive integer');
    }

    try {
        // Get the note to ensure it exists
        const note = await getNoteById(noteId);
        if (!note) {
            throw new Error(`Note with ID ${noteId} not found`);
        }

        // If bookId is provided, validate the book exists
        if (bookId !== null) {
            if (!Number.isInteger(bookId) || bookId <= 0) {
                throw new Error('Book ID must be a positive integer or null');
            }

            const book = await getBookById(bookId);
            if (!book) {
                throw new Error(`Book with ID ${bookId} not found`);
            }
        }

        // Update the note's book_id
        const updatedNote = await updateNote(noteId, { book_id: bookId });


        console.log(`${bookId ? 'Linked' : 'Unlinked'} note "${note.title}" (ID: ${noteId}) ${bookId ? `to book ID ${bookId}` : 'from book'}`);
        return updatedNote;

    } catch (error: any) {
        console.error('Error in linkNoteToBook:', error);
        throw new Error(`Failed to ${bookId ? 'link' : 'unlink'} note ${bookId ? 'to' : 'from'} book: ${error.message}`);
    }
};

// Function to automatically link notes to books when they are moved to book folders
export const autoLinkNotesToBooksInFolder = async (folderId: number): Promise<{ linked: number; errors: number }> => {
    try {
        // Get the folder to check if it's a book folder
        const folder = await getFolderById(folderId);
        if (!folder) {
            throw new Error(`Folder with ID ${folderId} not found`);
        }

        // If this folder is not linked to a book, nothing to do
        if (!folder.book_id) {
            console.log(`Folder "${folder.name}" (ID: ${folderId}) is not linked to a book`);
            return { linked: 0, errors: 0 };
        }

        // Get all notes in this folder
        const notes = await getNotesByFolderId(folderId);
        let linked = 0;
        let errors = 0;

        console.log(`Auto-linking ${notes.length} notes in folder "${folder.name}" to book ID ${folder.book_id}`);

        for (const note of notes) {
            try {
                // Only link if the note is not already linked to this book
                if (note.book_id !== folder.book_id) {
                    await updateNote(note.id!, { book_id: folder.book_id });


                    linked++;
                    console.log(`Auto-linked note "${note.title}" (ID: ${note.id}) to book ID ${folder.book_id}`);
                }
            } catch (error) {
                console.error(`Failed to auto-link note "${note.title}" (ID: ${note.id}) to book ID ${folder.book_id}:`, error);
                errors++;
            }
        }

        console.log(`Auto-linking completed: ${linked} linked, ${errors} errors`);
        return { linked, errors };

    } catch (error: any) {
        console.error('Error in autoLinkNotesToBooksInFolder:', error);
        throw new Error(`Failed to auto-link notes to books in folder: ${error.message}`);
    }
};

// Delete a note with backup event emission and deletion tracking
export const deleteNoteWithValidation = async (id: number): Promise<void> => {
    // Ensure id is a number
    if (typeof id !== 'number') {
        throw new Error('Note ID is required and must be a number');
    }

    try {
        // Get the note before deletion to have its data for the event and tracking
        const note = await getNoteById(id);
        if (!note) {
            throw new Error(`Note with ID ${id} not found`);
        }

        // Delete the note
        await deleteNote(id);

        // Notify database hooks about note deletion
        notifyNoteChange('delete', id, {
            title: note.title,
            folder_id: note.folder_id,
            book_id: note.book_id
        });


    } catch (error: any) {
        console.error('Error in deleteNoteWithValidation:', error);
        throw new Error(`Failed to delete note ${id}: ${error.message}`);
    }
};

/**
 * Calculate the backup path where a note would be stored
 * @param note - The note to calculate path for
 * @returns Promise<string> - The backup path
 */
const calculateNoteBackupPath = async (note: Note): Promise<string> => {
    try {
        // Get folder hierarchy to build backup path
        const folderPath = await calculateNoteFolderPath(note);
        const sanitizedTitle = sanitizeFilename(note.title || 'untitled');
        
        if (folderPath) {
            return `${folderPath}/${sanitizedTitle}.noti.json`; // Assuming .noti format
        } else {
            return `${sanitizedTitle}.noti.json`; // Root level
        }
    } catch (error) {
        console.warn('Error calculating note backup path:', error);
        return sanitizeFilename(note.title || 'untitled') + '.noti.json';
    }
};

/**
 * Calculate the folder hierarchy path for a note
 * @param note - The note to calculate folder path for
 * @returns Promise<string> - The folder path
 */
const calculateNoteFolderPath = async (note: Note): Promise<string> => {
    try {
        if (!note.folder_id) {
            return ''; // Root level
        }

        const folder = await getFolderById(note.folder_id);
        if (!folder) {
            return '';
        }

        // Build folder hierarchy path
        const pathParts: string[] = [];
        let currentFolder = folder;

        while (currentFolder) {
            pathParts.unshift(sanitizeFilename(currentFolder.name));
            
            if (currentFolder.parent_id) {
                currentFolder = await getFolderById(currentFolder.parent_id);
            } else {
                break;
            }
        }

        return pathParts.join('/');
    } catch (error) {
        console.warn('Error calculating note folder path:', error);
        return '';
    }
};

// Export default functions that will be used by IPC handlers
export default {
    createNote: createNoteWithValidation,
    createNoteForBook,
    getAllNotes: getNotesWithMetadata, // Use the enhanced getter
    getNoteById, // Assuming getNoteById from database-api is sufficient
    getNotesByFolderId, // Assuming direct use is okay, or wrap if metadata needed
    getNotesByBookId, // Assuming direct use is okay, or wrap if metadata needed
    updateNote: updateNoteWithValidation,
    deleteNote: deleteNoteWithValidation,
    searchNotes,
    exportNote,
    exportMultiple,
    importNote,
    linkNoteToBook,
    autoLinkNotesToBooksInFolder
};