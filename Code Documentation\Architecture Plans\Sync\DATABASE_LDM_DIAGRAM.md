# Noti Application Database - Logical Data Model (LDM)

This document presents a comprehensive Entity Relationship Diagram (ERD) for the Noti application database using Mermaid syntax. The database supports a note-taking application with books, folders, timer functionality, settings management, and backup capabilities.

## Database Overview

The Noti application database consists of **15 interconnected tables** that support the following core business domains:

- **Content Management**: Notes, folders, and books organization
- **Media Management**: File attachments and covers
- **Timer & Productivity**: Pomodoro sessions and cycle tracking
- **User Preferences**: Settings and theme management
- **Activity Tracking**: Recent items and search history
- **Data Protection**: Backup metadata and deletion tracking
- **Export Capabilities**: Export history and file management

## Entity Relationship Diagram

```mermaid
erDiagram
    notes {
        INTEGER id PK "Auto-increment primary key"
        TEXT title NOT_NULL "Note title (required, max 255 chars)"
        TEXT content "Note content in plain text"
        TEXT html_content "Note content in HTML format"
        INTEGER folder_id FK "Reference to parent folder (nullable)"
        INTEGER book_id FK "Reference to associated book (nullable)"
        TEXT type "Note type (default: 'text')"
        TEXT color "Note color for visual organization"
        INTEGER order "Display order within container"
        TIMESTAMP last_viewed_at "Last time note was viewed"
        TIMESTAMP created_at "Creation timestamp (ISO format)"
        TIMESTAMP updated_at "Last modification timestamp (ISO format)"
    }

    folders {
        INTEGER id PK "Auto-increment primary key"
        TEXT name NOT_NULL "Folder name (required)"
        INTEGER parent_id FK "Reference to parent folder (nullable)"
        INTEGER book_id FK "Reference to associated book (nullable)"
        TEXT color "Folder color for visual organization"
        INTEGER order "Display order within parent"
        TIMESTAMP created_at "Creation timestamp (ISO format)"
        TIMESTAMP updated_at "Last modification timestamp (ISO format)"
    }

    books {
        INTEGER id PK "Auto-increment primary key"
        TEXT title NOT_NULL "Book title (required)"
        TEXT author "Book author name"
        TEXT isbn "International Standard Book Number"
        TEXT cover_url "URL to book cover image"
        TEXT publication_date "Book publication date"
        TEXT description "Book description or summary"
        INTEGER page_count "Total number of pages"
        INTEGER current_page "Current reading progress"
        INTEGER rating "User rating (1-5 scale)"
        TEXT language "Book language"
        TEXT genres "Book genres (JSON string)"
        TEXT olid "OpenLibrary identifier"
        TEXT status "Reading status (default: 'unread')"
        TEXT custom_fields "Custom metadata (JSON string)"
        TIMESTAMP created_at "Creation timestamp (ISO format)"
        TIMESTAMP updated_at "Last modification timestamp (ISO format)"
    }

    recent_items {
        INTEGER id PK "Auto-increment primary key"
        INTEGER note_id FK "Reference to recently viewed note (nullable)"
        INTEGER book_id FK "Reference to recently viewed book (nullable)"
        TIMESTAMP viewed_at NOT_NULL "Timestamp when item was last viewed"
    }

    theme_settings {
        INTEGER id PK "Auto-increment primary key"
        TEXT theme_name NOT_NULL "Theme name identifier"
        BOOLEAN is_active NOT_NULL "Whether theme is currently active (default: 0)"
        TIMESTAMP updated_at "Last modification timestamp"
    }

    exports {
        INTEGER id PK "Auto-increment primary key"
        INTEGER note_id NOT_NULL FK "Reference to exported note"
        TEXT export_path NOT_NULL "File system path to exported file"
        TEXT export_type NOT_NULL "Export format type (e.g., 'pdf', 'md', 'html')"
        TIMESTAMP created_at "Export creation timestamp"
    }

    search_history {
        INTEGER id PK "Auto-increment primary key"
        TEXT query NOT_NULL "Search query string"
        TIMESTAMP created_at "Search timestamp"
    }

    settings {
        INTEGER id PK "Auto-increment primary key"
        TEXT key NOT_NULL UNIQUE "Setting key identifier"
        TEXT value_json "Setting value stored as JSON string"
        TEXT category "Setting category for organization"
        TIMESTAMP updated_at "Last modification timestamp"
    }

    media_files {
        INTEGER id PK "Auto-increment primary key"
        INTEGER note_id FK "Reference to associated note (nullable)"
        INTEGER book_id FK "Reference to associated book (nullable)"
        TEXT file_path NOT_NULL "File system path to media file"
        TEXT file_name NOT_NULL "Original filename"
        TEXT file_type "MIME type or file extension"
        INTEGER file_size "File size in bytes"
        BOOLEAN is_cover "Whether file is a book cover (default: 0)"
        TIMESTAMP created_at "Upload timestamp"
    }

    timer_sessions {
        INTEGER id PK "Auto-increment primary key"
        TIMESTAMP start_time NOT_NULL "Session start time"
        TIMESTAMP end_time "Session end time (nullable for active sessions)"
        INTEGER duration "Session duration in seconds"
        TEXT session_type "Type of session (e.g., 'work', 'break')"
        BOOLEAN is_completed "Whether session was completed (default: 0)"
        TIMESTAMP created_at "Session creation timestamp"
        TEXT focus "Focus area or subject"
        TEXT category "Session category (default: 'General')"
        TIMESTAMP updated_at "Last modification timestamp"
        TEXT session_name "User-defined session name"
        INTEGER pomodoro_cycles_completed "Number of completed pomodoro cycles (default: 0)"
        BOOLEAN is_user_session "Whether session is user-created (default: 1)"
    }

    pomodoro_cycles {
        INTEGER id PK "Auto-increment primary key"
        INTEGER session_id NOT_NULL FK "Reference to parent timer session"
        TEXT cycle_type NOT_NULL "Cycle type: 'pomodoro', 'short_break', 'long_break'"
        TIMESTAMP start_time NOT_NULL "Cycle start time"
        TIMESTAMP end_time "Cycle end time (nullable for active cycles)"
        INTEGER duration "Cycle duration in seconds"
        BOOLEAN completed "Whether cycle was completed (default: 0)"
        TIMESTAMP created_at "Cycle creation timestamp"
    }

    timer_settings {
        INTEGER id PK "Auto-increment primary key"
        INTEGER work_duration NOT_NULL "Work session duration in seconds (default: 1500)"
        INTEGER short_break_duration NOT_NULL "Short break duration in seconds (default: 300)"
        INTEGER long_break_duration NOT_NULL "Long break duration in seconds (default: 900)"
        INTEGER long_break_interval NOT_NULL "Cycles before long break (default: 4)"
        BOOLEAN auto_start_breaks "Auto-start break sessions (default: 1)"
        BOOLEAN auto_start_work "Auto-start work sessions (default: 1)"
        TIMESTAMP created_at "Settings creation timestamp"
        TIMESTAMP updated_at "Last modification timestamp"
    }

    backup_metadata {
        INTEGER id PK "Auto-increment primary key"
        TEXT backup_location NOT_NULL "Backup destination path"
        TIMESTAMP last_backup_timestamp "Timestamp of last successful backup"
        TEXT backup_type NOT_NULL "Backup type: 'manual' or 'auto'"
        TEXT status NOT_NULL "Backup status: 'in_progress', 'completed', 'failed'"
        INTEGER items_backed_up "Number of items backed up (default: 0)"
        INTEGER errors_count "Number of errors encountered (default: 0)"
        TIMESTAMP created_at "Backup initiation timestamp"
        TIMESTAMP completed_at "Backup completion timestamp"
    }

    backup_items {
        INTEGER id PK "Auto-increment primary key"
        INTEGER backup_metadata_id NOT_NULL FK "Reference to backup metadata"
        TEXT item_type NOT_NULL "Item type: 'note' or 'folder'"
        INTEGER item_id NOT_NULL "Reference to original item ID"
        TEXT item_path NOT_NULL "Backup file path"
        TIMESTAMP last_modified NOT_NULL "Original item last modification time"
        TEXT backup_status NOT_NULL "Item backup status: 'pending', 'completed', 'failed', 'skipped'"
        TEXT error_message "Error message if backup failed"
        TIMESTAMP created_at "Backup item creation timestamp"
    }

    backup_deletions {
        INTEGER id PK "Auto-increment primary key"
        TEXT item_type NOT_NULL "Deleted item type: 'note' or 'folder'"
        INTEGER item_id NOT_NULL "ID of deleted item"
        TEXT item_name NOT_NULL "Name of deleted item"
        TEXT backup_path "Path to backed up item"
        TEXT folder_path "Original folder path"
        TIMESTAMP deleted_at NOT_NULL "Deletion timestamp (default: CURRENT_TIMESTAMP)"
        BOOLEAN processed "Whether deletion has been processed (default: 0)"
        TIMESTAMP processed_at "Processing timestamp"
        TEXT processing_notes "Notes about deletion processing"
        TIMESTAMP created_at "Record creation timestamp"
    }

    %% Relationships
    folders ||--o{ notes : "contains"
    books ||--o{ notes : "categorizes"
    folders ||--o{ folders : "hierarchical"
    books ||--o{ folders : "organizes"
    notes ||--o{ recent_items : "tracks"
    books ||--o{ recent_items : "tracks"
    notes ||--o{ exports : "generates"
    notes ||--o{ media_files : "attachments"
    books ||--o{ media_files : "covers"
    timer_sessions ||--o{ pomodoro_cycles : "contains"
    backup_metadata ||--o{ backup_items : "details"
```

## Key Database Features

### 1. **Hierarchical Organization**
- **Folders**: Self-referencing hierarchy with `parent_id`
- **Books**: Can have associated folders for organization
- **Notes**: Can belong to both folders and books simultaneously

### 2. **Foreign Key Constraints**
- `ON DELETE CASCADE`: Child folders deleted when parent is deleted
- `ON DELETE SET NULL`: Notes unlinked when folder/book is deleted
- `ON DELETE CASCADE`: Cleanup of dependent records (exports, media, etc.)

### 3. **Data Integrity**
- **Check Constraints**: Enum validation for cycle types, backup statuses
- **Unique Constraints**: Setting keys, Books root folder prevention
- **NOT NULL Constraints**: Essential fields like titles, timestamps

### 4. **Performance Optimization**
- **Indexes**: Created on foreign keys, timestamps, and frequently queried fields
- **Composite Indexes**: Multi-column indexes for complex queries
- **WAL Mode**: Write-Ahead Logging for better concurrency

### 5. **Audit Trail**
- **Timestamps**: All entities track creation and modification times
- **Backup Tracking**: Complete history of backup operations
- **Deletion Tracking**: Soft delete tracking for recovery purposes

## Business Rules & Constraints

### Content Management
- Notes can exist independently or within folders/books
- Folders support unlimited nesting depth (with cycle prevention)
- Books automatically get a dedicated folder structure

### Timer System
- Timer sessions can contain multiple pomodoro cycles
- Cycles must belong to a valid session
- Settings provide default durations for all timer types

### Backup System
- Each backup operation creates metadata and item records
- Deletion tracking enables recovery of accidentally deleted items
- Items can have different backup statuses for granular control

### Media Management
- Media files can be attached to notes or books
- Book covers are special media files marked with `is_cover`
- File paths and metadata are preserved for proper linking

## Database Statistics

- **Total Tables**: 15
- **Total Relationships**: 12 foreign key relationships
- **Hierarchical Structures**: 2 (folders, timer sessions → cycles)
- **Audit Tables**: 3 (backup_metadata, backup_items, backup_deletions)
- **Configuration Tables**: 3 (settings, timer_settings, theme_settings)
- **Content Tables**: 4 (notes, folders, books, media_files)
- **Activity Tracking**: 3 (recent_items, search_history, exports)

This comprehensive database design supports the full functionality of the Noti application while maintaining data integrity, performance, and extensibility for future enhancements.