<template>
  <div class="dashboard-charts">
    <div class="section-header">
      <h2 class="section-title">Analytics Overview</h2>
      <button
        class="refresh-button"
        @click="refreshAllCharts"
        :disabled="isRefreshing"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          :class="{ 'rotating': isRefreshing }"
        >
          <path
            d="M13.65 2.35C12.18 0.88 10.21 0 8 0C3.58 0 0 3.58 0 8s3.58 8 8 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L9 5h7V-2l-2.35 2.35z"
            fill="var(--color-icon-fill)"
          />
        </svg>
      </button>
    </div>
    
    <div class="charts-grid">
      <!-- Row 1: Daily Focus and Activity charts -->
      <div class="chart-row">
        <div class="chart-item chart-item--large">
          <DailyFocusChart ref="dailyFocusChart" />
        </div>
        <div class="chart-item chart-item--medium">
          <ActivityChart ref="activityChart" />
        </div>
      </div>
      
      <!-- Row 2: Weekly Progress and Category charts -->
      <div class="chart-row">
        <div class="chart-item chart-item--large">
          <WeeklyProgressChart ref="weeklyProgressChart" />
        </div>
        <div class="chart-item chart-item--medium">
          <CategoryChart ref="categoryChart" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DailyFocusChart from '../timer/charts/DailyFocusChart.vue'
import WeeklyProgressChart from '../timer/charts/WeeklyProgressChart.vue'
import CategoryChart from '../timer/charts/CategoryChart.vue'
import ActivityChart from './charts/ActivityChart.vue'

// Chart component refs
const dailyFocusChart = ref<InstanceType<typeof DailyFocusChart> | null>(null)
const weeklyProgressChart = ref<InstanceType<typeof WeeklyProgressChart> | null>(null)
const categoryChart = ref<InstanceType<typeof CategoryChart> | null>(null)
const activityChart = ref<InstanceType<typeof ActivityChart> | null>(null)

const isRefreshing = ref(false)

const refreshAllCharts = async () => {
  if (isRefreshing.value) return
  
  isRefreshing.value = true
  
  try {
    // Refresh all charts in parallel
    await Promise.all([
      dailyFocusChart.value?.refreshData(),
      weeklyProgressChart.value?.refreshData(),
      categoryChart.value?.refreshData(),
      activityChart.value?.refreshData()
    ])
  } catch (error) {
    console.error('Failed to refresh charts:', error)
  } finally {
    // Add a small delay to show the refresh animation
    setTimeout(() => {
      isRefreshing.value = false
    }, 500)
  }
}

// Expose refresh method for parent components
defineExpose({
  refreshCharts: refreshAllCharts
})
</script>

<style scoped>
.dashboard-charts {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--color-text-primary);
}

.refresh-button {
  background: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover:not(:disabled) {
  background-color: var(--color-card-hover-bg);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-button svg {
  width: 16px;
  height: 16px;
}

.refresh-button svg.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.charts-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
}

.chart-item {
  min-height: 0; /* Allow flex items to shrink */
}

.chart-item--large {
  /* Takes up 2/3 of the width */
}

.chart-item--medium {
  /* Takes up 1/3 of the width */
}

/* Responsive design */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
}
</style>
