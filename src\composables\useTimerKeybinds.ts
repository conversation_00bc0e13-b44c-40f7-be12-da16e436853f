// Timer view keybinds composable
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useTimerKeybinds() {
  const isActive = ref(false)

  // Timer functions (to be passed from TimerView or PomodoroTimer component)
  let toggleTimer: () => void = () => console.log('⏯️ Toggle timer')
  let resetTimer: () => void = () => console.log('🔄 Reset timer')
  let skipTimer: () => void = () => console.log('⏭️ Skip timer')
  let switchToPomodoro: () => void = () => console.log('🍅 Switch to Pomodoro')
  let switchToShortBreak: () => void = () => console.log('☕ Switch to Short Break')
  let switchToLongBreak: () => void = () => console.log('🛋️ Switch to Long Break')
  let createNewSession: () => void = () => console.log('➕ Create new session')
  let endCurrentSession: () => void = () => console.log('🛑 End current session')
  let openTimerSettings: () => void = () => console.log('⚙️ Open timer settings')

  // Register timer-specific keybinds
  const registerTimerKeybinds = () => {
    console.log('⏰ Registering timer keybinds...')

    // Core timer controls
    globalKeybindManager.register({
      key: 'space',
      handler: (context, event) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          // Prevent default space behavior (scrolling)
          event?.preventDefault()
          toggleTimer()
        }
      },
      description: 'Start/pause timer',
      category: KeybindCategory.TIMER,
      priority: 'high',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'r',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          resetTimer()
        }
      },
      description: 'Reset timer',
      category: KeybindCategory.TIMER,
      priority: 'high',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 's',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          skipTimer()
        }
      },
      description: 'Skip timer',
      category: KeybindCategory.TIMER,
      priority: 'high',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    // Timer mode switching
    globalKeybindManager.register({
      key: 'digit1',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          switchToPomodoro()
        }
      },
      description: 'Switch to Pomodoro mode',
      category: KeybindCategory.TIMER,
      priority: 'medium',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'digit2',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          switchToShortBreak()
        }
      },
      description: 'Switch to Short Break mode',
      category: KeybindCategory.TIMER,
      priority: 'medium',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    globalKeybindManager.register({
      key: 'digit3',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen && !context.editorFocused) {
          switchToLongBreak()
        }
      },
      description: 'Switch to Long Break mode',
      category: KeybindCategory.TIMER,
      priority: 'medium',
      enabled: true,
      context: { view: 'timer', modalOpen: false, editorFocused: false }
    })

    // Session management
    globalKeybindManager.register({
      key: 'ctrl+n',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen) {
          createNewSession()
        }
      },
      description: 'Create new session',
      category: KeybindCategory.TIMER,
      priority: 'medium',
      enabled: true,
      context: { view: 'timer', modalOpen: false }
    })

    globalKeybindManager.register({
      key: 'ctrl+e',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen) {
          endCurrentSession()
        }
      },
      description: 'End current session',
      category: KeybindCategory.TIMER,
      priority: 'medium',
      enabled: true,
      context: { view: 'timer', modalOpen: false }
    })

    // Settings
    globalKeybindManager.register({
      key: 'ctrl+shift+s',
      handler: (context) => {
        if (context.view === 'timer' && !context.modalOpen) {
          openTimerSettings()
        }
      },
      description: 'Open timer settings',
      category: KeybindCategory.TIMER,
      priority: 'low',
      enabled: true,
      context: { view: 'timer', modalOpen: false }
    })

    console.log('✅ Timer keybinds registered')
  }

  // Unregister timer keybinds
  const unregisterTimerKeybinds = () => {
    console.log('🗑️ Unregistering timer keybinds...')
    
    globalKeybindManager.unregister('space')
    globalKeybindManager.unregister('r')
    globalKeybindManager.unregister('s')
    globalKeybindManager.unregister('digit1')
    globalKeybindManager.unregister('digit2')
    globalKeybindManager.unregister('digit3')
    globalKeybindManager.unregister('ctrl+n')
    globalKeybindManager.unregister('ctrl+e')
    globalKeybindManager.unregister('ctrl+shift+s')
  }

  // Activate timer keybinds
  const activate = () => {
    if (!isActive.value) {
      registerTimerKeybinds()
      isActive.value = true
      console.log('🟢 Timer keybinds activated')
    }
  }

  // Deactivate timer keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterTimerKeybinds()
      isActive.value = false
      console.log('🔴 Timer keybinds deactivated')
    }
  }

  // Setup functions (to be called from TimerView)
  const setupTimerFunctions = (functions: {
    toggleTimer?: () => void
    resetTimer?: () => void
    skipTimer?: () => void
    switchToPomodoro?: () => void
    switchToShortBreak?: () => void
    switchToLongBreak?: () => void
    createNewSession?: () => void
    endCurrentSession?: () => void
    openTimerSettings?: () => void
  }) => {
    if (functions.toggleTimer) toggleTimer = functions.toggleTimer
    if (functions.resetTimer) resetTimer = functions.resetTimer
    if (functions.skipTimer) skipTimer = functions.skipTimer
    if (functions.switchToPomodoro) switchToPomodoro = functions.switchToPomodoro
    if (functions.switchToShortBreak) switchToShortBreak = functions.switchToShortBreak
    if (functions.switchToLongBreak) switchToLongBreak = functions.switchToLongBreak
    if (functions.createNewSession) createNewSession = functions.createNewSession
    if (functions.endCurrentSession) endCurrentSession = functions.endCurrentSession
    if (functions.openTimerSettings) openTimerSettings = functions.openTimerSettings
    
    console.log('🔧 Timer functions configured')
  }

  // Auto-activate when mounted (if currently in timer view)
  onMounted(() => {
    // Check current route and activate if in timer view
    const currentPath = window.location.hash.slice(1) || window.location.pathname
    if (currentPath.includes('/timer')) {
      activate()
    }
  })

  // Cleanup on unmount
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupTimerFunctions,
    registerTimerKeybinds,
    unregisterTimerKeybinds
  }
}