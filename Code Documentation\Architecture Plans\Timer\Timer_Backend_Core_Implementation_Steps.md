# Timer Backend Core Implementation Steps

This document outlines the specific steps required to implement the core Timer backend functionality and integrate it with the frontend. It is derived from the more comprehensive "Timer Backend Enhanced Plan & Dashboard Strategy" document but focuses solely on getting the timer feature operational, excluding advanced dashboard-specific work for this phase.

## Phase 1: Backend Foundation - Database & Core API Updates

**Objective:** Establish the necessary database schema and update backend API functions to support core timer operations with enriched data.

*   **Step 1.1: Enhance `timer_sessions` Table Schema**
    *   **File:** [`electron/main/database/database.ts`](electron/main/database/database.ts)
    *   **Action:** Add the following columns to the `timer_sessions` table using `ALTER TABLE` statements. Ensure these operations are idempotent (e.g., by checking if the column exists or handling "duplicate column name" errors gracefully).
        *   `focus TEXT NULLABLE`
        *   `category TEXT NULLABLE`
        *   `updated_at TIMESTAMP NULLABLE`
    *   **Details:** Refer to Section 2.4.2 of `Timer_Backend_Enhanced_Plan_and_Dashboard_Strategy.md`.
    *   **Justification:** To store user-defined focus/category for sessions and track last modification time.

*   **Step 1.2: Implement Basic Indexing for `timer_sessions`**
    *   **File:** [`electron/main/database/database.ts`](electron/main/database/database.ts)
    *   **Action:** Add `CREATE INDEX IF NOT EXISTS` statements for essential query performance.
        *   `idx_timer_sessions_start_time ON timer_sessions (start_time DESC)`
        *   `idx_timer_sessions_session_type ON timer_sessions (session_type)`
        *   `idx_timer_sessions_category ON timer_sessions (category)` (if categories are expected to be filtered often early on)
        *   `idx_timer_sessions_is_completed ON timer_sessions (is_completed)`
    *   **Details:** Refer to Section 2.4.3 of `Timer_Backend_Enhanced_Plan_and_Dashboard_Strategy.md`.
    *   **Justification:** Improve performance for common queries like fetching session history or basic stats.

*   **Step 1.3: Update Core Timer API Functions**
    *   **File:** [`electron/main/api/timer-api.ts`](electron/main/api/timer-api.ts)
    *   **Actions:**
        1.  Modify `startTimerSession`:
            *   Update signature to accept optional `focus?: string` and `category?: string`.
            *   Update `INSERT` statement to include these new fields.
            *   Ensure `created_at` and `updated_at` (initially same as `created_at`) are set.
        2.  Modify `endTimerSession`:
            *   Ensure `end_time`, `duration`, and `is_completed` are correctly set.
            *   Ensure `updated_at` is set to the current timestamp.
        3.  Review `getTimerSession`, `getTimerSessionsByDateRange`, `getTodayTimerSessions`, `getTimerStatsByDateRange`, `deleteTimerSession`:
            *   Ensure these functions correctly retrieve/handle the new `focus`, `category`, and `updated_at` fields. For `getTimerSessionsByDateRange`, ensure it can return these fields. Basic filtering by category/type in `getTimerStatsByDateRange` can be considered if simple.
    *   **Details:** Refer to Sections 2.5.1, 2.5.2, and 2.5.3 of `Timer_Backend_Enhanced_Plan_and_Dashboard_Strategy.md`.
    *   **Justification:** To make the backend API aware of and correctly handle the enriched session data.

*   **Step 1.4: Update Timer IPC Handlers**
    *   **File:** [`electron/main/ipc-handlers.ts`](electron/main/ipc-handlers.ts)
    *   **Action:** Update the IPC handlers corresponding to the modified API functions in `timer-api.ts` (e.g., `timer:start`, `timer:end`) to correctly pass through any new parameters (like `focus`, `category`) from the renderer process to the main process API calls.
    *   **Justification:** To ensure data from the frontend reaches the updated backend logic.

## Phase 2: Frontend Connectivity - API Bridge & Type Definitions

**Objective:** Establish the communication layer and type safety between the frontend and the newly updated backend timer API.

*   **Step 2.1: Define Timer-Related TypeScript Interfaces**
    *   **File:** [`src/types/electron-api.d.ts`](src/types/electron-api.d.ts)
    *   **Actions:**
        1.  Define the `TimerSession` interface, including `id`, `start_time`, `end_time`, `duration`, `session_type`, `is_completed`, `created_at`, `updated_at`, and the new `focus: string | null`, `category: string | null` fields.
        2.  Define the `TimerStats` interface (can be basic for now, matching `getTimerStatsByDateRange` output).
        3.  Define the `TimerSettings` interface.
        4.  Define the `TimerAPI` interface, outlining all functions that will be exposed to the frontend (e.g., `start`, `end`, `getSession`, `getSessionsByDateRange`, `getSettings`, `updateSettings`). Ensure function signatures match the updated backend API (e.g., `start` includes `focus` and `category`).
    *   **Details:** Refer to Section 1.1 of the Original Plan, adapted for new fields.
    *   **Justification:** Provides type safety for frontend development and clarifies the API contract.

*   **Step 2.2: Update Global Window Interface**
    *   **File:** [`src/types/electron-api.d.ts`](src/types/electron-api.d.ts)
    *   **Action:** Add `timer: TimerAPI;` to the `Window.db` interface declaration.
    *   **Details:** Refer to Section 1.2 of the Original Plan.
    *   **Justification:** Makes the Timer API accessible globally in the renderer process via `window.db.timer`.

*   **Step 2.3: Expose Timer API in Preload Bridge**
    *   **File:** [`electron/preload/api-bridge.ts`](electron/preload/api-bridge.ts)
    *   **Action:** Add a `timer` object to the `dbApi` export. This object should contain functions that map to `ipcRenderer.invoke` calls for each function defined in the `TimerAPI` interface. Ensure parameters (including new ones like `focus`, `category` for `start`) are passed correctly.
    *   **Details:** Refer to Section 1.3 of the Original Plan, ensuring signatures match the updated `TimerAPI`.
    *   **Justification:** Securely exposes main process timer functionalities to the renderer process.

*   **Step 2.4: Update `useElectronAPI` Hook**
    *   **File:** [`src/useElectronAPI.ts`](src/useElectronAPI.ts)
    *   **Action:** Add `TimerAPI` to the `ElectronAPI` interface and ensure the hook returns the `timer` API.
    *   **Details:** Refer to Section 1.4 of the Original Plan.
    *   **Justification:** Provides a clean, typed way to access the Electron API in Vue components.

## Phase 3: Core Timer Frontend Integration

**Objective:** Connect the existing Timer Vue components to the live backend API, replacing mock data and enabling real timer operations.

*   **Step 3.1: Update `AddSessionModal.vue`**
    *   **File:** [`src/components/modals/AddSessionModal.vue`](src/components/modals/AddSessionModal.vue)
    *   **Actions:**
        1.  Add input fields for `focus` (text input) and `category` (text input or dropdown if predefined categories are desired later).
        2.  When the session is started, ensure these `focus` and `category` values are collected and emitted/passed to the function that calls the backend.
    *   **Justification:** Allows users to input focus and category for new sessions.

*   **Step 3.2: Update `TimerView.vue` (Main Logic)**
    *   **File:** [`src/views/TimerView.vue`](src/views/TimerView.vue)
    *   **Actions:**
        1.  **Import Types:** Import `TimerSession`, `TimerStats` from `../types/electron-api`.
        2.  **Remove Mock Data:** Delete all mock data initializations for `activeSession`, `completedSessions`, and `stats`.
        3.  **Implement `startSession`:**
            *   Modify to accept `focus` and `category` (from `AddSessionModal.vue`).
            *   Call `window.db.timer.start(sessionType, focus, category)`.
            *   Update `activeSession.value` with the returned session from the backend.
            *   Handle potential errors from the API call.
        4.  **Implement `endSession`:**
            *   Call `window.db.timer.end(activeSession.value.id)`.
            *   On success, update `completedSessions.value` (potentially by re-fetching or adding the ended session).
            *   Clear `activeSession.value`.
            *   Handle potential errors.
        5.  **Implement `loadSessions`:**
            *   Fetch session history using `window.db.timer.getSessionsByDateRange(startDate, endDate)`.
            *   Ensure the displayed session cards in `SessionCard.vue` (via props) can show `focus` and `category`.
            *   Transform backend data to frontend display format as needed.
        6.  **Implement `loadStats`:**
            *   Fetch statistics using `window.db.timer.getStatsByDateRange(startDate, endDate)`.
            *   Update `stats.value`.
        7.  **Data Loading:** Call `loadSessions` and `loadStats` on component mount (`onMounted`) and potentially after actions like ending a session.
    *   **Justification:** Connects the main timer view to live backend data and operations.

*   **Step 3.3: Update `PomodoroTimer.vue` (Settings & Control)**
    *   **File:** [`src/components/timer/PomodoroTimer.vue`](src/components/timer/PomodoroTimer.vue)
    *   **Actions:**
        1.  **Settings Integration:**
            *   On mount, call `window.db.timer.getSettings()` to load timer durations, auto-start preferences, etc.
            *   Update local reactive variables (`pomodoroTime`, `shortBreakTime`, etc.) with these settings.
            *   When user changes settings (e.g., via `TimerSettingsModal.vue`), call `window.db.timer.updateSettings()` with the new values.
        2.  **Session Control:**
            *   Ensure it correctly triggers the `startSession` and `endSession` logic (likely managed in `TimerView.vue` through props/events or by directly calling `window.db.timer` methods if appropriate for component structure) when the pomodoro timer starts, ends, or is manually controlled.
    *   **Justification:** Makes timer behavior configurable and persistent, and links pomodoro cycles to backend sessions.

*   **Step 3.4: Update `SessionCard.vue` (Display)**
    *   **File:** [`src/components/timer/SessionCard.vue`](src/components/timer/SessionCard.vue)
    *   **Action:** Ensure props are updated/added to accept and display `focus` and `category` for each session.
    *   **Justification:** Displays the enriched session data in the history.

## Phase 4: Testing and Refinement

**Objective:** Ensure the implemented core timer backend and frontend integration are working correctly and robustly.

*   **Step 4.1: Unit Testing**
    *   Write/update unit tests for all modified functions in `timer-api.ts`.
    *   Test new logic for handling `focus`, `category`, and `updated_at`.
    *   Test database interactions (mocking `sqlite3` or using an in-memory DB).
*   **Step 4.2: Integration Testing**
    *   Test the full flow: Frontend Call -> Preload Bridge -> IPC Handler -> API Function -> Database.
    *   Verify data persistence and retrieval for sessions with new fields.
    *   Test settings save/load.
*   **Step 4.3: End-to-End (E2E) Testing**
    *   Use tools like Playwright or Spectron to simulate user workflows:
        *   Creating a new session with focus and category.
        *   Starting, pausing, and ending the timer.
        *   Verifying the session appears correctly in the history.
        *   Checking that basic stats in `TimerView.vue` update.
        *   Modifying timer settings and observing behavior changes.
*   **Step 4.4: User Acceptance Testing (Manual)**
    *   Perform manual walkthroughs of all timer functionalities.
    *   Check for data consistency and UI responsiveness.
    *   Test error handling scenarios.
    *   Verify behavior across application restarts (e.g., settings persistence, active session recovery if implemented).

This checklist provides a focused path to delivering the core timer functionality. Once these steps are completed, the application will have a fully operational timer feature, and the foundation for future dashboard development will be significantly stronger.