# Book Cover Storage Refactor & Delete Book Functionality

## Files Modified

### Backend Files
- `electron/main/database/database.ts`
- `electron/main/database/database-api.ts`
- `electron/main/api/media-api.ts`
- `electron/main/api/books-api.ts`

### Frontend Files
- `src/types/electron-api.d.ts`
- `src/components/books/BookCard.vue`
- `src/views/BooksView.vue`
- `src/components/modals/DeleteBookModal.vue` (new file)

## Section of App
**Books Management System** - Cover storage and book deletion functionality

## Issues Identified and Fixed

### 1. **Database Schema Inconsistency**
**Problem**: The database schema had inconsistent cover storage with both `cover_data` BLOB and `cover_path` columns in the books table, but these columns didn't actually exist in the user's database.

**Solution**: 
- Removed `cover_data` and `cover_path` columns from books table schema
- Updated `media_files` table to support book covers with `book_id` and `is_cover` columns
- Implemented proper foreign key relationships

### 2. **Inefficient Cover Storage**
**Problem**: Book covers were stored directly in the books table as BLOB data, which is inefficient and inconsistent with how other media files are handled.

**Solution**:
- Moved book cover storage to `media_files` table (consistent with notes media handling)
- Added `book_id` and `is_cover` fields to `media_files` table
- Implemented `saveBookCover()` and `getBookCover()` functions in media-api.ts

### 3. **API Fallback Issues**
**Problem**: The system always fell back to making API requests for covers instead of using stored data properly.

**Solution**:
- Updated `getBooksWithMetadata()` to fetch cover data from `media_files` table
- Added `cover_media_url` field to frontend Book interface
- Implemented proper priority system: `cover_media_url` > `cover_url`

### 4. **Missing Delete Book Functionality**
**Problem**: No way to delete books that were accidentally added (mentioned in TODO.md).

**Solution**:
- Created `DeleteBookModal.vue` component with confirmation dialog
- Added delete button to `BookCard.vue` component
- Implemented delete handlers in `BooksView.vue`
- Added proper error handling and UI feedback

## Technical Implementation Details

### Database Schema Changes

```sql
-- Books table (simplified)
CREATE TABLE books (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  author TEXT,
  isbn TEXT,
  cover_url TEXT,  -- Only for fallback/reference
  publication_date TEXT,
  description TEXT,
  page_count INTEGER,
  current_page INTEGER,
  rating INTEGER,
  language TEXT,
  genres TEXT,
  olid TEXT,
  status TEXT,
  custom_fields TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Media files table (enhanced)
CREATE TABLE media_files (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  note_id INTEGER,
  book_id INTEGER,           -- NEW: Support for book covers
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_type TEXT,
  file_size INTEGER,
  is_cover BOOLEAN DEFAULT 0, -- NEW: Flag for cover images
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
  FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);
```

### Cover Storage Flow

1. **Book Creation**: When a book is added with a cover URL:
   ```typescript
   // Download cover image
   const coverData = await downloadCoverImageData(bookData.cover_url);
   
   // Save to media_files table
   await saveBookCover(createdBook.id, coverData);
   ```

2. **Cover Retrieval**: When displaying books:
   ```typescript
   // Get cover from media_files
   const coverFile = await getBookCover(book.id);
   if (coverFile) {
     book.cover_media_url = filePathToMediaUrl(coverFile.file_path);
   }
   ```

3. **Frontend Display Priority**:
   ```typescript
   // BookCard.vue - Cover display logic
   if (props.book.cover_media_url) {
     // Use stored cover (highest priority)
     return { backgroundImage: `url(${props.book.cover_media_url})` }
   } else if (props.book.cover_url) {
     // Fallback to online URL
     return { backgroundImage: `url(${props.book.cover_url})` }
   }
   ```

### Delete Book Implementation

1. **UI Component**: `DeleteBookModal.vue`
   - Warning dialog with book title
   - Confirmation required
   - Loading state during deletion

2. **BookCard Integration**:
   - Delete button overlay on hover
   - Emits delete event to parent

3. **BooksView Handling**:
   - Shows confirmation modal
   - Calls API to delete book
   - Refreshes book list
   - Handles errors gracefully

## Benefits of This Refactor

1. **Consistency**: Book covers now use the same storage system as other media files
2. **Performance**: No more redundant API calls for stored covers
3. **Reliability**: Proper database schema without non-existent columns
4. **User Experience**: Added ability to delete accidentally added books
5. **Maintainability**: Cleaner separation of concerns between books and media storage

## Migration Notes

- Existing books with `cover_url` will continue to work (fallback mechanism)
- New books will automatically store covers in `media_files` table
- Old `cover_data` and `cover_path` columns are safely removed
- No data loss during migration

## Testing Recommendations

1. Test book creation with cover download
2. Verify cover display priority (stored > online > default)
3. Test book deletion with confirmation
4. Verify media file cleanup on book deletion
5. Test fallback to online covers for legacy books 