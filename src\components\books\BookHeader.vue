<template>
  <div class="book-header">
    <div class="header-row">
      <div class="search-bar" @click="focusSearch">
        <img src="/icons/search-icon.svg" class="search-icon" alt="Search" />
        <input 
          ref="searchInputRef"
          type="text" 
          placeholder="Search books..." 
          v-model="searchInput" 
          @input="handleSearch" 
        />
        <button class="clear-search" v-if="searchInput" @click.stop="clearSearch">
          <img src="/icons/close-icon.svg" class="close-icon" alt="Clear" />
        </button>
      </div>
      
      <button class="new-book-button" @click="createNewBook">
        <img src="/icons/plus-icon.svg" class="plus-icon" alt="+" />
        <span class="button-text">New Book</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

export default defineComponent({
  name: 'BookHeader',
  props: {
    searchQuery: {
      type: String,
      default: ''
    }
  },
  emits: [
    'search',
    'createNew'
  ],
  setup(props, { emit }) {
    const searchInputRef = ref<HTMLInputElement | null>(null);
    const searchInput = ref(props.searchQuery);
    
    const focusSearch = () => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    };
    
    const handleSearch = () => {
      emit('search', searchInput.value);
    };
    
    const clearSearch = () => {
      searchInput.value = '';
      handleSearch();
      focusSearch();
    };
    
    const createNewBook = () => {
      emit('createNew');
    };
    
    return {
      searchInputRef,
      searchInput,
      focusSearch,
      handleSearch,
      clearSearch,
      createNewBook
    };
  }
});
</script>

<style scoped>
.book-header {
  background-color: var(--color-bg-primary);
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
  color: var(--color-text-primary);
  position: relative;
  z-index: 5; /* Much lower z-index to ensure it's under SidebarNavigation */
  border-bottom: 1px solid var(--color-border-primary);
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-input-bg);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid var(--color-input-border);
  cursor: text;
  width: 350px; /* Made wider as requested */
}

.search-bar:focus-within {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-input-focus);
}

.search-icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-primary);
  margin-right: 2px;
}

.search-bar input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-input-text);
  padding: 0 8px;
  user-select: text;
  -webkit-user-select: text;
}

.search-bar input::placeholder {
  color: var(--color-input-placeholder);
}

.clear-search {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.close-icon {
  width: 14px;
  height: 14px;
  color: var(--color-text-primary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.close-icon:hover {
  opacity: 1;
}

.new-book-button {
  width: 100%; /* Match the NotesView button width */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: var(--color-btn-secondary-bg);
  border: none;
  border-radius: 8px;
  color: var(--color-btn-secondary-text);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  max-width: 150px; /* Limit the maximum width */
}

.new-book-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.plus-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.button-text {
  white-space: nowrap;
}
</style>
