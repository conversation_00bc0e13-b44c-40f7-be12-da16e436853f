# Discord Rich Presence Customization

## Files Modified
- `public/discord-rpc-api.ts` - Complete rewrite of Discord RPC implementation

## What Was Done
Completely redesigned the Discord Rich Presence system to be simple, clean, and professional with user-controllable privacy settings and activity toggles.

## Key Changes

### 1. Simplified Interface
- **Removed**: All small icons/images (only keeping noti-logo)
- **Removed**: Complex emoji-heavy messages and customization options
- **Removed**: Detailed timer information, pomodoro counts, focus times
- **Kept**: Clean hover text for the main logo

### 2. Clean Activity States
- **Taking notes** - When user is writing notes
- **Writing about [Book Name]** - When writing about a book (with privacy option)
- **Writing about a book** - Generic version when privacy is enabled
- **In focus session** - When using the pomodoro timer
- **Configuring app** - When in settings
- **Idle** - After 3 minutes of inactivity

### 3. Individual Privacy Controls
New `DiscordRPCSettings` interface with individual toggles:
- `showNoteTaking: boolean` - Show/hide note-taking activity
- `showBookWriting: boolean` - Show/hide book writing activity  
- `showBookNames: boolean` - Show actual book names vs generic "a book"
- `showTimer: boolean` - Show/hide timer sessions
- `showSettings: boolean` - Show/hide settings activity

### 4. Automatic Idle Detection
- Tracks user activity automatically
- Switches to "Idle" after 3 minutes of inactivity
- Updates activity timestamp on any Discord activity update

### 5. Simplified API
- Single `setActivity(activityData: ActivityData)` method
- Automatic privacy filtering based on user settings
- Clean activity data structure with type safety

## New Interfaces

```typescript
export interface DiscordRPCSettings {
  enabled: boolean;
  showNoteTaking: boolean;
  showBookWriting: boolean;
  showBookNames: boolean;
  showTimer: boolean;
  showSettings: boolean;
}

export interface ActivityData {
  type: 'notes' | 'book' | 'timer' | 'settings' | 'idle';
  bookName?: string;
  timerType?: 'pomodoro' | 'shortBreak' | 'longBreak';
}
```

## Usage Examples

```typescript
// Set note-taking activity
discordRPC.setActivity({ type: 'notes' });

// Set book writing with privacy control
discordRPC.setActivity({ 
  type: 'book', 
  bookName: 'The Great Gatsby' 
});

// Set timer activity
discordRPC.setActivity({ type: 'timer' });

// Update settings
discordRPC.updateSettings({
  showBookNames: false,  // Hide book names for privacy
  showTimer: false       // Don't show timer sessions
});
```

## Professional Design Principles
1. **Minimal Information** - Only essential activity status
2. **User Privacy** - Complete control over what information is shared
3. **Clean Appearance** - No emojis or visual clutter
4. **Consistent Branding** - Always shows Noti logo with professional description
5. **Automatic Behavior** - Intelligent idle detection without user intervention

## Benefits
- **Simple**: Easy to understand and configure
- **Professional**: Clean, business-appropriate appearance
- **Private**: User controls exactly what information is shared
- **Complete**: Covers all major app activities
- **Reliable**: Automatic idle detection and activity tracking
