/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

import type { ElectronAPI } from './useElectronAPI';

declare global {
  interface Window {
    // expose in the `electron/preload/index.ts`
    ipcRenderer: import('electron').IpcRenderer
    db: ElectronAPI
    // For auto-save functionality
    autoSaveTimeout?: ReturnType<typeof setTimeout>
  }
}
