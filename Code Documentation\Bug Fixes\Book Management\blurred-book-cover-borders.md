# Blurred Book Cover Borders Feature

## Files Modified
- `src/components/books/BookCard.vue`

## Section
- UI Components / Book Display

## Issue Description
Book covers that don't perfectly fit the aspect ratio of the book cover container were showing blank white borders on the left and right sides. This created an inconsistent and less polished visual appearance.

## Solution Implemented
Added a blurred background effect that fills the empty space with a scaled and blurred version of the book cover itself, creating a seamless visual experience.

### Technical Implementation:

1. **Restructured Template**: Added separate elements for blurred background and main cover
   - `blurred-background` div (z-index: 0) - for the blurred background effect
   - `main-cover` div (z-index: 1) - for the sharp main cover image
2. **Computed Properties**: 
   - `blurredBackgroundStyle` - applies the cover image with blur styling
   - `coverImageStyle` - applies the cover image for the main sharp display
3. **CSS Layering**: 
   - Blurred background: z-index 0, blur filter 25px, brightness 70%, opacity 80%
   - Main cover: z-index 1, sharp and clear display
   - Default cover: z-index 1 when no image available
   - Loading overlay: z-index 2, appears above everything when loading

### Layer Structure (bottom to top):
1. **Blurred Background** (z-index: 0) - Covers entire container with blurred/scaled version
2. **Main Cover** (z-index: 1) - Sharp cover image positioned properly
3. **Default Cover** (z-index: 1) - SVG placeholder when no cover available
4. **Loading Overlay** (z-index: 2) - Loading indicator during book processing

### Visual Effects:
- **Blur Effect**: 25px blur creates a soft, pleasant background
- **Brightness**: Reduced to 70% to not overwhelm the main cover
- **Opacity**: Set to 80% for subtle blending
- **Scale**: 110% to eliminate any edge artifacts from the blur

### Benefits:
- Eliminates unsightly white borders
- Creates a cohesive color scheme that matches the book cover
- Maintains the sharp, readable main cover image
- Adds visual depth and professionalism to the book cards
- Automatically adapts to any book cover color palette
- Proper layering ensures blur stays in background only

### Bug Fix Update:
Fixed an initial layering issue where the blur effect was appearing on top of the main cover. Restructured the component to use separate elements with proper z-index layering to ensure the blurred background stays behind the sharp main cover image.

The feature only activates when a valid cover image is available, ensuring no performance impact on books without covers. 