<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="color-selection-modal">
        <div class="close-icon" @click="$emit('close')">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>

        <div class="modal-header">
          <h1 class="modal-title">Text Color</h1>
          <div class="modal-subtitle">Choose a color for your text</div>
        </div>

        <div class="modal-content">
          <!-- Preview of selected color -->
          <div class="color-preview-section" v-if="selectedColor">
            <div class="section-header">
              <h2>Preview</h2>
            </div>
            <div class="color-preview-container">
              <p class="preview-text" :style="{ color: selectedColor }">
                The quick brown fox jumps over the lazy dog.
              </p>
              <div class="preview-color-info">
                <span class="color-swatch" :style="{ backgroundColor: selectedColor }"></span>
                <span class="color-value">{{ selectedColor }}</span>
              </div>
            </div>
          </div>

          <!-- Predefined colors section -->
          <div class="color-section">
            <div class="section-header">
              <h2>Color Palette</h2>
            </div>

            <div class="color-grid">
              <div
                v-for="color in predefinedColors"
                :key="color.value"
                class="color-option"
                :class="{ 'selected': selectedColor === color.value }"
                :style="{ backgroundColor: color.value }"
                :title="color.name"
                @click="selectColor(color.value)"
              >
              </div>
            </div>
          </div>

          <!-- Custom color section -->
          <div class="color-section">
            <div class="section-header">
              <h2>Custom Color</h2>
            </div>

            <div class="custom-color-container">
              <input
                type="color"
                v-model="customColor"
                @input="selectCustomColor(customColor)"
                class="color-picker"
              />
              <input
                type="text"
                v-model="hexInput"
                @input="handleHexInput"
                placeholder="#000000"
                class="hex-input"
                maxlength="7"
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-cancel" @click="$emit('close')">
            Cancel
          </button>
          <button class="btn btn-primary" @click="applyColor">
            Apply Color
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'ColorSelectionModal',
  props: {
    currentColor: {
      type: String,
      default: '#000000'
    }
  },
  emits: ['close', 'apply-color'],
  setup(props, { emit }) {
    // Organized 5x3 color palette (15 colors total)
    const predefinedColors = [
      // Row 1: Neutrals (Black to White)
      { name: 'Black', value: '#000000' },
      { name: 'Dark Gray', value: '#4A4A4A' },
      { name: 'Gray', value: '#808080' },
      { name: 'Light Gray', value: '#C0C0C0' },
      { name: 'White', value: '#FFFFFF' },

      // Row 2: Warm Colors
      { name: 'Red', value: '#E53E3E' },
      { name: 'Orange', value: '#FF8C00' },
      { name: 'Yellow', value: '#F6E05E' },
      { name: 'Pink', value: '#ED64A6' },
      { name: 'Brown', value: '#8B4513' },

      // Row 3: Cool Colors
      { name: 'Green', value: '#38A169' },
      { name: 'Teal', value: '#319795' },
      { name: 'Blue', value: '#3182CE' },
      { name: 'Purple', value: '#805AD5' },
      { name: 'Indigo', value: '#5A67D8' }
    ];

    // State for color selection
    const selectedColor = ref(props.currentColor || '#000000');
    const customColor = ref('#000000'); // Keep custom color separate
    const hexInput = ref('#000000');

    // Function to select a predefined color
    const selectColor = (color: string) => {
      selectedColor.value = color;
    };

    // Function to select custom color
    const selectCustomColor = (color: string) => {
      selectedColor.value = color;
      customColor.value = color;
      hexInput.value = color;
    };

    // Handle hex input changes
    const handleHexInput = () => {
      const hex = hexInput.value;
      // Validate hex color format
      if (/^#[0-9A-F]{6}$/i.test(hex)) {
        selectCustomColor(hex);
      }
    };

    // Apply the selected color
    const applyColor = () => {
      if (selectedColor.value) {
        emit('apply-color', selectedColor.value);
      }
      emit('close');
    };

    // Watch for prop changes
    watch(() => props.currentColor, (newColor) => {
      if (newColor) {
        selectedColor.value = newColor;
        customColor.value = newColor;
        hexInput.value = newColor;
      }
    });

    return {
      predefinedColors,
      selectedColor,
      customColor,
      hexInput,
      selectColor,
      selectCustomColor,
      handleHexInput,
      applyColor
    };
  }
});
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  isolation: isolate;
  padding: 20px;
  box-sizing: border-box;
}

/* Main Modal Container */
.color-selection-modal {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 520px;
  max-width: 100%;
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  position: relative;
}

/* Close Button */
.close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-icon:hover {
  background-color: var(--color-nav-item-hover);
}

.close-icon img {
  width: 24px;
  height: 24px;
}

/* Modal Header */
.modal-header {
  padding: 32px 32px 24px 32px;
  text-align: center;
  border-bottom: 1px solid var(--color-modal-border);
  flex-shrink: 0;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

/* Modal Content */
.modal-content {
  padding: 24px 32px;
  flex: 1;
  overflow-y: auto;
}

/* Custom Scrollbar */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Section Styling */
.color-section {
  margin-bottom: 24px;
}

.color-section:last-child {
  margin-bottom: 0;
}

.section-header {
  margin-bottom: 16px;
}

.section-header h2 {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

/* Preview Section */
.color-preview-section {
  margin-bottom: 24px;
}

.color-preview-container {
  background-color: var(--color-bg-tertiary);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--color-border-primary);
}

.preview-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.preview-color-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: 1px solid var(--color-border-primary);
  box-shadow: 0 1px 3px var(--color-card-shadow);
}

.color-value {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* Color Grid */
.color-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  max-width: 100%;
  justify-items: center;
}

.color-option {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  cursor: pointer;
  border: 3px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 8px var(--color-card-shadow);
}

.color-option:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 16px var(--color-card-hover-shadow);
  border-color: var(--color-primary);
}

.color-option.selected {
  border-color: var(--color-primary);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 16px var(--color-card-hover-shadow);
}



/* Custom Color Section */
.custom-color-container {
  display: flex;
  gap: 16px;
  align-items: center;
}

.color-picker {
  width: 64px;
  height: 48px;
  border: 2px solid var(--color-border-primary);
  border-radius: 12px;
  cursor: pointer;
  background: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px var(--color-card-shadow);
  padding: 0;
  overflow: hidden;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 10px;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 10px;
  box-shadow: none;
}

.color-picker::-moz-color-swatch {
  border: none;
  border-radius: 10px;
}

.color-picker:hover {
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--color-card-hover-shadow);
}

.hex-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid var(--color-input-border);
  border-radius: 12px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  transition: all 0.2s ease;
}

.hex-input:focus {
  outline: none;
  border-color: var(--color-input-focus);
  background-color: var(--color-input-bg);
  box-shadow: 0 0 0 3px var(--color-nav-item-active);
}

.hex-input::placeholder {
  color: var(--color-input-placeholder);
}

/* Modal Footer */
.modal-footer {
  padding: 24px 32px 32px 32px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  border-top: 1px solid var(--color-border-primary);
  flex-shrink: 0;
  background-color: var(--color-bg-tertiary);
}

.btn {
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;
  font-family: 'Montserrat', sans-serif;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-cancel:hover {
  background-color: var(--color-btn-secondary-hover);
  border-color: var(--color-border-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}
</style>
