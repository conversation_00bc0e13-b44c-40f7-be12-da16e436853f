<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-overlay">
      <div class="delete-modal">
        <div class="close-icon" @click="onCancel">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>

        <div class="modal-header">
          <h1 class="modal-title">Add Image</h1>
          <div class="modal-subtitle">
            <div class="modal-input-group">
              <label for="imageUpload">Upload an image file</label>
              <input
                id="imageUpload"
                type="file"
                accept="image/*"
                @change="handleImageSelect"
                ref="imageInput"
              >
            </div>
            <div v-if="imagePreview" class="image-preview">
              <img :src="imagePreview" alt="Preview" style="max-width: 300px; max-height: 200px;" />
            </div>
          </div>
        </div>

        <div class="divider"></div>

        <div class="modal-footer">
          <button class="btn btn-cancel" @click="onCancel">
            Cancel
          </button>
          <button class="btn btn-primary" @click="onConfirm" :disabled="!selectedImage">
            Insert Image
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, ref, PropType } from 'vue';

export default defineComponent({
  name: 'ImageUploadModal',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  emits: ['close', 'confirm', 'image-selected'],
  setup(props, { emit }) {
    const imageInput = ref<HTMLInputElement | null>(null);
    const selectedImage = ref<File | null>(null);
    const imagePreview = ref<string | null>(null);

    const handleImageSelect = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        selectedImage.value = target.files[0];

        // Create preview
        const reader = new FileReader();
        reader.onload = () => {
          imagePreview.value = reader.result as string;
          // Notify parent component about the selected image
          emit('image-selected', selectedImage.value);
        };
        reader.readAsDataURL(selectedImage.value);
      }
    };

    const onCancel = () => {
      // Clear the input and preview
      selectedImage.value = null;
      imagePreview.value = null;
      if (imageInput.value) {
        imageInput.value.value = '';
      }
      emit('close');
    };

    const onConfirm = () => {
      if (selectedImage.value) {
        emit('confirm', selectedImage.value);
      }
    };

    return {
      imageInput,
      selectedImage,
      imagePreview,
      handleImageSelect,
      onCancel,
      onConfirm
    };
  }
});
</script>

<style scoped>
/* Image modal styles */
.image-preview {
  margin: 1rem 0;
  max-width: 100%;
  text-align: center;
}

.image-preview img {
  max-width: 300px;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Modal styles - these should be consistent with other modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.delete-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-shadow);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.modal-input-group {
  margin-bottom: 16px;
}

.modal-input-group:last-child {
  margin-bottom: 0;
}

.modal-input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--color-text-primary);
}

.modal-input-group input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid var(--color-input-border);
  font-size: 15px;
  box-sizing: border-box;
  font-family: inherit;
  background-color: var(--color-input-bg);
  color: var(--color-input-text);
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
