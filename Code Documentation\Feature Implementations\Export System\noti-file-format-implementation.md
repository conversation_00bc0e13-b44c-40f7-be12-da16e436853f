# .noti Custom File Format Implementation

## Executive Summary

This document details the implementation of the enhanced .noti custom file format for the Noti application. The implementation includes comprehensive export and import functionality with media embedding, integrity verification, and robust error handling.

## Files Modified

### 1. **electron/main/api/notes-api.ts**
- **Lines 27**: Added `import crypto from 'crypto';`
- **Lines 820-935**: Complete rewrite of `exportNoteToNoti()` function
- **Lines 938-1048**: Complete rewrite of `exportNoteToNotiToPath()` function
- **Lines 1066**: Changed file extension from `'noti.json'` to just `'noti'`
- **Lines 1294**: Changed exported filename from `${sanitizedTitle}.noti.json` to `${sanitizedTitle}.noti`
- **Lines 1348-1523**: Complete rewrite of `importNote()` function to support new format
- **Removed**: All backwards compatibility code for legacy .noti.json format
- **Removed**: References to non-existent `starred` and `tags` fields

### 2. **scripts/test-noti-format.ts** (New File)
- Created comprehensive test suite for .noti format functionality
- Tests export, import, media handling, error cases, and round-trip integrity

## Implementation Details

### Enhanced .noti File Format Specification

```json
{
  "version": "1.0",
  "type": "noti-note",
  "schema": "https://noti.app/schemas/note/v1.0",
  "metadata": {
    "id": 123,
    "title": "Note Title",
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T14:30:00.000Z",
    "last_viewed_at": "2024-01-15T14:30:00.000Z",
    "type": "text",
    "color": "#ff0000",
    "folder_id": 456,
    "book_id": 789,
    "export": {
      "version": "1.0.0",
      "app_version": "2.0.0",
      "exported_at": "2024-01-15T15:00:00.000Z"
    }
  },
  "content": {
    "html": "<p>Rich HTML content from TipTap editor</p>",
    "markdown": "Plain markdown content",
    "plain_text": "Plain text for search/preview",
    "statistics": {
      "word_count": 150,
      "character_count": 850,
      "reading_time": 1
    }
  },
  "media": [
    {
      "id": "media_1",
      "file_name": "image.png",
      "file_type": "image/png",
      "file_size": 102400,
      "original_path": "noti-media://path/to/image.png",
      "embedded": true,
      "data": "base64_encoded_data"
    }
  ],
  "integrity": {
    "algorithm": "sha256",
    "content_hash": "hash_of_content_section"
  }
}
```

### Key Features Implemented

#### 1. Export Functionality (Lines 820-935, 938-1048)
- **Enhanced JSON Structure**: Includes version, type, schema, and comprehensive metadata
- **Media Embedding**: Automatically embeds images as base64-encoded data
- **Content Statistics**: Calculates word count, character count, and reading time
- **Integrity Hash**: SHA-256 hash of content for verification
- **App Version Tracking**: Records app version for compatibility tracking

#### 2. Import Functionality (Lines 1348-1523)
- **Strict Validation**: Validates version, type, and required fields
- **Security Checks**: 50MB file size limit to prevent DoS attacks
- **Integrity Verification**: Verifies SHA-256 hash if present
- **Media Restoration**: Re-creates embedded media files and associates with note
- **Content Priority**: HTML > Markdown > Plain text for maximum fidelity

#### 3. Error Handling
- Invalid JSON detection and rejection
- Missing required fields validation
- Unsupported version rejection
- File size limit enforcement
- Graceful handling of media import failures

### Security Measures Implemented

1. **File Size Limit**: 50MB maximum to prevent memory exhaustion
2. **Content Validation**: Strict JSON schema validation
3. **Integrity Verification**: SHA-256 hash verification
4. **HTML Sanitization**: Escaped HTML for text imports
5. **Path Sanitization**: Sanitized filenames for all file operations

### Code Changes Detail

#### Export Function Enhancement
```typescript
// Old implementation (removed)
const exportNoteToNoti = async (note: Note): Promise<string> => {
    const outputPath = path.join(exportDir, `${sanitizedTitle}.noti.json`);
    fs.writeFileSync(outputPath, JSON.stringify(note, null, 2));
    return outputPath;
};

// New implementation (lines 820-935)
const exportNoteToNoti = async (note: Note): Promise<string> => {
    // Get app version
    // Calculate statistics
    // Build enhanced structure with media embedding
    // Add integrity hash
    // Write .noti file (not .noti.json)
};
```

#### Import Function Enhancement
```typescript
// Old implementation (removed backwards compatibility)
if (format.toLowerCase() === 'noti') {
    // Simple JSON parsing
}

// New implementation (lines 1380-1434)
if (format.toLowerCase() === 'noti') {
    // Strict validation
    // Security checks
    // Integrity verification
    // Media restoration
    // Enhanced content extraction
}
```

### Testing

The test suite (`scripts/test-noti-format.ts`) covers:

1. **Basic Export Test**: Validates structure, content, and integrity
2. **Export with Media**: Tests media embedding functionality
3. **Basic Import Test**: Validates metadata and content restoration
4. **Import with Media**: Tests media file restoration
5. **Error Handling**: Tests all validation and security measures
6. **Round-trip Test**: Ensures data integrity through export/import cycle

### Usage Examples

#### Export a Note
```typescript
const note = await getNoteById(123);
const exportPath = await exportNoteToNoti(note);
// Creates: "Note Title.noti" with embedded media
```

#### Import a Note
```typescript
const fileContent = fs.readFileSync('Note Title.noti', 'utf8');
const importedNote = await importNote(fileContent, 'noti', 'Imported Note');
// Restores note with all media files
```

### Performance Considerations

- **Media Embedding**: Base64 encoding increases file size by ~33%
- **Large Files**: 50MB limit prevents performance issues
- **Integrity Calculation**: SHA-256 hashing is fast for typical note sizes
- **JSON Parsing**: Native JSON.parse is optimized in V8

### Future Enhancements

1. **Compression**: Add gzip compression for large files
2. **Incremental Export**: Export only changed content
3. **Batch Operations**: Export/import multiple notes
4. **Encryption**: Add optional encryption for sensitive notes
5. **Cloud Integration**: Direct export to cloud storage

## Migration Guide

Since backwards compatibility was removed, users with existing .noti.json files should:

1. Keep the existing import functionality for .noti.json files separately
2. OR provide a migration tool to convert .noti.json to .noti format
3. Communicate the format change clearly in release notes

## Conclusion

The enhanced .noti format provides a robust, secure, and feature-rich way to export and import notes with full fidelity, including embedded media and integrity verification. The implementation follows security best practices and includes comprehensive error handling and testing.