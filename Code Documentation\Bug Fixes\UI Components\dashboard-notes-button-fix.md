# Dashboard Notes Button Fix - Router Import Issue

## Overview
Fixed the dashboard "New Note" button functionality that was failing to create new notes due to missing router import and instance in NotesView.vue.

## Files Modified
- `src/views/NotesView.vue`

## Root Cause Analysis
The dashboard notes button was correctly navigating to `/notes?action=create`, but the NotesView component couldn't handle the action parameter because:

1. **Missing Import**: Only `useRoute` was imported, but `useRouter` was missing
2. **Missing Router Instance**: No `router` instance was created in the setup function
3. **Failed Router Calls**: Two `router.replace()` calls (lines 1198 and 1239) were failing with `ReferenceError: router is not defined`

## What Was Happening
1. ✅ User clicks "New Note" in dashboard
2. ✅ QuickActions.vue navigates to `/notes?action=create`
3. ✅ NotesView.vue loads and detects `action=create` parameter
4. ✅ `loadNotes()` completes and auto-selects first note
5. ❌ **FAILURE**: `router.replace()` throws error because `router` is undefined
6. ❌ **RESULT**: `createNewNote()` never gets called

## How It Was Fixed

### Change 1: Added useRouter Import
**Location**: Line 205
```typescript
// BEFORE
import { useRoute } from 'vue-router';

// AFTER  
import { useRoute, useRouter } from 'vue-router';
```

### Change 2: Added Router Instance
**Location**: Lines 242-243
```typescript
// BEFORE
const route = useRoute();
// Get Discord activity tracking

// AFTER
const route = useRoute();
// Get the router instance for navigation
const router = useRouter();
// Get Discord activity tracking
```

## Technical Details
- The existing `handleCreateAction()` and `onMounted()` logic was correct
- The `createNewNote()` function was working properly
- Only the router navigation calls were failing silently
- This follows the same pattern used successfully in BooksView.vue and FoldersView.vue

## Testing
After the fix:
1. Dashboard "New Note" button should navigate to notes view
2. A new "Untitled Note" should be created automatically
3. The new note should be selected and ready for editing
4. URL should be cleaned (action parameter removed)

## Impact
- ✅ Dashboard notes button now works as intended
- ✅ No breaking changes to existing functionality
- ✅ Consistent with other view implementations
- ✅ Maintains all existing error handling and edge cases
