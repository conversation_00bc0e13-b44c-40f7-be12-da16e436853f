# Sync System Database Schema and Path Traversal Fix

## Files Modified
- `electron/main/database/database.ts`
- `electron/main/api/sync-logic/unified-sync-engine.ts`
- `electron/main/api/sync-logic/file-operations.ts`

## What Was Done
Fixed two critical sync system errors:
1. Database schema conflict between old item-based sync_state and new directory-based sync
2. Path traversal security blocking legitimate media file exports

## How It Was Fixed

### 1. Database Schema Conflict Resolution
The sync system had conflicting table schemas:
- Old `sync_state` table tracked individual items (item_type, item_id)
- New unified sync engine expected directory-based tracking (directory, last_sync_hash)

**Solution**: Created a new `sync_directory_state` table specifically for the unified sync engine:
```sql
CREATE TABLE IF NOT EXISTS sync_directory_state (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  directory TEXT UNIQUE NOT NULL,
  last_sync_hash TEXT,
  last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

### 2. Path Traversal Security Fix
The FileOperations class validates all paths to prevent directory traversal attacks. However, when exporting book covers, it needs to read from the app's media directory (outside the sync directory).

**Solution**: Added new methods for handling binary files without path validation:
- `readFileBuffer(filePath: string): Promise<Buffer>` - Reads files as buffers without validation
- `writeFileBuffer(filePath: string, content: Buffer): Promise<void>` - Writes buffers with validation

The sync engine now uses these methods specifically for media files while maintaining security for user content.

## Technical Details

### Database Changes
- Added `sync_directory_state` table creation in `database.ts`
- Added index `idx_sync_directory_state_directory` for performance
- Updated all references in `unified-sync-engine.ts` from `sync_state` to `sync_directory_state`

### File Operations Changes
- Added `readFileBuffer()` method that bypasses path validation for reading
- Added `writeFileBuffer()` method that maintains validation for writing
- Updated book cover export to use buffer methods instead of text methods

### Security Considerations
- Path validation remains active for all user content (notes, folders)
- Only media file reads bypass validation (necessary for accessing app data)
- Writes still validate paths to prevent writing outside sync directory

## Testing Notes
After these changes:
1. The sync_state table error should be resolved
2. Book covers should export successfully
3. Path traversal protection remains active for user content
4. The sync system should complete without errors

## Future Improvements
Consider migrating the old `sync_state` table data if needed, or remove it entirely if the item-based sync approach is no longer used.