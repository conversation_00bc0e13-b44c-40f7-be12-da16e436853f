/**
 * Unicode-safe filename and folder name sanitization utilities
 *
 * This module provides cross-platform safe filename sanitization that:
 * - Preserves Unicode characters including diacritics (é, ñ, ü, etc.)
 * - Removes only filesystem-unsafe characters
 * - Handles Windows, macOS, and Linux compatibility
 * - Manages reserved names and length limits
 * - Provides consistent behavior across the application
 */

/**
 * Characters that are unsafe for filenames across all major filesystems
 * Windows: < > : " | ? * \ /
 * macOS: : (colon is converted to slash in HFS+)
 * Linux: / (forward slash)
 *
 * We also include control characters (0x00-0x1F) and DEL (0x7F)
 */
const UNSAFE_FILENAME_CHARS = /[<>:"|?*\\/\x00-\x1F\x7F]/g;

/**
 * Reserved names on Windows (case-insensitive)
 * These cannot be used as filenames even with extensions
 */
const WINDOWS_RESERVED_NAMES = new Set([
  'CON', 'PRN', 'AUX', 'NUL',
  'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
  'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
]);

/**
 * Maximum filename length for most filesystems
 * NTFS: 255 characters
 * ext4: 255 bytes (but we use characters for simplicity)
 * HFS+: 255 UTF-16 code units
 */
const MAX_FILENAME_LENGTH = 255;

/**
 * Maximum folder name length (slightly more conservative)
 */
const MAX_FOLDERNAME_LENGTH = 200;

/**
 * Sanitize a filename to be safe across all major filesystems
 * while preserving Unicode characters including diacritics
 *
 * @param filename - The original filename
 * @param options - Sanitization options
 * @returns A filesystem-safe filename
 */
export function sanitizeFilename(
  filename: string,
  options: {
    maxLength?: number;
    replacement?: string;
    preserveExtension?: boolean;
  } = {}
): string {
  const {
    maxLength = MAX_FILENAME_LENGTH,
    replacement = '-',
    preserveExtension = true
  } = options;

  if (!filename || typeof filename !== 'string') {
    return 'untitled';
  }

  // Normalize Unicode to ensure consistent representation
  let sanitized = filename.normalize('NFC');

  // Split filename and extension if preserveExtension is true
  let name = sanitized;
  let extension = '';

  if (preserveExtension) {
    const lastDotIndex = sanitized.lastIndexOf('.');
    if (lastDotIndex > 0 && lastDotIndex < sanitized.length - 1) {
      name = sanitized.substring(0, lastDotIndex);
      extension = sanitized.substring(lastDotIndex);
    }
  }

  // Replace unsafe characters with the replacement string
  name = name.replace(UNSAFE_FILENAME_CHARS, replacement);

  // Remove leading and trailing whitespace and dots
  name = name.trim().replace(/^\.+|\.+$/g, '');

  // Handle Windows reserved names
  if (WINDOWS_RESERVED_NAMES.has(name.toUpperCase())) {
    name = `${name}_file`;
  }

  // Ensure the name is not empty after sanitization
  if (!name) {
    name = 'untitled';
  }

  // Combine name and extension
  let result = name + extension;

  // Truncate if too long, preserving extension
  if (result.length > maxLength) {
    const availableLength = maxLength - extension.length;
    if (availableLength > 0) {
      name = name.substring(0, availableLength);
      result = name + extension;
    } else {
      // If extension is too long, truncate everything
      result = result.substring(0, maxLength);
    }
  }

  // Final check to ensure result is not empty
  return result || 'untitled';
}

/**
 * Sanitize a folder name to be safe across all major filesystems
 * while preserving Unicode characters including diacritics
 *
 * @param folderName - The original folder name
 * @param options - Sanitization options
 * @returns A filesystem-safe folder name
 */
export function sanitizeFolderName(
  folderName: string,
  options: {
    maxLength?: number;
    replacement?: string;
  } = {}
): string {
  const {
    maxLength = MAX_FOLDERNAME_LENGTH,
    replacement = '-'
  } = options;

  if (!folderName || typeof folderName !== 'string') {
    return 'Untitled';
  }

  // Normalize Unicode to ensure consistent representation
  let sanitized = folderName.normalize('NFC');

  // Replace unsafe characters with the replacement string
  sanitized = sanitized.replace(UNSAFE_FILENAME_CHARS, replacement);

  // Remove leading and trailing whitespace and dots
  sanitized = sanitized.trim().replace(/^\.+|\.+$/g, '');

  // Handle Windows reserved names
  if (WINDOWS_RESERVED_NAMES.has(sanitized.toUpperCase())) {
    sanitized = `${sanitized}_folder`;
  }

  // Ensure the name is not empty after sanitization
  if (!sanitized) {
    sanitized = 'Untitled';
  }

  // Truncate if too long
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength).trim();
  }

  // Final check to ensure result is not empty
  return sanitized || 'Untitled';
}

/**
 * Sanitize a book title for use as a folder name
 * This is a specialized version of sanitizeFolderName for book titles
 *
 * @param bookTitle - The book title
 * @returns A filesystem-safe folder name based on the book title
 */
export function sanitizeBookTitle(bookTitle: string): string {
  return sanitizeFolderName(bookTitle || 'Untitled', {
    maxLength: MAX_FOLDERNAME_LENGTH,
    replacement: '-'
  });
}

/**
 * Sanitize a note title for use as a filename
 * This is a specialized version of sanitizeFilename for note titles
 *
 * @param noteTitle - The note title
 * @param extension - Optional file extension to preserve
 * @returns A filesystem-safe filename based on the note title
 */
export function sanitizeNoteTitle(noteTitle: string, extension?: string): string {
  const titleWithExtension = extension ? `${noteTitle}${extension}` : noteTitle;
  return sanitizeFilename(titleWithExtension || 'untitled', {
    maxLength: MAX_FILENAME_LENGTH,
    replacement: '-',
    preserveExtension: !!extension
  });
}

/**
 * Test if a filename is safe for use across all major filesystems
 *
 * @param filename - The filename to test
 * @returns True if the filename is safe, false otherwise
 */
export function isFilenameSafe(filename: string): boolean {
  if (!filename || typeof filename !== 'string') {
    return false;
  }

  // Check for unsafe characters
  if (UNSAFE_FILENAME_CHARS.test(filename)) {
    return false;
  }

  // Check for Windows reserved names
  const nameWithoutExtension = filename.replace(/\.[^.]*$/, '');
  if (WINDOWS_RESERVED_NAMES.has(nameWithoutExtension.toUpperCase())) {
    return false;
  }

  // Check length
  if (filename.length > MAX_FILENAME_LENGTH) {
    return false;
  }

  // Check for leading/trailing dots or whitespace
  if (filename !== filename.trim() || /^\.+|\.+$/.test(filename)) {
    return false;
  }

  return true;
}

/**
 * Test if a folder name is safe for use across all major filesystems
 *
 * @param folderName - The folder name to test
 * @returns True if the folder name is safe, false otherwise
 */
export function isFolderNameSafe(folderName: string): boolean {
  if (!folderName || typeof folderName !== 'string') {
    return false;
  }

  // Check for unsafe characters
  if (UNSAFE_FILENAME_CHARS.test(folderName)) {
    return false;
  }

  // Check for Windows reserved names
  if (WINDOWS_RESERVED_NAMES.has(folderName.toUpperCase())) {
    return false;
  }

  // Check length
  if (folderName.length > MAX_FOLDERNAME_LENGTH) {
    return false;
  }

  // Check for leading/trailing dots or whitespace
  if (folderName !== folderName.trim() || /^\.+|\.+$/.test(folderName)) {
    return false;
  }

  return true;
}
