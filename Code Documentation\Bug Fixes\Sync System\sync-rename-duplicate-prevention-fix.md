# Sync System Rename Duplicate Prevention Fix

## Overview

This document details the comprehensive fix implemented to resolve duplicate item creation when items (folders, books, notes) are renamed in the Noti application. The issue occurred because the sync system relied on name-based matching instead of ID-based tracking, causing renamed items to be treated as new items during sync operations.

## Problem Analysis

### Root Cause

The sync system used name-based identity matching for all item types:

1. **Folders**: Matched by `name + parent_id + book_id`
2. **Books**: Matched by `title + author`
3. **Notes**: Matched by `title + folder_id + book_id`

When an item was renamed:
1. Database ID remained the same, only the name changed
2. Sync export created new directory/file with new name
3. Old directory/file remained in sync location
4. On import, system couldn't match new name to existing item
5. **Result**: Duplicate items created

### Example Scenario

```
User Action: Rename folder "Project A" → "Project Alpha"

Database: folder.id = 5, folder.name = "Project Alpha" (ID unchanged)
Export: Creates /sync/Project Alpha/ directory
Issue: /sync/Project A/ directory still exists
Import: Can't find folder named "Project Alpha" in database
Result: Creates new folder "Project Alpha" + keeps old "Project A"
```

## Solution Architecture

### 1. ID-Based Identity Tracking

Instead of relying solely on names, the sync system now uses database IDs as the primary identity mechanism:

- **Manifest Storage**: Items stored as `type_id` format (`folder_5`, `book_123`, `note_456`)
- **Import Priority**: Check by ID first, fall back to name matching
- **Rename Detection**: Compare existing vs. incoming names for same ID

### 2. Comprehensive Item Type Coverage

The fix was implemented for all sync item types:

- **Folders**: Directory structures
- **Books**: Directory structures with metadata
- **Notes**: Individual markdown files

### 3. Cleanup System

Automatic cleanup of orphaned directories and files:

- **Rename Tracking**: Track old and new paths during import
- **Post-Sync Cleanup**: Remove old paths after successful sync
- **Empty Directory Cleanup**: Recursively remove empty parent directories

## Technical Implementation

### File Modified

**Primary File**: `electron/main/api/sync-logic/unified-sync-engine.ts`

### 1. Added ID-Based Existence Checks

```typescript
// NEW: Check by database ID
private async folderExistsById(id: number): Promise<Folder | null>
private async bookExistsById(id: number): Promise<Book | null>  
private async noteExistsById(id: number): Promise<Note | null>

// EXISTING: Check by name/location (kept for backwards compatibility)
private async folderExists(name: string, parentId: number | null, bookId: number | null): Promise<Folder | null>
private async bookExists(title: string, author?: string): Promise<Book | null>
private async noteExists(title: string, folderId: number | null, bookId: number | null): Promise<Note | null>
```

### 2. Enhanced Import Logic

**Pattern Applied to All Item Types** (`importFolder`, `importBook`, `importNote`):

```typescript
// Extract database ID from manifest item
const itemId = parseInt(item.id.replace('type_', ''), 10);

// PRIMARY: Check by ID (handles renames)
let existingItem = !isNaN(itemId) ? await this.itemExistsById(itemId) : null;

// FALLBACK: Check by name/location (backwards compatibility)
if (!existingItem) {
  existingItem = await this.itemExists(/* name-based parameters */);
}

// RENAME DETECTION: Track renamed items for cleanup
if (existingItem && existingItem.name !== newName) {
  console.log(`Detected rename: "${existingItem.name}" -> "${newName}"`);
  this.renamedItems.push({ oldPath, newPath });
}

// UPDATE: Apply new name and other changes
await updateItem(existingItem.id, { name: newName, /* other fields */ });
```

### 3. Rename Tracking System

```typescript
export class UnifiedSyncEngine extends EventEmitter {
  // Track renamed items for cleanup
  private renamedFolders: Array<{ oldPath: string; newPath: string }> = [];
  private renamedBooks: Array<{ oldPath: string; newPath: string }> = [];
  private renamedNotes: Array<{ oldPath: string; newPath: string }> = [];
```

### 4. Path Building Utilities

```typescript
// Build complete folder path recursively
private async buildFolderPath(folder: Folder, baseDirectory: string): Promise<string>

// Build complete note path considering folder/book hierarchy  
private async buildNotePath(note: Note, baseDirectory: string): Promise<string>
```

### 5. Comprehensive Cleanup System

```typescript
private async cleanupRenamedItems(): Promise<void> {
  // Clean up renamed folders (directories)
  for (const { oldPath, newPath } of this.renamedFolders) {
    if (oldExists && newExists && oldPath !== newPath) {
      await fs.rm(oldPath, { recursive: true, force: true });
      await this.cleanupEmptyParentDirectories(path.dirname(oldPath));
    }
  }
  
  // Clean up renamed books (directories)
  for (const { oldPath, newPath } of this.renamedBooks) {
    // Similar cleanup logic
  }
  
  // Clean up renamed notes (files)
  for (const { oldPath, newPath } of this.renamedNotes) {
    await fs.unlink(oldPath);
    await this.cleanupEmptyParentDirectories(path.dirname(oldPath));
  }
}
```

### 6. Integration with Sync Flow

```typescript
async sync(directory: string): Promise<SyncResult> {
  // ... existing sync logic ...
  
  // NEW: Clean up renamed items before updating manifest
  const hasRenames = this.renamedFolders.length > 0 || 
                     this.renamedBooks.length > 0 || 
                     this.renamedNotes.length > 0;
  if (hasRenames) {
    await this.cleanupRenamedItems();
  }
  
  // ... continue with manifest update ...
}
```

## Behavior Changes

### Before Fix

1. **Rename Item**: User renames "Folder A" to "Folder B"
2. **Export**: Creates new "Folder B" directory
3. **Problem**: "Folder A" directory remains
4. **Import**: Creates duplicate folder in database
5. **Result**: User sees both "Folder A" and "Folder B"

### After Fix

1. **Rename Item**: User renames "Folder A" to "Folder B"  
2. **Export**: Creates new "Folder B" directory
3. **Import**: Recognizes folder by ID, updates name to "Folder B"
4. **Cleanup**: Removes old "Folder A" directory
5. **Result**: User sees only "Folder B" (correct behavior)

## Backwards Compatibility

The fix maintains full backwards compatibility:

- **Legacy Syncs**: Items without ID information still work via name matching
- **Mixed Scenarios**: Can handle both ID-based and name-based items in same sync
- **Gradual Migration**: Existing sync directories automatically adopt ID-based tracking

## Edge Cases Handled

### 1. Missing Parent Relationships

```typescript
// Handles orphaned items gracefully
if (!parentFound) {
  console.warn(`Parent not found, creating orphaned path`);
  // Create in _orphaned_items directory
}
```

### 2. Concurrent Renames

```typescript
// Uses transaction boundaries to prevent race conditions
await withTransaction(async () => {
  // All database operations within transaction
});
```

### 3. Partial Sync Failures

```typescript
// Cleanup only occurs after successful import
try {
  await this.importItems(changes);
  await this.cleanupRenamedItems(); // Only if imports succeed
} catch (error) {
  // Cleanup lists are cleared but files remain for recovery
}
```

### 4. Directory Permission Issues

```typescript
try {
  await fs.rm(oldPath, { recursive: true, force: true });
} catch (error) {
  console.error(`Could not remove ${oldPath}:`, error);
  // Continue with other cleanup operations
}
```

## Performance Considerations

### Database Queries

- **ID Lookups**: Single indexed query `SELECT * FROM table WHERE id = ?`
- **Fallback Queries**: Existing name-based queries (unchanged performance)
- **Batch Operations**: All updates within transactions

### File System Operations

- **Cleanup Batching**: All renames tracked and cleaned up in single phase
- **Directory Traversal**: Recursive cleanup only for actually renamed items
- **Error Resilience**: Individual cleanup failures don't block entire sync

## Testing Scenarios

### 1. Simple Rename

```
Action: Rename folder "Docs" → "Documents"
Expected: Only "Documents" folder exists after sync
Verification: No "Docs" folder in sync directory
```

### 2. Nested Rename

```
Action: Rename book "Harry Potter" → "HP Series"
Expected: All notes in book use new path
Verification: No "/Harry Potter/" directory exists
```

### 3. Cross-Device Rename

```
Device A: Renames note "Todo" → "Tasks"
Device B: Syncs
Expected: Device B shows "Tasks", no duplicate
```

### 4. Multiple Renames

```
Action: Rename folder A→A1, book B→B1, note C→C1 in same session
Expected: All old paths cleaned up
Verification: Only new paths exist in sync directory
```

## Error Recovery

### Cleanup Failure Recovery

```typescript
// Each cleanup operation is independent
for (const item of renamedItems) {
  try {
    await cleanupItem(item);
  } catch (error) {
    // Log error but continue with other items
    console.error(`Cleanup failed for ${item.oldPath}:`, error);
  }
}
```

### Manifest Consistency

```typescript
// Manifest is updated only after all operations complete
await this.cleanupRenamedItems();
await manifestManager.saveManifest(directory, manifest);
```

## Monitoring and Logging

### Rename Detection Logs

```
[SYNC] Detected folder rename: "Project A" -> "Project Alpha"
[SYNC] Detected book rename: "Old Title" -> "New Title"
[SYNC] Detected note rename: "old-note" -> "new-note"
```

### Cleanup Logs

```
[SYNC] Cleaning up renamed items: 2 folders, 1 books, 3 notes
[SYNC] Cleaning up renamed folder: /sync/Project A
[SYNC] Removed empty directory: /sync/Old Parent
```

### Error Logs

```
[ERROR] Error cleaning up renamed folder /sync/path: Permission denied
[WARN] Could not clean up directory /sync/path: Directory not empty
```

## Future Enhancements

### 1. Advanced Conflict Resolution

- **Smart Merging**: Handle simultaneous renames on different devices
- **User Preferences**: Allow users to choose conflict resolution strategies

### 2. Performance Optimizations

- **Bulk Operations**: Batch multiple cleanup operations
- **Background Cleanup**: Defer cleanup to background process

### 3. Enhanced Monitoring

- **Sync Metrics**: Track rename frequency and cleanup efficiency
- **Health Checks**: Verify sync directory consistency

## Conclusion

This fix fundamentally resolves the duplicate creation issue when items are renamed, ensuring data integrity across sync operations. The solution maintains backwards compatibility while providing a robust foundation for future sync system enhancements.

The implementation correctly handles all item types (folders, books, notes) and includes comprehensive cleanup to prevent accumulation of orphaned directories and files in sync locations.