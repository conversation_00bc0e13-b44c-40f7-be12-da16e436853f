<template>
  <div class="chart-container">
    <h3 class="chart-title">Sessions by Category</h3>
    <div class="chart-wrapper">
      <Doughnut
        v-if="chartData && chartData.datasets[0].data.length > 0"
        :data="chartData"
        :options="chartOptions"
        :height="200"
      />
      <div v-else-if="chartData && chartData.datasets[0].data.length === 0" class="chart-empty">
        No sessions found for the selected period
      </div>
      <div v-else class="chart-loading">Loading chart data...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Doughnut } from 'vue-chartjs'
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'
import { useElectronAPI } from '../../../useElectronAPI'
import { useSettingsStore } from '../../../stores/settingsStore'
import { getChartColors, resolveTheme } from '../../../utils/themeUtils'

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend)

interface Session {
  id: number
  start_time: string
  duration?: number | null
  category?: string | null
  session_name?: string | null
  is_completed: number
}

const db = useElectronAPI()
const settingsStore = useSettingsStore()
const sessions = ref<Session[]>([])

// Get theme-aware colors
const categoryColors = computed(() => {
  const currentTheme = resolveTheme(settingsStore.currentTheme)
  return getChartColors(currentTheme)
})

const chartData = computed(() => {
  if (!sessions.value.length) {
    return {
      labels: [],
      datasets: [{
        data: [],
        backgroundColor: [],
        borderColor: [],
        borderWidth: 0
      }]
    }
  }

  // Group sessions by category and count them
  const categoryStats = sessions.value
    .filter(session => session.is_completed === 1)
    .reduce((acc, session) => {
      const category = session.category || 'Uncategorized'
      if (!acc[category]) {
        acc[category] = { count: 0, totalTime: 0 }
      }
      acc[category].count++
      acc[category].totalTime += session.duration || 0
      return acc
    }, {} as Record<string, { count: number; totalTime: number }>)

  const categories = Object.keys(categoryStats)
  const counts = categories.map(cat => categoryStats[cat].count)
  const colors = categories.map((_, index) => categoryColors.value[index % categoryColors.value.length])
  const borderColors = colors.map(color => color.replace('0.8', '1'))

  return {
    labels: categories,
    datasets: [
      {
        data: counts,
        backgroundColor: colors,
        borderColor: borderColors,
        borderWidth: 2,
        hoverBackgroundColor: colors.map(color => {
          // Get theme-aware hover color that matches app hover styling
          const currentTheme = resolveTheme(settingsStore.currentTheme)
          if (currentTheme === 'dark') {
            // In dark mode, make colors more prominent (higher opacity + slight brightness)
            return color.replace('0.8', '1.0').replace(/rgba\((\d+), (\d+), (\d+)/, (_, r, g, b) => {
              const newR = Math.min(255, parseInt(r) + 20)
              const newG = Math.min(255, parseInt(g) + 20)
              const newB = Math.min(255, parseInt(b) + 20)
              return `rgba(${newR}, ${newG}, ${newB}`
            })
          } else {
            // In light mode, make colors slightly darker for better contrast
            return color.replace('0.8', '1.0').replace(/rgba\((\d+), (\d+), (\d+)/, (_, r, g, b) => {
              const newR = Math.max(0, parseInt(r) - 20)
              const newG = Math.max(0, parseInt(g) - 20)
              const newB = Math.max(0, parseInt(b) - 20)
              return `rgba(${newR}, ${newG}, ${newB}`
            })
          }
        }),
        hoverBorderColor: borderColors,
        hoverBorderWidth: 3
      }
    ]
  }
})

const chartOptions = computed((): ChartOptions<'doughnut'> => {
  // Get theme-aware colors (matching other charts)
  const textColor = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-text-fallback').trim() || '#4a4a4a'
  const tooltipBg = getComputedStyle(document.documentElement).getPropertyValue('--color-bg-elevated').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-bg-fallback').trim() || 'rgba(0, 0, 0, 0.8)'
  const tooltipText = getComputedStyle(document.documentElement).getPropertyValue('--color-text-primary').trim() || getComputedStyle(document.documentElement).getPropertyValue('--color-chart-tooltip-text-fallback').trim() || '#fff'

  return {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        position: 'right',
        labels: {
          color: textColor,
          font: {
            family: 'Montserrat',
            size: 12,
            weight: 500
          },
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle'
        }
      },
      tooltip: {
        backgroundColor: tooltipBg,
        titleColor: tooltipText,
        bodyColor: tooltipText,
        borderColor: textColor,
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const category = context.label
            const count = context.parsed
            const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0)
            const percentage = ((count / total) * 100).toFixed(1)

            // Get total time for this category
            const categoryStats = sessions.value
              .filter(session => session.is_completed === 1 && (session.category || 'Uncategorized') === category)
              .reduce((total, session) => total + (session.duration || 0), 0)

            const hours = Math.floor(categoryStats / 3600)
            const minutes = Math.floor((categoryStats % 3600) / 60)
            const timeStr = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`

            return `${category}: ${count} sessions (${percentage}%) - ${timeStr}`
          }
        }
      }
    }
  }
})

const loadChartData = async () => {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const today = new Date().toISOString().split('T')[0]
    const sessionData = await db.timer.getSessionsByDateRange(thirtyDaysAgo, today)
    sessions.value = sessionData
  } catch (error) {
    console.error('Failed to load chart data:', error)
  }
}

onMounted(() => {
  loadChartData()
})

// Expose method to refresh data
defineExpose({
  refreshData: loadChartData
})
</script>

<style scoped>
.chart-container {
  background: var(--color-card-bg);
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  padding: 20px;
  margin-bottom: 15px;
}

.chart-title {
  color: var(--color-text-primary);
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 15px;
  font-family: 'Montserrat', sans-serif;
}

.chart-wrapper {
  height: 200px;
  position: relative;
}

.chart-loading,
.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-tertiary);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
  }
  
  .chart-wrapper {
    height: 180px;
  }
}
</style>
