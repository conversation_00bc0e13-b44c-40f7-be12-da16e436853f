// scripts/populateTestData.ts
import sqlite3, { Database } from 'sqlite3';
import path from 'node:path';
import fs from 'node:fs';
import os from 'node:os';

// Flag to determine if we should use the actual Noti database
// Set this to true with --use-real-db argument
let USE_REAL_DB = false;

// Database file locations
const TEST_DB_DIR = path.join(process.cwd(), 'test-data');
const TEST_DB_PATH = path.join(TEST_DB_DIR, 'noti-test-data.sqlite');

// Function to get the actual Noti database path
// This approximates what electron's app.getPath('userData') would return
function getNotiDbPath(): string {
    let appDataPath: string;

    // Determine the user's AppData path based on the platform
    if (process.platform === 'win32') {
        appDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'noti');
    } else if (process.platform === 'darwin') {
        appDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'noti');
    } else {
        // Linux and others
        appDataPath = path.join(os.homedir(), '.config', 'noti');
    }

    // Return the actual database path
    return path.join(appDataPath, 'noti-database.sqlite');
}

// Log file path
const LOG_FILE_PATH = path.join(process.cwd(), 'scripts', 'test_data_output.log');

// Simple file logger
let logStream: fs.WriteStream;
let isLogStreamClosed = false;

const initializeLogger = () => {
    // Ensure directory exists
    const logDir = path.dirname(LOG_FILE_PATH);
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
    // Clear previous log file or ensure it exists for appending
    if (fs.existsSync(LOG_FILE_PATH)) {
        fs.unlinkSync(LOG_FILE_PATH);
    }
    fs.writeFileSync(LOG_FILE_PATH, `${new Date().toISOString()} - Log initialized.\n`);
    logStream = fs.createWriteStream(LOG_FILE_PATH, { flags: 'a' });
    isLogStreamClosed = false;
};

const log = (message: string) => {
    console.log(message);
    if (logStream && !isLogStreamClosed) {
        logStream.write(`${new Date().toISOString()} - ${message}\n`);
    }
};

const logError = (message: string, error?: any) => {
    console.error(message, error);
    if (logStream && !isLogStreamClosed) {
        const errorMsg = error instanceof Error ? (error.stack || error.message) : JSON.stringify(error);
        logStream.write(`${new Date().toISOString()} - ERROR: ${message}\n${errorMsg}\n`);
    }
};

const closeLogger = () => {
    return new Promise<void>((resolve) => {
        if (logStream && !isLogStreamClosed) {
            isLogStreamClosed = true;
            logStream.end(() => {
                resolve();
            });
        } else {
            resolve();
        }
    });
};

// Get the database path based on configuration
function getDbPath(): string {
    if (USE_REAL_DB) {
        const realDbPath = getNotiDbPath();
        return realDbPath;
    } else {
        return TEST_DB_PATH;
    }
}

// Create and initialize the database
const initDatabase = (resetDatabase: boolean = false): Promise<Database> => {
    return new Promise((resolve, reject) => {
        const dbPath = getDbPath();
        const dbExists = fs.existsSync(dbPath);

        // Safety check for real database
        if (USE_REAL_DB && dbExists && resetDatabase) {
            log(`⚠️ WARNING: You are about to RESET the ACTUAL Noti database at: ${dbPath}`);
            log('This will DELETE ALL your existing notes, folders, and settings!');
            log('Press Ctrl+C NOW to abort if this is not what you want.');

            // Wait 5 seconds to give the user time to abort
            log('Waiting 5 seconds before proceeding...');
            setTimeout(() => {
                log('Proceeding with database reset...');
                continueInit(dbPath, dbExists, resetDatabase, resolve, reject);
            }, 5000);
        } else {
            continueInit(dbPath, dbExists, resetDatabase, resolve, reject);
        }
    });
};

const continueInit = (
    dbPath: string,
    dbExists: boolean,
    resetDatabase: boolean,
    resolve: (db: Database) => void,
    reject: (err: any) => void
) => {
    // Ensure directory exists for test database
    if (!USE_REAL_DB) {
        if (!fs.existsSync(TEST_DB_DIR)) {
            fs.mkdirSync(TEST_DB_DIR, { recursive: true });
        }
    }

    // Handle existing database
    if (dbExists) {
        if (resetDatabase) {
            log(`Removing existing database at ${dbPath}`);
            try {
                fs.unlinkSync(dbPath);
            } catch (err) {
                logError(`Failed to delete existing database: ${dbPath}`, err);
                reject(err);
                return;
            }
        } else if (USE_REAL_DB) {
            log(`Using existing Noti database at: ${dbPath}`);
            log('Test data will be ADDED to your existing database.');
        }
    }

    // Create/open database
    if (!dbExists || resetDatabase) {
        log(`Creating new database at ${dbPath}`);
    }

    const db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            logError('Failed to open/create database', err);
            reject(err);
            return;
        }

        // Enable foreign keys
        db.run('PRAGMA foreign_keys = ON', (pragmaErr) => {
            if (pragmaErr) {
                logError('Error enabling foreign keys', pragmaErr);
            }
        });

        // If using existing database, we assume schema is already set up
        if (dbExists && !resetDatabase) {
            log('Using existing database schema');
            resolve(db);
            return;
        }

        // Create tables
        db.serialize(() => {
            // Create Folders table first
            db.run(`CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                parent_id INTEGER,
                book_id INTEGER,
                color TEXT,
                "order" INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
            )`, (folderErr) => {
                if (folderErr) {
                    logError('Error creating folders table', folderErr);
                    reject(folderErr);
                    return;
                }

                // Create Notes table after folders table is created
                db.run(`CREATE TABLE IF NOT EXISTS notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT,
                    html_content TEXT,
                    folder_id INTEGER,
                    book_id INTEGER,
                    type TEXT,
                    color TEXT,
                    "order" INTEGER,
                    last_viewed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL
                )`, (notesErr) => {
                    if (notesErr) {
                        logError('Error creating notes table', notesErr);
                        reject(notesErr);
                        return;
                    }

                    // Create indexes for performance
                    db.run('CREATE INDEX IF NOT EXISTS idx_notes_folder_id ON notes (folder_id)', (idxErr1) => {
                        if (idxErr1) {
                            logError('Error creating index on notes.folder_id', idxErr1);
                            // Continue despite index error
                        }

                        db.run('CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders (parent_id)', (idxErr2) => {
                            if (idxErr2) {
                                logError('Error creating index on folders.parent_id', idxErr2);
                                // Continue despite index error
                            }

                            log('Database schema created successfully');
                            resolve(db);
                        });
                    });
                });
            });
        });
    });
};

// Close database connection
const closeDatabase = (db: Database): Promise<void> => {
    return new Promise((resolve, reject) => {
        db.close((err) => {
            if (err) {
                logError('Error closing database', err);
                reject(err);
                return;
            }
            log('Database connection closed');
            resolve();
        });
    });
};

// Folder API functions - Using a single database connection
const createFolder = (db: Database, folderData: { name: string; parent_id?: number; book_id?: number }): Promise<{ id: number; name: string }> => {
    return new Promise((resolve, reject) => {
        const { name, parent_id, book_id } = folderData;

        const sql = `INSERT INTO folders (name, parent_id, book_id) VALUES (?, ?, ?)`;
        db.run(sql, [name, parent_id || null, book_id || null], function(err) {
            if (err) {
                reject(err);
                return;
            }

            const id = this.lastID;
            resolve({ id, name });
        });
    });
};

// Notes API functions - Using a single database connection
const createNote = (db: Database, noteData: { title: string; content: string; folder_id: number }): Promise<{ id: number; title: string }> => {
    return new Promise((resolve, reject) => {
        const { title, content, folder_id } = noteData;

        const sql = `INSERT INTO notes (title, content, folder_id) VALUES (?, ?, ?)`;
        db.run(sql, [title, content, folder_id], function(err) {
            if (err) {
                reject(err);
                return;
            }

            const id = this.lastID;
            resolve({ id, title });
        });
    });
};

async function createFoldersAndNotes(n: number, resetDatabase: boolean) {
    const dbPath = getDbPath();
    log(`Starting data population with n = ${n}`);
    if (USE_REAL_DB) {
        log(`Using actual Noti database: ${dbPath}`);
    } else {
        log(`Using test database: ${dbPath}`);
    }

    let db: Database | null = null;

    try {
        // Initialize database (creates tables if they don't exist)
        db = await initDatabase(resetDatabase);
        log('Database initialized.');

        for (let i = 0; i < n; i++) {
            const topFolderName = `Test Top Folder ${String(i + 1).padStart(2, '0')}`;
            let createdTopFolder;
            try {
                createdTopFolder = await createFolder(db, { name: topFolderName });
                log(`Created Top Folder: ${createdTopFolder.name} (ID: ${createdTopFolder.id})`);
            } catch (e) {
                logError(`Failed to create top folder: ${topFolderName}`, e);
                continue; // Skip to next top folder if creation fails
            }

            for (let j = 0; j < n; j++) {
                const subFolderName = `Test Sub Folder ${String(j + 1).padStart(2, '0')} (in ${createdTopFolder.name})`;
                let createdSubFolder;
                try {
                    createdSubFolder = await createFolder(db, {
                        name: subFolderName,
                        parent_id: createdTopFolder.id,
                    });
                    log(`  Created Sub Folder: ${createdSubFolder.name} (ID: ${createdSubFolder.id}), Parent ID: ${createdTopFolder.id}`);
                } catch (e) {
                    logError(`Failed to create sub folder: ${subFolderName}`, e);
                    continue; // Skip to next sub folder
                }

                for (let k = 0; k < n; k++) {
                    const noteTitle = `Test Note ${String(k + 1).padStart(2, '0')} (in ${createdSubFolder.name})`;
                    try {
                        const createdNote = await createNote(db, {
                            title: noteTitle,
                            content: `This is test note ${k + 1} in folder ${createdSubFolder.name}. Timestamp: ${Date.now()}`,
                            folder_id: createdSubFolder.id,
                        });
                        log(`    Created Note: ${createdNote.title} (ID: ${createdNote.id}), Folder ID: ${createdSubFolder.id}`);
                    } catch (e) {
                        logError(`Failed to create note: ${noteTitle}`, e);
                    }
                }
            }
        }
        log('Data population script finished.');
    } catch (error) {
        logError('An error occurred during script execution:', error);
    } finally {
        try {
            if (db) {
                await closeDatabase(db);
            }
        } catch (e) {
            logError('Failed to close database connection:', e);
        }
    }
}

function checkArguments() {
    // Check if --use-real-db is in the arguments list
    if (process.argv.includes('--use-real-db')) {
        USE_REAL_DB = true;
    }

    // Check if --reset is in the arguments list
    const resetDatabase = process.argv.includes('--reset');

    // Get all numeric arguments (should be just one - the N parameter)
    const numericArgs = process.argv.filter(arg => !isNaN(parseInt(arg, 10)) && !arg.startsWith('-'));

    return {
        resetDatabase,
        remainingArgs: numericArgs
    };
}

async function main() {
    initializeLogger();

    // Parse command line arguments
    const { resetDatabase, remainingArgs } = checkArguments();

    // Get N parameter (number of items to create)
    const nParam = parseInt(remainingArgs[0], 10);
    if (isNaN(nParam) || nParam <= 0) {
        logError('Invalid N parameter.');
        console.error('Please provide a positive integer for N as a command line argument.');
        console.error('Usage: npm run populate-test-data -- <N> [--use-real-db] [--reset]');
        console.error('  --use-real-db: Use the actual Noti database instead of a test database');
        console.error('  --reset: Reset the database before adding test data (CAUTION with --use-real-db!)');
        await closeLogger();
        process.exit(1);
    }

    if (USE_REAL_DB) {
        console.log('\x1b[33m%s\x1b[0m', '⚠️  WARNING: Using the ACTUAL Noti database!');
        if (resetDatabase) {
            console.log('\x1b[31m%s\x1b[0m', '⚠️  WARNING: You specified --reset which will DELETE ALL EXISTING DATA!');
        } else {
            console.log('\x1b[33m%s\x1b[0m', 'Test data will be added to your existing database.');
        }
        console.log('Press Ctrl+C now to abort, or wait 3 seconds to continue...');
        await new Promise(resolve => setTimeout(resolve, 3000));
    }

    try {
        await createFoldersAndNotes(nParam, resetDatabase);

        // Output final location info
        const dbPath = getDbPath();
        log(`Database populated at: ${dbPath}`);
        if (USE_REAL_DB) {
            log('You can now run the Noti application to see the test data.');
        } else {
            log('You can open this database file in DB Browser for SQLite to view the data.');
        }
    } finally {
        await closeLogger();
    }
}

main().catch(async (e) => {
    logError('Unhandled error in main:', e);
    await closeLogger();
    process.exit(1);
});