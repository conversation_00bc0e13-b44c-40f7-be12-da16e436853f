# Noti Todo List

## 📝 Notes

### Tasks

### Completed ✓
- [x] Add Searchbar in Notes  
- [x] Add the export modal if note selected in the folder-content
- [x] Add the export modal if notes and folders are selected in the folder-content
- [x] Add import button functionality that opens PC file explorer
- [x] Add the search bar functionality in the toolbar
- [x] Add cancel functionality in the searchbar, with the cross icon like all the others
- [x] Add the functionality to open a note in the folder content
- [x] Add save modal in NotesView that resembles the Move Modal - Save note(s) in folders
- [x] Remove add button in the notes sidebar
- [x] PDF export Image
- [x] Allow only md, txt and noti files import, no images
- [x] Fix XSS vulnerability in import with md
- [x] Add change font color fonctionnality
- [x] Fix font change modal
- [x] Fix issue where you can spam ctrl+s even when no change was made to the note
- [x] Add Downloading bar when exporting
- [x] Fix the header to remain fixed when there is a lot of content in the note editor
- [x] Make Undo and Redo independant for each Note
- [x] Make sure that the font is also exported as PDF
- [x] Make the export fonctionnalities from NotesView in FoldersView

## 📚 Books

### Tasks

### Completed ✓
- [x] Improve book search for better results and easier re-search
- [x] Fix image resolution and sizing inside of bookcard
- [x] Set maximum amount of bookcards in recent notes to 4
- [x] Display message if trying to add book that already exists
- [x] Remove any mention of "OpenLibrary API" from the UI
- [x] Improve reactivity and speed of search and cover display
- [x] Fix bug where covers for books in search are incorrect
- [x] Fix time it takes to load the book info and cover once the add button is clicked
- [x] Fix the background refresh when the add button is clicked
- [x] Add the BookDetailsModal.vue
- [x] Add a way to delete a book that was accidentally added
- [x] Make the transition between adding a book and closing the modal smoother
- [x] Fix issue where editing without adding a cover removes the previous cover
- [x] Fix issue where folders are not automatically created after a book is added
- [x] Make notes moved to book folders linked to that book and viewable in bookdetailsmodal
- [x] Fix issue where notes created in book folders aren't given book_id or autogenerated title
- [x] Fix bug where cover gets deleted if book details changed right after adding
- [x] Make the search bar and New Note Button in BookDetailsModal/Notes Tab, the same width as class="notes-content" (Enzo)
- [x] Make the search bar same height as New Note button (Enzo)
- [x] Add a text that says to click on new book in the class="books-content" (only when no books) (Enzo)
- [x] Remove the click to escape Modals
- [x] Add year input verification like in AddBookManually in the Edit Mode
- [x] Add ISBN input verification (10-13 digits)
- [x] Remove the possiblity to add a negative number in the edit mode
- [x] Add error messages underneath the pages input in AddBookManually like Publication year when negative number, instead of not creating the book and giving this error code: Failed to add manual book: Error: Error invoking remote method 'books:create': Error: Page count must be a positive number
- [x] Add error messages underneath the inputs when adding the conditions for isbn and pages
- [x] Shorten the genre to one if more genres
- [x] Make sure title goes in a next line if too long
- [x] Shorten author to "Author: Author 1, ..." if more authors
- [x] Make the search functionnality fit if too long
- [x] Make sure that when you enter the edit mode in BookDetailsModal, and then switch to Notes tab, it cancels the edit mode (closes the edit mode)
- [x] Remove Note: cover_data column does not exist (expected for new installations) / Note: cover_path column does not exist (expected for new installations)
- [x] Make sure that the folder for a specific book gets moved to the root folder when the book got deleted only when it's not empty, if empty it gets deleted (not empty = folder or note are in the folder)
- [x] Make sure that notes belongs to a book, even when it's in a subfolder of the book folder (Books -> Harry Potter (where the notes get created for a book) -> Chapter 1
(subfolder) -> Notes)
- [x] Add the fonctionnality to open the recent note of a Book, and add icon on the right of it (recent-icon.svg)
- [x] Add a system that automatically expands language codes into their full language names (spa = Spanish)
- [x] Add "Modify" button to books in search for changing book cover and other info directly
- [x] Fix Go back button for EditBookModal
- [x] Add error message that explains you can't add a duplicate book: console error : BooksView.vue:487 Failed to add manual book: Error: Err
invoking remote method 'books:addFromOpenLibrary': Error: Failed to add book from OpenLibrary: Book with this OpenLibrary ID already exists i
your library
- [x] Make sure that when you open AddBookModal, it focuses the searchbar
- [x] Make sure that when you click on the go back button, it shows the last search you made before in AddBookModal (EditBookModal)
- [x] Make sure that when you click on the go back button, it shows the last search you made before in AddBookModal (AddBookManuallyModal)
- [x] Fix language data import in EditBookModal
- [x] Fix bug where after adding book, the addbookmodal still shows the last search
- [x] Add that the height of the Notes tab is always the same height as the Details tab
- [x] Add smoother responsive bookcard animation
- [x] Fix issue where the addbookdetails info doesnt fit the container

## 📁 Folders

### Tasks
- [ ] Change MoveModal to the one that is in NotesView

### Completed ✓
- [x] Implement the same system path as the one in the breadcrumb header
- [x] Add the amount of space the folders and their contents take in the folders-sidebar
- [x] Add the sort functionality
- [x] Rename Root to "All Folders"
- [x] Add the same click selection functionality that is in the notes-list to folder-list
- [x] Remove the loading animation/transition when changing folders
- [x] Remove Import Modal
- [x] Add the same click selection functionality in the folder content and link it to the checkbox functionality
- [x] Add move note modal
- [x] Add move folder modal
- [x] Add move elements modal
- [x] Add move functionality
- [x] Fix synchronization between both FoldersSidebar and FoldersContent
- [x] Remove New Folder Button in the folders-list
- [x] Fix Select Folder List
- [x] Make the All Folders Path in folders-list stay at its place when scrolling
- [x] Fix bug where deleted notes in folder-explorer don't disappear until folder page reload
- [x] Add style to the folders-list scrollbar
- [x] Fix the truncated number for path
- [x] Make it so that an image imported using ctrl+v gets correctly added to the media_files table
- [x] Fix bug where when you click to delete a subfolder and then go back to the parent folder, the delete button is still active in folder sidebar
- [x] Make it so that the "Books" folder in the folderNavigator correctly shows the amount of books in it, just like in the folder sidebar
- [x] Make sure if you add a book with an accent, it creates the folder with it
- [x] Fix bug where the rename note modal doesnt appear when renaming a note in the root folder
- [x] Fix bug where books folder in foldersnavigator still doesnt show the amount of books in it(not always, just sometimes, but idk why)
- [x] Fix issue where folderNavigator always says that a folder is empty even when it has a note
- [x] Disable rename and move and delete action for "Books"
- [x] Disable move action for folders in "Books"
- [x] In the Folders Sidebar, make sure that if the title of the folder is too long, it goes in a next line
- [x] Add color option to folders icon
- [x] Change the style of every scrollbar in FoldersView.vue to match the notes-list scrollbar
- [x] Fix the not updated Note delete counter, when deleting note in folder, going back, the note counter doesn't change

## ⏱️ Timer

### Tasks

### Completed ✓
- [x] Fix the dropdown menu in the AddSessionModal
- [x] Add database for sessions
- [x] Add StatCard.vue
- [x] Fix issue where cycles aren't counted if it is skipped prematurely
- [x] Fix issue where dropdown menu doesnt fit in screen
- [x] Fix issue where a scrollbar appears when you have too many sessions in session history
- [x] Remove placeholder emoji when session history is empty
- [x] Fix issue where timer control buttons shift to left when a session starts
- [x] Change number position glitching as time passes
- [x] Fix Modal (Sidebar bug, Teleport)
- [x] Remove play icon in the Start Session button of the AddSessionModal
- [x] Remove every hover text on buttons, elements, etc
- [x] Change icons for timer and the rest if needed
- [x] Fix issue where SessionCard clips the Nav Button
- [x] Remove click to escape modal of the AddSessionModal
- [x] Change style of modal to modal style of the application
- [x] Remove the color blue and replace with style of the application
- [x] Fix the searchbar

## 💾 Backup/ReverseBackup

### Tasks


### Completed ✓
- [x] Fix look of backup message in BackupSettings.vue
- [x] Fix backup ui component in settings page
- [x] Add backup history modal
- [x] Fix issue where auto-backup system doesn't trigger correctly
- [x] Fix issue where last backup container doesnt correctly show last sync date, if the backup was done through the auto-backup function
- [x] Add system to auto-backup system, that detects changes in backup itself and pulls them in, this way google drive/onedrive works seamlessly
- [x] Fix issue where import doesnt correctly recognize amount of books
- [x] Fix issue where book added through backup doesnt have the folder or ntoes in the right location
- [x] Fix issue where deleting book or folder or note, doesnt delete it in backup
- [x] Fix issue with reverse sync, where ti doesnt perfectly work
- [x] Fix issue where the rename inside app creates a duplicate folder
- [x] Make the manifest hidden
- [x] Try to add color to folder icons when importing backup/backing up
- [x] Fix issue where book added through backup doesnt get the cover image
- [x] Fix issue with show sync history modal, where it doesnt show history


## ⚙️ General

### Tasks
- [ ] Change the style of every modal to match the application style
- [ ] Make sure that every modal is a modal component in src\components\modals
- [ ] Complete all backend/optimization tasks
- [ ] Change blue elements to colors that fit the application style
- [ ] Add in general keyboard shortcut (espace, delete, enter)*
- [ ] Remove every hover text on buttons, elements, etc
- [ ] Organize every document
- [ ] Integrate Pinia State management into every page
- [ ] Create UML for entire project
- [ ] Create UML for each individual functionality, tracing path of data 

### Completed ✓
- [x] Remove generic window pop-up after export
- [x] Remove Toolbar connection with Folderlist
- [x] Remove shadow separator toolbar or header
- [x] Make the sidebar animation smoother
- [x] Fix bug where sidebar doesnt get grey overlay when a modal is opened
- [x] Put the open/close icon of sidebar on top of views
- [x] Develop the discord activity

## 💻 Website

### Tasks
- [ ] Develop a website

### Completed ✓
