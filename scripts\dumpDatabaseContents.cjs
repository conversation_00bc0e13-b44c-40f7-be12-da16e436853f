#!/usr/bin/env node

/**
 * Database Contents Dump Script (CommonJS version)
 * 
 * This script outputs the entire contents of the sync tables, folders, books, and notes tables
 * from the Noti SQLite database.
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const os = require('os');
const fs = require('fs');

// Get the database path (same as used in the main application)
const getDbPath = () => {
    if (process.platform === 'win32') {
        return path.join(os.homedir(), 'AppData', 'Roaming', 'noti', 'noti-database.sqlite');
    } else if (process.platform === 'darwin') {
        return path.join(os.homedir(), 'Library', 'Application Support', 'noti', 'noti-database.sqlite');
    } else {
        return path.join(os.homedir(), '.config', 'noti', 'noti-database.sqlite');
    }
};

// Function to print table contents in a formatted way
const printTableContents = (tableName, rows) => {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`TABLE: ${tableName.toUpperCase()}`);
    console.log(`${'='.repeat(60)}`);
    console.log(`Total records: ${rows.length}`);
    
    if (rows.length === 0) {
        console.log('No data found in this table.');
        return;
    }
    
    // Get column names from the first row
    const columns = Object.keys(rows[0]);
    
    // Print header
    console.log(`\n${'-'.repeat(60)}`);
    console.log('COLUMNS:', columns.join(' | '));
    console.log(`${'-'.repeat(60)}`);
    
    // Print each row
    rows.forEach((row, index) => {
        console.log(`\nRecord ${index + 1}:`);
        columns.forEach(column => {
            let value = row[column];
            
            // Handle different data types for better display
            if (value === null || value === undefined) {
                value = 'NULL';
            } else if (typeof value === 'string' && value.length > 100) {
                // Truncate very long strings
                value = value.substring(0, 100) + '...';
            }
            
            console.log(`  ${column}: ${value}`);
        });
    });
};

// Function to check if table exists
const checkTableExists = (db, tableName, callback) => {
    db.get(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
        (err, row) => {
            if (err) callback(err, false);
            else callback(null, !!row);
        }
    );
};

// Function to process a single table
const processTable = (db, tableName, callback) => {
    checkTableExists(db, tableName, (err, exists) => {
        if (err) {
            console.error(`❌ Error checking table '${tableName}':`, err.message);
            return callback(err);
        }
        
        if (!exists) {
            console.log(`\n⚠️  Table '${tableName}' does not exist in the database.`);
            return callback(null);
        }
        
        // Get all data from the table - handle tables without id column
        let query = `SELECT * FROM ${tableName}`;
        if (tableName === 'sync_state') {
            // sync_state uses composite primary key (item_type, item_id)
            query += ` ORDER BY item_type ASC, item_id ASC`;
        } else {
            query += ` ORDER BY id ASC`;
        }
        
        db.all(query, (err, rows) => {
            if (err) {
                console.error(`❌ Error querying table '${tableName}':`, err.message);
                return callback(err);
            }
            
            printTableContents(tableName, rows);
            callback(null);
        });
    });
};

// Main function to dump database contents
const dumpDatabaseContents = () => {
    const dbPath = getDbPath();
    
    console.log('Noti Database Contents Dump');
    console.log(`Database path: ${dbPath}`);
    console.log(`Timestamp: ${new Date().toISOString()}\n`);
    
    // Check if database file exists
    if (!fs.existsSync(dbPath)) {
        console.error(`❌ Database file not found at: ${dbPath}`);
        console.error('Make sure Noti has been run at least once to create the database.');
        process.exit(1);
    }
    
    const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
            console.error('❌ Error opening database:', err.message);
            process.exit(1);
        }
        
        console.log('✅ Successfully connected to database');
        console.log('\n📊 Starting database content dump...\n');
        
        // Define the tables we want to dump
        const tablesToDump = [
            // Sync tables
            'sync_state',
            'sync_sessions', 
            'sync_directory_state',
            // Main tables
            'folders',
            'books',
            'notes'
        ];
        
        let tableIndex = 0;
        
        // Process tables sequentially
        const processNextTable = () => {
            if (tableIndex >= tablesToDump.length) {
                // All tables processed
                console.log(`\n${'='.repeat(60)}`);
                console.log('✅ Database dump completed successfully!');
                console.log(`${'='.repeat(60)}\n`);
                
                db.close((closeErr) => {
                    if (closeErr) {
                        console.error('❌ Error closing database:', closeErr.message);
                        process.exit(1);
                    } else {
                        console.log('📝 Database connection closed.');
                    }
                });
                return;
            }
            
            const tableName = tablesToDump[tableIndex];
            tableIndex++;
            
            processTable(db, tableName, (err) => {
                if (err) {
                    console.error(`❌ Failed to process table '${tableName}':`, err.message);
                }
                // Continue with next table regardless of error
                processNextTable();
            });
        };
        
        // Start processing tables
        processNextTable();
    });
};

// Show help
const showHelp = () => {
    console.log(`
Noti Database Contents Dump Script (CommonJS version)

Usage: 
  node scripts/dumpDatabaseContents.cjs     # Output to console
  
The script will dump contents of these tables:
  - sync_state (sync status tracking)
  - sync_sessions (sync operation history) 
  - sync_directory_state (unified sync engine state)
  - folders (folder structure)
  - books (book information)
  - notes (notes content)

For more advanced options, use the TypeScript version:
  npm run dump-db -- --help
`);
};

// Check for help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// Run the script
dumpDatabaseContents();