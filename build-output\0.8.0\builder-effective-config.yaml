directories:
  output: build-output/${version}
  buildResources: build
$schema: https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json
appId: com.noti.app
asar: true
productName: Noti
files:
  - filter:
      - dist
      - dist-electron
npmRebuild: false
buildDependenciesFromSource: false
compression: normal
removePackageScripts: true
extraMetadata:
  main: dist-electron/main/index.js
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  artifactName: ${productName}-Mac-${version}-${arch}.${ext}
  category: public.app-category.productivity
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  artifactName: ${productName}-Windows-${version}-${arch}.${ext}
  sign: null
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
  artifactName: ${productName}-Linux-${version}-${arch}.${ext}
  category: Office
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  deleteAppDataOnUninstall: false
electronVersion: 29.4.6
