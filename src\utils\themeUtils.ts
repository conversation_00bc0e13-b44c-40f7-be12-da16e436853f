/**
 * Theme utility functions for the Noti application
 */

export type ThemeType = 'light' | 'dark'
// Future themes can be added here: | 'blue' | 'green' | 'purple' | 'high-contrast'

/**
 * Resolve the actual theme to apply based on the theme setting
 */
export function resolveTheme(theme: ThemeType): 'light' | 'dark' {
  // For future color themes, return the theme as-is: return theme
  return theme as 'light' | 'dark'
}

/**
 * Apply theme class to the document root
 */
export function applyTheme(theme: ThemeType): void {
  const resolvedTheme = resolveTheme(theme)
  const root = document.documentElement

  // Remove existing theme classes (add future themes here when needed)
  root.classList.remove('theme-light', 'theme-dark')

  // Add new theme class
  root.classList.add(`theme-${resolvedTheme}`)

  // Update meta theme-color for mobile browsers
  updateMetaThemeColor(resolvedTheme)
}

/**
 * Update the meta theme-color tag for mobile browsers
 */
function updateMetaThemeColor(theme: 'light' | 'dark'): void {
  let metaThemeColor = document.querySelector('meta[name="theme-color"]')

  if (!metaThemeColor) {
    metaThemeColor = document.createElement('meta')
    metaThemeColor.setAttribute('name', 'theme-color')
    document.head.appendChild(metaThemeColor)
  }

  // Define theme colors for mobile browser chrome
  // Future themes can be added here: 'blue': '#1e3a8a', 'green': '#166534', etc.
  const themeColors: Record<string, string> = {
    'light': '#ffffff',
    'dark': '#121212'
  }

  const color = themeColors[theme] || '#ffffff'
  metaThemeColor.setAttribute('content', color)
}



/**
 * Get CSS variable value for the current theme
 */
export function getCSSVariable(variableName: string): string {
  if (typeof window === 'undefined') return ''
  
  return getComputedStyle(document.documentElement)
    .getPropertyValue(variableName)
    .trim()
}

/**
 * Set CSS variable value
 */
export function setCSSVariable(variableName: string, value: string): void {
  if (typeof window === 'undefined') return
  
  document.documentElement.style.setProperty(variableName, value)
}

/**
 * Theme-aware color utilities
 */
export const themeColors = {
  primary: () => getCSSVariable('--color-primary'),
  primaryHover: () => getCSSVariable('--color-primary-hover'),
  bgPrimary: () => getCSSVariable('--color-bg-primary'),
  bgSecondary: () => getCSSVariable('--color-bg-secondary'),
  textPrimary: () => getCSSVariable('--color-text-primary'),
  textSecondary: () => getCSSVariable('--color-text-secondary'),
  borderPrimary: () => getCSSVariable('--color-border-primary'),
  borderSecondary: () => getCSSVariable('--color-border-secondary'),
}

/**
 * Initialize theme system
 */
export function initializeTheme(initialTheme: ThemeType = 'light'): void {
  // Apply initial theme
  applyTheme(initialTheme)
}

/**
 * Get theme-appropriate chart colors
 */
export function getChartColors(theme: 'light' | 'dark'): string[] {
  if (theme === 'dark') {
    return [
      'rgba(224, 224, 224, 0.8)',  // Light grey
      'rgba(170, 170, 170, 0.8)',  // Medium light grey
      'rgba(136, 136, 136, 0.8)',  // Medium grey
      'rgba(102, 102, 102, 0.8)',  // Medium dark grey
      'rgba(85, 85, 85, 0.8)',     // Dark grey
      'rgba(68, 68, 68, 0.8)',     // Very dark grey
      'rgba(153, 153, 153, 0.8)',  // Light medium grey
      'rgba(119, 119, 119, 0.8)',  // Dark medium grey
    ]
  }
  
  // Light theme colors (existing)
  return [
    'rgba(74, 74, 74, 0.8)',     // Main grey
    'rgba(136, 136, 136, 0.8)',  // Light grey
    'rgba(51, 51, 51, 0.8)',     // Dark grey
    'rgba(170, 170, 170, 0.8)',  // Medium light grey
    'rgba(102, 102, 102, 0.8)',  // Medium grey
    'rgba(187, 187, 187, 0.8)',  // Very light grey
    'rgba(85, 85, 85, 0.8)',     // Dark medium grey
    'rgba(153, 153, 153, 0.8)',  // Light medium grey
  ]
}
