# Manifest Field Verification Report

## Files Modified
- `electron/main/api/sync-logic/manifest-manager.ts` (analysis)
- `electron/main/database/database.ts` (schema analysis)
- `electron/main/database/database-api.ts` (interface analysis)

## What Was Done
Comprehensive analysis of the manifest generation system to verify field name mappings between database schema and manifest format, identifying critical inconsistencies that could cause sync failures.

## Issues Identified

### ❌ CRITICAL ISSUE 1: Field Name Mismatch in Book Metadata
**Problem**: Database field `publication_date` is mapped to manifest field `publicationYear`
- **Database field**: `publication_date` (TEXT)
- **Manifest field**: `publicationYear`
- **Location**: `manifest-manager.ts` line 188

**Evidence from code**:
```typescript
if (book.publication_date) bookMetadata.publicationYear = book.publication_date;
```

**Impact**: Creates confusion and potential data loss during sync operations.

### ❌ CRITICAL ISSUE 2: Field Name Mismatch in Book Metadata  
**Problem**: Database field `page_count` is mapped to manifest field `pageCount`
- **Database field**: `page_count` (INTEGER)
- **Manifest field**: `pageCount`
- **Location**: `manifest-manager.ts` line 190

**Evidence from code**:
```typescript
if (book.page_count !== undefined) bookMetadata.pageCount = book.page_count;
```

### ❌ CRITICAL ISSUE 3: Field Name Mismatch in Note Metadata
**Problem**: Database field `last_viewed_at` is mapped to manifest field `lastViewedAt`
- **Database field**: `last_viewed_at` (TIMESTAMP)
- **Manifest field**: `lastViewedAt`
- **Location**: `manifest-manager.ts` line 300

**Evidence from code**:
```typescript
if (note.last_viewed_at) noteMetadata.lastViewedAt = note.last_viewed_at;
```

### ❌ CRITICAL ISSUE 4: Inconsistent Timestamp Format Handling
**Problem**: Database uses `created_at`/`updated_at` but manifest uses `createdAt`/`updatedAt`
- **Database fields**: `created_at`, `updated_at` (snake_case)
- **Manifest fields**: `createdAt`, `updatedAt` (camelCase)
- **Location**: `manifest-manager.ts` lines 194-195

**Evidence from code**:
```typescript
if (book.created_at) bookMetadata.createdAt = book.created_at;
if (book.updated_at) bookMetadata.updatedAt = book.updated_at;
```

### ❌ CRITICAL ISSUE 5: Missing Cover URL in Manifest
**Problem**: Book covers are not properly included in manifest metadata
- **Database field**: `cover_url` is queried but not mapped to metadata
- **Manifest**: No `coverUrl` or `cover_url` field in metadata
- **Location**: `manifest-manager.ts` lines 125-131 (queried but not used)

**Evidence from database query**:
```sql
SELECT id, title as name, author, isbn, publication_date, description, 
       page_count, rating, status, olid, cover_url,
       created_at, updated_at 
FROM books 
```

But `cover_url` is never mapped to metadata in the book processing section.

## Database Schema vs Manifest Comparison

### Books Table Schema
```sql
CREATE TABLE IF NOT EXISTS books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    author TEXT,
    isbn TEXT,
    cover_url TEXT,
    publication_date TEXT,
    description TEXT,
    page_count INTEGER,
    current_page INTEGER,
    rating INTEGER,
    language TEXT,
    genres TEXT,
    olid TEXT,
    status TEXT,
    custom_fields TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### Manifest Example Analysis
The provided manifest example confirms these inconsistencies:
```json
{
  "metadata": {
    "author": "C. S. Forester",
    "publicationYear": "1942",    // ← Should be publication_date
    "pageCount": 142,             // ← Should be page_count  
    "rating": null,
    "status": "unread",
    "olid": "OL1937704W",
    "createdAt": "2025-06-16T19:55:17.710Z",  // ← Should be created_at
    "updatedAt": "2025-06-16T19:55:17.710Z"   // ← Should be updated_at
  }
}
```

## Missing Fields in Manifest
Several database fields are queried but not included in manifest metadata:
- `cover_url` → missing `coverUrl`
- `language` → not queried or included
- `genres` → not queried or included  
- `custom_fields` → not queried or included
- `current_page` → not queried or included

## Sync Import Compatibility Analysis

### ❌ CRITICAL CONFIRMATION: Import Logic Has Matching Issues
The import logic in `unified-sync-engine.ts` confirms the field mapping problems:

**Book Import Logic (lines 576, 578, 591, 593)**:
<augment_code_snippet path="electron/main/api/sync-logic/unified-sync-engine.ts" mode="EXCERPT">
````typescript
publication_date: metadata.publicationYear || existingBook.publication_date,
page_count: metadata.pageCount || existingBook.page_count,
// And for new books:
publication_date: metadata.publicationYear,
page_count: metadata.pageCount,
````
</augment_code_snippet>

**Export Logic Also Has Inconsistencies (lines 821, 823)**:
<augment_code_snippet path="electron/main/api/sync-logic/unified-sync-engine.ts" mode="EXCERPT">
````typescript
publicationYear: book.publication_date,
pageCount: book.page_count,
````
</augment_code_snippet>

### The Complete Field Mapping Problem
The system has a **bidirectional inconsistency**:

1. **Export**: Database `publication_date` → Manifest `publicationYear`
2. **Import**: Manifest `publicationYear` → Database `publication_date`

While this technically works, it creates several problems:

### ❌ CRITICAL ISSUE 6: Inconsistent Field Naming Convention
**Problem**: The system uses different naming conventions in different layers
- **Database Layer**: snake_case (`publication_date`, `page_count`, `last_viewed_at`)
- **Manifest Layer**: camelCase (`publicationYear`, `pageCount`, `lastViewedAt`)
- **API Layer**: Mixed conventions

**Impact**:
- Makes debugging difficult
- Creates confusion for developers
- Violates principle of least surprise
- Makes the codebase harder to maintain

### ❌ CRITICAL ISSUE 7: Missing Fields in Export/Import Cycle
**Problem**: Several database fields are not included in the export/import cycle:

**Missing from Export Query**:
- `language` - not queried in manifest generation
- `genres` - not queried in manifest generation
- `custom_fields` - not queried in manifest generation
- `current_page` - not queried in manifest generation

**Missing from Import Logic**:
- `cover_url` - queried but not mapped to manifest metadata
- `language` - not handled in import
- `genres` - not handled in import
- `custom_fields` - not handled in import

### ❌ CRITICAL ISSUE 8: Note Metadata Inconsistency
**Problem**: Note export and import have different field handling

**Export Logic (lines 933-935)**:
<augment_code_snippet path="electron/main/api/sync-logic/unified-sync-engine.ts" mode="EXCERPT">
````typescript
createdAt: note.created_at,
updatedAt: note.updated_at,
lastViewedAt: note.last_viewed_at
````
</augment_code_snippet>

**But Import Logic**: Only handles `type` and `color`, missing timestamp fields

### Data Loss Risk Assessment
The field mapping inconsistencies create these specific risks:

1. **Book Data Loss**: `language`, `genres`, `custom_fields`, `current_page` will be lost during sync
2. **Cover Data Loss**: `cover_url` is queried but not preserved in manifest metadata
3. **Note Timestamp Loss**: Export includes timestamps but import doesn't handle them
4. **Maintenance Complexity**: Mixed naming conventions make the system error-prone

## Recommendations

### 1. Immediate Fix Required
**Priority: CRITICAL** - Fix missing field mappings to prevent data loss:

```typescript
// In manifest-manager.ts, add missing book fields:
if (book.cover_url) bookMetadata.coverUrl = book.cover_url;
if (book.language) bookMetadata.language = book.language;
if (book.genres) bookMetadata.genres = book.genres;
if (book.custom_fields) bookMetadata.customFields = book.custom_fields;
if (book.current_page) bookMetadata.currentPage = book.current_page;
```

### 2. Standardize Field Naming
**Priority: HIGH** - Choose one convention and apply consistently:
- **Recommended**: Use camelCase in manifest for JSON compatibility
- Update all field mappings to be consistent
- Document the naming convention

### 3. Complete Import/Export Cycle
**Priority: HIGH** - Ensure all exported fields are imported:
- Add missing fields to import logic
- Add missing fields to database queries
- Test round-trip sync to verify no data loss

### 4. Add Validation
**Priority: MEDIUM** - Add validation to catch field mapping issues:
- Validate that all queried fields are mapped to manifest
- Validate that all manifest fields are handled in import
- Add unit tests for field mapping consistency
