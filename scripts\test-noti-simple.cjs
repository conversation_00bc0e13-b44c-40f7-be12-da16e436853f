#!/usr/bin/env node

/**
 * Simple test script for .noti file format
 * Run with: node scripts/test-noti-simple.js
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Test data
const testNoteData = {
    version: "1.0",
    type: "noti-note", 
    schema: "https://noti.app/schemas/note/v1.0",
    metadata: {
        id: 123,
        title: "Test Note",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        type: "text",
        color: "#ff0000",
        folder_id: null,
        book_id: null,
        export: {
            version: "1.0.0",
            app_version: "1.0.0", 
            exported_at: new Date().toISOString()
        }
    },
    content: {
        html: "<h1>Test Note</h1><p>This is a <strong>test</strong> note with HTML content.</p>",
        markdown: "# Test Note\n\nThis is a **test** note with HTML content.",
        plain_text: "Test Note\n\nThis is a test note with HTML content.",
        statistics: {
            word_count: 10,
            character_count: 45,
            reading_time: 1
        }
    },
    media: []
};

// Add integrity hash
const contentToHash = JSON.stringify({
    version: testNoteData.version,
    type: testNoteData.type,
    schema: testNoteData.schema,
    metadata: testNoteData.metadata,
    content: testNoteData.content,
    media: testNoteData.media
});

const contentHash = crypto.createHash('sha256').update(contentToHash).digest('hex');
testNoteData.integrity = {
    algorithm: "sha256",
    content_hash: contentHash
};

console.log('🧪 Testing .noti file format...\n');

// Test 1: Create .noti file
console.log('📝 Test 1: Creating .noti file...');
const testOutputDir = path.join(__dirname, '../test-output');
if (!fs.existsSync(testOutputDir)) {
    fs.mkdirSync(testOutputDir, { recursive: true });
}

const notiFilePath = path.join(testOutputDir, 'test-note.noti');
const notiContent = JSON.stringify(testNoteData, null, 2);

fs.writeFileSync(notiFilePath, notiContent);
console.log(`✅ Created: ${notiFilePath}`);
console.log(`📊 File size: ${(notiContent.length / 1024).toFixed(2)} KB`);

// Test 2: Validate file structure
console.log('\n🔍 Test 2: Validating file structure...');
try {
    const readContent = fs.readFileSync(notiFilePath, 'utf8');
    const parsedData = JSON.parse(readContent);
    
    // Validate structure
    const checks = [
        { name: 'Version check', test: parsedData.version === "1.0" },
        { name: 'Type check', test: parsedData.type === "noti-note" },
        { name: 'Schema check', test: parsedData.schema === "https://noti.app/schemas/note/v1.0" },
        { name: 'Metadata exists', test: !!parsedData.metadata },
        { name: 'Content exists', test: !!parsedData.content },
        { name: 'HTML content exists', test: !!parsedData.content.html },
        { name: 'Markdown content exists', test: !!parsedData.content.markdown },
        { name: 'Integrity exists', test: !!parsedData.integrity },
        { name: 'Title preserved', test: parsedData.metadata.title === "Test Note" },
        { name: 'Color preserved', test: parsedData.metadata.color === "#ff0000" }
    ];
    
    checks.forEach(check => {
        console.log(`  ${check.test ? '✅' : '❌'} ${check.name}`);
    });
    
    const allPassed = checks.every(check => check.test);
    console.log(`\n${allPassed ? '✅' : '❌'} Structure validation: ${allPassed ? 'PASSED' : 'FAILED'}`);
    
} catch (error) {
    console.log('❌ File validation failed:', error.message);
}

// Test 3: Integrity verification  
console.log('\n🔐 Test 3: Testing integrity verification...');
try {
    const readContent = fs.readFileSync(notiFilePath, 'utf8');
    const parsedData = JSON.parse(readContent);
    
    // Recalculate hash
    const verifyContent = JSON.stringify({
        version: parsedData.version,
        type: parsedData.type, 
        schema: parsedData.schema,
        metadata: parsedData.metadata,
        content: parsedData.content,
        media: parsedData.media
    });
    
    const verifyHash = crypto.createHash('sha256').update(verifyContent).digest('hex');
    const integrityValid = verifyHash === parsedData.integrity.content_hash;
    
    console.log(`  Original hash: ${parsedData.integrity.content_hash.substring(0, 16)}...`);
    console.log(`  Verified hash: ${verifyHash.substring(0, 16)}...`);
    console.log(`  ${integrityValid ? '✅' : '❌'} Integrity verification: ${integrityValid ? 'PASSED' : 'FAILED'}`);
    
} catch (error) {
    console.log('❌ Integrity verification failed:', error.message);
}

// Test 4: Format compliance
console.log('\n📋 Test 4: Checking format compliance...');
const formatChecks = [
    { name: 'File extension is .noti', test: notiFilePath.endsWith('.noti') },
    { name: 'Content-Type would be application/vnd.noti+json', test: true },
    { name: 'JSON is valid', test: true }, // Already validated above
    { name: 'Schema URL is accessible', test: testNoteData.schema.startsWith('https://') }
];

formatChecks.forEach(check => {
    console.log(`  ${check.test ? '✅' : '❌'} ${check.name}`);
});

// Test 5: Content fidelity
console.log('\n📄 Test 5: Content fidelity check...');
try {
    const readContent = fs.readFileSync(notiFilePath, 'utf8');
    const parsedData = JSON.parse(readContent);
    
    const htmlMatches = parsedData.content.html === testNoteData.content.html;
    const markdownMatches = parsedData.content.markdown === testNoteData.content.markdown;
    const statsExist = !!parsedData.content.statistics;
    
    console.log(`  ${htmlMatches ? '✅' : '❌'} HTML content preserved`);
    console.log(`  ${markdownMatches ? '✅' : '❌'} Markdown content preserved`);
    console.log(`  ${statsExist ? '✅' : '❌'} Statistics calculated`);
    
    if (statsExist) {
        console.log(`    📊 Word count: ${parsedData.content.statistics.word_count}`);
        console.log(`    📊 Character count: ${parsedData.content.statistics.character_count}`);
        console.log(`    📊 Reading time: ${parsedData.content.statistics.reading_time} min`);
    }
    
} catch (error) {
    console.log('❌ Content fidelity check failed:', error.message);
}

console.log('\n🏁 Test completed!');
console.log(`📁 Test file created at: ${notiFilePath}`);
console.log('\nTo test with the actual Noti app:');
console.log('1. Start the Noti application');
console.log('2. Try importing the test file: test-output/test-note.noti');
console.log('3. Check that the content, formatting, and metadata are preserved');

// Cleanup option
console.log('\nTo clean up test files, run:');
console.log(`rm -rf ${testOutputDir}`);