# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server (Vite + Electron in development mode)
- `npm run build` - Build for production (TypeScript check, Vite build, Electron packaging)
- `npm run preview` - Preview production build

### Database & Testing
- `npm run test:db` - Run database test utilities
- `npm run populate-test-data` - Populate database with test data for development
- `npm run populate-real-db` - Populate real database with test data
- `npm run populate-timer-test-data` - Populate timer-specific test data

### Validation Commands
- **Important**: Always run `npm run build` before committing changes to ensure TypeScript compilation succeeds
- **Pro Tip**: Never run "npm run build" if you want to check for type errors, run `npx tsc --noemit` or `vue-tsc --noEmit` instead
- Check for TypeScript errors with: `vue-tsc --noEmit`
- For linting: The project uses TypeScript's type checking as the primary validation method

## Application Architecture

### Core Technology Stack
- **Frontend**: Vue 3 + TypeScript + Vite + Vue Router + Pinia (state management)
- **Backend**: Electron main process + SQLite database + IPC communication
- **Rich Text Editing**: TipTap editor with extensive extensions
- **Charts**: Chart.js with Vue Chart.js wrapper
- **Book Data**: OpenLibrary API integration
- **Styling**: CSS custom properties with comprehensive theming system

### Application Structure
This is an Electron application with a clear separation between main process (Node.js) and renderer process (Vue.js):

#### Main Process (`electron/main/`)
- **Entry Point**: `index.ts` - Electron app initialization, window management, cleanup
- **Database**: `database/database.ts` - SQLite schema definition and initialization
- **IPC Layer**: `ipc-handlers.ts` - All IPC communication between main and renderer processes
- **API Modules**: Organized by feature in `api/` directory (notes, books, folders, timer, backup, etc.)
- **Media Protocol**: Custom `noti-media://` protocol for secure file access

#### Renderer Process (`src/`)
- **Views**: Main application screens (Dashboard, Notes, Books, Folders, Timer, Settings)
- **Components**: Reusable UI components organized by feature
- **Stores**: Pinia stores for state management (settings, timer)
- **Router**: Vue Router with hash-based routing for Electron compatibility

### Key Architectural Patterns

#### IPC Communication Pattern
- All database operations go through IPC handlers in `electron/main/ipc-handlers.ts`
- Frontend uses `window.electronAPI` (from preload script) to communicate with main process
- Consistent error handling across IPC boundaries
- Pattern: Frontend → api-bridge.ts → IPC → ipc-handlers.ts → API modules

#### Database Schema
- Comprehensive SQLite schema supporting notes, folders, books, timer sessions, media files, settings, and backup metadata
- Foreign key relationships with proper cascading deletes
- Indexing for performance on frequently queried columns
- Timestamp tracking with created_at and updated_at columns

#### State Management
- **Settings Store**: Theme management, user preferences, backup configurations
- **Timer Store**: Pomodoro timer state, session management, statistics
- Auto-initialization on app startup with proper cleanup
- Stores persist to database for cross-session continuity

#### Theme System
- CSS custom properties for comprehensive theming
- Light/dark mode support with system theme detection
- Theme switching with instant preview
- All colors centralized in `src/assets/themes.css`

#### Media File Handling
- Custom protocol (`noti-media://`) for secure image and media access
- Local storage in user data directory with database tracking
- Book cover download and caching system
- Automatic cleanup of orphaned media files

## Development Guidelines

### File Organization
- **Modular API design**: Each feature has its own API module in `electron/main/api/`
- **Component organization**: Components grouped by feature area (books/, modals/, timer/, etc.)
- **Consistent naming**: kebab-case for files, PascalCase for Vue components
- **Type definitions**: Centralized in `src/types/` directory

### Database Operations
- Always use the existing API modules rather than direct database queries
- Handle errors appropriately - database operations can fail
- Use transactions for operations affecting multiple tables
- Follow the established pattern: Frontend → IPC → API module → Database

### IPC Development
When adding new functionality:
1. Create API module in `electron/main/api/`
2. Add IPC handler in `ipc-handlers.ts`
3. Expose method in `api-bridge.ts`
4. Add type definitions to `electron-api.d.ts`
5. Use from frontend via `window.electronAPI`

### Component Development
- Follow Vue 3 Composition API patterns
- Use TypeScript for type safety
- Import commonly used utilities from existing modules
- Prefer `<script setup>` syntax for Vue components
- Use existing modal patterns from `src/components/modals/`

### Testing Data
- Use `npm run populate-test-data` to generate realistic test data
- Test with various data volumes to ensure performance
- Consider both empty states and populated states in UI development
- Use `npm run populate-timer-test-data` for timer-specific testing

## Common File Locations

### Configuration Files
- `package.json` - Dependencies and scripts
- `electron-builder.json5` - Electron packaging configuration
- `vite.config.ts` - Vite build configuration
- `tsconfig.json` - TypeScript configuration

### Key Application Files
- `src/App.vue` - Root component with theme management and store initialization
- `electron/main/index.ts` - Electron main process entry point
- `electron/main/database/database.ts` - Database schema and initialization
- `src/router/index.ts` - Vue Router configuration

### Feature-Specific Areas
- **Notes**: `src/components/notes/`, `electron/main/api/notes-api.ts`
- **Books**: `src/components/books/`, `electron/main/api/books-api.ts`
- **Timer**: `src/components/timer/`, `electron/main/api/timer-api.ts`, `src/stores/timerStore.ts`
- **Backup**: `electron/main/api/backup-*.ts`, `src/components/settings/BackupSettings.vue`

## Important Implementation Notes

### Security Considerations
- Web security is enabled in production
- Custom protocol handlers for safe file access
- No direct Node.js access from renderer process
- All file operations sanitized through `filename-sanitizer.ts`

### Performance Considerations
- Database queries are indexed for common operations
- Chart.js data updates are optimized for real-time display
- Auto-backup system runs on separate intervals to avoid blocking UI
- Virtual scrolling implemented for large lists (session history)
- Debounced search operations to prevent excessive queries

### Error Handling
- IPC operations include comprehensive error logging
- Database operations handle connection issues gracefully
- Frontend components show appropriate error states
- All async operations wrapped in try-catch blocks
- User-friendly error messages displayed via notifications

### Memory Management
- Stores have cleanup methods called on app unmount
- Database connections are properly closed on app quit
- Auto-backup manager is cleanly shutdown before exit
- Chart instances properly destroyed when components unmount
- Event listeners cleaned up in component lifecycle hooks

### Backup System Architecture
The backup system is the most complex feature:
- **Auto-backup**: Monitors changes via database hooks, creates incremental backups
- **Change Detection**: Tracks modifications to trigger backups
- **Storage Manager**: Handles file operations and folder structure
- **State Validator**: Ensures backup integrity
- **Event System**: Coordinates backup operations across components
- **Reverse Backup**: Imports data from backup files with conflict resolution

### Keybind System
Comprehensive keyboard shortcut system:
- **Global keybinds**: Available across all views (`src/composables/useGlobalKeybinds.ts`)
- **View-specific keybinds**: Books, Notes, Folders, Timer views have dedicated keybinds
- **Modal keybinds**: Escape to close, Enter to confirm
- **Editor keybinds**: Rich text formatting shortcuts in TipTap editor
- All keybinds defined in `src/types/keybinds.ts` with proper TypeScript typing

## Critical Knowledge for Development

### Most Common Pitfalls to Avoid

1. **IPC Communication is Mandatory**
   - NEVER access database directly from renderer process
   - ALWAYS use `window.electronAPI` for all backend operations
   - Missing IPC handlers cause silent failures

2. **Backup System Complexity**
   - The backup system has multiple interconnected components that must work in sync
   - Common issues: Double processing of notes, missing cleanup, event timing
   - Always check `processedNoteIds` to avoid duplicate backups
   - Backup events must be emitted AFTER database operations complete

3. **Timer State Management**
   - Timer state resets on navigation (stores unmount) - this is by design
   - Sessions record at cycle completion, not continuously
   - Auto-start requires explicit `shouldAutoStart` parameter

4. **Theme System Strict Rules**
   - NEVER hardcode colors - use CSS variables from `src/assets/themes.css`
   - Modal z-index must be > 10000 to appear above other modals
   - Always test both light and dark themes

5. **Memory Leak Prevention**
   - Clear ALL timeouts/intervals in `onUnmounted`
   - Destroy Chart.js instances on unmount
   - Remove all event listeners in cleanup

6. **File Path Security**
   - Use `noti-media://` protocol for media access
   - Always use absolute paths in main process
   - Quote paths with spaces in bash commands

7. **Build Before Commit**
   - ALWAYS run `npm run build` before committing
   - TypeScript errors will break production builds
   - Use `vue-tsc --noEmit` for quick checks

# AI Agent Documentation Rules

## Quick Rule
**Always create documentation for every bug fix, feature implementation, and code analysis. Place it in `Code Documentation/` in the correct subfolder.**

## Where to Put Documentation

### Bug Fixes → `Code Documentation/Bug Fixes/[Component]/`
- Timer issues → `Timer/`
- Session problems → `Session Management/`
- Book bugs → `Book Management/`
- UI issues → `UI Components/`
- Database problems → `Database/`

### New Features → `Code Documentation/Feature Implementations/[Component]/`
- Timer features → `Timer/`
- Book features → `Book Management/`
- AI features → `AI Integration/`
- Export features → `Export System/`

### Code Analysis → `Code Documentation/Code Analysis/`
- Single component review → `Component Analysis/`
- System-wide analysis → `Codebase Analysis/`

### Design Plans → `Code Documentation/Architecture Plans/[Component]/`

## Required in Every Doc
```markdown
# Title

## Files Modified
- List changed files

## What Was Done
- Description

## How It Was Fixed/Implemented
- Solution details
```

## File Naming
- Use kebab-case: `timer-bug-fix.md`
- Be descriptive: `book-search-enhancement.md`
- Include component: `session-state-fix.md`

## That's It!
When in doubt, check `Documentation-System-Overview.md` for details.
```