# Memory Leak Fix: Search Timeout Cleanup in BooksView

## Files Changed
- `src/views/BooksView.vue`

## Section
- Views / Books View Component

## Issue Description
The BooksView component had a memory leak vulnerability where the search debouncing timeout (`searchTimeout`) was not being cleared when the component unmounted. This could lead to memory leaks if the component was destroyed while a search timeout was still pending.

## Root Cause
The component was using `setTimeout` for search debouncing but only clearing it in the watch function when a new search was initiated. If the component unmounted while a timeout was active, the timeout would continue to exist in memory.

## Solution
Fixed the memory leak by:

1. **Added `onUnmounted` import**: Added `onUnmounted` to the Vue imports from the composition API
2. **Implemented cleanup logic**: Added an `onUnmounted` lifecycle hook that:
   - Checks if `searchTimeout` exists
   - Clears the timeout using `clearTimeout()`
   - Sets `searchTimeout` to `null` for good measure

## Code Changes
```javascript
// Added onUnmounted to imports
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';

// Added cleanup in lifecycle section
onUnmounted(() => {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
        searchTimeout = null;
    }
});
```

## Impact
- Prevents memory leaks when the BooksView component is unmounted
- Ensures proper cleanup of pending timeouts
- Improves overall application performance and stability
- No functional changes to the search behavior 