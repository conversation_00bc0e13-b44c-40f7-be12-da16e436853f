# Sync System Architecture Analysis

## Overview

The Noti application implements a sophisticated sync system that builds upon and extends the existing backup infrastructure. The sync system is designed to support bidirectional synchronization between multiple devices while maintaining backward compatibility with the backup system.

## Current Architecture

### Core Components

1. **SyncEngine** (`sync-engine.ts`)
   - Extends `BackupEngine` to leverage existing backup infrastructure
   - Implements three sync modes: upload, download, and bidirectional
   - Manages sync sessions with comprehensive progress tracking
   - Integrates conflict detection and resolution mechanisms

2. **SyncManifestManager** (`sync-manifest.ts`)
   - Manages sync manifests that track all items and their states
   - Includes enhanced directory structure mapping for fast reconstruction
   - Supports both database-to-manifest and backup-to-manifest generation
   - Implements manifest comparison and diff generation

3. **SyncConfigManager** (`sync-config.ts`)
   - Singleton pattern for global sync configuration
   - Manages device identification and sync settings
   - Persists configuration in SQLite database
   - Tracks sync status and history

4. **SyncConflictDetector** (`sync-conflict-detector.ts`)
   - Sophisticated conflict detection beyond simple timestamp comparison
   - Categorizes conflicts into 8 types (content, simultaneous edit, move, etc.)
   - Analyzes conflict severity and provides resolution recommendations
   - Supports both basic and enhanced conflict detection modes

5. **SyncConflictResolver** (`sync-conflict-resolver.ts`)
   - Automated conflict resolution engine
   - Implements multiple resolution strategies (newer wins, larger wins, merge, backup both)
   - Maintains compatibility with manual resolution requirements
   - Creates backups of conflicting versions before resolution

6. **SyncLockManager** (`sync-lock-manager.ts`)
   - Prevents concurrent sync operations across devices
   - Implements heartbeat mechanism for lock maintenance
   - Handles process cleanup and lock expiration
   - Supports multiple operation types (sync, backup, restore)

## Relationship with Backup System

### Inheritance Model
```
BackupEngine
    ↑
    |
SyncEngine (extends BackupEngine)
```

The `SyncEngine` class extends `BackupEngine`, which means:
- All backup functionality is available to sync operations
- Sync can leverage existing backup storage mechanisms
- Backup operations automatically create sync manifests (as of recent commits)

### Shared Infrastructure

1. **Storage Layer**
   - Both systems use `BackupStorageManager` for file operations
   - Same directory structure for backup and sync
   - Shared file format support (MD/Noti)

2. **Change Detection**
   - Both use `ChangeDetector` for identifying modified items
   - Shared timestamp-based change tracking
   - Common database schema for tracking changes

3. **Database Schema**
   - Backup tables: `backup_metadata`, `backup_items`, `backup_deletions`
   - Sync tables: `sync_config`, `sync_history`, `sync_conflicts`
   - Both systems interact with core tables: `notes`, `folders`, `books`

## How Sync Works

### 1. Initialization Phase
```typescript
// SyncEngine initialization
const syncEngine = new SyncEngine(backupConfig);
await syncEngine.initialize();
```
- Loads sync configuration
- Initializes manifest manager with remote directory
- Sets up lock manager and conflict resolver
- Establishes cleanup handlers

### 2. Upload Sync Flow
```
1. Validate sync environment
2. Acquire sync lock
3. Generate manifest from current database state
4. Load remote manifest (if exists)
5. Determine items to upload (newer local items)
6. Perform backup operations for changed items
7. Update remote manifest
8. Release lock
```

### 3. Download Sync Flow
```
1. Validate sync environment
2. Acquire sync lock
3. Load remote manifest
4. Generate local manifest
5. Determine items to download (newer remote items)
6. Import items using ReverseBackupImporter
7. Update local state
8. Release lock
```

### 4. Bidirectional Sync Flow
```
1. Load both local and remote manifests
2. Create sync plan with:
   - Items to upload (local newer)
   - Items to download (remote newer)
   - Conflicts (both modified)
   - Items to delete
3. Resolve conflicts (automated or manual)
4. Execute upload operations
5. Execute download operations
6. Create final manifest
7. Save updated manifest
```

## Manifest-Based Approach

### Manifest Structure
```typescript
interface SyncManifest {
    version: string;
    deviceId: string;
    deviceName: string;
    timestamp: string;
    format: 'md' | 'noti';
    directoryStructure: Record<string, DirectoryNode>;
    items: Record<string, SyncManifestItem>;
    summary: {
        totalItems: number;
        totalSize: number;
        noteCount: number;
        folderCount: number;
        bookCount: number;
    };
}
```

### Benefits
1. **Fast Comparison**: Hash-based change detection
2. **Directory Preservation**: Complete folder hierarchy tracking
3. **Multi-Device Support**: Device-specific manifest tracking
4. **Conflict Detection**: Comprehensive metadata for conflict analysis

## Conflict Detection and Resolution

### Conflict Types
1. **Content Conflict**: Different content with similar timestamps
2. **Simultaneous Edit**: Edits within 30 seconds
3. **Title Conflict**: Same content, different titles
4. **Move Conflict**: Item moved to different locations
5. **Deletion Conflict**: One side deleted, other modified
6. **Creation Conflict**: Different items with same name
7. **Metadata Conflict**: Different metadata, same content
8. **Directory Structure Conflict**: Folder hierarchy changes

### Resolution Strategies
1. **Auto Newer Wins**: Newer timestamp automatically wins
2. **Auto Larger Wins**: Larger file size wins
3. **Auto Merge Possible**: Simple content merge
4. **Manual Required**: User intervention needed
5. **Backup Both**: Keep both versions with different names

## Multi-Device Support

### Device Configuration
- Unique device ID (UUID)
- Device name (hostname)
- Per-device sync settings
- Conflict resolution preferences

### Sync Coordination
- Lock files prevent concurrent operations
- Heartbeat mechanism for active sync tracking
- Automatic lock cleanup on process exit
- Stale lock detection and recovery

## Extension Possibilities

### 1. Replace Backup System Entirely
The sync system could replace the backup system by:
- Making all backup operations sync-aware
- Using manifests for all backup tracking
- Implementing single-device "sync" as backup
- Leveraging conflict resolution for backup integrity

### 2. Cloud Integration
```typescript
// Potential cloud adapter interface
interface CloudStorageAdapter {
    upload(path: string, content: Buffer): Promise<void>;
    download(path: string): Promise<Buffer>;
    list(prefix: string): Promise<string[]>;
    delete(path: string): Promise<void>;
}
```

### 3. Real-time Sync
- File system watchers for instant change detection
- WebSocket/SSE for multi-device notifications
- Operational transformation for concurrent editing
- CRDT (Conflict-free Replicated Data Types) for notes

### 4. Selective Sync
- Per-folder sync configuration
- Tag-based sync filters
- Size-based sync policies
- Priority-based sync queues

### 5. Sync Plugins
```typescript
interface SyncPlugin {
    beforeSync(manifest: SyncManifest): Promise<void>;
    afterSync(result: SyncResult): Promise<void>;
    onConflict(conflict: ConflictDetails): Promise<ConflictResolution>;
}
```

## Migration Path: Backup to Sync

### Phase 1: Coexistence
- Backup system remains primary
- Sync system optional/experimental
- Shared storage and manifest generation

### Phase 2: Integration
- All backups generate sync manifests
- Sync UI integrated into backup settings
- Conflict resolution for backup operations

### Phase 3: Unification
- Single sync/backup engine
- Backup becomes "local sync"
- Cloud sync as remote backup

### Phase 4: Deprecation
- Remove legacy backup code
- Migrate all backup settings to sync
- Full sync-based architecture

## Performance Considerations

### Current Optimizations
1. **Incremental Sync**: Only changed items
2. **Parallel Operations**: Batch processing
3. **Indexed Queries**: Database performance
4. **Efficient Hashing**: SHA-256 for change detection

### Future Optimizations
1. **Delta Sync**: Only sync changed portions
2. **Compression**: Reduce transfer sizes
3. **Caching**: Local manifest caching
4. **Chunking**: Large file handling

## Security Considerations

### Current Security
- Local file system permissions
- No encryption in transit
- No authentication between devices
- Trust-based conflict resolution

### Enhanced Security
1. **Encryption**: End-to-end encryption for sync data
2. **Authentication**: Device pairing and verification
3. **Audit Trail**: Complete sync history logging
4. **Access Control**: Per-item sync permissions

## Conclusion

The sync system provides a robust foundation for multi-device synchronization while maintaining compatibility with the existing backup system. Its extensible architecture allows for:

1. **Gradual Migration**: From backup-only to full sync
2. **Cloud Integration**: Easy addition of cloud storage backends
3. **Enhanced Features**: Real-time sync, selective sync, plugins
4. **Performance Scaling**: Optimizations for large datasets

The manifest-based approach with sophisticated conflict detection and resolution makes it suitable for both local backup replacement and distributed synchronization scenarios.