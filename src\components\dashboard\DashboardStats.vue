<template>
  <div class="dashboard-stats">
    <div class="stats-grid">
      <!-- Total Notes -->
      <div class="stat-card notes-icon">
        <div class="icon-container">
          <img src="/icons/notes-icon.svg" alt="Notes" width="20" height="20" />
        </div>
        <div class="stat-number">{{ stats.totalNotes }}</div>
        <div class="stat-label">Total Notes</div>
      </div>

      <!-- Total Books -->
      <div class="stat-card books-icon">
        <div class="icon-container">
          <img src="/icons/books-icon.svg" alt="Books" width="20" height="20" />
        </div>
        <div class="stat-number">{{ stats.totalBooks }}</div>
        <div class="stat-label">Total Books</div>
      </div>

      <!-- Total Focus Time -->
      <div class="stat-card timer-icon">
        <div class="icon-container">
          <img src="/icons/timer-icon.svg" alt="Focus Time" width="20" height="20" />
        </div>
        <div class="stat-number">{{ stats.totalFocusTime }}</div>
        <div class="stat-label">Total Focus Time</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useElectronAPI } from '../../useElectronAPI'

interface DashboardStats {
  totalNotes: number
  totalBooks: number
  totalFocusTime: string
}

const db = useElectronAPI()

const stats = ref<DashboardStats>({
  totalNotes: 0,
  totalBooks: 0,
  totalFocusTime: '0h'
})

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours === 0) {
    return `${minutes}m`
  }
  
  return minutes === 0 ? `${hours}h` : `${hours}h ${minutes}m`
}

const loadStats = async () => {
  try {
    // Load notes count
    const notes = await db.notes.getAll()
    stats.value.totalNotes = notes.length

    // Load books count
    const books = await db.books.getAll()
    stats.value.totalBooks = books.length

    // Load timer stats - get sessions from last 30 days to calculate stats
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0]
    const todayStr = new Date().toISOString().split('T')[0]

    const sessions = await db.timer.getSessionsByDateRange(thirtyDaysAgoStr, todayStr)
    const completedSessions = sessions.filter(s => s.is_completed === 1)

    const totalFocusSeconds = completedSessions.reduce((total, session) => total + (session.duration || 0), 0)
    stats.value.totalFocusTime = formatDuration(totalFocusSeconds)

  } catch (error) {
    console.error('Failed to load dashboard stats:', error)
  }
}

onMounted(() => {
  loadStats()
})

// Expose refresh method for parent components
defineExpose({
  refreshStats: loadStats
})
</script>

<style scoped>
.dashboard-stats {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 24px;
}

.stat-card {
  background-color: var(--color-card-bg);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid var(--color-dashboard-stat-border);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-1px);
  border-color: var(--color-border-hover);
}

.icon-container {
  margin-bottom: 16px;
  width: 48px;
  height: 48px;
  background-color: var(--color-dashboard-action-icon-bg);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-dashboard-action-icon-border);
}

.icon-container img {
  width: 24px;
  height: 24px;
  filter: var(--icon-filter);
  transition: filter 0.2s ease;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-dashboard-stat-label);
  text-align: center;
  font-weight: 500;
  opacity: 0.8;
}

@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-stats {
    margin-bottom: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
    border-radius: 10px;
  }

  .icon-container {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
    border-radius: 10px;
  }

  .icon-container img {
    width: 20px;
    height: 20px;
  }

  .stat-number {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }

  .stat-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-number {
    font-size: 1.4rem;
  }
}
</style>
