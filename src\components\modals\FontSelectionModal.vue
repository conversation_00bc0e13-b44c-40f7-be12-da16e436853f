<template>
  <div class="modal-overlay">
    <div class="font-selection-modal">
      <div class="close-icon" @click="$emit('close')">
        <img src="/icons/close-icon.svg" alt="Close" />
      </div>
      
      <div class="modal-header">
        <h1 class="modal-title">Font Settings</h1>
        <div class="modal-subtitle">Select a font for your text</div>
      </div>
      
      <div class="divider"></div>
      
      <div class="modal-content">
        <!-- Preview of selected font -->
        <div class="font-preview-section" v-if="selectedFont">
          <div class="section-header">
            <h2>Preview</h2>
          </div>
          <div class="font-preview-container" :style="{ fontFamily: selectedFont }">
            <p class="preview-title">{{ selectedFont }}</p>
            <p class="preview-text">The quick brown fox jumps over the lazy dog.</p>
            <p class="preview-alphabet">ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789</p>
          </div>
        </div>
        
        <!-- Available fonts section -->
        <div class="font-section">
          <div class="section-header">
            <h2>Available Fonts</h2>
          </div>

          <div class="font-options">
            <div 
              v-for="font in builtInFonts" 
              :key="font"
              class="font-option" 
              :class="{ 'selected': selectedFont === font }"
              :style="{ fontFamily: font }"
              @click="selectFont(font)"
            >
              <span class="font-preview">{{ font }}</span>
              <span class="check-icon" v-if="selectedFont === font">✓</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="divider"></div>
      
      <div class="modal-footer">
        <button class="btn btn-cancel" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn btn-primary" @click="applyFont">
          Apply
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'FontSelectionModal',
  props: {
    currentFont: {
      type: String,
      default: 'Montserrat'
    }
  },
  emits: ['close', 'apply-font'],
  setup(props, { emit }) {
    // List of built-in fonts
    const builtInFonts = [
      'Montserrat',
      'Arial',
      'Times New Roman',
      'Georgia',
      'Courier New',
      'Verdana',
      'Helvetica',
      'Roboto',
      'Open Sans',
      'Lato'
    ];
    
    // State for font selection
    const selectedFont = ref(props.currentFont || 'Montserrat');
    
    // Apply the selected font
    const applyFont = () => {
      // Force emit with the exact font name string
      const fontName = selectedFont.value.trim();
      console.log('Applying font:', fontName);
      
      // Make sure we're emitting a valid font name
      if (fontName) {
        // Add debug info
        console.log('Emitting apply-font event with font:', fontName);
        
        // Emit the apply-font event
        emit('apply-font', fontName);
        
        // Close the modal
        emit('close');
      } else {
        console.error('No font selected');
      }
    };
    
    // Function to select a font
    const selectFont = (fontName: string) => {
      selectedFont.value = fontName;
    };
    
    // Watch for prop changes
    watch(() => props.currentFont, (newFont) => {
      if (newFont) {
        selectedFont.value = newFont;
      }
    });

    return {
      builtInFonts,
      selectedFont,
      selectFont,
      applyFont
    };
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.font-selection-modal {
  max-width: 700px;
  width: 90%;
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0px 4px 30px var(--color-card-hover-shadow);
  position: relative;
  font-family: 'Montserrat', sans-serif;
  z-index: 10000;
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.font-preview-section {
  margin-bottom: 20px;
  background-color: var(--color-bg-tertiary);
  border-radius: 8px;
  padding: 15px;
}

.font-preview-container {
  padding: 15px;
  background-color: var(--color-bg-primary);
  border-radius: 6px;
  border: 1px solid var(--color-border-secondary);
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--color-text-primary);
}

.preview-text {
  font-size: 16px;
  margin-bottom: 10px;
  color: var(--color-text-primary);
}

.preview-alphabet {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.close-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  cursor: pointer;
  z-index: 10;
}

.close-icon img {
  width: 24px;
  height: 24px;
}

.modal-header {
  padding: 40px 40px 20px 40px;
  text-align: left;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 24px;
  font-weight: 400;
  margin: 0;
}

.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
  flex-shrink: 0;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 40px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Custom scrollbar styling for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.font-section {
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h2 {
  font-size: 18px;
  color: var(--color-text-primary);
  font-weight: 600;
  margin: 0;
}

.font-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.font-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--color-bg-primary);
}

.font-option:hover {
  background-color: var(--color-nav-item-hover);
}

.font-option.selected {
  background-color: var(--color-nav-item-hover);
  border-color: var(--color-border-hover);
}

.font-preview {
  font-size: 16px;
  color: var(--color-text-primary);
}

.check-icon {
  color: var(--color-primary);
  font-weight: bold;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px 40px;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-cancel {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}
</style>
