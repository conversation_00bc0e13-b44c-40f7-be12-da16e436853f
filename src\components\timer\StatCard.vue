<template>
  <div class="stat-card">
    <div class="stat-card-container"></div>
    <div class="stat-value">{{ value }}</div>
    <div class="stat-title">{{ title }}</div>
    <div class="delete-icon-wrapper" v-if="showDelete" @click="$emit('delete')">
      <div>
        <svg
          width="17"
          height="17"
          viewBox="0 0 17 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="trash-icon"
        >
          <path
            d="M7.96053 0.759741C7.38178 0.759741 6.87877 0.746738 6.43386 0.920859L6.39989 0.934442C6.06151 1.07301 5.76068 1.28884 5.52163 1.56442L5.49793 1.59218L5.49788 1.59222L5.48364 1.60941C5.22441 1.92607 5.08098 2.32644 4.92884 2.78093L4.82828 3.08183H2.27779C1.84824 3.08183 1.5 3.42836 1.5 3.85585C1.5 4.28333 1.84824 4.62987 2.27779 4.62987H3.10569L3.68021 13.2057C3.70665 13.6005 3.72894 13.9417 3.77019 14.2214C3.81149 14.5013 3.87889 14.7777 4.0246 15.042L4.03896 15.0676L4.039 15.0677C4.27246 15.4754 4.62455 15.8032 5.04879 16.0078L5.04883 16.0079C5.33195 16.1444 5.62265 16.1954 5.91522 16.2185C6.19833 16.2409 6.54193 16.2403 6.93963 16.2403H10.0604C10.4581 16.2403 10.8017 16.2409 11.0848 16.2185C11.3774 16.1954 11.6681 16.1444 11.9512 16.0079L11.9512 16.0078C12.3689 15.8064 12.7166 15.4856 12.95 15.0867L12.961 15.0676C13.1168 14.7956 13.1872 14.5103 13.2299 14.2214C13.266 13.9767 13.2875 13.6849 13.3101 13.3512L13.8943 4.62987H14.7222C15.1518 4.62987 15.5 4.28333 15.5 3.85585C15.5 3.42836 15.1518 3.08183 14.7222 3.08183H12.1717L12.1385 2.98262L12.0712 2.78098C11.9163 2.31827 11.7704 1.91152 11.5021 1.59218L11.4784 1.56442C11.2313 1.27962 10.9183 1.05869 10.5662 0.920859L10.5452 0.912831C10.1055 0.747138 9.60917 0.759741 9.03947 0.759741H7.96053ZM11.7576 13.2531C11.7348 13.5885 11.7172 13.8173 11.6908 13.9965C11.662 14.1919 11.6304 14.2646 11.6094 14.3013L11.602 14.314C11.5243 14.444 11.4098 14.5487 11.2727 14.6148C11.2345 14.6332 11.1595 14.6597 10.9617 14.6753C10.7544 14.6917 10.4829 14.6922 10.0604 14.6922H6.93963C6.51716 14.6922 6.24563 14.6917 6.03831 14.6753C5.8528 14.6606 5.77526 14.6364 5.73484 14.6183L5.72723 14.6148C5.58574 14.5465 5.46838 14.4372 5.39058 14.3013C5.36956 14.2646 5.33804 14.1917 5.30923 13.9965C5.27903 13.7918 5.26041 13.5222 5.23233 13.1027L5.23231 13.1027L4.6647 4.62987H5.3714C5.38256 4.63012 5.39369 4.63009 5.4048 4.62987H11.5953C11.6064 4.63009 11.6175 4.63012 11.6286 4.62987H12.3353L11.7576 13.2531ZM9.03947 2.30778C9.72867 2.30778 9.8834 2.31998 9.98698 2.35776L9.99684 2.3615C10.118 2.40893 10.2253 2.48588 10.3088 2.58528C10.3636 2.65052 10.41 2.74188 10.5295 3.08183H6.47052C6.59 2.74194 6.63641 2.65051 6.69126 2.58523C6.7722 2.48893 6.87536 2.41374 6.99183 2.36604L7.00316 2.3615C7.1072 2.32079 7.24913 2.30778 7.96053 2.30778H9.03947Z"
            fill="var(--color-text-tertiary)"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [String, Number],
      required: true
    },
    showDelete: {
      type: Boolean,
      default: false
    }
  },
  emits: ['delete']
})
</script>

<style scoped>
.stat-card {
  width: 100%;
  min-width: 248px;
  height: 69px;
  position: relative;
  flex-shrink: 0;
}

@media (max-width: 640px) {
  .stat-card {
    min-width: 0;
    width: 100%;
  }
}

.stat-card-container {
  width: 100%;
  height: 69px;
  flex-shrink: 0;
  border-radius: 10px;
  border: 1px solid var(--color-card-border);
  position: absolute;
  left: 0px;
  top: 0px;
  background-color: var(--color-card-bg);
}

.stat-value {
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-weight: 700;
  font-size: 16px;
  color: var(--color-text-primary);
  width: 150px;
  position: absolute;
  left: 21px;
  top: 35px;
  height: 20px;
}

@media (max-width: 991px) {
  .stat-value {
    width: calc(100% - 80px);
  }
}

@media (max-width: 640px) {
  .stat-value {
    width: calc(100% - 80px);
    left: 16px;
    top: 35px;
  }
}

.stat-title {
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: var(--color-text-secondary);
  width: 150px;
  position: absolute;
  left: 21px;
  top: 14px;
  height: 15px;
}

@media (max-width: 991px) {
  .stat-title {
    width: calc(100% - 80px);
  }
}

@media (max-width: 640px) {
  .stat-title {
    width: calc(100% - 80px);
    left: 16px;
    top: 14px;
  }
}

.delete-icon-wrapper {
  display: flex;
  width: 35px;
  height: 35px;
  padding: 19px 25px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 10px;
  position: absolute;
  right: 16px;
  top: 17px;
  background-color: var(--color-btn-secondary-bg);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-icon-wrapper:hover {
  background-color: var(--color-btn-secondary-hover);
}

.trash-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  position: relative;
}
</style>
