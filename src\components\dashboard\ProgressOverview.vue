<template>
  <div class="progress-overview">
    <h2 class="section-title">Progress Overview</h2>
    
    <div class="progress-grid">
      <!-- Weekly Goals -->
      <div class="progress-card">
        <div class="card-header">
          <h3 class="card-title">This Week</h3>
          <div class="card-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"
                fill="var(--color-icon-fill)" />
            </svg>
          </div>
        </div>
        <div class="progress-content">
          <div class="progress-item">
            <div class="progress-label">Notes Created</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (weeklyProgress.notes / 10) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ weeklyProgress.notes }}/10</div>
          </div>
          <div class="progress-item">
            <div class="progress-label">Study Sessions</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (weeklyProgress.sessions / 7) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ weeklyProgress.sessions }}/7</div>
          </div>
          <div class="progress-item">
            <div class="progress-label">Focus Time</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (weeklyProgress.focusMinutes / 300) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ Math.round(weeklyProgress.focusMinutes) }}/300 min</div>
          </div>
        </div>
      </div>

      <!-- Monthly Goals -->
      <div class="progress-card">
        <div class="card-header">
          <h3 class="card-title">This Month</h3>
          <div class="card-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"
                fill="var(--color-icon-fill)" />
            </svg>
          </div>
        </div>
        <div class="progress-content">
          <div class="progress-item">
            <div class="progress-label">Books Added</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (monthlyProgress.books / 5) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ monthlyProgress.books }}/5</div>
          </div>
          <div class="progress-item">
            <div class="progress-label">Total Notes</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (monthlyProgress.notes / 50) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ monthlyProgress.notes }}/50</div>
          </div>
          <div class="progress-item">
            <div class="progress-label">Study Hours</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(100, (monthlyProgress.focusHours / 20) * 100)}%` }"></div>
            </div>
            <div class="progress-text">{{ Math.round(monthlyProgress.focusHours) }}/20 hrs</div>
          </div>
        </div>
      </div>

      <!-- Streaks -->
      <div class="progress-card">
        <div class="card-header">
          <h3 class="card-title">Streaks</h3>
          <div class="card-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z"
                fill="var(--color-icon-fill)" />
            </svg>
          </div>
        </div>
        <div class="progress-content">
          <div class="streak-item">
            <div class="streak-number">{{ streaks.daily }}</div>
            <div class="streak-label">Day Streak</div>
          </div>
          <div class="streak-item">
            <div class="streak-number">{{ streaks.weekly }}</div>
            <div class="streak-label">Week Streak</div>
          </div>
          <div class="streak-item">
            <div class="streak-number">{{ streaks.longest }}</div>
            <div class="streak-label">Longest Streak</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useElectronAPI } from '../../useElectronAPI'

interface WeeklyProgress {
  notes: number
  sessions: number
  focusMinutes: number
}

interface MonthlyProgress {
  books: number
  notes: number
  focusHours: number
}

interface Streaks {
  daily: number
  weekly: number
  longest: number
}

const db = useElectronAPI()

const weeklyProgress = ref<WeeklyProgress>({
  notes: 0,
  sessions: 0,
  focusMinutes: 0
})

const monthlyProgress = ref<MonthlyProgress>({
  books: 0,
  notes: 0,
  focusHours: 0
})

const streaks = ref<Streaks>({
  daily: 0,
  weekly: 0,
  longest: 0
})

const loadProgressData = async () => {
  try {
    const now = new Date()
    
    // Weekly progress (last 7 days)
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    
    // Monthly progress (last 30 days)
    const oneMonthAgo = new Date()
    oneMonthAgo.setDate(oneMonthAgo.getDate() - 30)
    
    // Load notes
    const allNotes = await db.notes.getAll()

    // Weekly notes
    const weeklyNotes = allNotes.filter(note => {
      const noteDate = new Date(note.created_at || '')
      return noteDate >= oneWeekAgo
    })
    weeklyProgress.value.notes = weeklyNotes.length

    // Monthly notes
    const monthlyNotes = allNotes.filter(note => {
      const noteDate = new Date(note.created_at || '')
      return noteDate >= oneMonthAgo
    })
    monthlyProgress.value.notes = monthlyNotes.length

    // Load books
    const allBooks = await db.books.getAll()
    const monthlyBooks = allBooks.filter(book => {
      const bookDate = new Date(book.created_at || '')
      return bookDate >= oneMonthAgo
    })
    monthlyProgress.value.books = monthlyBooks.length
    
    // Load timer sessions
    const weekAgoStr = oneWeekAgo.toISOString().split('T')[0]
    const monthAgoStr = oneMonthAgo.toISOString().split('T')[0]
    const todayStr = now.toISOString().split('T')[0]
    
    const weeklySessions = await db.timer.getSessionsByDateRange(weekAgoStr, todayStr)
    const monthlySessions = await db.timer.getSessionsByDateRange(monthAgoStr, todayStr)
    
    weeklyProgress.value.sessions = weeklySessions.filter(s => s.is_completed === 1).length
    weeklyProgress.value.focusMinutes = weeklySessions
      .filter(s => s.is_completed === 1)
      .reduce((total, session) => total + (session.duration || 0), 0) / 60
    
    monthlyProgress.value.focusHours = monthlySessions
      .filter(s => s.is_completed === 1)
      .reduce((total, session) => total + (session.duration || 0), 0) / 3600
    
    // Calculate streaks (simplified calculation)
    await calculateStreaks()
    
  } catch (error) {
    console.error('Failed to load progress data:', error)
  }
}

const calculateStreaks = async () => {
  try {
    // Get all completed sessions
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0]
    const todayStr = new Date().toISOString().split('T')[0]
    
    const sessions = await db.timer.getSessionsByDateRange(thirtyDaysAgoStr, todayStr)
    const completedSessions = sessions.filter(s => s.is_completed === 1)
    
    // Group sessions by date
    const sessionsByDate = new Map<string, number>()
    completedSessions.forEach(session => {
      const date = new Date(session.start_time).toISOString().split('T')[0]
      sessionsByDate.set(date, (sessionsByDate.get(date) || 0) + 1)
    })
    
    // Calculate current daily streak
    let currentStreak = 0
    let longestStreak = 0
    let tempStreak = 0
    
    const today = new Date()
    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today)
      checkDate.setDate(checkDate.getDate() - i)
      const dateStr = checkDate.toISOString().split('T')[0]
      
      if (sessionsByDate.has(dateStr)) {
        if (i === 0 || currentStreak > 0) {
          currentStreak++
        }
        tempStreak++
        longestStreak = Math.max(longestStreak, tempStreak)
      } else {
        if (i === 0) {
          currentStreak = 0
        }
        tempStreak = 0
      }
    }
    
    streaks.value.daily = currentStreak
    streaks.value.weekly = Math.floor(currentStreak / 7)
    streaks.value.longest = longestStreak
    
  } catch (error) {
    console.error('Failed to calculate streaks:', error)
  }
}

onMounted(() => {
  loadProgressData()
})

// Expose refresh method for parent components
defineExpose({
  refreshData: loadProgressData
})
</script>

<style scoped>
.progress-overview {
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--color-text-primary);
}

.progress-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.progress-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 8px;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.card-icon {
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon svg path {
  fill: white;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-label {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.progress-bar {
  height: 8px;
  background-color: var(--color-card-border);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  text-align: right;
}

.streak-item {
  text-align: center;
  padding: 10px;
  background-color: var(--color-dashboard-empty-bg);
  border-radius: 6px;
}

.streak-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.streak-label {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

@media (max-width: 768px) {
  .progress-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .progress-card {
    padding: 16px;
  }
}
</style>
