// Database testing utilities
import sqlite3 from 'sqlite3';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Types
type Database = sqlite3.Database;
type RunResult = sqlite3.RunResult;

// Create a test database in memory
export const createTestDb = (): Promise<Database> => {
    return new Promise((resolve, reject) => {
        const db: Database = new sqlite3.Database(':memory:', (err: Error | null) => {
            if (err) {
                console.error('Error creating test database:', err);
                reject(err);
                return;
            }

            // Enable foreign keys
            db.run('PRAGMA foreign_keys = ON', (pragmaErr: Error | null) => {
                if (pragmaErr) {
                    console.error('Error enabling foreign keys:', pragmaErr);
                    // Don't reject, just log
                }
            });

            resolve(db);
        });
    });
};

// Initialize test database schema
export const initTestDbSchema = (db: Database): Promise<Database> => {
    return new Promise((resolve, reject) => {
        // Create Notes table
        db.run(`CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT,
            html_content TEXT,
            folder_id INTEGER,
            book_id INTEGER,
            type TEXT,
            color TEXT,
            "order" INTEGER,
            last_viewed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
        )`, (createErr: Error | null) => {
            if (createErr) {
                console.error('Error creating notes table:', createErr);
                return reject(createErr);
            }

            // Create Folders table
            db.run(`CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                parent_id INTEGER,
                book_id INTEGER,
                color TEXT,
                "order" INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
            )`, (folderErr: Error | null) => {
                if (folderErr) {
                    console.error('Error creating folders table:', folderErr);
                    return reject(folderErr);
                }

                // Create Books table
                db.run(`CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    author TEXT,
                    isbn TEXT,
                    cover_path TEXT,
                    cover_url TEXT,
                    publication_date TEXT,
                    description TEXT,
                    page_count INTEGER,
                    current_page INTEGER,
                    rating INTEGER,
                    language TEXT,
                    genres TEXT,
                    olid TEXT,
                    status TEXT,
                    custom_fields TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )`, (bookErr: Error | null) => {
                    if (bookErr) {
                        console.error('Error creating books table:', bookErr);
                        return reject(bookErr);
                    }

                    // Create remaining tables in parallel
                    const tableQueries: string[] = [
                        `CREATE TABLE IF NOT EXISTS recent_items (...)`, // Simplified for brevity
                        `CREATE TABLE IF NOT EXISTS theme_settings (...)`,
                        `CREATE TABLE IF NOT EXISTS settings (...)`,
                        `CREATE TABLE IF NOT EXISTS timer_sessions (...)`,
                        `CREATE TABLE IF NOT EXISTS timer_settings (...)`
                        // Add full queries back here
                    ];

                    // Re-add full table creation queries here:
                    const fullTableQueries: string[] = [
                        `CREATE TABLE IF NOT EXISTS recent_items (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            note_id INTEGER,
                            book_id INTEGER,
                            viewed_at TIMESTAMP NOT NULL,
                            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
                            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
                        )`,
                        `CREATE TABLE IF NOT EXISTS theme_settings (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            theme_name TEXT NOT NULL,
                            is_active BOOLEAN NOT NULL DEFAULT 0,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )`,
                        `CREATE TABLE IF NOT EXISTS settings (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            key TEXT NOT NULL UNIQUE,
                            value_json TEXT,
                            category TEXT,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )`,
                        `CREATE TABLE IF NOT EXISTS timer_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            start_time TIMESTAMP NOT NULL,
                            end_time TIMESTAMP,
                            duration INTEGER,
                            session_type TEXT,
                            is_completed BOOLEAN DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )`,
                        `CREATE TABLE IF NOT EXISTS timer_settings (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            work_duration INTEGER NOT NULL DEFAULT 1500,
                            short_break_duration INTEGER NOT NULL DEFAULT 300,
                            long_break_duration INTEGER NOT NULL DEFAULT 900,
                            long_break_interval INTEGER NOT NULL DEFAULT 4,
                            auto_start_breaks BOOLEAN DEFAULT 1,
                            auto_start_work BOOLEAN DEFAULT 1,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )`
                    ];

                    let completed = 0;
                    let hasErrored = false;
                    fullTableQueries.forEach(query => {
                        db.run(query, (err: Error | null) => {
                            if (hasErrored) return;
                            if (err) {
                                console.error('Error creating table:', err);
                                hasErrored = true;
                                reject(err);
                                return;
                            }
                            completed++;
                            if (completed === fullTableQueries.length) {
                                resolve(db);
                            }
                        });
                    });
                });
            });
        });
    });
};

interface SampleIds {
    studyFolderId: number;
    mathFolderId: number;
    personalFolderId: number;
    bookId: number;
    noteId: number;
}

// Add sample data to test database
export const addSampleData = (db: Database): Promise<SampleIds> => {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            let studyFolderId: number, mathFolderId: number, personalFolderId: number, bookId: number, noteId: number;

            db.run(`INSERT INTO folders (name, parent_id, color, "order") VALUES ('Study Notes', NULL, '#4A86E8', 1)`, function (this: RunResult, err: Error | null) {
                if (err) return reject(err);
                studyFolderId = this.lastID;

                db.run(`INSERT INTO folders (name, parent_id, color, "order") VALUES ('Math', ?, '#6AA84F', 1)`, [studyFolderId], function (this: RunResult, err: Error | null) {
                    if (err) return reject(err);
                    mathFolderId = this.lastID;

                    db.run(`INSERT INTO folders (name, parent_id, color, "order") VALUES ('Personal', NULL, '#E69138', 2)`, function (this: RunResult, err: Error | null) {
                        if (err) return reject(err);
                        personalFolderId = this.lastID;

                        // Insert multiple notes requires separate calls or adjustment
                        // Inserting Algebra Note
                        db.run(`INSERT INTO notes (title, content, html_content, folder_id, type, color, "order", last_viewed_at) VALUES
                            ('Algebra Notes', 'Quadratic equations: ax² + bx + c = 0', '<p>Quadratic equations: ax² + bx + c = 0</p>', ?, 'text', '#4A86E8', 1, CURRENT_TIMESTAMP)`,
                            [mathFolderId], function (this: RunResult, err: Error | null) {
                            if (err) return reject(err);

                            // Inserting Personal Goals Note
                            db.run(`INSERT INTO notes (title, content, html_content, folder_id, type, color, "order", last_viewed_at) VALUES
                                ('Personal Goals', 'Learn programming, exercise more, read 20 books this year', '<p>Learn programming, exercise more, read 20 books this year</p>', ?, 'list', '#E69138', 1, CURRENT_TIMESTAMP)`,
                                [personalFolderId], function (this: RunResult, err: Error | null) {
                                if (err) return reject(err);

                                // Insert Book
                                db.run(`INSERT INTO books (title, author, isbn, cover_path, cover_url, publication_date, description, page_count, current_page, rating, language, genres, olid, status) VALUES ('The Great Gatsby', 'F. Scott Fitzgerald', '9780743273565', 'books/covers/gatsby.jpg', 'https://covers.openlibrary.org/b/isbn/9780743273565-L.jpg', '1925-04-10', 'The Great Gatsby is a 1925 novel by American writer F. Scott Fitzgerald.', 180, 45, 4, 'English', 'Fiction, Classic', 'OL103123W', 'reading')`,
                                    function (this: RunResult, err: Error | null) {
                                    if (err) return reject(err);
                                    bookId = this.lastID;

                                    // Insert Book Note
                                    db.run(`INSERT INTO notes (title, content, html_content, book_id, type, color, "order", last_viewed_at) VALUES ('The Great Gatsby - Chapter 1 Notes', 'Introduction to characters and setting...', '<p>Introduction to characters and setting...</p>', ?, 'book_note', '#6AA84F', 1, CURRENT_TIMESTAMP)`,
                                        [bookId], function (this: RunResult, err: Error | null) {
                                        if (err) return reject(err);
                                        noteId = this.lastID; // Assuming this is the noteId needed for recent_items

                                        // Insert Timer Settings
                                        db.run(`INSERT INTO timer_settings (work_duration, short_break_duration, long_break_duration, long_break_interval, auto_start_breaks, auto_start_work) VALUES (1500, 300, 900, 4, 1, 1)`,
                                            function (this: RunResult, err: Error | null) {
                                            if (err) return reject(err);

                                            // Insert Timer Session
                                            db.run(`INSERT INTO timer_sessions (start_time, end_time, duration, session_type, is_completed) VALUES (datetime('now', '-1 hour'), datetime('now', '-30 minutes'), 1800, 'work', 1)`,
                                                function (this: RunResult, err: Error | null) {
                                                if (err) return reject(err);

                                                // Insert Theme Setting
                                                db.run(`INSERT INTO theme_settings (theme_name, is_active) VALUES ('Light', 1)`,
                                                    function (this: RunResult, err: Error | null) {
                                                    if (err) return reject(err);

                                                    // Insert Recent Item
                                                    db.run(`INSERT INTO recent_items (note_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)`,
                                                        [noteId], function (this: RunResult, err: Error | null) {
                                                        if (err) return reject(err);

                                                        // Insert Setting
                                                        db.run(`INSERT INTO settings (key, value_json, category) VALUES ('default_save_path', '{"path": "~/Documents/Noti"}', 'general')`,
                                                            function (this: RunResult, err: Error | null) {
                                                            if (err) return reject(err);

                                                            // All inserts successful, resolve with IDs
                                                            resolve({
                                                                studyFolderId,
                                                                mathFolderId,
                                                                personalFolderId,
                                                                bookId,
                                                                noteId
                                                            });
                                                        }); // End settings
                                                    }); // End recent_items
                                                }); // End theme_settings
                                            }); // End timer_sessions
                                        }); // End timer_settings
                                    }); // End book note
                                }); // End book
                            }); // End personal goals note
                        }); // End algebra note
                    }); // End personal folder
                }); // End math folder
            }); // End study folder
        }); // End serialize
    }); // End Promise
};

// Close and cleanup test database
export const closeTestDb = (db: Database | null): Promise<void> => {
    return new Promise((resolve, reject) => {
        if (db) {
            db.close((err: Error | null) => {
                if (err) {
                    console.error('Error closing test database:', err);
                    reject(err);
                    return;
                }
                resolve();
            });
        } else {
            resolve(); // Resolve if db is null
        }
    });
};

interface TestResults {
    folders: any[];
    notes: any[];
    books: any[];
    relationships: any[];
    recentItems: any[];
    themeSettings: any[];
    timerSettings: any[];
    timerSessions: any[];
    settings: any[];
}

interface RunTestResult {
    success: boolean;
    results?: TestResults;
    error?: string;
}

// Run basic database tests
export const runBasicDbTests = async (): Promise<RunTestResult> => {
    let db: Database | null = null;
    try {
        console.log('Creating test database...');
        db = await createTestDb();

        console.log('Initializing schema...');
        await initTestDbSchema(db);

        console.log('Adding sample data...');
        const sampleIds: SampleIds = await addSampleData(db);
        console.log('Sample IDs:', sampleIds); // Log sample IDs for debugging

        console.log('Running tests...');

        // Helper function for running test queries
        const runTestQuery = <T>(query: string, testName: string, expectedCount: number, params: any[] = []): Promise<T[]> => {
            return new Promise((resolve, reject) => {
                if (!db) return reject(new Error('Database not initialized'));
                db.all(query, params, (err: Error | null, rows: T[]) => {
                    if (err) {
                        console.error(`Error running ${testName} test:`, err);
                        reject(err);
                        return;
                    }
                    console.log(`${testName} test:`, rows.length === expectedCount ? 'PASSED' : `FAILED (expected ${expectedCount}, got ${rows.length})`);
                    resolve(rows);
                });
            });
        };

        const foldersPromise = runTestQuery<any>('SELECT * FROM folders ORDER BY name', 'Folders', 3);
        const notesPromise = runTestQuery<any>('SELECT * FROM notes', 'Notes', 3); // Algebra, Personal Goals, Book Note
        const booksPromise = runTestQuery<any>('SELECT * FROM books', 'Books', 1);
        const relationshipPromise = runTestQuery<any>(
            `SELECT n.title, f.name as folder_name FROM notes n LEFT JOIN folders f ON n.folder_id = f.id WHERE n.folder_id IS NOT NULL`,
            'Note-Folder Relationships',
            2 // Algebra Notes (Math), Personal Goals (Personal)
        );
        const recentItemsPromise = runTestQuery<any>('SELECT * FROM recent_items', 'Recent Items', 1);
        const themeSettingsPromise = runTestQuery<any>('SELECT * FROM theme_settings', 'Theme Settings', 1);
        const timerSettingsPromise = runTestQuery<any>('SELECT * FROM timer_settings', 'Timer Settings', 1);
        const timerSessionsPromise = runTestQuery<any>('SELECT * FROM timer_sessions', 'Timer Sessions', 1);
        const settingsPromise = runTestQuery<any>('SELECT * FROM settings', 'Settings', 1);

        const [
            folders,
            notes,
            books,
            relationships,
            recentItems,
            themeSettings,
            timerSettings,
            timerSessions,
            settings
        ] = await Promise.all([
            foldersPromise,
            notesPromise,
            booksPromise,
            relationshipPromise,
            recentItemsPromise,
            themeSettingsPromise,
            timerSettingsPromise,
            timerSessionsPromise,
            settingsPromise
        ]);

        console.log('Test results summary: All tests PASSED/FAILED logs above.');
        // Optionally log detailed results if needed
        // console.log('- Folders:', folders);
        // console.log('- Notes:', notes);
        // ... etc.

        console.log('All tests completed!');

        return {
            success: true,
            results: {
                folders,
                notes,
                books,
                relationships,
                recentItems,
                themeSettings,
                timerSettings,
                timerSessions,
                settings
            }
        };
    } catch (error: any) {
        console.error('Test run failed:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        if (db) {
            await closeTestDb(db);
            console.log('Test database closed.');
        }
    }
};

// Check if this file is being run directly
const isMainModule = process.argv[1] === fileURLToPath(import.meta.url);

if (isMainModule) {
    runBasicDbTests()
        .then((results: RunTestResult) => { // Add type annotation here
            console.log('Tests finished with result:', results.success ? 'SUCCESS' : 'FAILURE');
            if (!results.success) {
                console.error('Error during tests:', results.error);
            }
            process.exit(results.success ? 0 : 1);
        })
        .catch((err: Error) => { // Add type annotation here
            console.error('Uncaught error running tests:', err);
            process.exit(1);
        });
}