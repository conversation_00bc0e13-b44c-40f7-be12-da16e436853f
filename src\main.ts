// Ultra-early preloading - import this FIRST
import './preload'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
// Import the fonts CSS first to ensure font availability
import './assets/fonts.css'
// Import theme system
import './assets/themes.css'
// Import mock API - will only activate when real Electron API is not available
import './types/mock-api'
// Import page load monitoring utilities
import { setupGlobalPerformanceUtils } from './composables/usePageLoadMonitoring'

// Component preloading is now handled in preload.ts

// Create Pinia instance
const pinia = createPinia()

// Create and mount the app with router and pinia
const app = createApp(App)
app.use(router)
app.use(pinia)

app.mount('#app')

// Setup global performance monitoring utilities
setupGlobalPerformanceUtils()

// Enhanced preloading status check
;(window as any).checkPreloading = () => {
  console.group('📊 Preloading Status')

  const preloadStats = (window as any).preloadStats
  if (preloadStats) {
    console.log(`⚡ Ultra-early preloading completed in ${preloadStats.ultraEarlyTime.toFixed(2)}ms`)
    console.log(`🕐 Completed at: ${new Date(preloadStats.completedAt).toLocaleTimeString()}`)
  } else {
    console.log('⚠️ Preload stats not available')
  }

  console.log('📈 Use pagePerf.testPreloading() to see performance improvements')
  console.log('🧪 Navigate between pages to test the preloading effectiveness')

  console.groupEnd()
}