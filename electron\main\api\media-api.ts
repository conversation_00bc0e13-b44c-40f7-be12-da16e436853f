// Media API operations
import path from 'node:path';
import fs from 'node:fs';
import { app } from 'electron';
import { dbRun, dbGet, dbAll } from '../database/database-api';
import { RunResult } from 'sqlite3';

// Define interfaces for media files
export interface MediaFile {
  id?: number;
  note_id: number | null;
  book_id: number | null;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  is_cover: boolean;
  created_at?: string;
}

// Helper to get media storage path
export const getMediaStoragePath = (): string => {
  const userDataPath = app.getPath('userData');
  const mediaPath = path.join(userDataPath, 'media');
  
  // Ensure media directory exists
  if (!fs.existsSync(mediaPath)) {
    fs.mkdirSync(mediaPath, { recursive: true });
  }
  
  return mediaPath;
};

// Helper to convert a file path to a noti-media URL
export const filePathToMediaUrl = (filePath: string): string => {
  try {
    // Normalize the path first to handle any inconsistencies
    const normalizedPath = path.normalize(filePath);
    
    // For Windows, ensure we have proper path separators
    let urlPath = normalizedPath;
    
    // Convert Windows backslashes to forward slashes for URL
    if (process.platform === 'win32') {
      urlPath = normalizedPath.replace(/\\/g, '/');
    }
    
    // Encode the path components to handle special characters
    const pathParts = urlPath.split('/');
    const encodedParts = pathParts.map(part => {
      // Don't encode the drive letter colon on Windows (e.g., C:)
      if (process.platform === 'win32' && part.match(/^[A-Za-z]:$/)) {
        return part;
      }
      return encodeURIComponent(part);
    });
    
    const encodedPath = encodedParts.join('/');
    
    return `noti-media://${encodedPath}`;
  } catch (error) {
    console.error('Error converting file path to media URL:', error);
    // Fallback to simple conversion
    const fallbackPath = filePath.replace(/\\/g, '/');
    return `noti-media://${fallbackPath}`;
  }
};

// Create a new media file entry and save the file to disk
export const saveMediaFile = async (
  noteId: number | null,
  fileData: number[] | Buffer,
  fileName: string,
  fileType: string,
  bookId: number | null = null,
  isCover: boolean = false
): Promise<MediaFile> => {
  try {
    // Create unique filename to prevent collisions
    const timestamp = Date.now();
    const uniqueFileName = `${timestamp}-${fileName}`;
    const mediaStoragePath = getMediaStoragePath();
    const filePath = path.join(mediaStoragePath, uniqueFileName);
    
    // Convert array to Buffer if needed
    const fileBuffer = Buffer.isBuffer(fileData) ? fileData : Buffer.from(fileData);
    
    // Save file to disk
    fs.writeFileSync(filePath, fileBuffer);
    
    // Get file size
    const fileSize = fileBuffer.length;
    
    // Save entry to database
    const query = `
      INSERT INTO media_files (
        note_id, book_id, file_path, file_name, file_type, file_size, is_cover, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `;
    
    const result: RunResult = await dbRun(query, [
      noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0
    ]);
    
    if (!result.lastID) {
      throw new Error('Failed to create media file entry, no ID returned');
    }
    
    // Return the created media file
    return getMediaFileById(result.lastID);
  } catch (error) {
    console.error('Error saving media file:', error);
    throw error;
  }
};

// Get a media file by ID
export const getMediaFileById = async (id: number): Promise<MediaFile> => {
  const query = 'SELECT * FROM media_files WHERE id = ?';

  try {
    return await dbGet<MediaFile>(query, [id]);
  } catch (error) {
    console.error(`Error getting media file with ID ${id}:`, error);
    throw error;
  }
};

// Get all media files for a note
export const getMediaFilesByNoteId = async (noteId: number): Promise<MediaFile[]> => {
  const query = 'SELECT * FROM media_files WHERE note_id = ? ORDER BY created_at DESC';

  try {
    return await dbAll<MediaFile>(query, [noteId]);
  } catch (error) {
    console.error(`Error getting media files for note ID ${noteId}:`, error);
    throw error;
  }
};

// Get all media files for a book
export const getMediaFilesByBookId = async (bookId: number): Promise<MediaFile[]> => {
  const query = 'SELECT * FROM media_files WHERE book_id = ? ORDER BY created_at DESC';

  try {
    return await dbAll<MediaFile>(query, [bookId]);
  } catch (error) {
    console.error(`Error getting media files for book ID ${bookId}:`, error);
    throw error;
  }
};

// Get book cover (first cover image for a book)
export const getBookCover = async (bookId: number): Promise<MediaFile | null> => {
  const query = 'SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1 ORDER BY created_at DESC LIMIT 1';

  try {
    const result = await dbAll<MediaFile>(query, [bookId]);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`Error getting book cover for book ID ${bookId}:`, error);
    throw error;
  }
};

// Save book cover specifically
export const saveBookCover = async (
  bookId: number,
  coverData: Buffer,
  fileName: string = 'cover.jpg'
): Promise<MediaFile> => {
  // First, delete any existing cover for this book
  const existingCover = await getBookCover(bookId);
  if (existingCover && existingCover.id) {
    await deleteMediaFile(existingCover.id);
  }

  // Save the new cover
  return await saveMediaFile(null, coverData, fileName, 'image/jpeg', bookId, true);
};

// Delete a media file (from database and disk)
export const deleteMediaFile = async (id: number): Promise<{ success: boolean; id: number }> => {
  try {
    // First get the file path
    const mediaFile = await getMediaFileById(id);
    
    if (!mediaFile) {
      throw new Error(`Media file with ID ${id} not found`);
    }
    
    // Delete from database
    const query = 'DELETE FROM media_files WHERE id = ?';
    const result: RunResult = await dbRun(query, [id]);
    
    // If successful in database, delete from disk too
    if (result.changes && result.changes > 0) {
      try {
        if (fs.existsSync(mediaFile.file_path)) {
          fs.unlinkSync(mediaFile.file_path);
        }
      } catch (fsError) {
        console.error(`Error deleting file from disk: ${mediaFile.file_path}`, fsError);
        // Continue even if file deletion fails - the database entry is removed
      }
      
      return { success: true, id };
    }
    
    return { success: false, id };
  } catch (error) {
    console.error(`Error deleting media file with ID ${id}:`, error);
    throw error;
  }
};

// Update a media file's note association
export const updateMediaFileNote = async (id: number, noteId: number | null): Promise<MediaFile> => {
  const query = 'UPDATE media_files SET note_id = ? WHERE id = ?';
  
  try {
    const result: RunResult = await dbRun(query, [noteId, id]);
    
    if (result.changes && result.changes > 0) {
      return getMediaFileById(id);
    }
    
    throw new Error(`Media file with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating media file with ID ${id}:`, error);
    throw error;
  }
};

// Export the media API as a default object
export default {
  saveMediaFile,
  getMediaFileById,
  getMediaFilesByNoteId,
  getMediaFilesByBookId,
  getBookCover,
  saveBookCover,
  deleteMediaFile,
  updateMediaFileNote,
  getMediaStoragePath,
  filePathToMediaUrl
};