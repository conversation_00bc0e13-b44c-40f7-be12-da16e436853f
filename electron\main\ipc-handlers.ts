// IPC Handlers for database operations
import { ipc<PERSON>ain, IpcMainInvokeEvent, dialog } from 'electron';
import { initDatabase } from './database/database';
import notesApi from './api/notes-api';
import foldersApi from './api/folders-api';
import recentItemsApi from './api/recent-items-api';
import settingsApi from './api/settings-api';
import timerApi from './api/timer-api';
import mediaApi, { filePathToMediaUrl } from './api/media-api';
import booksApi from './api/books-api';
import discordRpc from '../../public/discord-rpc-api';
import { syncAPI } from './api/sync-logic/sync-api';
import { databaseHooks } from './database/database-hooks';
import { Folder, Note, Book } from './database/database-api';
import { BookSearchResult } from './api/books-api';
import path from 'node:path';

// Constants for validation
const MAX_COVER_BYTES = 10 * 1024 * 1024; // 10MB limit for book covers

// Initialize database
let dbInitialized = false;

export const initializeIpcHandlers = async (): Promise<void> => {
    // Initialize database if not already done
    if (!dbInitialized) {
        try {
            await initDatabase();
            dbInitialized = true;
            console.log('Database initialized successfully');
            
            // Initialize sync API after database is ready
            await syncAPI.initialize();
            console.log('Sync API initialized successfully');
        } catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }


    // Register IPC handlers for all APIs
    registerNotesHandlers();
    registerFoldersHandlers();
    registerRecentItemsHandlers();
    registerSettingsHandlers();
    registerTimerHandlers();
    registerMediaHandlers();
    registerBooksHandlers();
    registerDiscordHandlers();
    registerSyncHandlers();
    registerDatabaseHooksHandlers();
    registerDialogHandlers();

    // Handle media URL formatting to ensure compatibility with both dev and production builds
    ipcMain.handle('media:getMediaUrl', async (_, filePath) => {
        // Always use the custom protocol for image files to ensure compatibility
        // with webSecurity being enabled in both dev and production
        return mediaApi.filePathToMediaUrl(filePath);
    });

    // Check and download missing book covers on startup
    try {
        console.log('Starting background cover download check...');
        // Run this in the background without blocking startup
        booksApi.checkAndDownloadMissingCovers().catch(error => {
            console.error('Background cover download check failed:', error);
        });
    } catch (error) {
        console.error('Failed to start background cover download check:', error);
        // Don't throw - this shouldn't block app startup
    }

};

// Register IPC handlers for Notes operations
const registerNotesHandlers = (): void => {
    // Create a new note
    ipcMain.handle('notes:create', async (_event: IpcMainInvokeEvent, note: Note) => {
        try {
            return await notesApi.createNote(note);
        } catch (error) {
            console.error('IPC notes:create error:', error);
            throw error;
        }
    });

    // Create a new note for a specific book
    ipcMain.handle('notes:createForBook', async (_event: IpcMainInvokeEvent, bookId: number, customTitle?: string, folderId?: number | null) => {
        try {
            return await notesApi.createNoteForBook(bookId, customTitle, folderId);
        } catch (error) {
            console.error(`IPC notes:createForBook error for book ID ${bookId}:`, error);
            throw error;
        }
    });

    // Get all notes (with optional filtering)
    ipcMain.handle('notes:getAll', async (_event: IpcMainInvokeEvent, options: any = {}) => {
        try {
            return await notesApi.getAllNotes(options);
        } catch (error) {
            console.error('IPC notes:getAll error:', error);
            throw error;
        }
    });

    // Get a note by ID
    ipcMain.handle('notes:getById', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await notesApi.getNoteById(id);
        } catch (error) {
            console.error(`IPC notes:getById error for ID ${id}:`, error);
            throw error;
        }
    });

    // Get notes by folder ID
    ipcMain.handle('notes:getByFolderId', async (_event: IpcMainInvokeEvent, folderId: number) => {
        try {
            return await notesApi.getNotesByFolderId(folderId);
        } catch (error) {
            console.error(`IPC notes:getByFolderId error for folder ID ${folderId}:`, error);
            throw error;
        }
    });

    // Get notes by book ID
    ipcMain.handle('notes:getByBookId', async (_event: IpcMainInvokeEvent, bookId: number) => {
        try {
            return await notesApi.getNotesByBookId(bookId);
        } catch (error) {
            console.error(`IPC notes:getByBookId error for book ID ${bookId}:`, error);
            throw error;
        }
    });

    // Update a note
    ipcMain.handle('notes:update', async (_event: IpcMainInvokeEvent, id: number, noteUpdates: Partial<Note>) => {
        try {
            return await notesApi.updateNote(id, noteUpdates);
        } catch (error) {
            console.error(`IPC notes:update error for ID ${id}:`, error);
            throw error;
        }
    });

    // Delete a note
    ipcMain.handle('notes:delete', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await notesApi.deleteNote(id);
        } catch (error) {
            console.error(`IPC notes:delete error for ID ${id}:`, error);
            throw error;
        }
    });    // Search notes
    ipcMain.handle('notes:search', async (_event: IpcMainInvokeEvent, searchTerm: string) => {
        try {
            return await notesApi.searchNotes(searchTerm);
        } catch (error) {
            console.error(`IPC notes:search error for term ${searchTerm}:`, error);
            throw error;
        }
    });

    // Export a single note
    ipcMain.handle('notes:export', async (_event: IpcMainInvokeEvent, id: number, format: string) => {
        try {
            return await notesApi.exportNote(id, format);
        } catch (error) {
            console.error(`IPC notes:export error for note ID ${id}:`, error);
            throw error;
        }
    });

    // Export multiple items (notes and/or folders)
    ipcMain.handle('notes:exportMultiple', async (_event: IpcMainInvokeEvent,
        items: Array<{ id: number; type: 'folder' | 'note'; name: string }>,
        format: string,
        options: { includeSubfolders?: boolean; includeNotes?: boolean } = {}) => {
        try {
            return await notesApi.exportMultiple(items, format, options);
        } catch (error) {
            console.error(`IPC notes:exportMultiple error:`, error);
            throw error;
        }
    });

    // Import note content
    ipcMain.handle('notes:import', async (_event: IpcMainInvokeEvent, content: string, format: string, title: string) => {
        try {
            return await notesApi.importNote(content, format, title);
        } catch (error) {
            console.error(`IPC notes:import error:`, error);
            throw error;
        }
    });

    // Link a note to a book
    ipcMain.handle('notes:linkToBook', async (_event: IpcMainInvokeEvent, noteId: number, bookId: number | null) => {
        try {
            return await notesApi.linkNoteToBook(noteId, bookId);
        } catch (error) {
            console.error(`IPC notes:linkToBook error for note ID ${noteId} and book ID ${bookId}:`, error);
            throw error;
        }
    });

    // Auto-link notes to books in a folder
    ipcMain.handle('notes:autoLinkInFolder', async (_event: IpcMainInvokeEvent, folderId: number) => {
        try {
            return await notesApi.autoLinkNotesToBooksInFolder(folderId);
        } catch (error) {
            console.error(`IPC notes:autoLinkInFolder error for folder ID ${folderId}:`, error);
            throw error;
        }
    });
};

// Register IPC handlers for Folders operations
const registerFoldersHandlers = (): void => {
    // Create a new folder
    ipcMain.handle('folders:create', async (_event: IpcMainInvokeEvent, folder: Folder) => {
        try {
            return await foldersApi.createFolder(folder);
        } catch (error) {
            console.error('IPC folders:create error:', error);
            throw error;
        }
    });

    // Get all folders
    ipcMain.handle('folders:getAll', async (_event: IpcMainInvokeEvent) => {
        try {
            return await foldersApi.getAllFolders();
        } catch (error) {
            console.error('IPC folders:getAll error:', error);
            throw error;
        }
    });

    // Get a folder by ID (with notes count)
    ipcMain.handle('folders:getById', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await foldersApi.getFolderById(id);
        } catch (error) {
            console.error(`IPC folders:getById error for ID ${id}:`, error);
            throw error;
        }
    });

    // Get child folders
    ipcMain.handle('folders:getChildren', async (_event: IpcMainInvokeEvent, parentId: number | null) => {
        try {
            return await foldersApi.getChildren(parentId);
        } catch (error) {
            console.error(`IPC folders:getChildren error for parent ID ${parentId}:`, error);
            throw error;
        }
    });

    // Get folder hierarchy (for tree view)
    ipcMain.handle('folders:getHierarchy', async (_event: IpcMainInvokeEvent) => {
        try {
            console.log('IPC: Fetching folder hierarchy');
            const hierarchy = await foldersApi.getFolderHierarchy();
            console.log(`IPC: Returning hierarchy with ${hierarchy.length} root items`);
            return hierarchy;
        } catch (error) {
            console.error('IPC folders:getHierarchy error:', error);
            // Instead of throwing, return an empty array to avoid breaking the client
            console.log('IPC: Returning empty hierarchy due to error');
            return [];
        }
    });

    // Get inherited book_id from parent hierarchy
    ipcMain.handle('folders:getInheritedBookId', async (_event: IpcMainInvokeEvent, parentId: number | null) => {
        try {
            return await foldersApi.getInheritedBookId(parentId);
        } catch (error) {
            console.error(`IPC folders:getInheritedBookId error for parent ID ${parentId}:`, error);
            throw error;
        }
    });

    // Update a folder
    ipcMain.handle('folders:update', async (_event: IpcMainInvokeEvent, id: number, folderUpdates: Partial<Folder>) => {
        try {
            return await foldersApi.updateFolder(id, folderUpdates);
        } catch (error) {
            console.error(`IPC folders:update error for ID ${id}:`, error);
            throw error;
        }
    });

    // Delete a folder
    ipcMain.handle('folders:delete', async (_event: IpcMainInvokeEvent, id: number, targetFolderId?: number | null) => {
        try {
            return await foldersApi.deleteFolder(id, targetFolderId || null);
        } catch (error) {
            console.error(`IPC folders:delete error for ID ${id}:`, error);
            throw error;
        }
    });
};

// Register IPC handlers for Recent Items operations
const registerRecentItemsHandlers = (): void => {
    // Add a recent note
    ipcMain.handle('recentItems:addNote', async (_event: IpcMainInvokeEvent, noteId: number) => {
        try {
            return await recentItemsApi.addRecentNote(noteId);
        } catch (error) {
            console.error(`IPC recentItems:addNote error for note ID ${noteId}:`, error);
            throw error;
        }
    });

    // Add a recent book
    ipcMain.handle('recentItems:addBook', async (_event: IpcMainInvokeEvent, bookId: number) => {
        try {
            return await recentItemsApi.addRecentBook(bookId);
        } catch (error) {
            console.error(`IPC recentItems:addBook error for book ID ${bookId}:`, error);
            throw error;
        }
    });

    // Get recent notes
    ipcMain.handle('recentItems:getNotes', async (_event: IpcMainInvokeEvent, limit?: number) => {
        try {
            return await recentItemsApi.getRecentNotes(limit);
        } catch (error) {
            console.error('IPC recentItems:getNotes error:', error);
            throw error;
        }
    });

    // Get recent books
    ipcMain.handle('recentItems:getBooks', async (_event: IpcMainInvokeEvent, limit?: number) => {
        try {
            return await recentItemsApi.getRecentBooks(limit);
        } catch (error) {
            console.error('IPC recentItems:getBooks error:', error);
            throw error;
        }
    });

    // Get all recent items
    ipcMain.handle('recentItems:getAll', async (_event: IpcMainInvokeEvent, limit?: number) => {
        try {
            return await recentItemsApi.getAllRecentItems(limit);
        } catch (error) {
            console.error('IPC recentItems:getAll error:', error);
            throw error;
        }
    });

    // Clear all recent items
    ipcMain.handle('recentItems:clearAll', async () => {
        try {
            return await recentItemsApi.clearRecentItems();
        } catch (error) {
            console.error('IPC recentItems:clearAll error:', error);
            throw error;
        }
    });

    // Delete a recent item
    ipcMain.handle('recentItems:delete', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await recentItemsApi.deleteRecentItem(id);
        } catch (error) {
            console.error(`IPC recentItems:delete error for ID ${id}:`, error);
            throw error;
        }
    });
};

// Register IPC handlers for Settings operations
const registerSettingsHandlers = (): void => {
    // General settings
    ipcMain.handle('settings:get', async (_event: IpcMainInvokeEvent, key: string) => {
        try {
            return await settingsApi.getSetting(key);
        } catch (error) {
            console.error(`IPC settings:get error for key ${key}:`, error);
            throw error;
        }
    });

    ipcMain.handle('settings:getByCategory', async (_event: IpcMainInvokeEvent, category: string) => {
        try {
            return await settingsApi.getSettingsByCategory(category);
        } catch (error) {
            console.error(`IPC settings:getByCategory error for category ${category}:`, error);
            throw error;
        }
    });

    ipcMain.handle('settings:getAll', async () => {
        try {
            return await settingsApi.getAllSettings();
        } catch (error) {
            console.error('IPC settings:getAll error:', error);
            throw error;
        }
    });

    ipcMain.handle('settings:set', async (_event: IpcMainInvokeEvent, key: string, value: any, category?: string) => {
        try {
            return await settingsApi.setSetting(key, value, category);
        } catch (error) {
            console.error(`IPC settings:set error for key ${key}:`, error);
            throw error;
        }
    });

    ipcMain.handle('settings:delete', async (_event: IpcMainInvokeEvent, key: string) => {
        try {
            return await settingsApi.deleteSetting(key);
        } catch (error) {
            console.error(`IPC settings:delete error for key ${key}:`, error);
            throw error;
        }
    });

    // Theme settings
    ipcMain.handle('themes:getActive', async () => {
        try {
            return await settingsApi.getActiveTheme();
        } catch (error) {
            console.error('IPC themes:getActive error:', error);
            throw error;
        }
    });

    ipcMain.handle('themes:getAll', async () => {
        try {
            return await settingsApi.getAllThemes();
        } catch (error) {
            console.error('IPC themes:getAll error:', error);
            throw error;
        }
    });

    ipcMain.handle('themes:create', async (_event: IpcMainInvokeEvent, themeName: string) => {
        try {
            return await settingsApi.createTheme(themeName);
        } catch (error) {
            console.error(`IPC themes:create error for name ${themeName}:`, error);
            throw error;
        }
    });

    ipcMain.handle('themes:setActive', async (_event: IpcMainInvokeEvent, themeId: number) => {
        try {
            return await settingsApi.setActiveTheme(themeId);
        } catch (error) {
            console.error(`IPC themes:setActive error for ID ${themeId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('themes:delete', async (_event: IpcMainInvokeEvent, themeId: number) => {
        try {
            return await settingsApi.deleteTheme(themeId);
        } catch (error) {
            console.error(`IPC themes:delete error for ID ${themeId}:`, error);
            throw error;
        }
    });
};

// Register IPC handlers for Timer operations
const registerTimerHandlers = (): void => {
    // Timer sessions
    ipcMain.handle('timer:start', async (_event: IpcMainInvokeEvent, sessionType?: string, focus?: string, category?: string) => {
        try {
            return await timerApi.startTimerSession(sessionType, focus, category);
        } catch (error) {
            console.error('IPC timer:start error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:end', async (_event: IpcMainInvokeEvent, sessionId: number) => {
        try {
            return await timerApi.endTimerSession(sessionId);
        } catch (error) {
            console.error(`IPC timer:end error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:getSession', async (_event: IpcMainInvokeEvent, sessionId: number) => {
        try {
            return await timerApi.getTimerSession(sessionId);
        } catch (error) {
            console.error(`IPC timer:getSession error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:getSessionsByDateRange', async (_event: IpcMainInvokeEvent, startDate: string, endDate: string) => {
        try {
            return await timerApi.getTimerSessionsByDateRange(startDate, endDate);
        } catch (error) {
            console.error('IPC timer:getSessionsByDateRange error:', error);
            throw error;
        }
    });



    ipcMain.handle('timer:getTodaySessions', async () => {
        try {
            return await timerApi.getTodayTimerSessions();
        } catch (error) {
            console.error('IPC timer:getTodaySessions error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:getStatsByDateRange', async (_event: IpcMainInvokeEvent, startDate: string, endDate: string) => {
        try {
            return await timerApi.getTimerStatsByDateRange(startDate, endDate);
        } catch (error) {
            console.error('IPC timer:getStatsByDateRange error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:deleteSession', async (_event: IpcMainInvokeEvent, sessionId: number) => {
        try {
            return await timerApi.deleteTimerSession(sessionId);
        } catch (error) {
            console.error(`IPC timer:deleteSession error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:updateSession', async (_event: IpcMainInvokeEvent, sessionId: number, updateData: any) => {
        try {
            return await timerApi.updateSession(sessionId, updateData);
        } catch (error) {
            console.error(`IPC timer:updateSession error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    // Timer settings
    ipcMain.handle('timer:getSettings', async () => {
        try {
            return await timerApi.getTimerSettings();
        } catch (error) {
            console.error('IPC timer:getSettings error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:updateSettings', async (_event: IpcMainInvokeEvent, settingsUpdates: any) => {
        try {
            return await timerApi.updateTimerSettings(settingsUpdates);
        } catch (error) {
            console.error('IPC timer:updateSettings error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:resetSettings', async () => {
        try {
            return await timerApi.resetTimerSettings();
        } catch (error) {
            console.error('IPC timer:resetSettings error:', error);
            throw error;
        }
    });

    // New session management handlers
    ipcMain.handle('timer:createUserSession', async (_event: IpcMainInvokeEvent, sessionName: string, focus?: string, category?: string) => {
        try {
            return await timerApi.createUserSession(sessionName, focus, category);
        } catch (error) {
            console.error('IPC timer:createUserSession error:', error);
            throw error;
        }
    });

    ipcMain.handle('timer:endUserSession', async (_event: IpcMainInvokeEvent, sessionId: number) => {
        try {
            return await timerApi.endUserSession(sessionId);
        } catch (error) {
            console.error(`IPC timer:endUserSession error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:getActiveUserSession', async () => {
        try {
            return await timerApi.getActiveUserSession();
        } catch (error) {
            console.error('IPC timer:getActiveUserSession error:', error);
            throw error;
        }
    });

    // Pomodoro cycle management handlers
    ipcMain.handle('timer:startPomodoroInSession', async (_event: IpcMainInvokeEvent, sessionId: number, cycleType: string) => {
        try {
            return await timerApi.startPomodoroInSession(sessionId, cycleType);
        } catch (error) {
            console.error(`IPC timer:startPomodoroInSession error for session ID ${sessionId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:completePomodoroInSession', async (_event: IpcMainInvokeEvent, sessionId: number, cycleId: number) => {
        try {
            return await timerApi.completePomodoroInSession(sessionId, cycleId);
        } catch (error) {
            console.error(`IPC timer:completePomodoroInSession error for session ID ${sessionId}, cycle ID ${cycleId}:`, error);
            throw error;
        }
    });

    ipcMain.handle('timer:cancelActiveCycle', async (_event: IpcMainInvokeEvent, sessionId: number, cycleId: number) => {
        try {
            return await timerApi.cancelActiveCycle(sessionId, cycleId);
        } catch (error) {
            console.error(`IPC timer:cancelActiveCycle error for session ID ${sessionId}, cycle ID ${cycleId}:`, error);
            throw error;
        }
    });

    // Utility functions
    ipcMain.handle('timer:syncAllSessionPomodoroCounts', async () => {
        try {
            return await timerApi.syncAllSessionPomodoroCounts();
        } catch (error) {
            console.error('IPC timer:syncAllSessionPomodoroCounts error:', error);
            throw error;
        }
    });
};

// Register IPC handlers for Media Files operations
const registerMediaHandlers = (): void => {
    // Save a media file
    ipcMain.handle('media:save', async (_event: IpcMainInvokeEvent,
        noteId: number | null, fileBuffer: Buffer, fileName: string, fileType: string) => {
        try {
            return await mediaApi.saveMediaFile(noteId, fileBuffer, fileName, fileType);
        } catch (error) {
            console.error('IPC media:save error:', error);
            throw error;
        }
    });

    // Get media file by ID
    ipcMain.handle('media:getById', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await mediaApi.getMediaFileById(id);
        } catch (error) {
            console.error(`IPC media:getById error for ID ${id}:`, error);
            throw error;
        }
    });

    // Get media files by note ID
    ipcMain.handle('media:getByNoteId', async (_event: IpcMainInvokeEvent, noteId: number) => {
        try {
            return await mediaApi.getMediaFilesByNoteId(noteId);
        } catch (error) {
            console.error(`IPC media:getByNoteId error for note ID ${noteId}:`, error);
            throw error;
        }
    });

    // Delete a media file
    ipcMain.handle('media:delete', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await mediaApi.deleteMediaFile(id);
        } catch (error) {
            console.error(`IPC media:delete error for ID ${id}:`, error);
            throw error;
        }
    });

    // Update a media file's note association
    ipcMain.handle('media:updateNote', async (_event: IpcMainInvokeEvent, id: number, noteId: number | null) => {
        try {
            return await mediaApi.updateMediaFileNote(id, noteId);
        } catch (error) {
            console.error(`IPC media:updateNote error for ID ${id}:`, error);
            throw error;
        }
    });

    // Save book cover specifically
    ipcMain.handle('media:saveBookCover', async (_event: IpcMainInvokeEvent, bookId: number, coverData: Uint8Array | number[], fileName: string = 'cover.jpg') => {
        try {
            // Validate input parameters
            if (typeof bookId !== 'number' || bookId <= 0) {
                throw new Error('Invalid book ID provided');
            }

            if (!coverData) {
                throw new Error('Cover data is required');
            }

            // Validate coverData is an array or Uint8Array
            if (!Array.isArray(coverData) && !(coverData instanceof Uint8Array)) {
                throw new Error('Cover data must be an array or Uint8Array');
            }

            // Check size limits
            const dataLength = coverData.length;
            if (dataLength === 0) {
                throw new Error('Cover data cannot be empty');
            }

            if (dataLength > MAX_COVER_BYTES) {
                throw new Error(`Cover data size (${dataLength} bytes) exceeds maximum allowed size (${MAX_COVER_BYTES} bytes)`);
            }

            // Convert to Buffer efficiently
            const buffer = coverData instanceof Uint8Array ? Buffer.from(coverData) : Buffer.from(coverData);

            return await mediaApi.saveBookCover(bookId, buffer, fileName);
        } catch (error) {
            console.error(`IPC media:saveBookCover error for book ID ${bookId}:`, error);
            throw error;
        }
    });

    // Get media storage path (useful for frontend to construct URLs)
    ipcMain.handle('media:getStoragePath', async () => {
        try {
            return mediaApi.getMediaStoragePath();
        } catch (error) {
            console.error('IPC media:getStoragePath error:', error);
            throw error;
        }
    });
};

// Register IPC handlers for Books operations
const registerBooksHandlers = (): void => {
    // Create a new book
    ipcMain.handle('books:create', async (_event: IpcMainInvokeEvent, book: Partial<Book>, downloadCover: boolean = true) => {
        try {
            return await booksApi.createBook(book, downloadCover);
        } catch (error) {
            console.error('IPC books:create error:', error);
            throw error;
        }
    });

    // Get all books
    ipcMain.handle('books:getAll', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.getAllBooks();
        } catch (error) {
            console.error('IPC books:getAll error:', error);
            throw error;
        }
    });

    // Get all books with note counts
    ipcMain.handle('books:getAllWithNoteCounts', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.getAllBooksWithNoteCounts();
        } catch (error) {
            console.error('IPC books:getAllWithNoteCounts error:', error);
            throw error;
        }
    });

    // Get books with metadata (enhanced for frontend)
    ipcMain.handle('books:getBooksWithMetadata', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.getBooksWithMetadata();
        } catch (error) {
            console.error('IPC books:getBooksWithMetadata error:', error);
            throw error;
        }
    });

    // Get a book by ID
    ipcMain.handle('books:getById', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await booksApi.getBookById(id);
        } catch (error) {
            console.error(`IPC books:getById error for ID ${id}:`, error);
            throw error;
        }
    });

    // Get book by ISBN
    ipcMain.handle('books:getByIsbn', async (_event: IpcMainInvokeEvent, isbn: string) => {
        try {
            return await booksApi.getBookByIsbn(isbn);
        } catch (error) {
            console.error(`IPC books:getByIsbn error for ISBN ${isbn}:`, error);
            throw error;
        }
    });

    // Get book by OpenLibrary ID
    ipcMain.handle('books:getByOlid', async (_event: IpcMainInvokeEvent, olid: string) => {
        try {
            return await booksApi.getBookByOlid(olid);
        } catch (error) {
            console.error(`IPC books:getByOlid error for OLID ${olid}:`, error);
            throw error;
        }
    });

    // Update a book
    ipcMain.handle('books:update', async (_event: IpcMainInvokeEvent, id: number, bookUpdates: Partial<Book>) => {
        try {
            return await booksApi.updateBook(id, bookUpdates);
        } catch (error) {
            console.error(`IPC books:update error for ID ${id}:`, error);
            throw error;
        }
    });

    // Delete a book
    ipcMain.handle('books:delete', async (_event: IpcMainInvokeEvent, id: number) => {
        try {
            return await booksApi.deleteBook(id);
        } catch (error) {
            console.error(`IPC books:delete error for ID ${id}:`, error);
            throw error;
        }
    });

    // Get recent books
    ipcMain.handle('books:getRecent', async (_event: IpcMainInvokeEvent, days: number = 14) => {
        try {
            return await booksApi.getRecentBooks(days);
        } catch (error) {
            console.error(`IPC books:getRecent error for last ${days} days:`, error);
            throw error;
        }
    });

    // Search books locally
    ipcMain.handle('books:search', async (_event: IpcMainInvokeEvent, searchTerm: string) => {
        try {
            return await booksApi.searchBooks(searchTerm);
        } catch (error) {
            console.error(`IPC books:search error for term ${searchTerm}:`, error);
            throw error;
        }
    });

    // Search books online via OpenLibrary
    ipcMain.handle('books:searchOnline', async (_event: IpcMainInvokeEvent, query: string, limit: number = 20) => {
        try {
            return await booksApi.searchBooksOnline(query, limit);
        } catch (error) {
            console.error(`IPC books:searchOnline error for query ${query}:`, error);
            throw error;
        }
    });

    // Search books both locally and online
    ipcMain.handle('books:searchHybrid', async (_event: IpcMainInvokeEvent, searchTerm: string, includeOnline: boolean = true, onlineLimit: number = 10) => {
        try {
            return await booksApi.searchBooksHybrid(searchTerm, includeOnline, onlineLimit);
        } catch (error) {
            console.error(`IPC books:searchHybrid error for term ${searchTerm}:`, error);
            throw error;
        }
    });

    // Get detailed book information from OpenLibrary
    ipcMain.handle('books:getDetailsFromOpenLibrary', async (_event: IpcMainInvokeEvent, olid: string) => {
        try {
            return await booksApi.getBookDetailsFromOpenLibrary(olid);
        } catch (error) {
            console.error(`IPC books:getDetailsFromOpenLibrary error for OLID ${olid}:`, error);
            throw error;
        }
    });

    // Add book from OpenLibrary search result
    ipcMain.handle('books:addFromOpenLibrary', async (_event: IpcMainInvokeEvent, searchResult: BookSearchResult) => {
        try {
            return await booksApi.addBookFromOpenLibrary(searchResult);
        } catch (error) {
            console.error('IPC books:addFromOpenLibrary error:', error);
            throw error;
        }
    });

    // Download cover image
    ipcMain.handle('books:downloadCover', async (_event: IpcMainInvokeEvent, coverUrl: string, filename: string) => {
        try {
            return await booksApi.downloadCoverImageData(coverUrl);
        } catch (error) {
            console.error(`IPC books:downloadCover error for URL ${coverUrl}:`, error);
            throw error;
        }
    });

    // Ensure folders for all books
    ipcMain.handle('books:ensureFoldersForAll', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.ensureFoldersForAllBooks();
        } catch (error) {
            console.error('IPC books:ensureFoldersForAll error:', error);
            throw error;
        }
    });

    // Get books without folders
    ipcMain.handle('books:getBooksWithoutFolders', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.getBooksWithoutFolders();
        } catch (error) {
            console.error('IPC books:getBooksWithoutFolders error:', error);
            throw error;
        }
    });

    // Check and download missing covers
    ipcMain.handle('books:checkAndDownloadMissingCovers', async (_event: IpcMainInvokeEvent) => {
        try {
            return await booksApi.checkAndDownloadMissingCovers();
        } catch (error) {
            console.error('IPC books:checkAndDownloadMissingCovers error:', error);
            throw error;
        }
    });
};


// Register IPC handlers for Discord Rich Presence operations
const registerDiscordHandlers = (): void => {
    // Initialize Discord RPC
    ipcMain.handle('discord:initialize', async () => {
        try {
            return await discordRpc.initialize();
        } catch (error) {
            console.error('IPC discord:initialize error:', error);
            throw error;
        }
    });

    // Set Discord enabled state
    ipcMain.handle('discord:setEnabled', async (_event: IpcMainInvokeEvent, enabled: boolean) => {
        try {
            await discordRpc.setEnabled(enabled);
            return true;
        } catch (error) {
            console.error('IPC discord:setEnabled error:', error);
            throw error;
        }
    });

    // Set activity
    ipcMain.handle('discord:setActivity', async (_event: IpcMainInvokeEvent, activityData: any) => {
        try {
            await discordRpc.setActivity(activityData);
            return true;
        } catch (error) {
            console.error('IPC discord:setActivity error:', error);
            throw error;
        }
    });

    // Set active state
    ipcMain.handle('discord:setActiveState', async (_event: IpcMainInvokeEvent) => {
        try {
            discordRpc.setActiveState();
            return true;
        } catch (error) {
            console.error('IPC discord:setActiveState error:', error);
            throw error;
        }
    });

    // Set idle activity
    ipcMain.handle('discord:setIdle', async (_event: IpcMainInvokeEvent) => {
        try {
            await discordRpc.setIdle();
            return true;
        } catch (error) {
            console.error('IPC discord:setIdle error:', error);
            throw error;
        }
    });

    // Update settings
    ipcMain.handle('discord:updateSettings', async (_event: IpcMainInvokeEvent, settings: any) => {
        try {
            discordRpc.updateSettings(settings);
            return true;
        } catch (error) {
            console.error('IPC discord:updateSettings error:', error);
            throw error;
        }
    });

    // Clear activity
    ipcMain.handle('discord:clearActivity', async () => {
        try {
            await discordRpc.clearActivity();
            return true;
        } catch (error) {
            console.error('IPC discord:clearActivity error:', error);
            throw error;
        }
    });

    // Get Discord status
    ipcMain.handle('discord:getStatus', async () => {
        try {
            return discordRpc.getStatus();
        } catch (error) {
            console.error('IPC discord:getStatus error:', error);
            throw error;
        }
    });

    // Destroy Discord RPC
    ipcMain.handle('discord:destroy', async () => {
        try {
            await discordRpc.destroy();
            return true;
        } catch (error) {
            console.error('IPC discord:destroy error:', error);
            throw error;
        }
    });

    // Test Discord connection
    ipcMain.handle('discord:testConnection', async () => {
        try {
            return await discordRpc.testConnection();
        } catch (error) {
            console.error('IPC discord:testConnection error:', error);
            throw error;
        }
    });
};

// Register IPC handlers for Sync operations
const registerSyncHandlers = (): void => {
    // Perform sync
    ipcMain.handle('sync:perform', async (_event: IpcMainInvokeEvent, directory: string) => {
        try {
            return await syncAPI.performSync(directory);
        } catch (error) {
            console.error('IPC sync:perform error:', error);
            throw error;
        }
    });

    // Import backup
    ipcMain.handle('sync:import', async (_event: IpcMainInvokeEvent, directory: string) => {
        try {
            return await syncAPI.importBackup(directory);
        } catch (error) {
            console.error('IPC sync:import error:', error);
            throw error;
        }
    });

    // Get sync status
    ipcMain.handle('sync:getStatus', async () => {
        try {
            return await syncAPI.getStatus();
        } catch (error) {
            console.error('IPC sync:getStatus error:', error);
            throw error;
        }
    });

    // Configure sync settings
    ipcMain.handle('sync:configure', async (_event: IpcMainInvokeEvent, settings: any) => {
        try {
            return await syncAPI.configure(settings);
        } catch (error) {
            console.error('IPC sync:configure error:', error);
            throw error;
        }
    });

    // Browse for sync directory
    ipcMain.handle('sync:browseDirectory', async () => {
        try {
            const result = await dialog.showOpenDialog({
                properties: ['openDirectory', 'createDirectory'],
                title: 'Select Sync Directory',
                buttonLabel: 'Select Directory'
            });

            if (result.canceled || result.filePaths.length === 0) {
                return null;
            }

            return result.filePaths[0];
        } catch (error) {
            console.error('IPC sync:browseDirectory error:', error);
            throw error;
        }
    });
};

// Register IPC handlers for Database Hooks operations
const registerDatabaseHooksHandlers = (): void => {
    // Get change history
    ipcMain.handle('database-hooks:getChangeHistory', async (_event: IpcMainInvokeEvent, limit?: number) => {
        try {
            return databaseHooks.getChangeHistory(limit);
        } catch (error) {
            console.error('IPC database-hooks:getChangeHistory error:', error);
            throw error;
        }
    });

    // Get changes for specific item type
    ipcMain.handle('database-hooks:getChangesForItemType', async (_event: IpcMainInvokeEvent, itemType: string, limit?: number) => {
        try {
            return databaseHooks.getChangesForItemType(itemType as any, limit);
        } catch (error) {
            console.error('IPC database-hooks:getChangesForItemType error:', error);
            throw error;
        }
    });

    // Get change statistics
    ipcMain.handle('database-hooks:getChangeStats', async (_event: IpcMainInvokeEvent, since?: string) => {
        try {
            const sinceDate = since ? new Date(since) : undefined;
            return databaseHooks.getChangeStats(sinceDate);
        } catch (error) {
            console.error('IPC database-hooks:getChangeStats error:', error);
            throw error;
        }
    });

    // Clear change history
    ipcMain.handle('database-hooks:clearHistory', async () => {
        try {
            databaseHooks.clearHistory();
            return { success: true };
        } catch (error) {
            console.error('IPC database-hooks:clearHistory error:', error);
            throw error;
        }
    });
};

// Register IPC handlers for Dialog operations
const registerDialogHandlers = (): void => {
    // Handle folder selection dialog
    ipcMain.handle('dialog:selectFolder', async () => {
        try {
            const result = await dialog.showOpenDialog({
                properties: ['openDirectory', 'createDirectory'],
                title: 'Select Folder',
                buttonLabel: 'Select'
            });

            if (result.canceled || result.filePaths.length === 0) {
                return null;
            }

            return result.filePaths[0];
        } catch (error) {
            console.error('IPC dialog:selectFolder error:', error);
            throw error;
        }
    });
};

